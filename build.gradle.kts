// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        // Use Aliyun mirrors for better connectivity in China
        maven("https://maven.aliyun.com/repository/google")
        maven("https://maven.aliyun.com/repository/central") 
        maven("https://maven.aliyun.com/repository/public")
        // Fallback to original repositories
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.4.0")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.22")
        val navVersion = "2.5.3"
        classpath("androidx.navigation:navigation-safe-args-gradle-plugin:$navVersion")
    }
}

allprojects {
    repositories {
        // Use Aliyun mirror for better connectivity in China
        maven {
            name = "AliYunGoogle"
            url = uri("https://maven.aliyun.com/repository/google")
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        maven {
            name = "AliYunCentral"
            url = uri("https://maven.aliyun.com/repository/central")
        }
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}

//plugins {
//    id("com.android.application") version "7.2.2" apply false
//    id("com.android.library") version "7.2.2" apply false
//}

tasks.register<Delete>("clean") {
    delete(rootProject.buildDir)
}