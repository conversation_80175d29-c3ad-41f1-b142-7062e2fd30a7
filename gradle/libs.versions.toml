[versions]
agp = "8.4.0"
androidxJunit = "1.1.3"
appcompatVersion = "1.2.0"
appUpdateVersion = "2.1.0"
arouterAnnotation = "1.0.6"
arouterApi = "1.5.2"
assetDelivery = "2.2.2"
banner = "2.1.0"
baserecyclerviewadapterhelper = "3.0.6"
constraintlayout = "2.2.1"
coreKtxVersion = "1.13.1"
countryPickerAndroid = "2.0"
espressoCoreVersion = "3.4.0"
eventbus = "3.3.1"
exoplayerCore = "2.17.1"
fastjson = "1.2.76"
glide = "4.11.0"
glideTransformations = "4.3.0"
googleCloudTranslate = "2.7.0"
installreferrer = "2.2"
kotlin = "1.9.22"
coreKtx = "1.12.0"
junit = "4.13.2"
junitVersion = "1.1.5"
espressoCore = "3.5.1"
appcompat = "1.6.1"
lifecycleCommonJava8 = "2.9.2"
lottie = "5.2.0"
material = "1.10.0"
materialVersion = "1.5.0"
okhttp = "5.0.0-alpha.2"
photoview = "2.3.0"
pictureselector = "v3.11.1"
playServicesAdsIdentifier = "18.1.0"
recyclerview = "1.4.0"
retrofit = "2.9.0"
review = "2.0.1"
roomRuntime = "2.6.1"
rxandroid = "1.2.1"
rxbinding = "4.0.0"
rxjava = "3.1.5"
smartrefreshlayout = "1.1.3"
utilcodex = "1.31.1"

[libraries]
adapter-rxjava3 = { module = "com.squareup.retrofit2:adapter-rxjava3", version.ref = "retrofit" }
androidx-appcompat-v120 = { module = "androidx.appcompat:appcompat", version.ref = "appcompatVersion" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-core-ktx-v1131 = { module = "androidx.core:core-ktx", version.ref = "coreKtxVersion" }
androidx-espresso-core-v340 = { module = "androidx.test.espresso:espresso-core", version.ref = "espressoCoreVersion" }
androidx-junit-v113 = { module = "androidx.test.ext:junit", version.ref = "androidxJunit" }
androidx-lifecycle-common-java8 = { module = "androidx.lifecycle:lifecycle-common-java8", version.ref = "lifecycleCommonJava8" }
androidx-lifecycle-process = { module = "androidx.lifecycle:lifecycle-process", version.ref = "lifecycleCommonJava8" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "recyclerview" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "roomRuntime" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "roomRuntime" }
arouter-annotation = { module = "com.alibaba:arouter-annotation", version.ref = "arouterAnnotation" }
arouter-api = { module = "com.alibaba:arouter-api", version.ref = "arouterApi" }
arouter-compiler = { module = "com.alibaba:arouter-compiler", version.ref = "arouterApi" }
asset-delivery = { module = "com.google.android.play:asset-delivery", version.ref = "assetDelivery" }
banner = { module = "com.youth.banner:banner", version.ref = "banner" }
baserecyclerviewadapterhelper = { module = "com.github.CymChad:BaseRecyclerViewAdapterHelper", version.ref = "baserecyclerviewadapterhelper" }
compress = { module = "io.github.lucksiege:compress", version.ref = "pictureselector" }
converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit" }
converter-scalars = { module = "com.squareup.retrofit2:converter-scalars", version.ref = "retrofit" }
country-picker-android = { module = "com.github.yesterselga:country-picker-android", version.ref = "countryPickerAndroid" }
eventbus = { module = "org.greenrobot:eventbus", version.ref = "eventbus" }
exoplayer-core = { module = "com.google.android.exoplayer:exoplayer-core", version.ref = "exoplayerCore" }
exoplayer-dash = { module = "com.google.android.exoplayer:exoplayer-dash", version.ref = "exoplayerCore" }
exoplayer-ui = { module = "com.google.android.exoplayer:exoplayer-ui", version.ref = "exoplayerCore" }
fastjson = { module = "com.alibaba:fastjson", version.ref = "fastjson" }
feature-delivery = { module = "com.google.android.play:feature-delivery", version.ref = "appUpdateVersion" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
glide-transformations = { module = "jp.wasabeef:glide-transformations", version.ref = "glideTransformations" }
google-cloud-translate = { module = "com.google.cloud:google-cloud-translate", version.ref = "googleCloudTranslate" }
installreferrer = { module = "com.android.installreferrer:installreferrer", version.ref = "installreferrer" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "okhttp" }
lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
material-v150 = { module = "com.google.android.material:material", version.ref = "materialVersion" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
photoview = { module = "com.github.chrisbanes:PhotoView", version.ref = "photoview" }
pictureselector = { module = "io.github.lucksiege:pictureselector", version.ref = "pictureselector" }
play-app-update = { module = "com.google.android.play:app-update", version.ref = "appUpdateVersion" }
play-services-ads-identifier = { module = "com.google.android.gms:play-services-ads-identifier", version.ref = "playServicesAdsIdentifier" }
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
review = { module = "com.google.android.play:review", version.ref = "review" }
rxandroid = { module = "io.reactivex:rxandroid", version.ref = "rxandroid" }
rxbinding = { module = "com.jakewharton.rxbinding4:rxbinding", version.ref = "rxbinding" }
rxjava = { module = "io.reactivex.rxjava3:rxjava", version.ref = "rxjava" }
smartrefreshheader = { module = "com.scwang.smartrefresh:SmartRefreshHeader", version.ref = "smartrefreshlayout" }
smartrefreshlayout = { module = "com.scwang.smartrefresh:SmartRefreshLayout", version.ref = "smartrefreshlayout" }
ucrop = { module = "io.github.lucksiege:ucrop", version.ref = "pictureselector" }
utilcodex = { module = "com.blankj:utilcodex", version.ref = "utilcodex" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
android-library = { id = "com.android.library", version.ref = "agp" }

