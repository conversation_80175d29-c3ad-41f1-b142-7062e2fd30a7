{"baseHead": "<!DOCTYPE html><html lang='en'><head><meta charset='UTF-8'><meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0;' name='viewport'/><meta http-equiv='X-UA-Compatible' content='ie=edge'><title>Document</title><style>html,body {height: 100%;-webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box;font-family: 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;margin: 0;padding: 0;border: 0;font-size: 14px}.rong-link-site {color: #0579f4;}.rong-pc {width: 30%;min-width: 400px;margin: 0 auto;border-left: 1px solid #DFDFDF;border-right: 1px solid #DFDFDF;min-height: 100%;}.rong-time {width: 100%;height: 40px;text-align: center;color: #999999;position: relative;}.rong-hr {width: 96%;border-bottom: 1px solid #DFDFDF;position: absolute;top: 20px;z-index: -1;margin-left: 2%;}.rong-time-value {text-align: center;height: 40px;line-height: 40px;background: #fff;padding: 0 10px;margin: 0 auto}.rong-message {margin: 10px;padding-bottom: 10px;border-bottom: 1px solid #DFDFDF;color: #999999;margin-left: 49px;position: relative;}.rong-message-user {display: inline-block;vertical-align: top;position: absolute;margin-left: -39px}.rong-message-user-bg {display: inline-block;width: 39px;height: 39px;background: #ccc;text-align: center;line-height: 39px;border-radius: 8px;color: #FFF;object-fit: cover;}.rongcloud-message-body {display: inline-block;vertical-align: top;padding-left: 5px;max-width: 65%}.rongcloud-message-user-name {font-size: 16px;color: #262626;margin-bottom: 4px}.rongcloud-message-text pre {margin: 0;padding: 0;white-space: pre-wrap;white-space: -moz-pre-wrap;white-space: -pre-wrap;white-space: -o-pre-wrap;word-break: break-all;word-wrap: break-word}.rong-message-time {display: inline-block;position: absolute;right: 10px;color: #999999;font-size: 14px;}.rongcloud-message-combinemsg {width: 100%;margin-left: 50px;margin-top: 10px;padding-top: 15px;border-top: 1px solid #ccc}.rong-message-file {display: inline-block;vertical-align: top;padding-left: 5px;max-width: 64%;}.rong-message-file div {overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}.rongcloud-message-img img {max-width: 230px;max-height: 250px;border: none;vertical-align: middle;border-radius: 5px}.rong-message-file img {width: 32px;height: 32px}.rong-message-file img {border-radius: 4px}.rong-message-combine {border: 1px solid #C4C4C4;border-radius: 8px;padding: 10px 0;width: 224px;word-break: break-all}a {text-decoration: none;color: #999999}.rong-combine-title {padding-left: 10px;padding-bottom: 5px;color: #262626}.rong-combine-body {padding-left: 10px;line-height: 20px;margin-bottom: 10px}.rong-conbine-foot {border-top: 1px solid #DFDFDF;margin: 0 10px;padding-top: 5px}.rong-none-user {margin-left: 49px}.rong-none-user .rong-none-user-img {display: none}.rong-big-img {position: absolute;top: 0;width: 100%;height: 100%;background: #fff;display: none}.rong-big-video {position: absolute;top: 0;width: 100%;height: 100%;background: #fff;display: none}.rong-big-img img {position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);max-width: 100%;max-height: 100%;border: none;vertical-align: middle}video {position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);max-width: 100%;max-height: 100%;border: none;vertical-align: middle}</style><style>{%style%}</style></head><body><div class='rong-main'>", "baseBottom": "</div><div class='rong-big-img' onclick='hide(\"rong-big-img\")'></div><div class='rong-big-video' onclick='hide(\"rong-big-video\")'></div><div class='rong-big-a'></div><script>window.onload = function () {if (document.getElementsByName('RC:TxtMsg').length > 0) {for (var i = 0; i < document.getElementsByName('RC:TxtMsg').length; i++) {var text = document.getElementsByName('RC:TxtMsg')[i].children[1].children[0].innerHTML;var replaceText = '';replaceText = replaceUri(text, function (uri, protocol) {var link = uri;if (!protocol) {link = 'http://' + uri;}phnumAfter = \"<a class='rong-link-site' onclick=show({link:'\" + link + \"',type:'link'}) target='_blank' href='\" + link + \"'>\" + uri + \"</a>\"; return phnumAfter;});var numArr = getNum(replaceText);if (numArr && text === replaceText) {for (var j = 0; j < numArr.length; j++) {var phStr = numArr[j].replace(/[^0-9]/ig, '');if (phStr.length == 7 || phStr.length == 11) {phnumAfter = \"<span  class='rong-link-site' onclick=show({phoneNum:\" + numArr[j] + \",type:'phone'})>\" + numArr[j] + \"</span>\";replaceText = replaceText.replace(numArr[j], phnumAfter);}}}document.getElementsByName('RC:TxtMsg')[i].children[1].children[0].innerHTML = replaceText;}}};var domainArray = ['.com', '.net', '.org', '.biz', '.coop', '.info', '.museum', '.name', '.pro', '.edu', '.gov', '.int', '.mil', '.ac', '.ad', '.ae', '.af', '.ag', '.ai', '.al', '.am', '.an', '.ao', '.aq', '.ar', '.as', '.at', '.au', '.aw', '.az', '.ba', '.bb', '.bd', '.be', '.bf', '.bg', '.bh', '.bi', '.bj', '.bm', '.bn', '.bo', '.br', '.bs', '.bt', '.bv', '.bw', '.by', '.bz', '.ca', '.cc', '.cd', '.cf', '.cg', '.ch', '.ci', '.ck', '.cl', '.cm', '.cn', '.co', '.cr', '.cu', '.cv', '.cx', '.cy', '.cz', '.de', '.dj', '.dk', '.dm', '.do', '.dz', '.ec', '.ee', '.eg', '.eh', '.er', '.es', '.et', '.fi', '.fj', '.fk', '.fm', '.fo', '.fr', '.ga', '.gd', '.ge', '.gf', '.gg', '.gh', '.gi', '.gl', '.gm', '.gn', '.gp', '.gq', '.gr', '.gs', '.gt', '.gu', '.gv', '.gy', '.hk', '.hm', '.hn', '.hr', '.ht', '.hu', '.id', '.ie', '.il', '.im', '.in', '.io', '.iq', '.ir', '.is', '.it', '.je', '.jm', '.jo', '.jp', '.ke', '.kg', '.kh', '.ki', '.km', '.kn', '.kp', '.kr', '.kw', '.ky', '.kz', '.la', '.lb', '.lc', '.li', '.lk', '.lr', '.ls', '.lt', '.lu', '.lv', '.ly', '.ma', '.mc', '.md', '.me', '.mh', '.mk', '.ml', '.mm', '.mn', '.mo', '.mp', '.mq', '.mr', '.ms', '.mt', '.mu', '.mv', '.mw', '.mx', '.my', '.mz', '.na', '.nc', '.ne', '.nf', '.ng', '.ni', '.nl', '.no', '.np', '.nr', '.nu', '.nz', '.om', '.pa', '.pe', '.pf', '.pg', '.ph', '.pk', '.pl', '.pm', '.pn', '.pr', '.ps', '.pt', '.pw', '.py', '.qa', '.re', '.ro', '.rw', '.ru', '.sa', '.sb', '.sc', '.sd', '.se', '.sg', '.sh', '.si', '.sj', '.sk', '.sl', '.sm', '.sn', '.so', '.sr', '.st', '.sv', '.sy', '.sz', '.tc', '.td', '.tf', '.tg', '.th', '.tj', '.tk', '.tm', '.tn', '.to', '.tp', '.tr', '.tt', '.tv', '.tw', '.tz', '.ua', '.ug', '.uk', '.um', '.us', '.uy', '.uz', '.va', '.vc', '.ve', '.vg', '.vi', '.vn', '.vu', '.ws', '.wf', '.ye', '.yt', '.yu', '.za', '.zm', '.zw', '.mg', '.site'];function replaceUri(str, callback) {var result = '';var mailReg = '[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\\\.[a-zA-Z0-9_-]+)+';var protocolReg = '((?:http|https|ftp)\\:\\/\\/)?';var ipReg = '(?:(?:25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])\\\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])';var hostReg = '(?!@)(?:[a-z0-9-]{1,36}\\\\.)+[a-z]{2,6}';var portReg = '(?:\\\\:[0-9]{1,5})?';var pathReg = '(?:(?:/[a-zA-Z0-9.,;?\\\\\\'+&%$#=~_\\\\-!()*\\\\/]*)?)';var uriReg = new RegExp('(?:(' + mailReg + ')|(' + protocolReg + '(?:(' + ipReg + ')|(' + hostReg + '))' + portReg + pathReg + '))', 'ig');result = str.replace(uriReg, function (uriStr, protocol, ip, host) {return callback.apply(null, arguments);});return result;}function getNum(text) {var value = text.match(/\\d+(\\.\\d+)?/g);return value;}var scroll_top = 0;function show(data) {buttonClick(data);if (data.isGIF) {showBig('img', data.fileUrl);}if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.buttonClick && window.webkit.messageHandlers.buttonClick.postMessage) {window.webkit.messageHandlers.buttonClick.postMessage(data);} else if (window.interface && window.interface.sendInfoToAndroid) {window.interface.sendInfoToAndroid(JSON.stringify(data));} else if (window.RCFlutterInterface && window.RCFlutterInterface.postMessage) {window.RCFlutterInterface.postMessage(JSON.stringify(data));} else if (data.type == 'RC:CombineMsg') {window.open(data.fileUrl, '_self')} else if (data.type != 'RC:LBSMsg') {if (data.fileUrl) {data.type === 'RC:SightMsg' &&  showBig('video', data.fileUrl);data.type === 'RC:ImgMsg' &&   showBig('img', data.fileUrl);data.type === 'RC:FileMsg' &&   showBig('a', data.fileUrl);};}};function buttonClick(json) { };function hide(id) {document.getElementsByClassName(id)[0].style.display = 'none';document.getElementsByClassName(id)[0].children[0].remove();document.getElementsByClassName('rong-main')[0].style.display = 'block';window.scrollTo(0, scroll_top)};function isWeb() {var browser = {versions: function () {var u = navigator.userAgent,app = navigator.appVersion;return {android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1,iPhone: u.indexOf('iPhone') > -1 || u.indexOf('Mac') > -1,iPad: u.indexOf('iPad') > -1,}}()};if (browser.versions.android || browser.versions.iPhone || browser.versions.iPad) {return false} else {return true}};function showBig(type, url) {scroll_top = getScrollTop();event.currentTarget.name = new Date().getTime();var imgNode = document.createElement(type);imgNode.src = url;imgNode.id = event.currentTarget.name;if(type == 'a') {imgNode.href = url;imgNode.click();return;}document.getElementsByClassName('rong-big-' + type)[0].style.display = 'block';if (type == 'video') {imgNode['x-webkit-airplay'] = 'true';imgNode['webkit-playsinline'] = 'true';imgNode.autoplay = 'autoplay';imgNode.controls = 'controls';}document.getElementsByClassName('rong-big-' + type)[0].name = event.currentTarget.name;document.getElementsByClassName('rong-big-' + type)[0].append(imgNode);document.getElementsByClassName('rong-main')[0].style.display = 'none'}function getScrollTop() {var scroll_top = 0;if (document.documentElement && document.documentElement.scrollTop) {scroll_top = document.documentElement.scrollTop} else if (document.body) {scroll_top = document.body.scrollTop}return scroll_top}var os = function () {var ua = navigator.userAgent,isWindowsPhone = /(?:Windows Phone)/.test(ua),isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone, isAndroid = /(?:Android)/.test(ua),isPhone = /(?:iPhone)/.test(ua) && !isTablet,isPc = !isPhone && !isAndroid && !isSymbian;return { isPc: isPc };}();if (os.isPc) {document.getElementsByClassName('rong-main')[0].classList.add('rong-pc')}</script></body></html>", "time": "<div class='rong-time' name='timeTemplate'><div class='rong-hr'></div><span class='rong-time-value'>{%time%}</span></div>", "CombineMsgBody": "<div><span class='rong-combin-body-text'>{%text%}</span></div>", "RC:TxtMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:TxtMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:SightMsg": "<div class='rong-message {%showUser%}' onClick='show({fileUrl:\"{%fileUrl%}\",duration:\"{%duration%}\",imageBase64:\"{%imageBase64%}\",type:\"RC:SightMsg\"})'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:SightMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rong-message-file'><img class='rong-message-user-bg' src='https://cdn.ronghub.com/sight_msg.png' /></div><div class='rong-message-file'><div>{%fileName%}</div><div>{%size%}</div></div></div></div></div>", "RC:ImgMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:ImgMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-img' onClick='show({imgUrl:\"{%imgUrl%}\",fileUrl:\"{%fileUrl%}\",type:\"RC:ImgMsg\"})'><img src='{%imgUrl%}'></div></div></div></div>", "RC:CombineMsg": "<div class='rong-message {%showUser%}' onClick='show({fileUrl:\"{%fileUrl%}\",title:\"{%title%}\",type:\"RC:CombineMsg\"})'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:CombineMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rong-message-combine' ><div class='rong-combine-title'>{%title%}</div><div class='rong-combine-body'>{%combineBody%}</div><div class='rong-conbine-foot'>{%foot%}</div></div></div></div></div>", "RC:FileMsg": "<div class='rong-message {%showUser%}' onClick='show({fileName:\"{%fileName%}\",fileType:\"{%fileType%}\",fileUrl:\"{%fileUrl%}\",fileSize:\"{%fileSize%}\",type:\"RC:FileMsg\"})'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:FileMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rong-message-file'><img class='rong-message-user-bg' src='{%fileIcon%}' /></div><div class='rong-message-file'><div>{%fileName%}</div><div>{%size%}</div></div></div></div></div>", "RC:VcMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:VcMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:CardMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:CardMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:StkMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:StkMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:ImgTextMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:ImgTextMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:LBSMsg": "<div class='rong-message {%showUser%}' onclick='show({locationName:\"{%locationName%}\",latitude:\"{%latitude%}\",longitude:\"{%longitude%}\",type:\"RC:LBSMsg\"})'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:LBSMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rong-message-file'><img class='rong-message-user-bg' src='https://cdn.ronghub.com/lbs_msg.png'></div><div class='rong-message-file'><div>{%locationName%}</div></div></div></div></div>", "RC:GIFMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:ImgMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-img'><img src='{%fileUrl%}' onerror='this.src=`data:image/png;base64,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`'></div></div></div></div>", "RCJrmf:RpMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RCJrmf:RpMsg'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:VCSummary": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg' /></div><div class='rongcloud-message-body'><div name='RC:VCSummary'><div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>"}