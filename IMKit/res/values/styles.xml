<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="PictureThemeWindowStyle">
        <item name="android:windowEnterAnimation">@anim/rc_picture_anim_album_show</item>
        <item name="android:windowExitAnimation">@anim/rc_picture_anim_album_dismiss</item>
    </style>

    <style name="PictureThemeDialogWindowStyle">
        <item name="android:windowEnterAnimation">@anim/rc_picture_anim_modal_in</item>
        <item name="android:windowExitAnimation">@anim/rc_picture_anim_modal_out</item>
    </style>

    <style name="Picture.Theme.Dialog.AudioStyle">
        <item name="android:windowEnterAnimation">@anim/rc_picture_anim_enter</item>
        <item name="android:windowExitAnimation">@anim/rc_picture_anim_exit</item>
    </style>

    <style name="Picture.Theme.AlertDialog" parent="android:Theme.Dialog">
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowBackground">@color/picture_color_transparent</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:backgroundDimAmount">0.4</item>
    </style>


    <style name="Picture.Theme.Dialog" parent="@android:style/Theme.Dialog">

        <!-- 边框 -->
        <item name="android:windowFrame">@android:color/transparent</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 半透明 -->
        <item name="android:windowIsTranslucent">false</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <!-- 自己想要的背景 -->
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <!--仿微信样式，注意每一项都不能少-->
    <style name="picture.WeChat.style" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <!--标题栏背景色-->
        <item name="colorPrimary">@color/rc_background_main_color</item>
        <!--状态栏背景色-->
        <item name="colorPrimaryDark">@color/rc_background_main_color</item>
        <!--是否改变图片列表界面状态栏字体颜色为黑色-->
        <item name="picture.statusFontColor">false</item>
        <!--返回键图标-->
        <item name="picture.leftBack.icon">@drawable/rc_picture_icon_close</item>
        <!--标题下拉箭头-->
        <item name="picture.arrow_down.icon">@drawable/rc_picture_icon_wechat_down</item>
        <!--标题上拉箭头-->
        <item name="picture.arrow_up.icon">@drawable/rc_picture_icon_wechat_up</item>
        <!--标题文字颜色-->
        <item name="picture.title.textColor">@color/app_color_white</item>
        <!--标题栏右边文字-->
        <item name="picture.right.textColor">@color/app_color_white</item>
        <!--图片列表勾选样式-->
        <item name="picture.checked.style">@drawable/rc_picture_check_selector</item>
        <!--开启图片列表勾选数字模式,开启的话勾选样式要换-->
        <item name="picture.style.checkNumMode">true</item>
        <!--选择图片样式0/9-->
        <item name="picture.style.numComplete">false</item>
        <!--图片列表底部背景色-->
        <item name="picture.bottom.bg">@color/app_color_grey</item>
        <!--图片列表预览文字颜色-->
        <item name="picture.preview.textColor">@color/app_color_white</item>
        <!--图片列表已完成文字颜色-->
        <item name="picture.complete.textColor">@color/app_color_white</item>
        <!--图片已选数量圆点背景色-->
        <item name="picture.num.style">@drawable/num_oval_blue</item>
        <!--预览界面标题文字颜色-->
        <item name="picture.ac_preview.title.textColor">@color/app_color_white</item>
        <!--预览界面已完成文字颜色-->
        <item name="picture.ac_preview.complete.textColor">@color/app_color_white</item>
        <!--预览界面标题栏背景色-->
        <item name="picture.ac_preview.title.bg">@color/app_color_grey</item>
        <!--预览界面底部背景色-->
        <item name="picture.ac_preview.bottom.bg">@color/picture_color_70</item>
        <!--预览界面返回箭头-->
        <item name="picture.preview.leftBack.icon">@drawable/rc_picture_icon_back</item>
        <!--裁剪页面标题背景色-->
        <item name="picture.crop.toolbar.bg">@color/app_color_grey</item>
        <!--裁剪页面状态栏颜色-->
        <item name="picture.crop.status.color">@color/app_color_grey</item>
        <!--裁剪页面标题文字颜色-->
        <item name="picture.crop.title.color">@color/app_color_white</item>
        <!--相册文件夹列表选中图标-->
        <item name="picture.folder_checked_dot">@drawable/rc_picture_orange_oval</item>
    </style>


    <style name="Picture.Theme.Translucent" parent="Base.Theme.NoActionBar">
        <item name="android:windowBackground">@color/picture_color_transparent
        </item> <!-- 背景色透明度 -->
        <item name="android:windowNoTitle">true</item><!-- 无标题 -->
        <item name="android:windowIsTranslucent">true</item><!-- 半透明,设置为false无透明效果 -->
    </style>

    <style name="Base.Theme.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar" />
    <style name="Theme.AppCompat.Empty" parent=""/>
    <style name="Theme.AppCompat.Light.NoActionBar" parent=""/>
</resources>