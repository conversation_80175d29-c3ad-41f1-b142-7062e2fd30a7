<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 设置聊天室一次拉取消息数目 设置0默认拉取10条，设置-1默认不拉取，其他值为拉取自定义条数 -->
    <integer name="rc_chatroom_first_pull_message_count">10</integer>

    <!-- 设置在会话列表或会话页面是否显示“连接状态”的提示 -->
    <bool name="rc_is_show_warning_notification">true</bool>

    <!-- 设置未听的语音消息，是否连续播放 -->
    <bool name="rc_play_audio_continuous">true</bool>

    <!-- 进入客服会话，弹出评价菜单超时时间(单位：秒)-->
    <!-- 设置为 0 时，任何时候离开客服会话时，都会弹出评价菜单-->
<!--    <integer name="rc_custom_service_evaluation_interval">60</integer>-->

    <!-- 退出客服页面是否关闭客服 -->
<!--    <bool name="rc_stop_custom_service_when_quit">true</bool>-->

    <!-- 默认图片保存路径，在会话界面查看大图，长按时选择保存，会使用这个配置-->
    <string name="rc_image_default_saved_path">/RongCloud/Image/</string>

    <!-- 默认视频保存路径，在会话界面查看，长按时选择保存，会使用这个配置-->
    <string name="rc_video_default_saved_path">/RongCloud/Video/</string>

    <!-- 默认文件保存路径，在会话界面查看，长按时选择保存，会使用这个配置-->
    <string name="rc_file_default_saved_path">/RongCloud/File/</string>

    <!-- 设置是否支持消息撤回-->
    <bool name="rc_enable_message_recall">true</bool>
    <!-- 消息撤回有效时间（单位：秒）-->
    <integer name="rc_message_recall_interval">120</integer>
    <!-- 消息撤回重新编辑有效时间（单位：秒）-->
    <integer name="rc_message_recall_edit_interval">300</integer>

    <!-- 是否打开 @ 功能-->
    <bool name="rc_enable_mentioned_message">true</bool>

    <!-- 设置已读回执，目前仅支持单聊、群聊、讨论组 -->
    <bool name="rc_read_receipt">true</bool>

    <!-- 设置已读回执有效时间（单位：秒）-->
    <integer name="rc_read_receipt_request_interval">120</integer>

    <!-- 开启之后，用户在其他端上阅读过的消息，当前客户端会清掉该消息的未读数。目前仅支持单聊、群聊、讨论组。-->
    <bool name="rc_enable_sync_read_status">true</bool>

    <!--WebView 是否忽略 SSL 证书错误检测-->
<!--    <bool name="rc_open_https_certificate">false</bool>-->

    <bool name="rc_location_2D">false</bool>

    <!--PictureSelectorActivity 选择媒体资源时，是否包含视频文件 -->
    <bool name="rc_media_selector_contain_video">false</bool>

    <!--进入会话或会话列表界面，是否清除通知栏通知-->
    <bool name="rc_wipe_out_notification_message">true</bool>


    <!--是否允许 WebView 加载 JavaScript-->
    <bool name="rc_set_java_script_enabled">true</bool>

    <!--在线时是否自动下载高质量语音消息-->
    <bool name="rc_enable_automatic_download_voice_msg">true</bool>

    <!--gif 自动下载的最大值， 超过就需点击手动下载（单位 KB）-->
    <integer name="rc_gifmsg_auto_download_size">1024</integer>

    <!--是否启用阅后即焚插件-->
    <bool name="rc_open_destruct_plugin">false</bool>

    <!--多选时,最大多选消息数 -->
    <integer name="rc_max_message_selected_count">100</integer>

    <!--是否开启合并转发功能,默认关闭 -->
    <bool name="rc_enable_send_combine_message">false</bool>

    <!--在前台非会话页面时，接收到新消息是否响铃 -->
    <bool name="rc_sound_in_foreground">true</bool>

    <!--在前台非会话页面时，接收到新消息是否震动 -->
    <bool name="rc_vibrate_in_foreground">true</bool>

    <!--会话页面右上角的未读 @ 消息数提示，当收到的消息中有 @ 消息时，进入会话之后，是否在右上角提示未读 @ 消息数-->
    <bool name="rc_enable_unread_mention">true</bool>

    <!-- 进入会话界面，默认拉取历史消息数量 -->
    <integer name="rc_conversation_history_message_count">10</integer>
    <!-- 进入会话界面，默认拉取远端历史消息数量 -->
    <integer name="rc_conversation_remote_message_count">10</integer>
    <!-- 进入会话界面，默认显示未读消息数量 -->
    <integer name="rc_conversation_show_unread_message_count">10</integer>

    <!-- 实时位置功能是否请求后台位置权限,Android 10及以上版本位置权限拆分为了前台位置和后台位置权限 -->
    <bool name="rc_enable_background_location_permission">false</bool>

    <!-- 翻译功能的默认原语言类型-->
    <string name="rc_translation_src_language">zh_CN</string>
    <!-- 翻译功能的默认目标语言类型-->
    <string name="rc_translation_target_language">en</string>

</resources>