<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="5dp"
    android:background="@android:color/transparent"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <ImageView
        android:id="@+id/rc_checkbox"
        android:layout_marginStart="20dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/rc_select_conversation_checkbox" />

    <ImageView
        android:id="@+id/rc_user_portrait"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginStart="16dp"
        android:background="@android:color/transparent"
        android:scaleType="centerCrop"
        android:src="@drawable/rc_default_portrait" />

    <TextView
        android:id="@+id/rc_user_name"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="8dp"
        android:gravity="center"
        android:textColor="#262626"
        android:textSize="16sp" />

</LinearLayout>