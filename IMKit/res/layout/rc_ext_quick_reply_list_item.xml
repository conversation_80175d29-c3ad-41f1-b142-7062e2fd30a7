<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/rc_phrases_tv"
        style="@style/TextStyle.Alignment"
        android:layout_centerInParent="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="22dp"
        android:layout_marginEnd="22dp"
        android:gravity="start"
        android:textColor="@color/rc_secondary_color"
        android:textSize="16sp"
        android:maxLines="2"
        android:maxLength="50"
        android:maxEms="20" />

</RelativeLayout>