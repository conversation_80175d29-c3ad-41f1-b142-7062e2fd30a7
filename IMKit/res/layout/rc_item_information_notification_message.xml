<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="center"
    android:orientation="horizontal"
    android:paddingStart="2dp"
    android:paddingTop="4dp"
    android:paddingEnd="2dp"
    android:paddingBottom="4dp"
    android:background="@drawable/rc_corner_style">

    <TextView
        android:id="@+id/rc_msg"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingStart="4dp"
        android:paddingTop="4dp"
        android:paddingEnd="4dp"
        android:paddingBottom="4dp"
        android:textColor="@color/rc_text_color_primary_inverse"
        android:textSize="14sp"
        android:autoLink="phone"/>

    <TextView
        android:id="@+id/rc_edit"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@id/rc_msg"
        android:gravity="center"
        android:paddingStart="4dp"
        android:paddingTop="4dp"
        android:paddingEnd="2dp"
        android:paddingBottom="4dp"
        android:text="@string/rc_you_recalled_edit"
        android:textColor="@color/rc_blue"
        android:textSize="14sp"
        android:visibility="gone" />
</LinearLayout>
