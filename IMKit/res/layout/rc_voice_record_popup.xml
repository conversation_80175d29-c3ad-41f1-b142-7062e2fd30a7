<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="186dp"
        android:layout_height="186dp"
        android:layout_gravity="center"
        android:background="@drawable/rc_voice_record_popup_bg"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/rc_audio_state_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="11dp"
            android:layout_marginStart="41dp"
            android:layout_marginEnd="41dp"
            android:layout_marginTop="20dp" />

        <TextView
            android:id="@+id/rc_audio_timer"
            style="@style/TextStyle.Alignment"
            android:layout_width="105dp"
            android:layout_height="105dp"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="11dp"
            android:layout_marginStart="41dp"
            android:layout_marginEnd="41dp"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:textColor="@android:color/white"
            android:textSize="80sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/rc_audio_state_text"
            style="@style/TextStyle.Alignment"
            android:layout_width="160dp"
            android:layout_height="40dp"
            android:layout_alignParentBottom="true"
            android:layout_gravity="center"
            android:layout_marginBottom="11dp"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"
            android:layout_marginTop="11dp"
            android:background="@drawable/rc_voice_cancel_background"
            android:gravity="center"
            android:textColor="@android:color/white"
            android:textSize="13dp" />
    </RelativeLayout>
</FrameLayout>