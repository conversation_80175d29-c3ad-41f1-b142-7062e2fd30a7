<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rc_ac_layout_preview_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/rc_ac_ll_download_file_detail_info"
        android:layout_width="160dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="68dp"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/rc_ac_iv_file_type_image"
            android:layout_width="@dimen/rc_file_preview_image_size"
            android:layout_height="@dimen/rc_file_preview_image_size"
            android:layout_gravity="center_horizontal" />

        <TextView
            android:id="@+id/rc_ac_tv_file_name"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/rc_file_preview_name_margin_top"
            android:ellipsize="middle"
            android:singleLine="true"
            android:textColor="@color/rc_file_preview_name"
            android:textSize="@dimen/rc_file_preview_name_size" />
    </LinearLayout>

    <TextView
        android:id="@+id/rc_ac_tv_file_size"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="@dimen/rc_file_preview_margin_bottom"
        android:layout_marginTop="@dimen/rc_file_preview_margin_top"
        android:textColor="#a8a8a8"
        android:textSize="@dimen/rc_file_preview_size" />

<!--    <LinearLayout-->
<!--        android:id="@+id/rc_ac_ll_progress_view"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_gravity="center"-->
<!--        android:orientation="vertical"-->
<!--        android:visibility="gone">-->

<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->
<!--            android:layout_gravity="center_vertical"-->
<!--            android:layout_marginLeft="40dp"-->
<!--            android:layout_marginRight="40dp"-->
<!--            android:orientation="horizontal">-->

<!--            <ProgressBar-->
<!--                android:id="@+id/rc_ac_pb_download_progress"-->
<!--                style="@style/rc_pb_file_download_progress"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="8dp"-->
<!--                android:layout_gravity="center_vertical"-->
<!--                android:layout_marginLeft="3dp"-->
<!--                android:layout_marginRight="3dp"-->
<!--                android:layout_weight="1" />-->

<!--            <ImageView-->
<!--                android:id="@+id/rc_btn_cancel"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginRight="36dp"-->
<!--                android:src="@drawable/rc_btn_download_cancel" />-->
<!--        </LinearLayout>-->

<!--        <TextView-->
<!--            android:id="@+id/rc_ac_tv_download_progress"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center_horizontal"-->
<!--            android:layout_marginTop="22dp"-->
<!--            android:textColor="#343434"-->
<!--            android:textSize="16sp" />-->
<!--    </LinearLayout>-->

    <TextView
        android:id="@+id/rc_ac_btn_download_button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/rc_file_preview_download_button_height"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:layout_marginStart="@dimen/rc_file_preview_download_button_left_margin"
        android:layout_marginEnd="@dimen/rc_file_preview_download_button_left_margin"
        android:background="@drawable/rc_ac_btn_file_download_open_button"
        android:textColor="#ffffff"
        android:stateListAnimator="@null"
        android:textSize="@dimen/rc_file_preview_download_button_text_size"
        android:text="@string/rc_ac_file_preview_begin_download" />
</LinearLayout>