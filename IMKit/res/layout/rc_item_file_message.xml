<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/rc_btn_cancel"
        android:layout_width="@dimen/rc_file_item_cancel_size"
        android:layout_height="@dimen/rc_file_item_cancel_size"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:src="@drawable/rc_file_icon_cancel" />

    <ProgressBar
        android:id="@+id/rc_progress"
        style="?android:attr/progressBarStyle"
        android:layout_width="13dp"
        android:layout_height="13dp"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="11dp"
        android:indeterminateDrawable="@drawable/rc_progress_sending_style"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/rc_message"
        android:layout_width="@dimen/rc_file_item_width"
        android:layout_height="@dimen/rc_file_item_height"
        android:background="@drawable/rc_bg_file_message_send"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/rc_margin_size_12"
            android:layout_marginTop="@dimen/rc_margin_size_12"
            android:orientation="horizontal">

            <FrameLayout
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="@dimen/rc_margin_size_12">

                <ImageView
                    android:id="@+id/rc_msg_iv_file_type_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    tools:src="@drawable/rc_file_icon_word" />

                <io.rong.imkit.widget.FileRectangleProgress
                    android:id="@+id/rc_msg_pb_file_upload_progress"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:backgroundColor="#99000000"
                    app:circleCorner="3dp"
                    app:circleProgress="0"
                    app:startAngle="270" />

            </FrameLayout>

            <RelativeLayout
                android:layout_width="@dimen/rc_file_item_content_width"
                android:layout_height="@dimen/rc_file_item_content_height"
                android:layout_gravity="center_vertical">

                <io.rong.imkit.widget.EllipsizeTextView
                    android:id="@+id/rc_msg_tv_file_name"
                    style="@style/TextStyle.Alignment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_marginTop="-3dp"
                    android:gravity="top"
                    android:maxLines="2"
                    android:textColor="@color/rc_text_main_color"
                    android:textSize="@dimen/rc_file_item_name_size"
                    app:RCEllipsizeIndex="8" />

                <TextView
                    android:id="@+id/rc_msg_tv_file_size"
                    style="@style/TextStyle.Alignment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_gravity="bottom"
                    android:layout_marginBottom="-2dp"
                    android:gravity="bottom"
                    android:text="149kb"
                    android:textColor="#C7CBCE"
                    android:textSize="@dimen/rc_file_item_size" />

                <TextView
                    android:id="@+id/rc_msg_canceled"
                    style="@style/TextStyle.Alignment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="-2dp"
                    android:text="@string/rc_ac_file_download_progress_pause"
                    android:textColor="#a8a8a8"
                    android:textSize="12sp"
                    android:visibility="gone" />
            </RelativeLayout>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>