<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@id/rc_layout"
    android:layout_width="231dp"
    android:layout_height="wrap_content"
    android:background="@android:color/white">

    <TextView
        android:id="@+id/rc_title"
        style="@style/RCTheme.Message.TextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="6dp"
        android:background="@android:color/transparent"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:lines="2" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/rc_title"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/rc_content"
            style="@style/RCTheme.Message.RichContent.TextView"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:layout_below="@+id/rc_title"
            android:layout_marginStart="12dp"
            android:background="@android:color/transparent"
            android:ellipsize="end"
            android:gravity="start"
            android:lines="3"
            android:autoLink="web"
            android:paddingBottom="9dp" />

        <ImageView
            android:id="@+id/rc_img"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:layout_marginStart="5dp"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="5dp"
            android:layout_toEndOf="@id/rc_content"
            android:background="@android:color/transparent"
            android:scaleType="center"
            android:src="@drawable/rc_ic_def_rich_content" />

    </LinearLayout>

</RelativeLayout>