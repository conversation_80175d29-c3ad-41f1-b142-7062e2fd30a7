<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ProgressBar
        android:id="@+id/rc_web_progress"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:layout_gravity="center"
        android:layout_marginTop="160dp"
        android:indeterminateDrawable="@drawable/rc_web_loading_style"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/rc_web_download_failed"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_gravity="center"
        android:layout_marginTop="160dp"
        android:src="@drawable/rc_web_load_fail"
        android:visibility="gone" />

    <TextView
        android:id="@+id/rc_web_download_text"
        style="@style/TextStyle.Alignment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/rc_combine_webview_loading"
        android:textColor="#FF666666"
        android:textSize="30px"
        android:visibility="gone" />

    <io.rong.common.RongWebView
        android:id="@+id/rc_webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

</LinearLayout>