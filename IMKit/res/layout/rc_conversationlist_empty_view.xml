<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:layout_marginTop="@dimen/rc_margin_size_127"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/rc_empty_iv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/rc_conversation_list_empty"/>

    <TextView
        android:id="@+id/rc_empty_tv"
        style="@style/TextStyle.Alignment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/rc_conversation_list_empty_prompt"
        android:textColor="@color/rc_secondary_color"
        android:textAlignment="center"
        android:textSize="@dimen/rc_font_text_third_size" />
</LinearLayout>