<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/rc_view_pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:overScrollMode="never"
            android:scrollbarSize="0dp"
            android:scrollbars="none"
            android:visibility="visible"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/rc_emotion_tab_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#ffffff"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/rc_emoticon_tab_add"
                    android:layout_width="60dp"
                    android:layout_height="36dp"
                    android:scaleType="center"
                    android:visibility="gone"
                    android:src="@drawable/rc_ext_tab_add"/>

                <HorizontalScrollView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:scrollbars="none"
                    android:layout_gravity="center_vertical">

                    <LinearLayout
                        android:id="@+id/rc_emotion_scroll_tab"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"/>
                </HorizontalScrollView>
                
                <ImageView
                    android:id="@+id/rc_emoticon_tab_setting"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp"
                    android:src="@drawable/rc_ext_tab_setting" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2px"
                android:layout_alignParentTop="true"
                android:background="@color/rc_divider_color"/>
        </RelativeLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="2px"
        android:layout_alignParentTop="true"
        android:background="@color/rc_divider_color"/>
</RelativeLayout>