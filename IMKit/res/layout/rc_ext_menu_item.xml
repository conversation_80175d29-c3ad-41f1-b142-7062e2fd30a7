<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/rc_menu_title"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:singleLine="true"
        android:textColor="@color/rc_ext_menu_item_text_color"
        android:textSize="@dimen/rc_font_text_third_size" />

    <ImageView
        android:id="@+id/rc_menu_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="5dp"
        android:background="@android:color/transparent"
        android:src="@drawable/rc_default_portrait"
        android:visibility="gone" />

    <View
        android:layout_width="0.05dip"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:background="@android:color/darker_gray" />
</RelativeLayout>


