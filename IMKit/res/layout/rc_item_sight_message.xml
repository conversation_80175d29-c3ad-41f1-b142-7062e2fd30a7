<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:orientation="horizontal">

    <RelativeLayout
        android:id="@+id/rc_sight_operation"
        android:layout_width="32dp"
        android:layout_height="match_parent"
        android:visibility="gone">

        <ImageView
            android:id="@+id/rc_sight_operation_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:src="@drawable/rc_file_icon_cancel"
            android:visibility="gone" />
    </RelativeLayout>

    <io.rong.imkit.widget.RCMessageFrameLayout
        android:id="@+id/rc_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/rc_sight_thumb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <!--android:scaleType="centerCrop"
        app:RCMinShortSideSize="140dp"-->

        <ImageView
            android:id="@+id/rc_sight_tag"
            android:layout_width="@dimen/rc_sight_play_size"
            android:layout_height="@dimen/rc_sight_play_size"
            android:layout_gravity="center"
            android:scaleType="centerCrop"
            android:src="@drawable/rc_icon_video_play" />

        <ProgressBar
            android:id="@+id/compressVideoBar"
            android:layout_width="@dimen/rc_sight_progress_size"
            android:layout_height="@dimen/rc_sight_progress_size"
            android:layout_gravity="center"
            android:visibility="gone" />

        <io.rong.imkit.widget.CircleProgressView
            android:id="@+id/rc_sight_progress"
            android:layout_width="41dp"
            android:layout_height="41dp"
            android:layout_gravity="center"
            android:visibility="gone" />

        <TextView
            android:id="@+id/rc_sight_duration"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginBottom="4dp"
            android:layout_marginEnd="2dp"
            android:textColor="@android:color/white"
            android:textSize="12sp" />
    </io.rong.imkit.widget.RCMessageFrameLayout>
</LinearLayout>
