<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:orientation="vertical">

    <TextView
        android:id="@+id/rc_sub_menu_title"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:padding="10dp"
        android:textSize="@dimen/rc_font_nav_or_date_size"
        android:textColor="@color/rc_secondary_color"
        android:layout_gravity="center" />

    <View
        android:id="@+id/rc_sub_menu_divider_line"
        android:layout_width="match_parent"
        android:layout_height="0.5px"
        android:layout_marginStart="3dp"
        android:layout_marginEnd="5dp"
        android:visibility="gone"
        android:background="@android:color/darker_gray" />
</LinearLayout>