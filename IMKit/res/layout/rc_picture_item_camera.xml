<?xml version="1.0" encoding="utf-8"?>
<io.rong.imkit.picture.widget.SquareRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/picture_color_light_grey"
    android:gravity="center_vertical"
    android:orientation="vertical">
    <TextView
        android:id="@+id/tvCamera"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="8dp"
        android:drawableTop="@drawable/rc_picture_icon_camera"
        android:gravity="center"
        android:text="@string/rc_picture_take_picture"
        android:textColor="@color/picture_color_white"
        android:textSize="14dp" />

</io.rong.imkit.picture.widget.SquareRelativeLayout>