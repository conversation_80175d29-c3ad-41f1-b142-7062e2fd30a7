<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".picture.PictureSelectorActivity">

    <FrameLayout
        android:id="@+id/fl_top"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:background="@color/rc_background_main_color"
        app:layout_constraintBottom_toTopOf="@id/picture_recycler"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/ll_Album"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/picture_title"
                style="@style/TextStyle.Alignment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:maxEms="11"
                android:text="@string/rc_picture_camera_roll"
                android:textColor="@color/rc_text_main_color"
                android:textSize="@dimen/rc_font_secondary_size" />

            <ImageView
                android:id="@+id/ivArrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:scaleType="centerInside"
                android:src="?attr/picture.arrow_down.icon" />
        </LinearLayout>

        <TextView
            android:id="@+id/picture_cancel"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:gravity="center"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="@string/rc_picture_cancel"
            android:textColor="@color/rc_main_theme"
            android:textSize="@dimen/rc_font_text_third_size" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_bottom"
        android:layout_width="0dp"
        android:layout_height="45dp"
        android:background="@color/rc_background_main_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/picture_recycler">

        <TextView
            android:id="@+id/picture_id_preview"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="start"
            android:enabled="false"
            android:gravity="center"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="@string/rc_picture_preview"
            android:textColor="@color/rc_text_main_color"
            android:textSize="@dimen/rc_font_secondary_size" />

        <TextView
            android:id="@+id/picture_tv_ok"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:enabled="false"
            android:gravity="center"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="@string/rc_picture_send"
            android:textColor="@color/rc_main_theme"
            android:textSize="@dimen/rc_font_secondary_size" />
    </FrameLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/picture_recycler"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="2dp"
        app:layout_constraintBottom_toTopOf="@id/fl_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fl_top" />

    <TextView
        android:id="@+id/tv_empty"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:lineSpacingExtra="3dp"
        android:text="@string/rc_picture_empty"
        android:textColor="@color/rc_white_color"
        android:textSize="18dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
