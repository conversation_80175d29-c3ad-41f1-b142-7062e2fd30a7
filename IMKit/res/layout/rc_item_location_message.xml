<?xml version="1.0" encoding="utf-8"?>
<io.rong.imkit.widget.RCMessageFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rc_location"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_centerInParent="true"
    android:layout_centerHorizontal="true"
    android:layout_gravity="center"
    android:gravity="center_horizontal">

    <ImageView
        android:id="@+id/rc_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="centerCrop"
        android:layout_gravity="end"
        android:src="@drawable/rc_ic_location_item_default" />

    <TextView
        android:id="@+id/rc_location_content"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom|end"
        android:background="@drawable/rc_corner_location_style"
        android:gravity="center"
        android:maxLines="2"
        android:minHeight="24dp"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:textColor="@android:color/white"
        android:textSize="12sp" />
</io.rong.imkit.widget.RCMessageFrameLayout>