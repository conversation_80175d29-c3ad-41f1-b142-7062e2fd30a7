<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:gravity="center"
    android:background="@android:color/white"
    android:orientation="vertical">

    <ScrollView
        android:id="@+id/rc_scroll"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fadingEdge="none"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/rc_scroll_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/rc_close_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:paddingBottom="5dp"
                android:paddingEnd="5dp"
                android:paddingStart="5dp"
                android:paddingTop="5dp"
                android:src="@drawable/rc_cs_close" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_marginTop="10dp"
                android:text="@string/rc_cs_evaluate_title"
                android:textColor="@color/rc_secondary_color"
                android:textSize="17sp" />

            <RatingBar
                android:id="@+id/rc_rating_bar"
                android:progressDrawable="@drawable/rc_cs_ratingbar"
                android:minHeight="62.5dp"
                android:maxHeight="62.5dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="24dp"
                android:numStars="5"
                android:rating="5"
                android:stepSize="1" />

            <TextView
                android:id="@+id/rc_evaluate_level"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="@string/rc_cs_very_satisfactory"
                android:textColor="#f4aa3a"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/rc_cs_resolved_or_not"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="24dp"
                android:text="@string/rc_cs_resolved_or_not"
                android:textColor="@color/rc_secondary_color"
                android:textSize="17sp" />

            <LinearLayout
                android:id="@+id/rc_resolve_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="21dp"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/rc_cs_resolved"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="21dp"
                    android:clickable="true"
                    android:src="@drawable/rc_cs_resolved_hover" />

                <ImageView
                    android:id="@+id/rc_cs_resolving"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:clickable="true"
                    android:src="@drawable/rc_cs_follow" />

                <ImageView
                    android:id="@+id/rc_cs_unresolved"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:clickable="true"
                    android:src="@drawable/rc_cs_unresolved" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <EditText
        android:id="@+id/rc_cs_evaluate_content"
        style="@style/EditTextStyle.Alignment"
        android:layout_width="224dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="21dp"
        android:layout_marginEnd="21dp"
        android:layout_marginTop="15dp"
        android:paddingStart="5dp"
        android:background="@drawable/rc_cs_comment_bg"
        android:cursorVisible="true"
        android:gravity="top|start"
        android:hint="@string/rc_cs_please_comment"
        android:maxLength="200"
        android:maxLines="4"
        android:minLines="3"
        android:scrollbars="vertical"
        android:textColor="@color/rc_secondary_color"
        android:textColorHint="#999999"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/rc_submit_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="28dp"
        android:layout_marginTop="24dp"
        android:background="@drawable/rc_cs_submit_comment"
        android:gravity="center"
        android:text="@string/rc_cs_submit_evaluate_content"
        android:textColor="@android:color/white" />

</LinearLayout>
