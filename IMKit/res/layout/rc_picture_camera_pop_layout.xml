<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/fl_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/picture_color_70">

    <LinearLayout
        android:id="@+id/ll_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/picture_color_fa"
        android:orientation="vertical">

        <TextView
            android:id="@+id/picture_tv_photo"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/rc_picture_item_select_bg"
            android:gravity="center"
            android:text="@string/rc_picture_photograph"
            android:textColor="@color/picture_color_53575e"
            android:textSize="14sp" />

        <TextView
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/picture_color_e" />

        <TextView
            android:id="@+id/picture_tv_video"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/rc_picture_item_select_bg"
            android:gravity="center"
            android:text="@string/rc_picture_record_video"
            android:textColor="@color/picture_color_53575e"
            android:textSize="14sp" />

        <TextView
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/picture_color_e" />

        <TextView
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="6dp"
            android:background="@color/picture_color_e" />

        <TextView
            android:id="@+id/picture_tv_cancel"
            style="@style/TextStyle.Alignment"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@drawable/rc_picture_item_select_bg"
            android:gravity="center"
            android:text="@string/rc_picture_cancel"
            android:textColor="@color/picture_color_53575e"
            android:textSize="14sp" />
    </LinearLayout>
</FrameLayout>