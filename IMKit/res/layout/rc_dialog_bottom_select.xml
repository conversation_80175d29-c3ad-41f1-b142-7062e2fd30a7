<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rc_dialog_bottom_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/rc_bottom_dialog_bg"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/rc_dialog_bottom_item_to_cancel_spacing"
        android:background="@color/rc_item_space_color" />

    <TextView
        android:id="@+id/rc_dialog_bottom_item_cancel"
        style="@style/TextStyle.Alignment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/rc_dialog_bottom_text_item_margin"
        android:gravity="center"
        android:text="@string/rc_cancel"
        android:textColor="@color/rc_dialog_bottom_text_color"
        android:textSize="@dimen/rc_dialog_bottom_text_size" />
</LinearLayout>