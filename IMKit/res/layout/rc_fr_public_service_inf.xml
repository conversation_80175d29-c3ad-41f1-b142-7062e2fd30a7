<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@android:color/white"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:layout_height="64dp">

                <ImageView
                    android:id="@+id/portrait"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/rc_default_portrait"
                    android:layout_centerVertical="true"
                    />

                <TextView
                    android:id="@+id/name"
                    style="@style/RCTheme.Message.TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="10dp"
                    android:layout_toRightOf="@+id/portrait"
                    android:singleLine="true"/>

                <TextView
                    android:id="@+id/account"
                    style="@style/RCTheme.Message.RichContent.TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="3dp"
                    android:layout_below="@+id/name"
                    android:layout_marginStart="12dp"
                    android:layout_toRightOf="@id/portrait"/>
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/rel_group_intro"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="17dp">

                <TextView
                    style="@style/RCTheme.Message.TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="24dp"
                    android:id="@+id/func"
                    android:text="@string/rc_pub_service_info_description"/>

                <TextView
                    android:id="@+id/description"
                    style="@style/RCTheme.Message.RichContent.TextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="18dp"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="32dp"
                    />
            </LinearLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginEnd="16dp"
                android:layout_marginStart="16dp"
                android:background="#e4e4e4"/>

            <io.rong.imkit.widget.SettingItemView
                android:id="@+id/notification"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_content="@string/rc_pub_service_notification"
                app:item_divider="true"
                app:item_switch="true" />

            <Button
                android:id="@+id/enter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="14dp"
                android:textSize="16sp"
                android:background="@drawable/rc_btn_public_service_enter_selector"
                android:text="@string/rc_pub_service_info_enter"
                android:textColor="@color/rc_text_color_primary_inverse"/>

            <Button
                android:id="@+id/follow"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="17dp"
                android:textSize="16sp"
                android:background="@drawable/rc_btn_public_service_enter_selector"
                android:text="@string/rc_pub_service_info_follow"
                android:textColor="@color/rc_text_color_primary_inverse"/>

            <Button
                android:id="@+id/unfollow"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="14dp"
                android:textSize="16sp"
                android:background="@drawable/rc_btn_public_service_unfollow_selector"
                android:text="@string/rc_pub_service_info_unfollow"
                android:textColor="@color/rc_text_color_primary_inverse"/>

        </LinearLayout>


    </ScrollView>
</LinearLayout>