<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/rc_picture_item_select_bg"
    android:orientation="vertical"
    android:padding="6dp">


    <ImageView
        android:id="@+id/first_image"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_centerVertical="true" />

    <TextView
        android:id="@+id/tv_sign"
        style="@style/TextStyle.Alignment"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_alignEnd="@id/first_image"
        android:layout_margin="4dp"
        android:background="?attr/picture.folder_checked_dot"
        android:visibility="invisible" />

    <TextView
        android:id="@+id/tv_folder_name"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginTop="5dp"
        android:layout_toEndOf="@id/first_image"
        android:text="@string/rc_picture_camera_roll_num"
        android:textColor="@color/picture_color_4d"
        android:textSize="16sp" />



</RelativeLayout>