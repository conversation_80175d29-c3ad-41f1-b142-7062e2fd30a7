<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rc_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/rc_voice_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4.5dp"
            android:layout_marginTop="4.5dp"
            android:layout_marginEnd="4.5dp"
            android:adjustViewBounds="true"
            android:gravity="center_vertical"
            android:maxWidth="230dp"
            android:minWidth="52dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="0dp"
            app:layout_goneMarginStart="0dp">

            <ImageView
                android:id="@+id/rc_voice"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/rc_voice_receive_play3" />

            <TextView
                android:id="@+id/rc_duration"
                style="@style/TextStyle.Alignment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|start"
                android:layout_marginStart="6dp"
                android:textColor="@color/rc_voice_color"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>

        <FrameLayout
            android:id="@+id/fl_send_fire"
            android:layout_width="wrap_content"
            android:layout_height="15dp"
            android:background="@drawable/rc_fire_bg"
            android:paddingStart="4.5dp"
            android:paddingEnd="4.5dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_send_fire"
                android:layout_width="@dimen/rc_margin_size_15"
                android:layout_height="@dimen/rc_margin_size_15"
                android:layout_gravity="center"
                android:src="@drawable/rc_fire" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_receiver_fire"
            android:layout_width="wrap_content"
            android:layout_height="15dp"
            android:layout_gravity="end|top"
            android:paddingStart="4.5dp"
            android:paddingEnd="4.5dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_receiver_fire"
                android:layout_width="@dimen/rc_margin_size_15"
                android:layout_height="@dimen/rc_margin_size_15"
                android:layout_gravity="center"
                android:src="@drawable/rc_fire" />

            <TextView
                android:id="@+id/tv_receiver_fire"
                style="@style/TextStyle.Alignment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/rc_image_msg_count_down"
                android:gravity="center"
                android:maxLines="1"
                android:textColor="#333333"
                android:textSize="11dp"
                android:visibility="gone" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/rc_voice_unread"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="2dp"
        android:src="@drawable/rc_voice_unread" />

</LinearLayout>