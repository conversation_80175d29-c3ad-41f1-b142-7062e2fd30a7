<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <TextView
        android:id="@+id/letter"
        style="@style/TextStyle.Alignment"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_weight="1.0"
        android:paddingBottom="5dip"
        android:paddingStart="12dip"
        android:paddingTop="5dip"
        android:background="#f0f0f6"
        android:text="A"
        android:textColor="#999999" />

    <LinearLayout
        android:id="@+id/memberItem"
        android:layout_width="fill_parent"
        android:paddingEnd="12dp"
        android:layout_height="52dp"
        android:background="@drawable/rc_mention_select_list_item"
        android:orientation="horizontal">

        <ImageView
            android:layout_gravity="center_vertical"
            android:id="@+id/rc_user_portrait"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:scaleType="centerCrop"
            android:layout_marginStart="12dp" />

        <TextView
            android:id="@+id/rc_user_name"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:lines="1"
            android:ellipsize="end"
            android:maxLength="32"
            android:layout_marginStart="12dp"
            android:textColor="#353535"
            android:textSize="16sp" />

    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="1px"
        android:layout_marginStart="13dp"
        android:layout_marginEnd="16dp"
        android:background="#ccc" />

</LinearLayout>