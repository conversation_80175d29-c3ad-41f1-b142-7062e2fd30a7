<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/input_panel_voice_toggle"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="6.5dp"
        android:src="@drawable/rc_ext_toggle_voice"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <io.rong.imkit.widget.RongEditText
        android:id="@+id/edit_btn"
        style="@style/EditTextStyle.Alignment"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="6.5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="6.5dp"
        android:background="@drawable/rc_ext_panel_editbox_background"
        android:maxLines="4"
        android:minHeight="39dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/input_panel_emoji_btn"
        app:layout_constraintStart_toEndOf="@+id/input_panel_voice_toggle"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/press_to_speech_btn"
        style="@style/TextStyle.Alignment"
        android:layout_width="0dp"
        android:layout_height="@dimen/rc_ext_input_panel_editbox_height"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="5dp"
        android:layout_marginTop="6.5dp"
        android:layout_marginBottom="6.5dp"
        android:background="@drawable/rc_ext_voice_idle_button"
        android:gravity="center"
        android:text="@string/rc_voice_press_to_input"
        android:textColor="@color/rc_text_main_color"
        android:textSize="@dimen/rc_font_nav_or_date_size"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/input_panel_emoji_btn"
        app:layout_constraintStart_toEndOf="@+id/input_panel_voice_toggle"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/input_panel_emoji_btn"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginTop="6.5dp"
        android:layout_marginBottom="6.5dp"
        android:layout_marginEnd="5dp"
        android:src="@drawable/rc_ext_input_panel_emoji"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/input_panel_add_or_send" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/input_panel_add_or_send"
        android:layout_width="41dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6.5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="6.5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/input_panel_add_btn"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginEnd="4dp"
            android:src="@drawable/rc_ext_input_panel_add"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/input_panel_send_btn"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:background="@drawable/rc_send_background"
            android:text="@string/rc_send"
            android:textColor="@color/rc_white_color"
            android:textSize="@dimen/rc_font_text_third_size"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
