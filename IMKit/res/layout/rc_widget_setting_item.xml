<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="14dp" />

    <TextView
        android:id="@+id/tv_content"
        style="@style/TextStyle.Alignment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="14dp"
        android:layout_toEndOf="@id/iv_image"
        android:textColor="@color/rc_text_main_color"
        android:textSize="@dimen/rc_font_secondary_size" />

    <ImageView
        android:id="@+id/iv_tag_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="14dp"
        android:layout_toEndOf="@id/tv_content" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="14dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_value"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="1"
            android:textColor="@color/rc_text_main_color"
            android:textSize="@dimen/rc_font_secondary_size" />

        <io.rong.imkit.widget.switchbutton.SwitchButton
            android:id="@+id/sb_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            app:kswAnimationDuration="300"
            app:kswBackDrawable="@drawable/rc_switchbtn_check_selector"
            app:kswBackMeasureRatio="1.5"
            app:kswThumbMargin="1dp"
            app:kswThumbDrawable="@drawable/rc_switchbtn_thumb"
            app:kswThumbRadius="10dp" />

        <ImageView
            android:id="@+id/iv_select_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/iv_right_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />


    </LinearLayout>


    <View
        android:id="@+id/v_divider"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="14dp"
        android:background="@color/rc_item_background_color" />
</RelativeLayout>