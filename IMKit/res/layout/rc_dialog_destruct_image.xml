<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_sight"
        style="@style/TextStyle.Alignment"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="@string/rc_dialog_item_sight"
        android:textColor="#333333"
        android:textSize="13sp" />
    <View
        android:id="@+id/view_horizontal"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#e0e0e0" />
    <TextView
        android:id="@+id/tv_album"
        style="@style/TextStyle.Alignment"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="@string/rc_dialog_item_select_from_album"
        android:textColor="#333333"
        android:textSize="13sp" />
    <View
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:background="#f7f9fa" />
    <TextView
        android:id="@+id/tv_cancel"
        style="@style/TextStyle.Alignment"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:gravity="center"
        android:text="@string/rc_cancel"
        android:textColor="#333333"
        android:textSize="13sp" />

</LinearLayout>
