<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <io.rong.imkit.picture.photoview.PhotoView
        android:id="@+id/preview_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="2dp" />

    <io.rong.imkit.picture.widget.longimage.SubsamplingScaleImageView
        android:id="@+id/longImg"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/iv_play"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:src="@drawable/rc_picture_icon_video_play"
        android:visibility="invisible" />
</FrameLayout>