<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fl_bubble"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_unread"
        style="@style/RCTheme.Message.TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4.5dp"
        android:layout_marginTop="4.5dp"
        android:layout_marginEnd="4.5dp"
        android:background="@drawable/rc_ic_bubble_right"
        android:drawablePadding="11.5dp"
        android:padding="12dp"
        android:text="@string/rc_click_to_view"
        android:textColor="#A0A5AB"
        android:visibility="gone"
        app:drawableEndCompat="@drawable/rc_item_fire_t"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="0dp"
        app:layout_goneMarginStart="0dp" />

    <TextView
        android:id="@+id/rc_text"
        style="@style/RCTheme.Message.TextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4.5dp"
        android:layout_marginTop="4.5dp"
        android:layout_marginEnd="4.5dp"
        android:gravity="center_vertical|start"
        android:maxWidth="223dp"
        android:padding="16dp"
        android:textColor="@color/rc_text_main_color"
        android:textColorLink="@color/rc_main_theme"
        android:textSize="@dimen/rc_font_secondary_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="0dp"
        app:layout_goneMarginStart="0dp" />

    <FrameLayout
        android:id="@+id/fl_send_fire"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_send_fire"
            android:layout_width="@dimen/rc_margin_size_15"
            android:layout_height="@dimen/rc_margin_size_15"
            android:layout_gravity="center"
            android:src="@drawable/rc_fire" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl_receiver_fire"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|top"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_receiver_fire"
            android:layout_width="@dimen/rc_margin_size_15"
            android:layout_height="@dimen/rc_margin_size_15"
            android:layout_gravity="center"
            android:src="@drawable/rc_fire" />

        <TextView
            android:id="@+id/tv_receiver_fire"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/rc_image_msg_count_down"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/rc_white_color"
            android:textSize="11dp"
            android:visibility="gone" />
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>