<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_height="wrap_content"
    android:layout_width="220dp"
    android:paddingEnd="14dp"
    android:paddingStart="14dp"
    android:orientation="vertical"
    android:background="@android:color/white">

    <TextView
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:textSize="16dp"
        android:paddingTop="8dp"
        android:paddingBottom="6dp"
        android:textColor="#353535"
        android:id="@+id/rc_title"/>

    <TextView
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:textSize="14dp"
        android:textColor="#999999"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:id="@+id/rc_time"/>

    <ImageView
        android:layout_width="fill_parent"
        android:layout_height="160dp"
        android:background="@android:color/white"
        android:paddingBottom="6dp"
        android:paddingTop="6dp"
        android:id="@+id/rc_img"
        android:scaleType="centerCrop"
        android:layout_gravity="center" />

    <TextView
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:textSize="14dp"
        android:textColor="#999999"
        android:paddingBottom="6dp"
        android:paddingTop="6dp"
        android:id="@+id/rc_content"/>

    <ImageView
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:paddingBottom="8dp"
        android:paddingTop="6dp"
        android:background="@color/picture_color_eb"/>

    <TextView
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:paddingBottom="8dp"
        android:paddingTop="8dp"
        android:textSize="14dp"
        android:textColor="#353535"
        android:text="@string/rc_read_all"/>

</LinearLayout>