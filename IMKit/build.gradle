apply plugin: 'com.android.library'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: 'kotlin-kapt'

android {
    compileSdkVersion 35
    namespace 'io.rong.imkit'

    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 35
        versionName "5.5.3"
        multiDexEnabled true
    }

    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            res.srcDirs = ['res']
            assets.srcDirs = ['assets']
            jniLibs.srcDirs = ['libs']
        }
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    api project(':IMLib')
    implementation 'com.github.bumptech.glide:glide:4.11.0'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'androidx.fragment:fragment:1.2.5'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation "androidx.viewpager2:viewpager2:1.1.0-alpha01"
    implementation "androidx.documentfile:documentfile:1.0.1"
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'
    implementation "androidx.room:room-runtime:2.4.0"
    implementation 'androidx.core:core-ktx:1.13.1'
    implementation 'androidx.multidex:multidex:2.0.1'
    kapt 'androidx.room:room-compiler:2.6.1'
    //kapt "androidx.room:room-compiler:2.7.1"
}
