plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
}

android {
    namespace 'com.juicy.app'
    compileSdk 35

    defaultConfig {
        applicationId "test.liveapp.android"
        minSdk 24
        targetSdk 35
        versionCode 5
        versionName "1.1.0"
        multiDexEnabled true
        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters 'armeabi', 'armeabi-v7a', 'arm64-v8a'
        }
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        def df = new Date().format("yyMMddHHmmss", TimeZone.getTimeZone("GMT+8:00"))
        setProperty("archivesBaseName", "App_v${versionName}_${versionCode}_${df}")
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
    }
    lintOptions {
        checkReleaseBuilds false

    }
    buildFeatures {
        viewBinding = true
        dataBinding = true
        buildConfig = true
    }

    dataBinding {
        enabled = true
    }

    signingConfigs {
        debug {
            storeFile file('juicy.jks')
            storePassword 'juicy123'
            keyAlias 'juicy'
            keyPassword 'juicy123'
        }
        release {
            storeFile file('juicy.jks')
            storePassword 'juicy123'
            keyAlias 'juicy'
            keyPassword 'juicy123'
        }
    }

    buildTypes {
        debug {
            zipAlignEnabled true
            shrinkResources true
            minifyEnabled true
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {

            // 混淆
            minifyEnabled true
            //Zipalign优化
            zipAlignEnabled true
            signingConfig signingConfigs.release
//            移除无用的resource文件
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/INDEX.LIST'
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    sourceSets {
        main {
            assets {
                srcDirs 'src\\main\\assets'
            }
        }
    }
    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
            excludes += 'META-INF/INDEX.LIST'
            excludes += 'META-INF/DEPENDENCIES'
        }
    }
}

dependencies {
    // 本地模块依赖
    api project(':IMKit')

    // ========== Android 核心库 ==========
    implementation libs.androidx.core.ktx.v1131
    api libs.androidx.appcompat.v120
    api libs.material.v150
    api libs.androidx.constraintlayout
    api libs.androidx.recyclerview

    // ========== 生命周期组件 ==========
    api libs.androidx.lifecycle.common.java8
    api libs.androidx.lifecycle.process

    // ========== 数据库 ==========
    api libs.androidx.room.runtime
    kapt libs.androidx.room.compiler

    // ========== 测试框架 ==========
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit.v113
    androidTestImplementation libs.androidx.espresso.core.v340

    // ========== 路由框架 ==========
    implementation libs.arouter.api
    kapt libs.arouter.compiler
    implementation libs.arouter.annotation

    // ========== 网络请求库 ==========
    api libs.okhttp
    api libs.logging.interceptor
    api libs.retrofit
    api libs.converter.gson
    implementation libs.converter.scalars
    api libs.adapter.rxjava3

    // ========== 响应式编程 ==========
    api libs.rxandroid
    api libs.rxjava
    api libs.rxbinding

    // ========== 图片加载与处理 ==========
    api libs.glide
    api libs.glide.transformations
    api libs.photoview
    api libs.pictureselector
    api libs.compress
    api libs.ucrop

    // ========== UI 组件库 ==========
    api libs.smartrefreshlayout
    api libs.smartrefreshheader
    api libs.baserecyclerviewadapterhelper
    api libs.banner
    api libs.country.picker.android

    // ========== 动画库 ==========
    implementation libs.lottie

    // ========== 音视频处理 ==========
    //noinspection UseTomlInstead
    implementation 'io.agora.rtc:full-sdk:3.7.0.2'
    api libs.exoplayer.core
    api libs.exoplayer.dash
    api libs.exoplayer.ui

    // ========== 事件总线 ==========
    api libs.eventbus

    // ========== 实时通信 ==========
    api('io.socket:socket.io-client:1.0.0') {
        exclude group: 'org.json', module: 'json'
    }

    // ========== 工具类库 ==========
    api libs.utilcodex
    implementation libs.fastjson

    // ========== 日志打印 ==========
    api('com.github.ihsanbal:LoggingInterceptor:3.1.0') {
        exclude group: 'org.json', module: 'json'
    }

    // ========== 支付相关 ==========
    api 'com.android.billingclient:billing:7.0.0'

    // ========== 第三方SDK ==========
    // Facebook SDK
    api 'com.facebook.android:facebook-android-sdk:[5,6)'
    // AppsFlyer SDK
    api 'com.appsflyer:af-android-sdk:6.15.2'
    // Google 广告标识符
    implementation libs.play.services.ads.identifier
    // 安装来源追踪
    api libs.installreferrer

    // ========== Google 服务 ==========
    // 谷歌翻译
    implementation libs.google.cloud.translate
    // Google Play 服务
    implementation libs.asset.delivery
    implementation libs.feature.delivery
    implementation libs.review
    implementation libs.play.app.update
}
