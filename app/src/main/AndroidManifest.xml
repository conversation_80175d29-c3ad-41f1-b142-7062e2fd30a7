<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />


    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"
        tools:ignore="SelectedPhotoAccess" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"
        tools:ignore="SelectedPhotoAccess" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <application
        android:name=".JuicyApplication"
        android:icon="@mipmap/ic_launcher"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:allowBackup="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.AppCompat.Light.NoActionBar"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true"
        tools:replace="android:fullBackupContent,android:dataExtractionRules"
        tools:targetApi="31">
        <activity
            android:name="com.juicy.Login.LaunchActivity"
            android:enabled="true"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.SplashActivityBg">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.juicy.message.View.MessageActivity"
            android:windowSoftInputMode="adjustResize"
            android:screenOrientation="portrait"
            android:exported="false" />
        <activity
            android:name="com.juicy.my.LocaleActivity"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="com.juicy.my.AppInfoActivity"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name=".CoreActivity"
            android:exported="true"
            android:configChanges="uiMode|screenSize|smallestScreenSize|screenLayout"
            android:screenOrientation="portrait">
        </activity>
        <activity android:name="com.juicy.app.modules.video.VideoFullActivity"
            android:screenOrientation="portrait"
            android:exported="false"/>
        <activity android:name=".modules.base.activity.WebViewActivity"
            android:screenOrientation="portrait"
            android:exported="false"/>
        <activity
            android:name="com.juicy.app.modules.rank.UserRankActivity"
            android:exported="true"
            android:screenOrientation="portrait" />


        <activity
            android:name="com.juicy.my.BlockListActivity"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>



        <activity
            android:name=".modules.base.activity.PremiumActivity"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="com.juicy.my.TierActivity"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="com.juicy.my.SetAppActivity"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name=".modules.base.activity.BotActivity"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <activity
            android:name=".modules.base.activity.BasicWebActivity"
            android:exported="false"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name="com.juicy.my.EditUserInfoActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            android:configChanges="uiMode"
            android:screenOrientation="portrait">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>


        <activity android:name=".modules.call.VideoCallActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            android:configChanges="uiMode"
            android:screenOrientation="portrait"/>

        <activity
            android:name="com.juicy.Login.AuthActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity android:name="com.juicy.app.modules.info.ProfileActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            />
        <receiver
            android:name="com.appsflyer.SingleInstallBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/fb_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/fb_client_token" />
            
    </application>


</manifest>