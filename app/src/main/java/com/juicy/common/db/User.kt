package com.juicy.common.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "user_table")
class User(
    callType: String,
    duration: String,
    callOverTime: String,
    photo: String,
    name: String,
    id: String
) {
    @JvmField
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "user_id")
    var userId: Int = 0

    @JvmField
    @ColumnInfo(name = "id")
    var id: String = ""


    @JvmField
    @ColumnInfo(name = "call_type")
    var callType: String = ""


    @JvmField
    @ColumnInfo(name = "duration")
    var duration: String = ""


    @JvmField
    @ColumnInfo(name = "over_time")
    var callOverTime: String = ""


    @JvmField
    @ColumnInfo(name = "photo")
    var photo: String = ""


    @JvmField
    @ColumnInfo(name = "name")
    var name: String = ""

    init {
        this.name = name
        this.photo = photo
        this.duration = duration
        this.callOverTime = callOverTime
        this.callType = callType
        this.id = id
    }
}
