package com.juicy.common.db

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query

@Dao
interface UserDao {
    @Insert
    fun insertUser(vararg users: User)

    @Query("SELECT * FROM USER_TABLE ORDER BY over_time DESC LIMIT 10 OFFSET (:page - 1) * 10")
    fun queryAllUser(page: Int): LiveData<List<User>>


    @Query("DELETE FROM USER_TABLE")
    fun deleteAll()
}
