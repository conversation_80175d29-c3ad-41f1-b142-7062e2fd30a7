package com.juicy.common.db

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.juicy.app.JuicyApplication.Companion.juicyApplication

@Database(entities = [User::class], version = 1, exportSchema = false)
abstract class CallDatabase : RoomDatabase() {
    abstract fun userDao(): UserDao?
    companion object {
        private var callDatabase: CallDatabase? = null

        @JvmStatic
        val instance: CallDatabase?
            get() {
                if (callDatabase == null) {
                    juicyApplication?.applicationContext?.let {
                        callDatabase =
                            Room.databaseBuilder(
                                it,
                                CallDatabase::class.java,
                                "call_db"
                            ).build()
                    }
                }
                return callDatabase
            }
    }
}
