package com.juicy.common.networks.delegate

import android.text.TextUtils
import com.google.gson.Gson
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.UserIdParamsBean
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.config.Cache
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetUserExtraInfoInterface {
    /**添加接口缓存 */
    @JvmStatic
    fun getUserExtraInfo(userId: String): com.juicy.common.model.bean.GiftItemBean? {
        val json = Cache.instance.networkCacheManager["user_extra_info_$userId"] as? String ?:""
        if (TextUtils.isEmpty(json)) {
            return null
        }
        val azBean = Gson().fromJson(json, com.juicy.common.model.bean.GiftItemBean::class.java)
        return azBean
    }

    fun getUserExtraInfo(userId: String, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.GiftItemBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .getUserExtraInfo(UserIdParamsBean(userId))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.GiftItemBean>>() {
                override fun onNext(giftItemBeanNetResponseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.GiftItemBean>) {
                    super.onNext(giftItemBeanNetResponseBean)
                    //保存缓存
                    try {
                        Cache.instance.networkCacheManager.putObject(
                            "user_extra_info_$userId",
                            giftItemBeanNetResponseBean.data
                        )
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    callBack.onNext(giftItemBeanNetResponseBean)
                }
            })
    }
}