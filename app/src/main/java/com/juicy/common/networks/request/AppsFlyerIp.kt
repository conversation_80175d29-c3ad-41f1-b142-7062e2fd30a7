package com.juicy.common.networks.request


import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import okhttp3.Interceptor

import okhttp3.Request

import okhttp3.Response

import java.io.IOException


/**
 * 需要放在所有短连接中
 */
class AppsFlyerIp : Interceptor {
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        // 以拦截到的请求为基础建立一个新的请求对象，而后插入Header
        val request: Request = chain.request().newBuilder() //获取到请求头
            //数据可能为空
            .addHeader(Constant.UTM_SOURCE, Cache.instance.IsNull(Constant.MEDIA_SOURCE)?:"")
            .addHeader(Constant.AF_ADGROUP_ID, Cache.instance.IsNull(Constant.ADGROUP_ID)?:"")
            .addHeader(Constant.AF_ADSET, Cache.instance.IsNull(Constant.ADSET)?:"")
            .addHeader(Constant.AF_ADSET_ID, Cache.instance.IsNull(Constant.ADSET_ID)?:"")
            .addHeader(Constant.AF_STATUS_L, Cache.instance.IsNull(Constant.AF_STATUS)?:"")
            .addHeader(Constant.AF_AGENCY_L, Cache.instance.IsNull(Constant.AF_AGENCY)?:"")
            .addHeader(Constant.AF_CHANNEL_L, Cache.instance.IsNull(Constant.AF_CHANNEL)?:"")
            .addHeader(Constant.CAMPAIGN, Cache.instance.IsNull(Constant.CAMPAIGN)?:"")
            .addHeader(Constant.CAMPAIGN_ID, Cache.instance.IsNull(Constant.CAMPAIGN_ID)?:"")
            .build()
        // 开始请求
        return chain.proceed(request)
    }
}
