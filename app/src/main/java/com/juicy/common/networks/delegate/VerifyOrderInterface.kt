package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object VerifyOrderInterface {
    fun VerifyOrder(bl: com.juicy.common.model.bean.CheckOrderParamBean, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<*>>) {
        RetrofitManage.instance.create(Service::class.java)
            .rechargePayment(RetrofitManage.requestBody(bl))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}