package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.config.Cache
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetCoinCountInterface {
    fun getCoin(callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .userCoins
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .map { response: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean> ->
                // 在这里处理成功事件
                if (response != null && response.isOk) {
                    if (response.data != null) {
                        val data = response.data
                        if (Cache.instance.userInfo != null) {
                            Cache.instance?.userInfo?.availableCoins =
                                data!!.availableCoins.toInt()
                            Cache.instance.networkCacheManager.putObject(
                                "userInfo_" + Cache.instance?.userInfo?.userId + "",
                                Cache.instance.userInfo
                            )
                        }
                    }
                }
                response // 返回原始响应以供后续使用
            }
            .subscribe(callBack)
    }
}
