package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.model.bean.OssPolicyINfoBean
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetUserOssPolicyInterface {
    fun getUserOssPolicy(callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<OssPolicyINfoBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .GetUserOssPolicy()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}