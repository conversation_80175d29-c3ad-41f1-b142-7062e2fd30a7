package com.juicy.common.networks.delegate


import com.juicy.app.JuicyApplication
import com.juicy.common.networks.Service
import com.juicy.common.model.result.LoginUserInfoResult
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.utils.ApplicationUtil
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import okhttp3.FormBody

object LoginInterface {
    fun login(callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<LoginUserInfoResult>>) {
        val formBody: FormBody = FormBody.Builder()
            .add("oauthType", "4")
            .add("token", ApplicationUtil.getAndroidId(JuicyApplication.juicyApplication)?:"")
            .build()
        RetrofitManage.instance.create(Service::class.java)
            .login(formBody)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}