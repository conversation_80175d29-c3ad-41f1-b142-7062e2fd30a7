package com.juicy.common.networks.delegate

import com.juicy.common.config.ProtocolPool
import com.juicy.common.utils.MyUploadUtil
import okhttp3.MultipartBody
import retrofit2.Call
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.HeaderMap
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part

object GiveAvatarToOssInterface {
    @JvmStatic
    fun getAvatarToOss(filePath: String?): com.juicy.common.model.bean.ImageInfoBean? {
        val retrofit = Retrofit.Builder()
            .baseUrl(MyUploadUtil.ossHost) //oss域名地址
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        val service = retrofit.create(
            Service::class.java
        )
        val header: MutableMap<String, String> = ProtocolPool.commonHeaderWithToken?.toMutableMap()
            ?: mutableMapOf<String, String>()
        try {
            val call = service.GiveAvatarToOss(
                header,
                MyUploadUtil.getUploadAvatarBody(filePath?:"").parts
            ).execute()
            val bean = call.body()
            if (bean != null && bean.code == 0) {
                return bean.data
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    interface Service {
        @Multipart
        @POST("/")
        fun GiveAvatarToOss(
            @HeaderMap headers: Map<String, String>,
            @Part parts: List<MultipartBody.Part>
        ): Call<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ImageInfoBean?>?>
    }
}