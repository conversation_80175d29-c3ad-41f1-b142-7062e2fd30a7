package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object DeleteMediaInterface {
    fun delete(deleteMediaId: String?, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.MediaInfoBean>>>) {
        RetrofitManage.instance.create(Service::class.java)
            .updateMedia(
                com.juicy.common.model.bean.MediaActionBean.getDeleteCr(deleteMediaId)
            )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}
