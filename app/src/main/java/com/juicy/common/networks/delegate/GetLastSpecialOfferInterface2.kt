package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetLastSpecialOfferInterface2 {
    fun getLastSpecialOffer(
        getRechargeInfo: com.juicy.common.model.bean.GoodsParamsBean,
        callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>
    ) {
        RetrofitManage.instance.create(Service::class.java)
            .GetLastSpecialOffer2(getRechargeInfo)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}