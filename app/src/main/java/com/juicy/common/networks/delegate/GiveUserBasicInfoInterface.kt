package com.juicy.common.networks.delegate

import android.content.Context
import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GiveUserBasicInfoInterface {
    fun SaveUserInfo(
        context: Context?,
        saveUserInfo: com.juicy.common.model.bean.EditUserInfoBean,
        callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>
    ) {
        RetrofitManage.instance.create(Service::class.java)
            .saveUserBasicInfo(saveUserInfo)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}