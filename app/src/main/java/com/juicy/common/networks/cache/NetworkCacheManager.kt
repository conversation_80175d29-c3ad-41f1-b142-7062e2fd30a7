package com.juicy.common.networks.cache

import android.content.Context
import android.util.LruCache
import com.google.gson.Gson
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException

class NetworkCacheManager(context: Context, cacheDirName: String) {
    private val memoryCache: LruCache<String, String>
    private val diskCacheDir: File

    init {
        memoryCache = LruCache(MEMORY_CACHE_SIZE)
        diskCacheDir = File(context.cacheDir, cacheDirName)

        if (!diskCacheDir.exists()) {
            diskCacheDir.mkdirs()
        }
    }

    fun put(key: String, value: String) {
        try {
            memoryCache.put(key, value)
            writeToDisk(key, value)
        } finally {
        }
    }

    fun putObject(key: String, value: Any?) {
        try {
            val json = Gson().toJson(value)
            put(key, json)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    operator fun get(key: String): String? {
        try {
            var value = memoryCache[key]
            if (value == null) {
                value = readFromDisk(key)
                if (value != null) {
                    memoryCache.put(key, value)
                }
            }

            if (isCacheExpired(key)) {
                remove(key)
                return null
            }

            return value
        } finally {
        }
    }

    fun remove(key: String) {
        try {
            memoryCache.remove(key)
            val file = File(diskCacheDir, key)
            if (file.exists()) {
                file.delete()
            }
        } finally {
        }
    }

    private fun writeToDisk(key: String, value: String) {
        val file = File(diskCacheDir, key)
        try {
            FileOutputStream(file).use { fos ->
                fos.write(value.toByteArray())
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    private fun readFromDisk(key: String): String? {
        val file = File(diskCacheDir, key)
        if (!file.exists()) {
            return null
        }

        try {
            FileInputStream(file).use { fis ->
                val bytes = ByteArray(file.length().toInt())
                fis.read(bytes)
                return String(bytes)
            }
        } catch (e: IOException) {
            e.printStackTrace()
            return null
        }
    }

    private fun isCacheExpired(key: String): Boolean {
        val file = File(diskCacheDir, key)
        if (!file.exists()) {
            return true
        }

        val lastModified = file.lastModified()
        val currentTime = System.currentTimeMillis()

        return (currentTime - lastModified) > CACHE_VALIDITY_PERIOD
    }

    companion object {
        private const val MEMORY_CACHE_SIZE = 1024 * 1024 * 10 // 10 MB
        private const val CACHE_VALIDITY_PERIOD = (1000 * 60 * 60 // 1 hour
                ).toLong()
    }
}
