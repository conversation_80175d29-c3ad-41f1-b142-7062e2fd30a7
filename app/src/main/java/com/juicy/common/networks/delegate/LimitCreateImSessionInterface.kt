package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object LimitCreateImSessionInterface {
    fun createImSession(anchorId: String, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ImSessionBlanceBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .createImSession(com.juicy.common.model.bean.BroadcasterIdParamsBean(anchorId.toLong()))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}