package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.model.result.UserStratResult
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object ConfigStrategyInterface {
    fun getStrategy(callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<UserStratResult>>) {
        RetrofitManage.instance.create(Service::class.java)
            .strategy
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}
