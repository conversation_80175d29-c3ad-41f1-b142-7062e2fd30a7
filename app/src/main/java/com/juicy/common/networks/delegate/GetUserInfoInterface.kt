package com.juicy.common.networks.delegate

import android.text.TextUtils
import com.google.gson.Gson
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.UserIdParamsBean
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.config.Cache
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetUserInfoInterface {
    fun getUserInfo(userId: String, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .getUserInfo(UserIdParamsBean(userId))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                    try {
                        //保存缓存
                        Cache.instance.networkCacheManager.putObject(
                            "userInfo_$userId",
                            t.data
                        )
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    callBack.onNext(t)
                }
            })
    }

    //获取缓存
    @JvmStatic
    fun getCacheUserInfo(userId: String): com.juicy.common.model.bean.JuicyUserInfoBean? {
        val json = Cache.instance.networkCacheManager["userInfo_$userId"] as? String ?:""
        if (TextUtils.isEmpty(json)) {
            return null
        }
        val juicyUserInfoBean = Gson().fromJson(json, com.juicy.common.model.bean.JuicyUserInfoBean::class.java)
        return juicyUserInfoBean
    }
}