package com.juicy.common.networks.request

import android.os.Build
import android.text.TextUtils
import com.juicy.app.JuicyApplication
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppUtil.gid
import com.juicy.common.utils.AppUtil.phoneSystemVesion
import com.juicy.common.utils.AppUtil.pkgName
import com.juicy.common.utils.AppUtil.timeZone
import com.juicy.common.utils.ApplicationUtil.getAndroidId
import com.juicy.common.utils.LanguageTool.deviceLanguage
import com.juicy.common.utils.LanguageTool.getLocaleLanguageCountryFunc
import com.juicy.common.utils.SpSaveUtil.getStringValue
import okhttp3.Interceptor

import okhttp3.Request

import okhttp3.Response

import java.io.IOException

class TokenHeaderIp : Interceptor {
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        // 以拦截到的请求为基础建立一个新的请求对象，而后插入Header
        val request: Request = chain.request().newBuilder()
            .addHeader("pkg", pkgName)
            .addHeader("ver", Cache.instance.buildVersionName?:"")
            .addHeader(
                "device-id",
                getAndroidId(JuicyApplication.juicyApplication)?:""
            ) //                .addHeader("utm-source", "")
            .addHeader("model", Build.MODEL)
            .addHeader("lang", deviceLanguage)
            .addHeader("sys_lan", deviceLanguage)
            .addHeader("is_anchor", "false")
            .addHeader("platform", "Android")
            .addHeader(
                "Authorization",
                "Bearer " + getStringValue(SpKeyPool.APP_TOKEN_KEY, "")
            ) //新增参数
            .addHeader("platform_ver", phoneSystemVesion)
            .addHeader(
                "rc_type",
                if (TextUtils.isEmpty(Cache.instance.rctype)) "" else Cache.instance.rctype?:""
            )
            .addHeader("time_zone", if (TextUtils.isEmpty(timeZone)) "" else timeZone)
            .addHeader("device_country", getLocaleLanguageCountryFunc(JuicyApplication.juicyApplication!!))
            .addHeader("device_lang", deviceLanguage)
            .addHeader("google_ad_id", gid?:"") //风控要求上报
            //                .addHeader("is_enable_vpn", AppUtil.getIsEnableVpn(UtilApplication.getApplication()) ? "1":"0")
            //                .addHeader("is_enable_proxy", AppUtil.isProxyEnabled(UtilApplication.getApplication()) ? "1":"0")
            //                .addHeader("sim_country", AppUtil.getSimCountryCode(UtilApplication.getApplication()))
            //                .addHeader("is_auto   _time_zone", AppUtil.isAutoTimeZoneEnabled(UtilApplication.getApplication()) ? "1":"0")
            .build()
        // 开始请求
        return chain.proceed(request)
    }
}
