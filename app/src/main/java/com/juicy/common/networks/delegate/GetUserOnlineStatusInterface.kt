package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.model.bean.UserIdParamsBean
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetUserOnlineStatusInterface {
    fun getUserStatus(userId: String?, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<String>>) {
        RetrofitManage.instance.create(Service::class.java)
            .GetUserOnlineStatus(UserIdParamsBean(userId?:""))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}