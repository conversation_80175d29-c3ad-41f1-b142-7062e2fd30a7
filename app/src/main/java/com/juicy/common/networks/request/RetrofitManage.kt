package com.juicy.common.networks.request

import com.blankj.utilcode.util.GsonUtils
import com.ihsanbal.logging.LoggingInterceptor

import com.juicy.app.MyBuildConfig
import com.juicy.common.config.ApiPool
import com.juicy.common.utils.SSLUtil.hostnameVerifier
import com.juicy.common.utils.SSLUtil.sSLSocketFactory
import com.juicy.common.utils.SSLUtil.trustManager
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.RequestBody

import retrofit2.Retrofit
import retrofit2.adapter.rxjava3.RxJava3CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.util.concurrent.TimeUnit

object RetrofitManage {
    private var retrofit: Retrofit? = null

    @JvmStatic
    val instance: Retrofit
        get() {
            if (retrofit == null) {
                retrofit = Retrofit.Builder()
                    .baseUrl(ApiPool.BASE_URL)
                    .client(netClient)
                    .addConverterFactory(GsonConverterFactory.create())
                    .addConverterFactory(ScalarsConverterFactory.create())
                    .addCallAdapterFactory(RxJava3CallAdapterFactory.create()).build()
            }

            return retrofit!!
        }

    private val netClient: OkHttpClient
        get() {
            val DEFAULT_TIMEOUT: Long = 10000
            val builder: OkHttpClient.Builder = OkHttpClient.Builder()
                .readTimeout(DEFAULT_TIMEOUT, TimeUnit.MILLISECONDS)
                .writeTimeout(DEFAULT_TIMEOUT, TimeUnit.MILLISECONDS)
                .connectTimeout(DEFAULT_TIMEOUT, TimeUnit.MILLISECONDS)
                .addInterceptor(TokenHeaderIp())
                .addInterceptor(UserIdHeaderIp())
                .addInterceptor(AppsFlyerIp())
                .addInterceptor(UrlIp())
                .addInterceptor(SafetyInterceptor())
                .sslSocketFactory(sSLSocketFactory, trustManager)
                .hostnameVerifier(hostnameVerifier)
//            if (MyBuildConfig.DEBUG) {
//                return builder.addInterceptor(LoggingInterceptor.Builder().build()).build()
//            }
            /*else{
            return  builder.addInterceptor(new LoggingInterceptor.Builder().build()).build();
        }*/

            return builder.build()
        }

    @JvmStatic
    fun resetRetrofitClient() {
        retrofit = Retrofit.Builder()
            .baseUrl(ApiPool.BASE_URL)
            .client(netClient)
            .addConverterFactory(GsonConverterFactory.create())
            .addConverterFactory(ScalarsConverterFactory.create())
            .addCallAdapterFactory(RxJava3CallAdapterFactory.create()).build()
    }

    @JvmStatic
    fun requestBody(content: Any): RequestBody {
        return RequestBody.create(
            "application/json; charset=utf-8".toMediaTypeOrNull(),
            GsonUtils.toJson(content).toString()
        )
    }
}
