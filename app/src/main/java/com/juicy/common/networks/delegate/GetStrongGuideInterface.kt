package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.model.bean.StrongGuildBean
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetStrongGuideInterface {
    fun GetStrongGuide(callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<StrongGuildBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .GetStrongGuide()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}