package com.juicy.common.networks.request.SocketUtil

import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ToastUtils
import com.google.gson.Gson
import com.juicy.app.modules.base.dialog.AssessDialog
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.model.event.AvailableCoinsChangeEvent
import com.juicy.common.model.event.PayResultEvent
import com.juicy.common.model.message_event.MyHaseCoinsObject
import com.juicy.common.model.message_event.EstHangUpTimeBean
import com.juicy.common.model.message_event.MatchChatFreeTimesBean
import com.juicy.common.model.message_event.GiftAskEventBean
import com.juicy.common.model.message_event.MessageEventBean
import com.juicy.common.model.message_event.AppOnCallBean
import com.juicy.common.model.message_event.AppOnHangUpBean
import com.juicy.common.model.message_event.AppOnPickupBean
import com.juicy.common.model.message_event.AppRechargeOrderStatusBean
import com.juicy.common.networks.request.SocketUtil.ModelCallBack.Connect
import com.juicy.common.networks.request.SocketUtil.ModelCallBack.getConnectSubs
import com.juicy.common.networks.request.SocketUtil.SocketUtilsInterface.SUtil
import com.juicy.common.networks.delegate.GetCoinCountInterface.getCoin
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import io.socket.client.Socket
import io.socket.emitter.Emitter
import org.greenrobot.eventbus.EventBus
import org.json.JSONException
import org.json.JSONObject
import rx.Observable
import rx.Subscription
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.TimeUnit

class SocketModelImp(private val sUtil: SUtil) : SocketUtilsInterface.Model {
    private var subscribe: Subscription? = null

    override fun reconnect(modelCallBack: Connect) {
        Observable.interval(60, TimeUnit.SECONDS)
            .subscribe { modelCallBack.Successful() }
    }

    override fun getConnectSubs(mSkIo: Socket?, modelCallBack: getConnectSubs) {
        subscribe = Observable.interval(60, TimeUnit.SECONDS).subscribe { aLong: Long? ->
            if (null != mSkIo && mSkIo.connected()) {
//                Log.d("zfr", "SocketIoUtils connected");
                modelCallBack.Successful(subscribe)

                //                GetPresentedInterface.getPresented(new ObservableCallBack<NetBean<BsBean>>() {
//                    @Override
//                    public void onSubscribe(Disposable d) {
//                        super.onSubscribe(d);
//                    }
//
//                    @Override
//                    public void onNext(NetBean<BsBean> bsBaseBean) {
//                        super.onNext(bsBaseBean);
//                        if (bsBaseBean != null && bsBaseBean.getCode() == 0) {
//                            Cache.getInstance().presented = bsBaseBean.getData();
//                        }
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//                        super.onError(e);
//                    }
//                }); // 提高机器人呼叫频率
            } else {
                modelCallBack.UnSuccessful()
            }
        }
    }

    override fun registerEvent(mSkIo: Socket, mEmitterListener: Emitter.Listener?) {
        mSkIo.on("responseEvent", mEmitterListener)
        mSkIo.on("messageEvent", mEmitterListener)
        mSkIo.on(Socket.EVENT_CONNECT_TIMEOUT, mEmitterListener)
    }

    override fun onReceive(key: String, keyId: String, timestamp: String?, jsonObject: JSONObject) {
        Log.d("TAG", "onReceive: $jsonObject")
        try {
            if (!eventBusBackList.isEmpty()) {
                var newTimestamp: String? = null
                if (jsonObject.has("timestamp")) {
                    newTimestamp = jsonObject.getString("timestamp")
                }
                if (newTimestamp == null) {
                    newTimestamp = timestamp
                }
                if (newTimestamp == null) {
                    return
                }
                newTimestamp = newTimestamp + key
                if (messageCacheMap[newTimestamp] == null) {
                    if (messageCacheMap.size > 200) {
                        messageCacheMap.clear()
                    }
                    messageCacheMap[newTimestamp] = ""
                    eventHandle(key, keyId, jsonObject)
                }
            }
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }

    fun hasFlashRunFragment(): Boolean {
        val currentActivity = currentActivity
        if (currentActivity == null || currentActivity.javaClass.simpleName == null) {
            return false
        }
        if (currentActivity.javaClass.simpleName != "VideoCallActivity") {
            return false
        }
        val activity = currentActivity as AppCompatActivity

        val fragmentManager = activity.supportFragmentManager
        val fragment = fragmentManager.findFragmentByTag("FlashRunFragmentTag") ?: return false
        return true
    }

    private fun eventHandle(key: String, keyId: String, jsonObject: JSONObject) {
        Log.d("TAG", "eventHandle: ")
        for (eventBusBack in eventBusBackList) {
            if (null != eventBusBack) {
                val gson = Gson()
                if (key == ONCALL) {
                    val currentActivity = currentActivity
                    if (currentActivity != null && currentActivity.javaClass.simpleName != null && currentActivity.javaClass.simpleName == "VideoCallActivity") { //正在通话或者匹配不弹出呼叫
                        return
                    }
                    val appOnCallBean = gson.fromJson(
                        jsonObject.toString(),
                        AppOnCallBean::class.java
                    )

                    Cache.instance.callingChannelName = appOnCallBean.channelName?:""
                    eventBusBack.call(appOnCallBean)
                } else if (key == ONHANGUP) {
                    val appOnHangUpBean = gson.fromJson(
                        jsonObject.toString(),
                        AppOnHangUpBean::class.java
                    )
                    if (appOnHangUpBean.reason == Constant.NORMAL) {
                        appOnHangUpBean.reason = Constant.CL_REMOTE_USER_LEFT
                    }

                    if (Cache.instance.callingChannelName == null ||
                        Cache.instance.callingChannelName.isEmpty() ||
                        appOnHangUpBean.channelName == Cache.instance.callingChannelName ||
                        hasFlashRunFragment()
                    ) {
                        eventBusBack.hangup(appOnHangUpBean)
                    }
                } else if (key == ONPICKUP) {
                    //onPickUp、呼叫被接听
                    //判断是否是当前呼叫，去除匹配和通话界面
                    val appOnPickupBean = gson.fromJson(
                        jsonObject.toString(),
                        AppOnPickupBean::class.java
                    )

                    if (Cache.instance.callingChannelName == null ||
                        Cache.instance.callingChannelName.isEmpty() ||
                        appOnPickupBean.channelName == Cache.instance.callingChannelName ||
                        hasFlashRunFragment()
                    ) {
                        Cache.instance.callingChannelName = appOnPickupBean.channelName?:""
                        eventBusBack.pickUp(appOnPickupBean)
                    }
                } else if (key == ESTIMATEDHANGUPTIME) {
                    val estHangUpTimeBean = gson.fromJson(
                        jsonObject.toString(),
                        EstHangUpTimeBean::class.java
                    )
                    eventBusBack.EstimatedHangUp(estHangUpTimeBean)
                } else if (key == AVAILABLECOINS) {
                    val myHaseCoinsObject = gson.fromJson(
                        jsonObject.toString(),
                        MyHaseCoinsObject::class.java
                    )
                    eventBusBack.AvailableCoins(myHaseCoinsObject)
                } else if (key == ONCHAT) {
                    //onChat、对方发送聊天信息文本消息
                    val messageEventBean = gson.fromJson(
                        jsonObject.toString(),
                        MessageEventBean::class.java
                    )
                    eventBusBack.messageEvent(messageEventBean)
                } else if (key == MESSAGEEVENT) {
                    //messageEvent、1v1视频页聊天文本消息
                    val messageEventBean = gson.fromJson(
                        jsonObject.toString(),
                        MessageEventBean::class.java
                    )
                    eventBusBack.messageEvent(messageEventBean)
                } else if (key == ONGIFTASK) {
                    try {
                        //onGiftAsk、被索取礼物
                        val giftAskEventBean = gson.fromJson(
                            jsonObject.optString("content"),
                            GiftAskEventBean::class.java
                        )
                        eventBusBack.giftAsk(giftAskEventBean)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                } else if (key == RECHARGEORDERSTATUS) {
                    //第三方支付状态回传
                    val appRechargeOrderStatusBean = gson.fromJson(
                        jsonObject.toString(),
                        AppRechargeOrderStatusBean::class.java
                    )

                    if (appRechargeOrderStatusBean.status == 2) {
                        eventBusBack.rechargeSuccessBack(appRechargeOrderStatusBean)
                        ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Payment_successful"))
                    } else if (appRechargeOrderStatusBean.status == 3) {
                        ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Recharge_failed_Please_try_again_later"))
                    }
                } else if (key == FLASHCHATFREETIMES) {
                    // 快速匹配免费次数剩余数量下发
                    val matchChatFreeTimesBean = gson.fromJson(
                        jsonObject.toString(),
                        MatchChatFreeTimesBean::class.java
                    )
                    eventBusBack.flashChatFreeTimeBack(matchChatFreeTimesBean)
                }
            }
        }
    }

    interface SocketEventBusBack {
        fun hangup(appOnHangUpBean: AppOnHangUpBean?) {
        }

        fun call(appOnCallBean: AppOnCallBean?) {
        }

        fun pickUp(appOnPickupBean: AppOnPickupBean?) {
        }

        fun EstimatedHangUp(estHangUpTimeBean: EstHangUpTimeBean?) {
        }

        fun AvailableCoins(myHaseCoinsObject: MyHaseCoinsObject) {
            getCoin(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>() {
                override fun onNext(coinData: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>) {
                    super.onNext(coinData)
                    EventBus.getDefault().post(AvailableCoinsChangeEvent())
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                }
            })
        }

        fun onChat(eventObject: MessageEventBean?) {
        }

        fun messageEvent(eventObject: MessageEventBean?) {
        }

        fun giftAsk(giftAskEventBean: GiftAskEventBean?) {
        }

        fun rechargeSuccessBack(appRechargeOrderStatusBean: AppRechargeOrderStatusBean?) {
            EventBus.getDefault().post(PayResultEvent(true))
            if (Cache.instance.webViewActivity != null) {
                Cache.instance.webViewActivity!!.refreshAgentWeb()
            }
            if (!getBooleanVal(SpKeyPool.HAS_EVALUATE, false)!!) {
                putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
                val currentActivity = currentActivity as BaseActivity?
                if (currentActivity != null && !isDestroy(currentActivity)) {
                    val assessDialog = AssessDialog()
                    assessDialog.show(currentActivity.supportFragmentManager, "")
                }
            }
            getUserInfo(
                Cache.instance.userInfo!!.userId,
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                    override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                        if (azBaseBean != null) {
                            if (azBaseBean.isOk && azBaseBean.data != null) {
                                Cache.instance.userInfo = azBaseBean.data
                            } else if (Constant.TOKEN_EXPIRE_CODES.contains(azBaseBean.code)) {
                            }
                        }
                    }

                    override fun onError(e: Throwable) {
                        Log.e("SocketModeIMpl", "mine user info error" + e.message)
                    }
                })
            //           ThreadUtil.execute(new Runnable() {
//               @Override
//               public void run() {
//                   try {
//                       Response<NetBean<AzBean>> execute = RetrofitManage
//                               .getInstance()
//                               .create(Service.class)
//                               .getUserInfo2(new AmUserIdBean(Cache.getInstance().userInfo.getUserId()))
//                               .execute();
//                       if (execute.body().getData() != null) {
//                           Cache.getInstance().userInfo = execute.body().getData();
//                       }
//                   } catch (IOException e) {
//                       e.printStackTrace();
//                   }
//               }
//           });
        }


        fun flashChatFreeTimeBack(matchChatFreeTimesBean: MatchChatFreeTimesBean?) {
        }
    }

    companion object {
        private val eventBusBackList = CopyOnWriteArrayList<SocketEventBusBack>()
        private const val ONCALL = "onCall"
        private const val ONHANGUP = "onHangUp"
        private const val ONPICKUP = "onPickUp"
        private const val ESTIMATEDHANGUPTIME = "estimatedHangUpTime"
        private const val AVAILABLECOINS = "availableCoins"
        private const val ONCHAT = "onChat"
        private const val MESSAGEEVENT = "messageEvent"
        private const val ONGIFTASK = "onGiftAsk"
        private const val RECHARGEORDERSTATUS = "rechargeOrderStatus"
        private const val FLASHCHATFREETIMES = "flashChatFreeTimes"
        private val messageCacheMap: MutableMap<String, String?> = HashMap()
        @JvmStatic
        fun addMessageEvent(socketEventBusBack: SocketEventBusBack) {
            eventBusBackList.add(socketEventBusBack)
        }

        //组件销毁时删除监听对象
        @JvmStatic
        fun removeMessageEvent(socketEventBusBack: SocketEventBusBack) {
            eventBusBackList.remove(socketEventBusBack)
        }
    }
}
