package com.juicy.common.networks.request

import android.net.Uri
import android.text.TextUtils
import android.util.Base64
import android.util.Log
import com.juicy.common.config.ApiPool.BASE_URL
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.SpSaveUtil.getStringValue
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

object SafetyKey {
    const val transformation: String = "AES/ECB/PKCS5Padding"
    const val algorithm: String = "AES"
    fun encodeString(infoString: ByteArray?, aesKey: ByteArray?): String {
        try {
            val key = SecretKeySpec(aesKey, algorithm)

            val cipher = Cipher.getInstance(transformation)
            cipher.init(Cipher.ENCRYPT_MODE, key)
            val encryptedData = cipher.doFinal(infoString)
            return Base64.encodeToString(encryptedData, Base64.DEFAULT)
        } catch (e: Exception) {
            Log.e("EncryptionError", "Error encrypting data", e)
        }
        return ""
    }

    //解密
    fun decodeString(sourceData: ByteArray?, aesKey: ByteArray?): String {
        try {
            val key = SecretKeySpec(aesKey, algorithm)
            val cipher = Cipher.getInstance(transformation)
            cipher.init(Cipher.DECRYPT_MODE, key)
            return String(cipher.doFinal(sourceData))
        } catch (e: Exception) {
            return ""
        }
    }

    val defaultKey: ByteArray
        /**获取App通用秘钥 */
        get() {
            var host =
                checkNotNull(Uri.parse(BASE_URL).host)
            var defaultKey = ""
            if (host.length > 32) {
                //获取前32位
                defaultKey = host.substring(0, 32)
            } else {
                val key = StringBuilder()
                for (i in 0 until 32 - host.length) {
                    key.append("0")
                }
                host += key.toString()
                defaultKey = host
            }
            return defaultKey.toByteArray()
        }


    val encryptKey: String?
        get() {
            var key = Cache.instance.encryptKey
            if (TextUtils.isEmpty(key)) {
                key = getStringValue(SpKeyPool.ENCRYPT_KEY, "")
            }
            return key
        }
}
