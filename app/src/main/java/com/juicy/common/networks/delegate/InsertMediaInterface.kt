package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object InsertMediaInterface {
    fun insert(filePath: com.juicy.common.model.bean.MediaActionBean, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.MediaInfoBean>>>) {
        RetrofitManage.instance.create(Service::class.java)
            .updateMedia(filePath)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)


    }
}