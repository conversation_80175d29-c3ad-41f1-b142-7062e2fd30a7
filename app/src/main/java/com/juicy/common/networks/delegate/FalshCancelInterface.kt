package com.juicy.common.networks.delegate

import androidx.annotation.Keep
import com.google.gson.Gson
import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody

object FalshCancelInterface {
    fun cancel(clientSessionId: String?, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<String>>) {
        val onFalshReq = OnFalshCReq()

        onFalshReq.clientSessionId = clientSessionId

        val gson = Gson()
        val body = RequestBody.create(
            "application/json; charset=utf-8".toMediaTypeOrNull(),
            gson.toJson(onFalshReq)
        )
        RetrofitManage.instance.create(Service::class.java)
            .flashCancel(body)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }

    @Keep
    private class OnFalshCReq {
        var clientSessionId: String? = null
    }
}