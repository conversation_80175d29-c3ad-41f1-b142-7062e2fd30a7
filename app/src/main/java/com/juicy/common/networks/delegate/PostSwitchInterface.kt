package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object PostSwitchInterface {
    fun postSwitch(mode: Int, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .postSwitch(com.juicy.common.model.bean.AppModeSwitchBean(mode))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}
