package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetBlockListInterface {
    fun blockList(callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.SimpleUserInfoBean>>>) {
        RetrofitManage.instance.create(Service::class.java)
            .requestBlockList()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}
