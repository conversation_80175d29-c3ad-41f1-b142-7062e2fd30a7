package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GiftListInterface {
    fun giftList(callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<ArrayList<com.juicy.common.model.bean.OrderInfoBean>>>) {
        RetrofitManage.instance.create(Service::class.java)
            .giftList()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}