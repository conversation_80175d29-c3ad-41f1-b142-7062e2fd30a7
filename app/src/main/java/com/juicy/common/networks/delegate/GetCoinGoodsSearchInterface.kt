package com.juicy.common.networks.delegate

import android.content.Context
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetCoinGoodsSearchInterface {
    fun getCoinGoodsSearch(
        context: Context?,
        getGoods: PlayParamsBean,
        callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>
    ) {
        RetrofitManage.instance.create(Service::class.java)
            .GetCoinGoodsSearch(getGoods)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}