package com.juicy.common.networks.request.SocketUtil

import android.os.Handler
import android.os.Looper
import com.juicy.common.networks.request.AppsFlyerIp
import com.juicy.common.networks.request.SocketUtil.ModelCallBack.getConnectSubs
import com.juicy.common.networks.request.SocketUtil.SocketUtilsInterface.SUtil
import com.juicy.common.networks.request.TokenHeaderIp
import com.juicy.common.networks.delegate.GetHeartbeat.heartbeat
import com.juicy.common.networks.delegate.GetPresentedInterface.getPresented
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.ApiPool
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.ThreadUtil.runOnUIThread
import io.reactivex.rxjava3.disposables.Disposable
import io.socket.client.IO
import io.socket.client.Socket
import io.socket.emitter.Emitter
import okhttp3.Call
import okhttp3.OkHttpClient
import okhttp3.WebSocket
import org.json.JSONException
import org.json.JSONObject
import rx.Observable
import rx.Subscription
import java.net.URISyntaxException
import java.util.Timer
import java.util.TimerTask
import java.util.concurrent.TimeUnit

object SocketUtils : SUtil {
    var model: SocketUtilsInterface.Model =
        SocketModelImp(this)

    // 震动处理
    private val pattern = longArrayOf(200L, 600L, 800L, 600L)


    private var mSkIo: Socket? = null
    private var connectSubs: Subscription? = null
    private var timer: Timer? = null
    private var timerTask: TimerTask? = null
    private var firstGetPresented = false
    private val skIoUrl: String
        get() = ApiPool.LONG_LIVE_HOST

    fun initAndConnect() {
        val httpClient: OkHttpClient = OkHttpClient.Builder()
            .addNetworkInterceptor(TokenHeaderIp())
            .addInterceptor(AppsFlyerIp())
            .build()
        val opts = IO.Options()
        opts.forceNew = true
        opts.reconnection = true

        // getUserToken -> 登录成功后返回的token
        opts.query = "token=" + getStringValue(SpKeyPool.APP_TOKEN_KEY, "")
        opts.webSocketFactory = httpClient as WebSocket.Factory
        opts.callFactory = httpClient as Call.Factory
        try {
            // getSkIoUrl -> 长连url
            // eg(测试环境): http://**************:18089
            mSkIo = IO.socket(skIoUrl, opts)
            mSkIo?.let {
                // 此处可进行事件注册
                model.registerEvent(it, mEmitterListener)
                //新加注册方法监听链接成功状态
                it.on(Socket.EVENT_CONNECT, Emitter.Listener { // 连接成功后的逻辑,调用金币领取接口
                    getPresented(3)
                })

                // 开始连接
                it.connect()
            }
            timer = Timer()
            timerTask = object : TimerTask() {
                override fun run() {
                    Cache.instance.isFirstOpen = false
                    if (null != timer && null != timerTask) {
                        timer!!.cancel()
                        timerTask!!.cancel()
                        timer = null
                        timerTask = null
                    }
                }
            }
            timer!!.schedule(timerTask, 5000)
        } catch (e: URISyntaxException) {
            e.printStackTrace()
        }

        Observable.interval(60, TimeUnit.SECONDS)
            .subscribe { aLong: Long? ->
                heartbeat(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
                    override fun onNext(booleanBaseBean: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                        super.onNext(booleanBaseBean)
                    }

                    override fun onError(e: Throwable) {
                        super.onError(e)
                    }
                })
                if (mSkIo != null) {
                    if (!mSkIo!!.connected()) {
                        reconnect()
                    }
                }
            }

        model.getConnectSubs(mSkIo, object : getConnectSubs {
            override fun Successful(subscribe: Subscription?) {
                connectSubs = subscribe
                //不是socket 连接成功的意思，暂时没有用
                if (connectSubs != null) {
                    connectSubs!!.unsubscribe()
                }
            }

            override fun UnSuccessful() {
            }
        })
    }

    //失败重试3次
    //领取金币接口，增加机器人呼叫概率
    private fun getPresented(count: Int) {
        if (Cache.instance.userStratResult == null || Cache.instance.userStratResult!!.isReviewPkg == null || Cache.instance?.userStratResult?.isReviewPkg == true) return
        getPresented(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ExpCoinsBean>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(bsBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ExpCoinsBean>) {
                super.onNext(bsBaseBean)
                if (bsBaseBean != null && bsBaseBean.code == 0) {
                    firstGetPresented = true
                    Cache.instance.presented = bsBaseBean.data
                }
            }

            override fun onError(e: Throwable) {
                val handler = Handler(Looper.getMainLooper())
                handler.postDelayed({ getPresented(count - 1) }, 1000)
                super.onError(e)
            }
        })
    }

    fun reconnect() {
        if (null != mSkIo && !mSkIo!!.connected()) {
            mSkIo!!.connect()
        }
    }

    //前后台切换后判断重连
    fun appLifecycleCheck() {
        if (null != mSkIo && !mSkIo!!.connected()) {
            mSkIo!!.connect()
        } else {
            if (!firstGetPresented) {
                firstGetPresented = true
                return
            }
            getPresented(3) // 提高机器人呼叫频率
        }
    }

    val isSocketConnected: Boolean
        get() {
            if (null != mSkIo) {
                return mSkIo!!.connected()
            }
            return false
        }


    fun sendMsg(name: String?, jsonObject: JSONObject?) {
        if (null != mSkIo) {
            mSkIo!!.emit(name, jsonObject)
        }
    }

    fun disconnect() {
        if (null != mSkIo) {
            mSkIo!!.disconnect()
        }
        if (null != connectSubs && !connectSubs!!.isUnsubscribed) {
            connectSubs!!.unsubscribe()
        }
    }

    var mEmitterListener: Emitter.Listener =
        Emitter.Listener { args -> // 切换主线程
            runOnUIThread { handleCall(args) }
        }

    override fun handleCall(args: Array<Any>?) {
        if (args != null && args[0] is JSONObject) {
//            Log.d("zfr", "SocketIoUtils handleCall " + args[0].toString());
            val `object` = args[0] as JSONObject
            // 判断Socket io是否断开连接
            if (!isSocketConnected) {
                reconnect()
            }
            try {
                val code = `object`.getString("code") ?: return
                if (code != "200") return

                // 解析数据结构，处理消息数据
                val key = `object`.getString("command")
                val keyId = `object`.getString("commandId")
                var timestamp: String? = null
                if (`object`.has("timestamp")) {
                    timestamp = `object`.getString("timestamp")
                }
                val jsonObject = `object`.getJSONObject("data")
                model.onReceive(key, keyId, timestamp, jsonObject) // 回调处理消息数据
            } catch (e: JSONException) {
                e.printStackTrace()
            }
        }
    }

}
