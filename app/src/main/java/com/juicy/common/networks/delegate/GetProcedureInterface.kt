package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.model.bean.TypeParamsBean
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetProcedureInterface {
    fun GetProcedure(callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<String>>>) {
        RetrofitManage.instance.create(Service::class.java)
            .getProcedure(TypeParamsBean(1))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}