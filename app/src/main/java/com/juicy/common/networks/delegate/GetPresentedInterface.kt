package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetPresentedInterface {
    fun getPresented(callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ExpCoinsBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .presented
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .retry(3)
            .subscribe(callBack)
    }
}