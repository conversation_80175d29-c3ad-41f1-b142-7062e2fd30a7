package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.SpSaveUtil
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import org.json.JSONObject

object WallSearchInterface {
    fun wallSearch(
        isFirst: Boolean,
        category: String?,
        tag: String?,
        page: Int,
        callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.AnchorInfoBean>>>
    ) {
        try {
            val json = JSONObject()
            json.put("isPageMode", true)
            json.put("isRemoteImageUrl", true)
            json.put("limit", 20)
            json.put("page", page)
            json.put("tag", tag)
            json.put("category", category)

            val countryCode = SpSaveUtil.getStringValue(SpKeyPool.CURRECTCOUNTRY, "")
            if (countryCode != "" && isFirst) {
                json.put("region", countryCode)
            }

            val body =
                RequestBody.create("application/json; charset=utf-8".toMediaTypeOrNull(), json.toString())

            RetrofitManage.instance.create(Service::class.java)
                .wallSearch(body)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(callBack)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}