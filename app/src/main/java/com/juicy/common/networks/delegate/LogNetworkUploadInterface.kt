package com.juicy.common.networks.delegate


import com.juicy.app.MyBuildConfig
import com.juicy.common.networks.request.AppsFlyerIp
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.networks.request.SafetyInterceptor
import com.juicy.common.networks.request.TokenHeaderIp
import com.juicy.common.networks.request.UrlIp
import com.juicy.common.networks.request.UserIdHeaderIp
import com.juicy.common.config.ApiPool
import okhttp3.OkHttpClient
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST
import java.util.concurrent.TimeUnit

object LogNetworkUploadInterface {
    fun <T> LogResult(logBean: List<T>): Any{
        val retrofit = Retrofit.Builder() //日志打点上报  接口https://log.正式域名/log/live-chat
            .baseUrl(ApiPool.LOG_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
        val service = retrofit.create(ServiceApi::class.java)
        try {
            val call = service.logLiveChat(RetrofitManage.requestBody(logBean)).execute()
            val bean: com.juicy.common.model.bean.NetResponseBean<*>? = call.body()
            if (bean != null && bean.code == 0) {
                return bean.data ?: false
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    val okHttpClient: OkHttpClient
        get() {
            val DEFAULT_TIMEOUT = 10000L
            return if (MyBuildConfig.DEBUG)
                OkHttpClient.Builder()
                    .readTimeout(DEFAULT_TIMEOUT, TimeUnit.MILLISECONDS)
                    .writeTimeout(DEFAULT_TIMEOUT, TimeUnit.MILLISECONDS)
                    .connectTimeout(DEFAULT_TIMEOUT, TimeUnit.MILLISECONDS)
                    //.addInterceptor(LoggingInterceptor.Builder().build())
                    .addInterceptor(UserIdHeaderIp())
                    .addInterceptor(TokenHeaderIp())
                    .addInterceptor(AppsFlyerIp())
                    .addInterceptor(SafetyInterceptor())
                    .addInterceptor(UrlIp())
                    .build()
            else
                OkHttpClient.Builder()
                    .readTimeout(DEFAULT_TIMEOUT, TimeUnit.MILLISECONDS)
                    .writeTimeout(DEFAULT_TIMEOUT, TimeUnit.MILLISECONDS)
                    .connectTimeout(
                        DEFAULT_TIMEOUT,
                        TimeUnit.MILLISECONDS
                    ) //                .addInterceptor(new LoggingInterceptor.Builder().build())
                    .addInterceptor(TokenHeaderIp())
                    .addInterceptor(UserIdHeaderIp())
                    .addInterceptor(AppsFlyerIp())
                    .addInterceptor(SafetyInterceptor())
                    .addInterceptor(UrlIp())
                    .build()
        }

    private interface ServiceApi {
        //日志打点上报
        @POST("/prodApi/disk_first_map/remove")
        fun logLiveChat(@Body body: RequestBody?): Call<com.juicy.common.model.bean.NetResponseBean<*>?>
    }
}