package com.juicy.common.networks.delegate

import android.content.Context
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.UserIdsParamsBean
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetUserListOnlineStatusInterface {
    fun getUserListOnlineStatus(
        context: Context?,
        userIds: List<String>,
        callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Map<String, String>>>
    ) {
        RetrofitManage.instance.create(Service::class.java)
            .getUserListOnlineStatus(UserIdsParamsBean(userIds))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}