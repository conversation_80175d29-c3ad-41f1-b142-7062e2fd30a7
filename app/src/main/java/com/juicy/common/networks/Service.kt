package com.juicy.common.networks

import com.juicy.common.model.bean.UserIdsParamsBean
import com.juicy.common.model.bean.UserIdParamsBean
import com.juicy.common.model.bean.PageParamsBean
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.model.bean.PayResultBean
import com.juicy.common.model.bean.RankInfoBean
import com.juicy.common.model.bean.OrderStatusBean
import com.juicy.common.model.bean.StrongGuildBean
import com.juicy.common.model.bean.OssPolicyINfoBean
import com.juicy.common.model.bean.VideoCallInfoBean
import com.juicy.common.model.bean.PayChannelBean
import com.juicy.common.model.bean.TypeParamsBean
import com.juicy.common.model.bean.TokenIsValidParamBean
import com.juicy.common.model.result.LoginUserInfoResult
import com.juicy.common.model.result.UserStratResult
import com.juicy.common.model.message_event.AppOnCallBean
import io.reactivex.rxjava3.core.Observable
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

interface Service {

    @POST("/prodApi/v1/diagram_minimum_item/post")
    fun appConfigCall(): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyConfigBean>>

    @POST("/prodApi/v3/config_min_array/search")
    fun getRongToken(@Body imAppKeyBean: com.juicy.common.model.bean.ImAppKeyBean): Observable<com.juicy.common.model.bean.NetResponseBean<String>> // 获取融云code
    @POST("/prodApi/v3/agent_minimum_value/download")
    fun GetFriendsList(@Body friendsList: PageParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.BaseUserInfoBean>>>
    //评价主播接口 POST    SubmitBean
    @POST("/prodApi/res/config_average_price/list")
    fun EvaluateSubmit(@Body body: com.juicy.common.model.bean.AnchorScoreBean): Observable<com.juicy.common.model.bean.NetResponseBean<Void>>

    @POST("/prodApi/v1/config_max_hash/get")
    fun GetGoodsPromotion(@Body getGoods: PlayParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ActivityInfoBean>> // 主页面/登录页初始化 暂不处理token失效（新用户促销）

    @POST("/prodApi/v2/msg_maximum_string/get")
    fun GetRechargeInfo(@Body getRechargeInfo: com.juicy.common.model.bean.InvitationGoodParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>> //融云接口不处理token失效（活动促销）


    @POST("/prodApi/resource/game_item/del")
    fun cancelBlock(@Body blockUserId: com.juicy.common.model.bean.AnchorBlockBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>


    //话术接口
    @POST("/prodApi/resource/promotion_first_table/upload")
    fun getProcedure(@Body getType: TypeParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<List<String>>>

    //归因信息上报接口
    @POST("/prodApi/v0/disk_last_string/search")
    fun submitInstallReferer(@Body getInstallRefererArgs: com.juicy.common.model.bean.AppInstallReferrerBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    //新版三方支付
    @POST("/prodApi/resource/doc_last_result/remove")
    fun payChannel(): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ExtraBean>> // 支付接口不处理 token

    //PP请求接口  /prodApi/v1/product_array/get
    @POST("/prodApi/v0/config_max_result/delete")
    fun paypalCreate(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.PayUrlBean>> // 支付接口不处理 token

    //优惠商品(活动促销)
    @POST("/prodApi/resource/promotion_minimum_group/remove")
    fun GetLastSpecialOffer2(@Body getRechargeInfo: com.juicy.common.model.bean.GoodsParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>> // 前置接口已处理token

    //TODO 校验token
    @POST("/prodApi/config_second_result/add")
    fun getTokenIsValid(@Body tokenIsValidParamBean: TokenIsValidParamBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>


    //其余三方支付
    //@POST("//prodApi/res/promotion_top_value/post")
    //Observable<BaseBean<RechargeCreateBean>> rechargeCreate(@Body RequestBody requestBody);
    //轮询接口
    @POST("/prodApi/network_minimum_table/patch")
    fun rechargeSearch(@Body param: com.juicy.common.model.bean.OrderNoParamBean): Observable<com.juicy.common.model.bean.NetResponseBean<OrderStatusBean>> // 支付接口不处理 token

    @POST("/prodApi/v1/doc_average_array/update")
    fun updateAgoraUid(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>
    @POST("/prodApi/v3/memory_min_list/patch")
    fun getUserExtraInfo(@Body getRechargeInfo: UserIdParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.GiftItemBean>>

    //成功支付后，获取回调接口Purchase对象进行回传数据校验。
    @POST("/prodApi/memory_max_hash/remove")
    fun rechargePayment(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<Void>> // 支付接口不处理 token
    //风控接口
    @POST("/prodApi/money_average_item/delete")
    fun uploadRisk(@Body info: com.juicy.common.model.bean.InfoItemBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    @POST("/prodApi/v2/document_maximum_map/remove")
    fun GetCoinGoodsSearch(@Body getGoods: PlayParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>

    //获取后置摄像头配置接口
    @POST("/prodApi/resource/doc_second_result/search")
    fun rearCameraConfig(): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.CameraCOnfigInfoBean>> // 支付接口不处理 token
    @POST("/prodApi/v1/memory_maximum_array/upload")
    fun deleteAccount(): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    @POST("/prodApi/v3/goods_maximum_map/list")
    fun heartbeat(): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    @POST("/prodApi/v2/game_rank/remove")
    fun postSwitch(@Body appModeSwitchBean: com.juicy.common.model.bean.AppModeSwitchBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>



    @POST("/prodApi/v0/memory_minimum_rank/search")
    fun getUserListOnlineStatus(@Body param: UserIdsParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<Map<String, String>>>

    @POST("/prodApi/v2/followlist_min_string/set")
    fun GetUserOnlineStatus(@Body param: UserIdParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<String>>
    //主播排行榜列表查询 POST
    @POST("/prodApi/res/doc_maximum_hash/download")
    fun rankSearch(@Body body: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<RankInfoBean>>

    //主播排行榜列表查询 POST
    @POST("/prodApi/v0/diagram_group/update")
    fun userSearch(@Body body: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<RankInfoBean>>

    //开通后置摄像头接口
    @POST("/prodApi/v2/money_second_hash/set")
    fun rearCameraOpen(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>


    @get:POST("/prodApi/v0/report_hash/list")
    val strategy: Observable<com.juicy.common.model.bean.NetResponseBean<UserStratResult>>

    @get:POST("/prodApi/v0/report_hash/list")
    val strategyCall: Observable<com.juicy.common.model.bean.NetResponseBean<UserStratResult>>

    @POST("/prodApi/v3/report_top_hash/update")
    fun login(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<LoginUserInfoResult>> // 登录页不处理token失效

    //登录接口
    @POST("/prodApi/v3/report_top_hash/update")
    fun app_login(@Body loginBean: com.juicy.common.model.bean.LoginParamBean): Observable<com.juicy.common.model.bean.NetResponseBean<LoginUserInfoResult>>

    @POST("/prodApi/v3/diagram_table/remove")
    fun giveUserGift(@Body askGiftEventBean: com.juicy.common.model.bean.AskGiftEventBean): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.CoinsBean>>

    //生成支付订单(谷歌支付生成订单、其余三方支付生成订单)
    @POST("/prodApi/res/promotion_top_value/post")
    fun rechargeCreate(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<PayResultBean>> // 支付接口不处理 token

    @POST("/prodApi/v3/userorder_second_key/add")
    fun pickUp(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    @POST("/prodApi/v2/user_top_rank/set")
    fun GetUserOssPolicy(): Observable<com.juicy.common.model.bean.NetResponseBean<OssPolicyINfoBean>> // OSS配置信息不处理token失效(oss上传权限)

    @POST("/prodApi/memory_json/upload")
    fun insertRecord(@Body blockOrReport: com.juicy.common.model.bean.AnchorHBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>
    //获取屏蔽列表 POST


    @get:POST("/prodApi/resource/promotion_min_group/remove")
    val userLevel: Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.LevelInfoBean>>

    //查询通话结果接口 POST   ChannelNameBean
    @POST("/prodApi/v3/config_top_result/upload")
    fun VideoCallsResult(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorWallTagBean>>

    @POST("/prodApi/mem_min_json/remove")
    fun cancelFriend(@Body followUserId: com.juicy.common.model.bean.FollowUserIdParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    @POST("/prodApi/res/doc_maximum_group/remove")
    fun GetCheckRecharge(@Body getRechargeInfo: com.juicy.common.model.bean.ACheckInvBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>> //融云接口不处理token失效


    @POST("/prodApi/v3/activity_first_rank/add")
    fun giftList(): Observable<com.juicy.common.model.bean.NetResponseBean<ArrayList<com.juicy.common.model.bean.OrderInfoBean>>>

    @POST("/prodApi/resource/network_average_item/delete")
    fun UpdateAvatar(@Body updateAvatar: com.juicy.common.model.bean.AvatarUploadBean): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorMedialBean>> // 图片上传不处理token失效

    @POST("/prodApi/v0/harddisk_minimum_value/get")
    fun saveUserBasicInfo(@Body saveUserInfo: com.juicy.common.model.bean.EditUserInfoBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    @POST("/prodApi/v3/config_maximum_group/search")
    fun create(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<AppOnCallBean>>

    @POST("/prodApi/report_min_array/patch")
    fun hangup(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    @POST("/prodApi/gift_min_rank/remove")
    fun join(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    //获取用户信息
    @POST("/prodApi/v3/stock_min_group/download")
    fun getUserInfo(@Body param: UserIdParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>> //部分融云接口不处理token失效

    @POST("/prodApi/v3/stock_min_group/download")
    fun getUserInfo2(@Body param: UserIdParamsBean): Call<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>

    //主播墙接口
    @POST("/prodApi/v1/userorder_maximum_table/post")
    fun wallSearch(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.AnchorInfoBean>>>
    @POST("/prodApi/v2/agent_string/add")
    fun requestBlockList(): Observable<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.SimpleUserInfoBean>>>

    //查询通话结果接口 POST   ChannelNameBean
    @POST("/prodApi/v3/config_top_result/upload")
    fun CallResultBean(@Body body: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.CallResultInfoBean>>

    @POST("/prodApi/link_min_item/post")
    fun GiveAppsFlyerArgs(@Body appsFlyerArgs: com.juicy.common.model.bean.AdjustBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>> // AF接口不处理 token

    @POST("/prodApi/v2/diagram_max_group/patch")
    fun addFriend(@Body followUserId: com.juicy.common.model.bean.AnchorIDBean): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    //免打扰接口
    @POST("/prodApi/v2/order_group/update")
    fun switchNotDisturb(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<Boolean>>

    //Banner信息
    @POST("/prodApi/resource/followlist_first_value/delete")
    fun GetBannerInfo(): Observable<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.BannerItemBean>>> // 广告接口不处理 token

    //导量
    @POST("/prodApi/v3/product_first_string/add")
    fun GetStrongGuide(): Observable<com.juicy.common.model.bean.NetResponseBean<StrongGuildBean>> // 广告接口不处理 token

    //优惠商品(活动促销)
    @POST("/prodApi/res/message_top_json/post")
    fun GetLastSpecialOffer(@Body getRechargeInfo: com.juicy.common.model.bean.GoodsParamsBean): Call<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>> // 前置接口已处理token

    //退出登录
    @POST("/prodApi/v2/report_second_string/upload")
    fun signOut(): Observable<Any> // 退出接口不处理 token

    //保存用户基本信息(注册页面)
    @POST("/prodApi/v0/link_last_array/remove")
    fun saveBasicInfo(@Body body: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<Void>> // 注册页未登录

    @get:POST("/prodApi/v2/stock_max_table/upload")
    val presented: Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ExpCoinsBean>>

    @get:POST("/prodApi/device_group/post")
    val userCoins: Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>

    @get:POST("/prodApi/v1/activity_last_array/post")
    val randomBroadcaster: Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>

    @get:POST("/prodApi/resource/goods_item/remove")
    val robotFAQ: Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.RobotBean>>

    //快速匹配接口
    @POST("/prodApi/resource/activity_max_string/update")
    fun flashChat(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<VideoCallInfoBean>>

    //取消匹配接口
    @POST("/prodApi/v3/meminfo_second_list/get")
    fun flashCancel(@Body requestBody: RequestBody): Observable<com.juicy.common.model.bean.NetResponseBean<String>>

    //订阅相关接口
    @POST("/prodApi/v1/activity_min_group/list")
    fun GetSubscribeSearch(@Body getSubscribe: PayChannelBean): Observable<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>

    //IM限制接口
    @POST("/prodApi/v2/msg_second_list/search")
    fun createImSession(@Body getAnchorId: com.juicy.common.model.bean.BroadcasterIdParamsBean): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ImSessionBlanceBean>>

    @POST("/prodApi/v1/document_second_result/patch")
    fun updateMedia(@Body getMediaInfo: com.juicy.common.model.bean.MediaActionBean): Observable<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.MediaInfoBean>>>


    companion object {
        const val getAppConfig: String = "/prodApi/v1/diagram_minimum_item/post"
    }
}
