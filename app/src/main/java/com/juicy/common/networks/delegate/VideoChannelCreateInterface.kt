package com.juicy.common.networks.delegate

import androidx.annotation.Keep
import com.google.gson.Gson
import com.juicy.common.networks.Service
import com.juicy.common.model.message_event.AppOnCallBean
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import java.util.UUID

object VideoChannelCreateInterface {
    fun create(
        toUserId: String?,
        callSource: Int,
        callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<AppOnCallBean>>
    ) {
        val onCallReq = OnCallReq()
        onCallReq.toUserId = toUserId
        onCallReq.clientSessionId = UUID.randomUUID().toString()
        onCallReq.callType = 1
        onCallReq.callSource = callSource
        onCallReq.playSupportType = 1
        onCallReq.isFree = false
        onCallReq.supportVideoSdks = ArrayList()
        onCallReq.supportVideoSdks?.add(1)

        val gson = Gson()
        val body = RequestBody.create(
            "application/json; charset=utf-8".toMediaTypeOrNull(),
            gson.toJson(onCallReq)
        )
        RetrofitManage.instance.create(Service::class.java)
            .create(body)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }

    @Keep
    private class OnCallReq {
        var toUserId: String? = null
        var clientSessionId: String? = null
        var callType: Int = 0
        var callSource: Int = 0
        var playSupportType: Int = 0
        var isFree: Boolean = false
        var supportVideoSdks: MutableList<Int>? = null
    }
}