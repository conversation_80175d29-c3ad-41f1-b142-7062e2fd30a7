package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import org.json.JSONObject

object VideoCallsResultInterface {
    fun videoCallsResult(channelName: String?, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorWallTagBean>>) {
        try {
            val json = JSONObject()
            json.put("channelName", channelName) // 通话频道号
            json.put("isRemoteImageUrl", true) //是否返回远程图片地址
            val body =
                RequestBody.create("application/json; charset=utf-8".toMediaTypeOrNull(), json.toString())
            RetrofitManage.instance.create(Service::class.java)
                .VideoCallsResult(body)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(callBack)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}