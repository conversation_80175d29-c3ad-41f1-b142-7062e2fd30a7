package com.juicy.common.networks.request

import com.juicy.common.config.Cache
import okhttp3.Interceptor

import okhttp3.Request

import okhttp3.Response

import java.io.IOException

class UserIdHeaderIp : Interceptor {
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        // 以拦截到的请求为基础建立一个新的请求对象，而后插入Header
        val request: Request = chain.request().newBuilder()
            .addHeader(
                "user_id",
                if ((null == Cache.instance.userInfo ||  Cache.instance.userInfo?.userId.isNullOrEmpty()))
                    ""
                else
                    Cache.instance.userInfo?.userId?:""
            )
            .build()
        // 开始请求
        return chain.proceed(request)
    }
}