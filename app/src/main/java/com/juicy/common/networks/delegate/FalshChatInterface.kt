package com.juicy.common.networks.delegate

import androidx.annotation.Keep
import com.google.gson.Gson
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.VideoCallInfoBean
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import java.util.Date
import java.util.Random

object FalshChatInterface {
    fun falshChat(clientSessionId: String?, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<VideoCallInfoBean>>) {
        val onFalshReq = OnFalshReq()
        val currentTimeInSeconds = (Date().time / 1000.0).toLong()
        val dateTimeIntervalStr = currentTimeInSeconds.toString()
        val startIndex = 0
        val endIndex = dateTimeIntervalStr.length - 3
        val batchIdPrefix = dateTimeIntervalStr.substring(startIndex, endIndex).toInt()
        val batchIdSuffix = (Random().nextInt(9) + 1) * 100000
        onFalshReq.batchId = batchIdPrefix + batchIdSuffix
        onFalshReq.callSource = 12
        onFalshReq.supportVideoSdks = ArrayList()
        onFalshReq.supportVideoSdks?.add(1)
        onFalshReq.clientSessionId = clientSessionId

        val gson = Gson()
        val body = RequestBody.create(
            "application/json; charset=utf-8".toMediaTypeOrNull(),
            gson.toJson(onFalshReq)
        )
        RetrofitManage.instance.create(Service::class.java)
            .flashChat(body)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }

    @Keep
    private class OnFalshReq {
        var batchId: Int? = null //支付通道（IAP-苹果内购 GP-谷歌支付）
        var clientSessionId: String? = null

        var callSource: Int? = null

        var supportVideoSdks: MutableList<Int>? = null
    }
}