package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import org.json.JSONObject

object VideoJoinAndAgoraUidUpdateInterface {
    fun joinAndUpdate(
        agoraUid: String?,
        channelName: String?,
        callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>
    ) {
        try {
            val service = RetrofitManage.instance.create(
                Service::class.java
            )
            val joinJson = JSONObject()
            joinJson.put("channelName", channelName) // 通话频道号

            val body = RequestBody.create(
                "application/json; charset=utf-8".toMediaTypeOrNull(),
                joinJson.toString()
            )

            val updateJson = JSONObject()
            updateJson.put("agoraUid", agoraUid) // 声网uid

            val body1 = RequestBody.create(
                "application/json; charset=utf-8".toMediaTypeOrNull(),
                updateJson.toString()
            )

            service.join(body).mergeWith(
                service.updateAgoraUid(body1)
            ).subscribe(callBack)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
