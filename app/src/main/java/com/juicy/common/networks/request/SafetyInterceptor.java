package com.juicy.common.networks.request;

import static com.juicy.common.config.ApiPool.BASE_URL;
import static com.juicy.common.config.ApiPool.LOG_URL;

import android.app.Activity;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.NonNull;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.blankj.utilcode.util.ToastUtils;
import com.juicy.common.networks.Service;
import com.juicy.common.status.UserOnlineStatusService;
import com.juicy.common.config.Constant;
import com.juicy.common.config.SpKeyPool;
import com.juicy.common.utils.AppActivityUtil;
import com.juicy.common.utils.AppUtil;
import com.juicy.common.utils.LanguageManager;
import com.juicy.common.utils.SpSaveUtil;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import okhttp3.Headers;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;

public class SafetyInterceptor implements Interceptor {
    @NonNull
    @Override
    public Response intercept(@NonNull Chain chain) throws IOException {

        if(AppUtil.checkNetworkNoToast()) {
            throw new IOException(Objects.requireNonNull(LanguageManager.getInstance()).getLocalTranslate("Network_error_please_check_the_network"));
        }

        Request request = chain.request();
        final String requestUrl = request.url().host();
        if(requestUrl.equals(Uri.parse(BASE_URL).getHost()) ||requestUrl.equals(Uri.parse(LOG_URL).getHost()) ){
            request = createRequest(request);

            Response response;
            response = chain.proceed(request);
            String token = request.headers().get("auth_token");
            response = handleResponse(token,response);
            return response;
        }
        return chain.proceed(request);
    }


    private Request createRequest(Request request) throws IOException {
        if(request.method().equalsIgnoreCase("post")){
            Map<String,String> headers = new HashMap<>();
            for (String name : request.headers().names()) {
                String value = request.headers().get(name);
                headers.put(name,value);
            }

            final String haderStr = JSON.toJSONString(headers);
            //获取Body
            Map<String, Object> bodyMap = new HashMap<>();
            //获取全部Hander 头转化为Json字符串
            bodyMap.put("http_headers",haderStr);

            RequestBody requestBody = request.body();
            if (requestBody != null) {
                if (isPlaintext(requestBody.contentType())) {
                    Buffer buffer = new Buffer();
                    requestBody.writeTo(buffer);
                    String bodyStr = buffer.readString(getCharset(requestBody.contentType() != null ? requestBody.contentType() : MediaType.parse("application/json")));
                    Log.d("", "\trequest body:" + bodyStr);
                    if (bodyStr.startsWith("[") && bodyStr.endsWith("]")) {
                        //数组
                        bodyMap.put("list",JSON.parseObject(bodyStr,new TypeReference<List<Object>>(){}));
                    } else if (bodyStr.startsWith("{") && bodyStr.endsWith("}")) {
                        //对象 new TypeReference<Map<String, Object>>(){}

                        Map<String, Object> map = JSON.parseObject(bodyStr,new TypeReference<Map<String, Object>>(){});
                        bodyMap.putAll(map);
                    }
                }
            }

            if(!bodyMap.isEmpty()){
                //判断当前请求是否AppConfig接口请求
                String encryptStr = null;
                if(request.url().url().getPath().equals(Service.getAppConfig)){
                    encryptStr = SafetyKey.INSTANCE.encodeString(JSON.toJSONString(bodyMap).getBytes(),SafetyKey.INSTANCE.getDefaultKey());
                }else{
                    encryptStr = SafetyKey.INSTANCE.encodeString(JSON.toJSONString(bodyMap).getBytes(), Objects.requireNonNull(SafetyKey.INSTANCE.getEncryptKey()).getBytes());
                }

                if(requestBody != null){
                    requestBody = RequestBody.create(encryptStr,requestBody.contentType() != null ? requestBody.contentType() : MediaType.parse("application/json"));
                    Request.Builder builder = request.newBuilder();
                    builder.setHeaders$okhttp(new Headers.Builder());
                    builder.addHeader("Content-Type","application/json");
                    builder.addHeader("sec_ver","0");

                    String token = SpSaveUtil.getStringValue(SpKeyPool.APP_TOKEN_KEY,"");
                    assert token != null;
                    builder.addHeader("auth_token", token.isEmpty() ? "" : md5(token));
                    builder.setBody$okhttp(requestBody);
                    return builder.build();
                }
            }
        }

        return request;
    }
    public static String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();

            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
    ///解包
    private Response handleResponse(String token,Response response) {
        //提前 response 的body内容
        ResponseBody responseBody = response.body();
        String data = null;
        if(responseBody != null){
            try {
                byte[] bytes = responseBody.bytes();
                Charset charset = getCharset(responseBody.contentType() != null ? responseBody.contentType() : MediaType.parse("application/json"));
                String source = new String(bytes, charset);
                if(!TextUtils.isEmpty(source)){
                    source = source.replaceAll("\r\n", "");
                    if(response.request().url().url().getPath().equals(Service.getAppConfig)){
                        String baseStr = null;
                        baseStr = SafetyKey.INSTANCE.decodeString(Base64.decode(source,Base64.DEFAULT),SafetyKey.INSTANCE.getDefaultKey());

                        Map<String,Object> baseMap =   JSON.parseObject(baseStr,new TypeReference<Map<String, Object>>(){});
                        if(baseMap != null && baseMap.get("data") instanceof Map){
                            Map<String,Object> dataMap = (Map<String,Object>)baseMap.get("data");
                            String bounded = new String(Base64.decode((String) dataMap.get("cat"),Base64.DEFAULT));
                            String competent = new String(Base64.decode((String) dataMap.get("pig"),Base64.DEFAULT));
                            String delivered = new String(Base64.decode((String) dataMap.get("chicken"),Base64.DEFAULT));
                            delivered = delivered.replaceAll("\r\n", "");
                            String dataStr = SafetyKey.INSTANCE.decodeString(Base64.decode(delivered,Base64.DEFAULT),(bounded+competent).getBytes());
                            Map<String,Object> m = JSON.parseObject(dataStr,new TypeReference<Map<String, Object>>(){});
                            baseMap.put("data",m);
                            baseMap.put("code","0");
                            data = JSON.toJSONString(baseMap);
                        }
                    }else{
                        data = SafetyKey.INSTANCE.decodeString(Base64.decode(source,Base64.DEFAULT), SafetyKey.INSTANCE.getEncryptKey().getBytes());
                    }
                }
            } catch (Exception ignored) {
                ignored.printStackTrace();
            }
        }
        if(data != null){
            Log.d("","\tresponse body:" + data);
            Activity currentActivity =  AppActivityUtil.getCurrentActivity();
            if (currentActivity != null) {
                currentActivity.getClass().getSimpleName();
                if (!currentActivity.getClass().getSimpleName().equals("AuthActivity")) {

                    String newToken = SpSaveUtil.getStringValue(SpKeyPool.APP_TOKEN_KEY, "");
                    if (newToken != null) {
                        newToken = md5(newToken);
                    }
                    if (!token.isEmpty() && token.equals(newToken)) {
                        Object object = JSON.parse(data);
                        if (object instanceof Map) {

                            Object code = ((Map) object).get("code");
                            int codeInt = 0;
                            if (code instanceof String) {
                                codeInt = Integer.parseInt((String) code);
                            } else {
                                codeInt = (int) code;
                            }

                            if (Constant.TOKEN_EXPIRE_CODES.contains(codeInt)) {
                                if (codeInt == 10010003) {
                                    String msg = (String) ((Map) object).get("msg");
                                    if (msg != null) {
                                        ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate(msg));
                                    }
                                } else {
                                    ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate("Token_expired_please_login_again"));
                                }
                                UserOnlineStatusService.exitToLogin();
                            }
                        }
                    }

                }
            }

            response = response.newBuilder()
                .body(ResponseBody.create(MediaType.parse("application/json;charset=UTF-8"), data))
                .build();
        }


        return response;
    }

    private static boolean isPlaintext(MediaType mediaType) {
        if (mediaType == null) return false;
        if (mediaType.type().equals("text")) {
            return true;
        }
        String subtype = mediaType.subtype();
        subtype = subtype.toLowerCase();
        //
        return subtype.contains("x-www-form-urlencoded") || subtype.contains("json") || subtype.contains("xml") || subtype.contains("html");
    }


    private static Charset getCharset(MediaType contentType) {
        Charset charset = contentType != null ? contentType.charset(StandardCharsets.UTF_8) : StandardCharsets.UTF_8;
        if (charset == null) charset = StandardCharsets.UTF_8;
        return charset;
    }



}
