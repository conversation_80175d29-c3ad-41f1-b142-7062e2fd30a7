package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GetGoodsPromotionInterface {
    fun getGoodsPromotion(getGoods: PlayParamsBean, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ActivityInfoBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .GetGoodsPromotion(getGoods)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}