package com.juicy.common.networks.delegate

import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.config.ProtocolPool
import com.juicy.common.utils.MyUploadUtil
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.ObservableSource
import io.reactivex.rxjava3.functions.Function
import io.reactivex.rxjava3.schedulers.Schedulers
import okhttp3.MultipartBody
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.HeaderMap
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part

object UpdateAvatarInterface {
    fun update(filePath: String?, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorMedialBean>>) {
        val OssRetrofit = Retrofit.Builder()
            .baseUrl(MyUploadUtil.ossHost) //oss域名地址
            .addConverterFactory(GsonConverterFactory.create())
            .build()
        val UploadOssService = OssRetrofit.create(
            Service::class.java
        )
        val header: Map<String, String> = ProtocolPool.commonHeaderWithToken?: mapOf<String,String>()

        val allService = RetrofitManage.instance.create(
            com.juicy.common.networks.Service::class.java
        )


        UploadOssService.GiveAvatarToOss(
            header,
            MyUploadUtil.getUploadAvatarBody(filePath?:"").parts
        )
            .subscribeOn(Schedulers.io())
            .concatMap<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorMedialBean>>(
                Function<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ImageInfoBean>, ObservableSource<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorMedialBean>>> { bnBaseBean ->
                    if (bnBaseBean.isOk && bnBaseBean.data != null) {
                        allService.UpdateAvatar(
                            com.juicy.common.model.bean.AvatarUploadBean(
                                bnBaseBean.data?.filename ?: ""
                            )
                        )
                    } else {
                        Observable.never()
                    }
                }
            )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }

    interface Service {
        @Multipart
        @POST("/")
        fun GiveAvatarToOss(
            @HeaderMap headers: Map<String, String>,
            @Part parts: List<MultipartBody.Part>
        ): Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ImageInfoBean>>
    }
}
