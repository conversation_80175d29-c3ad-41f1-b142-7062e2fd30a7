package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.model.bean.PayResultBean
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object CreateOrderInterface {
    fun CreateOrder(bm: com.juicy.common.model.bean.CreateOrderBean, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<PayResultBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .rechargeCreate(RetrofitManage.requestBody(bm))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }

    fun CreateOtherOrder(bo: com.juicy.common.model.bean.PayInfoBean, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<PayResultBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .rechargeCreate(RetrofitManage.requestBody(bo))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}