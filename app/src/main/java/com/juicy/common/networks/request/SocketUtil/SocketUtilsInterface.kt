package com.juicy.common.networks.request.SocketUtil

import com.juicy.common.networks.request.SocketUtil.ModelCallBack.Connect
import com.juicy.common.networks.request.SocketUtil.ModelCallBack.getConnectSubs
import io.socket.client.Socket
import io.socket.emitter.Emitter
import org.json.JSONObject

interface SocketUtilsInterface {
    interface Model {
        fun reconnect(modelCallBack: Connect)

        fun getConnectSubs(mSkIo: Socket?, modelCallBack: getConnectSubs)

        fun registerEvent(mSkIo: Socket, mEmitterListener: Emitter.Listener?)

        fun onReceive(key: String, keyId: String, timestamp: String?, jsonObject: JSONObject)
    }

    interface SUtil {
        fun handleCall(args: Array<Any>?)
    }
}
