package com.juicy.common.networks.delegate

import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

object GiveUpdateAvatarInterface {
    fun getUpdateAvatar(updateAvatar: com.juicy.common.model.bean.AvatarUploadBean, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorMedialBean>>) {
        RetrofitManage.instance.create(Service::class.java)
            .UpdateAvatar(updateAvatar)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(callBack)
    }
}