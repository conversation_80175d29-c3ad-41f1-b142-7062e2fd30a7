package com.juicy.common.status

import android.content.Intent
import android.os.Handler
import android.os.Looper
import com.alibaba.android.arouter.launcher.ARouter
import com.juicy.app.JuicyApplication.Companion.juicyApplication
import com.juicy.app.R
import com.juicy.common.model.event.OnlineStatusChangeEvent
import com.juicy.common.networks.delegate.GetUserListOnlineStatusInterface.getUserListOnlineStatus
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.RongCloud.RongUtil.rongLogot
import com.juicy.common.utils.SpSaveUtil.putStringValue
import org.greenrobot.eventbus.EventBus
import java.util.Timer
import java.util.TimerTask

class UserOnlineStatusService private constructor() {
    var isStart: Boolean = false
        private set
    private var timer: Timer? = null
    private val handler = Handler(Looper.getMainLooper())
    private val statusTimeDic: MutableMap<String, StatusAndTime> =
        HashMap()
    private val userIds: MutableList<String> =
        ArrayList()

    init {
        start()
    }

    fun start() {
        stop()
        startTimer()
        isStart = true
    }

    fun stop() {
        stopTimer()
        isStart = false
    }

    private fun startTimer() {
        stopTimer()
        timer = Timer()
        timer!!.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                handler.post { checkStatus() }
            }
        }, 0, REQUEST_TIME_INTERVAL)
    }

    private fun stopTimer() {
        if (timer != null) {
            timer!!.cancel()
            timer = null
        }
    }

    fun addUserId(userId: String) {
        userIds.remove(userId)

        userIds.add(0, userId)

        if (userIds.size > MAX_USER_COUNT + OFFSET) {
            userIds.subList(MAX_USER_COUNT, userIds.size).clear()
        }
    }

    fun removeUserId(userId: String) {
        userIds.remove(userId)
    }

    fun forceRefresh() {
        for ((key, value) in statusTimeDic) {
            statusTimeDic[key] = StatusAndTime(0, value.status)
        }
        checkStatus()
    }

    fun getStatus(userId: String): String? {
        val item = statusTimeDic[userId] ?: return null
        return item.status
    }

    fun addUserIds(newUserIds: List<String>) {
        if (newUserIds.isEmpty()) {
            return
        }
        for (userId in newUserIds) {
            addUserId(userId)
        }
        checkStatus()
    }

    fun addUserIdsAndRefresh(newUserIds: List<String>) {
        if (newUserIds.isEmpty()) {
            return
        }
        for (userId in newUserIds) {
            val item = statusTimeDic[userId]
            if (item != null) {
                statusTimeDic[userId] = StatusAndTime(0, item.status)
            }
            addUserId(userId)
        }
        checkStatus()
    }

    fun addUserIdAndForceRefresh(userId: String) {
        val item = statusTimeDic[userId]
        if (item != null) {
            statusTimeDic[userId] = StatusAndTime(0, item.status)
        }
        addUserId(userId)
        checkStatus()
    }

    private fun checkStatus() {
        // 检查登录状态
        if (!Cache.instance.isLogin()) {
            return
        }

        val currentTime = System.currentTimeMillis() / 1000
        var timeOutUserIds: MutableList<String> = ArrayList()

        for (userId in userIds) {
            val status = statusTimeDic[userId]
            if (status == null || status.time <= (currentTime - VALID_DURATION / 1000)) {
                timeOutUserIds.add(userId)
            }
        }

        if (timeOutUserIds.size > MAX_REQUEST_COUNT) {
            timeOutUserIds = timeOutUserIds.subList(0, MAX_REQUEST_COUNT)
        }

        if (timeOutUserIds.isEmpty()) {
            return
        }
        if (!canShow()) return

        // 调用获取在线状态的接口
        getUserListOnlineStatus(
            juicyApplication!!.applicationContext,
            timeOutUserIds,
            object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Map<String, String>>>() {
                override fun onNext(mapBaseBean: com.juicy.common.model.bean.NetResponseBean<Map<String, String>>) {
                    super.onNext(mapBaseBean)
                    if (mapBaseBean?.data != null && !mapBaseBean.data!!.isEmpty()) {
                        val nowTime = System.currentTimeMillis() / 1000

                        // 清理过期数据
                        val timeOutIds: MutableList<String> = ArrayList()
                        for ((key, value) in statusTimeDic) {
                            if (value.time <= nowTime - VALID_DURATION * 1.5 / 1000) {
                                timeOutIds.add(key)
                            }
                        }
                        for (id in timeOutIds) {
                            statusTimeDic.remove(id)
                        }

                        // 更新状态
                        for ((key, value) in mapBaseBean.data!!) {
                            statusTimeDic[key] = StatusAndTime(nowTime, value)
                        }
                        // 发送状态更新广播
                        EventBus.getDefault().post(OnlineStatusChangeEvent())
                    }
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                }
            })
    }

    fun onEnterBackground() {
        stopTimer()
    }

    fun onEnterForeground() {
        startTimer()
        checkStatus()
    }

    fun getStatusColor(status: String): Int {
        var color = 0

        if (status == Constant.IN_CALL) {
            color = R.drawable.bg_incall_coral
        } else if (status == Constant.ONLINE || status == Constant.AVAILABLE) {
            color =  R.drawable.bg_online_frost
        } else if (status == Constant.BUSY) {
            color = R.drawable.bg_busy_snow
        } else if (status == Constant.OFFLINE) {
            color = R.drawable.bg_offline_lead
        }
        return color
    }

    fun getStatusEnableCall(status: String): Boolean {
        var isEnableCall = false
        if (status == Constant.ONLINE || status == Constant.AVAILABLE) {
            isEnableCall = true
        }
        return isEnableCall
    }

    companion object {
        @JvmStatic
        @Volatile
        var instance: UserOnlineStatusService? = null
            //方便调用，初始化使用带参数的
            get() {
                if (field == null) {
                    synchronized(UserOnlineStatusService::class.java) {
                        if (field == null) {
                            field = UserOnlineStatusService()
                        }
                    }
                }
                return field
            }
            private set

        // 配置参数
        private const val MAX_USER_COUNT = 20
        private const val MAX_REQUEST_COUNT = 20
        private const val REQUEST_TIME_INTERVAL: Long = 2500 // 5秒
        private const val OFFSET = 10
        private const val VALID_DURATION: Long = 5000 // 10秒


        @JvmStatic
        fun exitToLogin() {
            rongLogot()
            putStringValue(SpKeyPool.APP_TOKEN_KEY, "")
            Cache.instance.isLogin = true
            Cache.instance.allHomeRequest = false
            Cache.instance.guideHasShow = false
            Cache.instance.saleActivityHasShow = false
            Cache.instance.presentedHasShow = false
            Cache.instance.userPromotionHasShow = false
            Cache.instance.userPromotionResetShow = true
            Cache.instance.saleActivityResetShow = true
            Cache.instance.salePromotionCreateTime = 0
            Cache.instance.userPromotionCreateTime = 0
            Cache.instance.salePromotionGood = null
            Cache.instance.userPromotionGood = null
            Cache.instance.bannerList = null
            Cache.instance.coinGoods = null
            Cache.instance.currentFirstTab = 0
            Cache.instance.currentSecondTab = 0
            Cache.instance.guide = null
            Cache.instance.userStratResult = null

            ARouter
                .getInstance()
                .build(Constant.LOGIN_ACTIVITY_ROUTE)
                .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                .navigation()
        }

        private fun canShow(): Boolean {
            //判断当前是否登录

            if (!Cache.instance.isLogin()) {
                return false
            }

            //判断页面路由
            val currentActivity = currentActivity
            if (currentActivity == null || currentActivity.javaClass.simpleName == null) {
                return true
            }
            //当前是否在登录页面
            if (currentActivity.javaClass.simpleName == "AuthActivity") {
                return false
            }


            //如果当前在首页并且是匹配
            if (currentActivity.javaClass.simpleName == "CoreActivity") {
                if (Cache.instance.currentIndex == 1) {
                    return false
                }
            }
            if (currentActivity.javaClass.simpleName == "VideoCallActivity") {
                return false
            }

            return true
        }
    }
}