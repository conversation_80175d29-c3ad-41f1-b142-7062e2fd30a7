package com.juicy.common.utils

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.provider.Settings
import android.text.TextUtils
import android.view.WindowManager
import com.juicy.app.MyBuildConfig
import com.juicy.common.config.SpKeyPool
import java.lang.ref.SoftReference
import java.util.Locale

//import com.rong.location.BuildConfig;
object AppMessageUtil {
    private val LOCK = Any()

    /**
     * 用于判断是否需要清理的后台应用---使用过1s即认为是后台应用
     */
    const val TOTAL_TIME_IN_FOREGROUND_THRESHOLD: Long = 1000L

    var mAppInfoList: SoftReference<List<PackageInfo>>? = null

    private var ANDROID_ID: String? = null

    /**
     * 获取应用名称
     */
    fun getAppName(context: Context): String? {
        var appName: String? = null
        try {
            val ai = context.packageManager.getApplicationInfo(
                context.packageName, PackageManager.GET_META_DATA
            )
            appName = context.resources.getString(ai.labelRes)
        } catch (e: PackageManager.NameNotFoundException) {
        }
        return appName
    }


    @JvmStatic
    val appVersionName: String
        /**
         * 获取versionName
         */
        get() {
            val result = MyBuildConfig.VERSION_NAME
            return result
        }

    @JvmStatic
    val appVersionCode: Int
        /**
         * 获取versionCode
         */
        get() {
            val result = MyBuildConfig.VERSION_CODE
            return result
        }

    val defaultLanguageCode: String
        get() {
            var nativeLangCode = SpSaveUtil.getStringValue(SpKeyPool.LANGUAGE, "")
            if (TextUtils.isEmpty(nativeLangCode)) {
                nativeLangCode = Locale.getDefault().language
            }
            return nativeLangCode!!
        }

    fun getScreenSize(context: Context): IntArray {
        val manager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = manager.defaultDisplay

        return intArrayOf(
            display.width, display.height
        )
    }

    @JvmStatic
    fun getAndroidId(context: Context): String? {
        if (TextUtils.isEmpty(ANDROID_ID)) {
            ANDROID_ID =
                Settings.System.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        }
        return ANDROID_ID
    }
}
