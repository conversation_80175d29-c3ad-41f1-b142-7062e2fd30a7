package com.juicy.common.utils

import android.os.Build
import android.text.Html
import android.text.Spanned
import com.google.gson.Gson
import com.juicy.app.JuicyApplication
import com.juicy.common.config.SpKeyPool
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader

class LanguageManager private constructor() {
    @JvmField
    var supportLanguage: List<String?> = mutableListOf<String?>(
        "en",
        "ar",
        "es",
        "tr",
        "ko",
        "de",
        "ja",
        "it",
        "hi",
        "zh-tw",
        "th",
        "fr",
        "pt"
    )

    var languageMap: Map<String, String> = HashMap()

    @JvmField
    var isLanguageForce: Boolean = false

    fun changeLanguage(languaCode: String) {
        SpSaveUtil.putStringValue(SpKeyPool.LANGUAGE_CODE, languaCode)
        //切换多语言
        setAppResourceLanguage(languaCode)
    }

    val languageCode: String?
        get() =//获取多语言
            SpSaveUtil.getStringValue(SpKeyPool.LANGUAGE_CODE, "")

    fun initLanguage() {
        var languageCode = translateLanguageCode

        if (mutableListOf<String?>("ar", "he", "fa", "ur").contains(languageCode)) {
            languageCode = "ar"
        }

        if (!supportLanguage.contains(languageCode)) {
            languageCode = "en"
        }
        setAppResourceLanguage(languageCode!!)
    }

    val translateLanguageCode: String?
        get() {
            //获取多语言
            var languageCode = languageCode
            if (languageCode!!.isEmpty()) {
                //读取设备语音
                var locale =
                    LanguageTool.getLocaleLanguageCodeFunc(JuicyApplication.juicyApplication!!)
                // 国内直翻繁体
                if ("zh" == locale) {
                    locale = "zh-tw"
                }
                languageCode = locale
            }
            return languageCode
        }

    fun setAppResourceLanguage(languaCode: String) {
        //设置多语言
        //1.读取文件
        //2.判断是否是阿语，如果是切换镜像

        isLanguageForce = if (languaCode == "ar") {
            //
            true
        } else {
            //
            false
        }
        //        String readAsset = ZFileUtils.readAsset(mContext, fileName);
        languageMap = getSupportLanguageJson("translate/$languaCode.json")
    }

    fun getLocalTranslate(key: String): String {
        var value = languageMap[key]
        value = htmlToString(value)
        if (value != null) {
            return value
        }
        return key
    }

    fun getSupportLanguageJson(fileName: String): Map<String, String> {
        var br: BufferedReader? = null
        var jsonMap: Map<String, String> = HashMap()
        try {
            val `is` = JuicyApplication.juicyApplication!!.assets.open(fileName)
            val jsonBuilder = StringBuilder()
            br = BufferedReader(InputStreamReader(`is`))
            var line: String?
            while (null != (br.readLine().also { line = it })) {
                jsonBuilder.append(line).append("\r\n")
            }

            jsonMap = Gson().fromJson<Map<String, String>>(
                jsonBuilder.toString(),
                MutableMap::class.java
            )
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            if (null != br) {
                try {
                    br.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return jsonMap
    }

    companion object {
        @JvmStatic
        var instance: LanguageManager? = null
            get() {
                if (field == null) {
                    synchronized(LanguageManager::class.java) {
                        if (field == null) {
                            field = LanguageManager()
                        }
                    }
                }
                return field
            }
            private set

        //网页转义
        fun htmlToString(html: String?): String? {
            try {
                var spannedText: Spanned? = null
                spannedText =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        Html.fromHtml(html, Html.FROM_HTML_MODE_LEGACY)
                    } else {
                        Html.fromHtml(html)
                    }
                return spannedText.toString()
            } catch (e: Exception) {
                return html
            }
        }
    }
}
