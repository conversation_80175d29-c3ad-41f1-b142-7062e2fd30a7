package com.juicy.common.utils

import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.ContextWrapper
import com.blankj.utilcode.util.AppUtils
import java.io.BufferedReader
import java.io.InputStreamReader

object CommonUtils {
    //Double类型转换String类型
    fun doubleTrans(d: Double): String {
        if (Math.round(d) - d == 0.0) {
            return d.toLong().toString()
        }
        return d.toString()
    }

    //解决在dialog强制转换成activity
    @JvmStatic
    fun scanForActivity(cont: Context?): Activity? {
        if (cont == null) return null
        else if (cont is Activity) return cont
        else if (cont is ContextWrapper) return scanForActivity(cont.baseContext)

        return null
    }


    //获取Assets目录下文件
    fun getFromAssets(context: Context, fileName: String): String? {
        try {
            val inputReader = InputStreamReader(context.resources.assets.open(fileName))
            val bufReader = BufferedReader(inputReader)
            var line = ""
            var Result = ""
            while ((bufReader.readLine().also { line = it }) != null) Result += line
            return Result
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    //截取页面名称
    fun getLocalClassName(componentName: ComponentName): String {
        val packageName = AppUtils.getAppPackageName()
        val className = componentName.className
        val subStringStartOne = className.indexOf("/")
        val subStringEndOne = className.indexOf("}")
        val commonPackageName = className.substring(subStringStartOne + 1, subStringEndOne)
        //page.AgoraVideoActivity
        return commonPackageName.substring(packageName.length + 1)
    }
}
