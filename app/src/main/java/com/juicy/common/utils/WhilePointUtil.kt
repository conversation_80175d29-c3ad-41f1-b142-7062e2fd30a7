package com.juicy.common.utils

import android.content.Context
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.core.content.res.ResourcesCompat
import com.juicy.app.R

object WhilePointUtil {
    fun selectTabPoint(currentPagerIndex: Int, pointLl: LinearLayout) {
        for (i in 0 until pointLl.getChildCount()) {
            (pointLl.getChildAt(i) as ImageView).setImageResource(R.drawable.bg_point_unselected_steel)
            val params: LinearLayout.LayoutParams =
                LinearLayout.LayoutParams(AppDeviceUtils.dip2px(5f), AppDeviceUtils.dip2px(5f))
            params.setMargins(0, 0, AppDeviceUtils.dip2px(4f), 0)
            pointLl.getChildAt(i).setLayoutParams(params)
        }
        (pointLl.getChildAt(currentPagerIndex) as ImageView).setImageResource(R.drawable.bg_point_selected_black)
        val params: LinearLayout.LayoutParams =
            LinearLayout.LayoutParams(AppDeviceUtils.dip2px(10f), AppDeviceUtils.dip2px(5f))
        params.setMargins(0, 0, AppDeviceUtils.dip2px(4f), 0)
        pointLl.getChildAt(currentPagerIndex).setLayoutParams(params)
    }

    fun drawTabPoint(pageSize: Int, pointLl: LinearLayout, context: Context) {
        for (i in 0 until pageSize) {
            val point: ImageView = ImageView(context)
            point.setImageDrawable(
                ResourcesCompat.getDrawable(
                    context.getResources(),
                    R.drawable.bg_point_unselected_steel,
                    context.getTheme()
                )
            )
            var params: LinearLayout.LayoutParams =
                LinearLayout.LayoutParams(AppDeviceUtils.dip2px(5f), AppDeviceUtils.dip2px(5f))
            params.setMargins(0, 0, AppDeviceUtils.dip2px(4f), 0)
            if (i == 0) {
                point.setImageResource(R.drawable.bg_point_selected_black)
                params = LinearLayout.LayoutParams(
                    AppDeviceUtils.dip2px(10f),
                    AppDeviceUtils.dip2px(5f)
                )
                params.setMargins(0, 0, AppDeviceUtils.dip2px(4f), 0)
            }
            pointLl.addView(point, params)
        }
    }
}
