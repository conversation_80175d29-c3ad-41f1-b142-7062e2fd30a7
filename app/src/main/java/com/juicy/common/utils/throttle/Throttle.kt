package com.juicy.common.utils.throttle

import android.os.Handler
import java.util.Timer
import java.util.TimerTask

object Throttle {
    private val operations: MutableMap<String, ThrottleOperation> = HashMap()
    private val handler = Handler()

    @JvmStatic
    fun throttle(tag: String, onExecuteduration: Runnable): Boolean {
        return throttle(tag, 1000, onExecuteduration)
    }

    @JvmStatic
    @JvmOverloads
    fun throttle(
        tag: String,
        duration: Long,
        onExecute: Runnable,
        onAfter: Runnable? = Runnable {}
    ): Boolean {
        val throttled = operations.containsKey(tag)
        if (throttled) {
            return true
        }

        val timer = Timer()
        val operation = ThrottleOperation(onExecute, timer, onAfter)
        operations[tag] = operation

        timer.schedule(object : TimerTask() {
            override fun run() {
                handler.post {
                    val removed =
                        operations.remove(tag)
                    if (removed != null && removed.onAfter != null) {
                        removed.onAfter.run()
                    }
                }
            }
        }, duration)

        onExecute.run()
        return false
    }


    fun cancel(tag: String) {
        val operation = operations.remove(tag)
        operation?.timer?.cancel()
    }

    fun cancelAll() {
        for (operation in operations.values) {
            operation.timer.cancel()
        }
        operations.clear()
    }

    fun count(): Int {
        return operations.size
    }
}