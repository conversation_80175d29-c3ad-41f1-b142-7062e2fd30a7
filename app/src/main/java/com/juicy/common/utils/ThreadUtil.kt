package com.juicy.common.utils

import android.os.Handler
import android.os.Looper
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit

object ThreadUtil {
    private val sUIHandler: Handler

    init {
        sUIHandler = Handler(Looper.getMainLooper())
    }

    private val fixedThreadExecutor: ExecutorService = Executors.newFixedThreadPool(5)

    private val singleThreadExecutor: ScheduledExecutorService = Executors.newScheduledThreadPool(1)

    /**
     * 定时循环任务执行
     */
    private val sScheduledExecutorService: ScheduledExecutorService =
        Executors.newScheduledThreadPool(3)

    // 单线程确保串行任务
    fun executeOnSingleThread(runnable: Runnable?) {
        singleThreadExecutor.execute(runnable)
    }

    fun executeOnSingleThreadDelay(runnable: Runnable?, delay: Long, unit: TimeUnit?) {
        singleThreadExecutor.schedule(runnable, delay, unit)
    }

    /**
     * 添加并执行定时循环任务
     *
     * @param command
     * @param initialDelay
     * @param delay
     * @param unit
     * @return
     */
    fun executeOnSchedule(
        command: Runnable,
        initialDelay: Long,
        delay: Long,
        unit: TimeUnit?
    ): ScheduledFuture<*> {
        return sScheduledExecutorService.scheduleWithFixedDelay(command, initialDelay, delay, unit)
    }

    @JvmStatic
    fun execute(runnable: Runnable?) {
        fixedThreadExecutor.execute(runnable)
    }

    val isInMainThread: Boolean
        get() {
            return (Looper.myLooper() == Looper.getMainLooper())
        }

    fun runOnUIThreadNextLoop(runnable: Runnable) {
        sUIHandler.post(runnable)
    }

    @JvmStatic
    fun runOnUIThread(runnable: Runnable) {
        if (isInMainThread) {
            runnable.run()
        } else {
            sUIHandler.post(runnable)
        }
    }

    @JvmStatic
    fun runDelayOnUIThread(runnable: Runnable, delayMillis: Long) {
        sUIHandler.postDelayed(runnable, delayMillis)
    }

    fun removeRunnable(runnable: Runnable) {
        sUIHandler.removeCallbacks(runnable)
    }
}
