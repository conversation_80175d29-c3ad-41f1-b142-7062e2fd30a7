package com.juicy.common.utils.view

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView

open class TransableImageView : AppCompatImageView {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    override fun refreshDrawableState() {
        super.refreshDrawableState()

        alpha = if (isEnabled) {
            if (isPressed) {
                0.4f
            } else {
                1.0f
            }
        } else {
            0.4f
        }
    }
}
