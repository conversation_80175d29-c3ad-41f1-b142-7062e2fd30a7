package com.juicy.common.utils

import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.os.Build
import android.os.Environment
import android.os.StatFs
import android.provider.Settings
import android.util.DisplayMetrics
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import com.juicy.app.JuicyApplication
import java.io.BufferedReader
import java.io.File
import java.io.FileNotFoundException
import java.io.FileReader
import java.io.IOException
import java.io.InputStreamReader

object AppDeviceUtils {
    /**
     * 内存总大小
     */
    private var mTotalMem = 0L

    /**
     * dip转换px
     */
    @JvmStatic
    fun dip2px(dipValue: Float): Int {
        return dip2px(JuicyApplication.juicyApplication!!, dipValue)
    }

    /**
     * dip转换px
     */
    @JvmStatic
    fun dip2px(context: Context, dipValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (dipValue * scale + 0.5f).toInt()
    }

    /**
     * px转换dip
     */
    fun px2dip(pxValue: Float): Int {
        return px2dip(JuicyApplication.juicyApplication!!, pxValue)
    }

    /**
     * px转换dip
     */
    fun px2dip(context: Context, pxValue: Float): Int {
        val scale = context.resources.displayMetrics.density
        return (pxValue / scale + 0.5f).toInt()
    }

    val totalMemory: Long
        // 获得总内存
        get() {
            if (mTotalMem != 0L) {
                return mTotalMem
            }
            val mTotal: Long
            // /proc/meminfo读出的内核信息进行解释
            val path = "/proc/meminfo"
            var content: String? = null
            var br: BufferedReader? = null
            try {
                br = BufferedReader(FileReader(path), 8)
                var line: String?
                if ((br.readLine().also { line = it }) != null) {
                    content = line
                }
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            } catch (e: IOException) {
                e.printStackTrace()
            } finally {
                if (br != null) {
                    try {
                        br.close()
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
            }
            // beginIndex
            val begin = content!!.indexOf(':')
            // endIndex
            val end = content.indexOf('k')

            // 截取字符串信息
            content = content.substring(begin + 1, end).trim { it <= ' ' }
            mTotal = content.toInt().toLong()
            mTotalMem = mTotal * 1000
            return mTotalMem
        }

    fun externalStorageAvailable(): Boolean {
        return Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
    }

    val availableInternalStorageSize: Long
        get() {
            val path = Environment.getDataDirectory()
            val stat = StatFs(path.path)
            val blockSize = stat.blockSize.toLong()
            val availableBlocks = stat.availableBlocks.toLong()
            return availableBlocks * blockSize
        }

    val totalInternalStorageSize: Long
        get() {
            val path = Environment.getDataDirectory()
            val stat = StatFs(path.path)
            val blockSize = stat.blockSize.toLong()
            val totalBlocks = stat.blockCount.toLong()
            return totalBlocks * blockSize
        }

    val availableExternalStorageSize: Long
        get() {
            if (externalStorageAvailable()) {
                val path = Environment.getExternalStorageDirectory()
                val stat = StatFs(path.path)
                val blockSize = stat.blockSize.toLong()
                val availableBlocks = stat.availableBlocks.toLong()
                return availableBlocks * blockSize
            } else {
                return -1
            }
        }

    val totalExternalStorageSize: Long
        get() {
            if (externalStorageAvailable()) {
                val path = Environment.getExternalStorageDirectory()
                val stat = StatFs(path.path)
                val blockSize = stat.blockSize.toLong()
                val totalBlocks = stat.blockCount.toLong()
                return totalBlocks * blockSize
            } else {
                return -1
            }
        }

    /**
     * 检查内部可用空间是否小于一个百分比
     * @param percent
     * @return 小于就返回true，否则返回false
     */
    fun checkStorageLessThanPercent(percent: Int): Boolean {
        val available = availableExternalStorageSize
        val total = totalExternalStorageSize
        return (available * 1f / total) < (percent / 100f)
    }

    /**
     * 获取设备屏幕大小
     *
     * @param context
     * @return 0 width,1 height
     */
    @JvmStatic
    fun getScreenSize(context: Context?): IntArray {
        var context = context
        if (context == null) {
            context = JuicyApplication.juicyApplication
        }
        val dm = context!!.applicationContext.resources.displayMetrics
        val screenWidth = dm.widthPixels
        val screenHeight = dm.heightPixels

        return intArrayOf(
            screenWidth, screenHeight
        )
    }

    /**
     * 获取系统状态栏的高度 px 一般都是50px
     *
     * @return
     */
    fun getStatusHeight(context: Activity): Int {
        var height = 0
        val frame = Rect()
        context.window.decorView.getWindowVisibleDisplayFrame(frame)
        height = frame.top
        if (height == 0) {
            height = getStatusHeightByDimen(context)
        }
        return height
    }

    fun getStatusHeightByDimen(context: Activity): Int {
        var height = 0
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            height = context.resources.getDimensionPixelSize(resourceId)
        }
        return height
    }


    /**
     * 是否开启adb调试
     *
     * @param context
     * @return
     */
    fun isDebugEnable(context: Context): Boolean {
        val adb = Settings.Secure.getInt(context.contentResolver, Settings.Global.ADB_ENABLED, 0)
        return adb == 1
    }

    val isDeviceRooted: Boolean
        /**
         * 是否root
         *
         * @return
         */
        get() = checkRootBuildTags() || checkRootFile() || checkSuFile()

    private fun checkRootBuildTags(): Boolean {
        val buildTags = Build.TAGS
        return buildTags != null && buildTags.contains("test-keys")
    }

    private fun checkRootFile(): Boolean {
        val paths = arrayOf(
            "/system/app/Superuser.apk",
            "/sbin/su",
            "/system/bin/su",
            "/system/xbin/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/data/local/su",
            "/su/bin/su"
        )
        for (path in paths) {
            if (File(path).exists()) return true
        }
        return false
    }

    private fun checkSuFile(): Boolean {
        var process: Process? = null
        try {
            //  /system/xbin/which 或者 /system/bin/which
            process = Runtime.getRuntime().exec(arrayOf("which", "su"))
            val `in` = BufferedReader(InputStreamReader(process.inputStream))
            return `in`.readLine() != null
        } catch (t: Throwable) {
            return false
        } finally {
            process?.destroy()
        }
    }


    /**
     * 检测底部虚拟导航栏是否存在
     */
    fun isNavigationBarExist(activity: AppCompatActivity): Boolean {
        val view = activity.window.decorView
        if (view is ViewGroup) {
            val vp = view
            for (i in 0 until vp.childCount) {
                vp.getChildAt(i).context.packageName
                if (vp.getChildAt(i).id != View.NO_ID && "navigationBarBackground" == activity.resources.getResourceEntryName(
                        vp.getChildAt(i).id
                    )
                ) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * 获取底部虚拟导航栏的高度
     *
     * @return
     */
    fun getNavigationBarHeight(activity: AppCompatActivity): Int {
        var height = 0
        //屏幕实际尺寸
        val dm = DisplayMetrics()
        activity.windowManager.defaultDisplay.getRealMetrics(dm)
        //        int phoneHeight = dm.heightPixels;
        if (isNavigationBarExist(activity)) {
            val resourceId =
                activity.resources.getIdentifier("navigation_bar_height", "dimen", "android")
            if (resourceId > 0) {
                //获取NavigationBar的高度
                height = activity.resources.getDimensionPixelSize(resourceId)
            }
        }
        //        if (height > 0) {
//            //处理全屏模式下，部分手机isNavigationBarExist()始终返回true，NavigationBar的高度
//            int diffValue = (CommonUtils.appScreenHeight + StatusBarUtil.getStatusBarHeight(this) + height) - phoneHeight;
//            if (diffValue > 0) {
//                height -= diffValue;
//            }
//        }
        return height
    }
}
