package com.juicy.common.utils.view

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.util.LayoutDirection
import android.view.Gravity
import android.view.KeyEvent
import android.view.View
import android.view.View.OnFocusChangeListener
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.text.TextUtilsCompat
import com.juicy.app.R
import com.juicy.common.utils.LanguageManager
import java.util.Locale

class EditItem : RelativeLayout {
    var editText: AppCompatEditText? = null
        private set
    var editBg: AppCompatImageView? = null
        private set
    var titleName: AppCompatTextView? = null
        private set
    var rightGone: View? = null
        private set

   lateinit var ivEditRight:AppCompatImageView

    constructor(context: Context?) : super(context) {
        initView()
    }

    private fun initView() {
        val view = inflate(context, R.layout.item_footer, this)
        editBg = view.findViewById(R.id.barProgress)
        editText = view.findViewById(R.id.sortIcon)
        titleName = view.findViewById(R.id.counterReload)
        rightGone = view.findViewById(R.id.uploadThin)
        ivEditRight = view.findViewById(R.id.maskLarge)


        // 处理回车键关闭键盘
        editText?.setOnEditorActionListener(TextView.OnEditorActionListener { v: TextView?, actionId: Int, event: KeyEvent? ->
            if (actionId == EditorInfo.IME_ACTION_DONE || actionId == EditorInfo.IME_ACTION_NEXT ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER)
            ) {
                // 隐藏键盘
                val imm = context
                    .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(editText?.getWindowToken(), 0)
                editText?.clearFocus()
                true
            }
            false
        })
        if (TextUtilsCompat.getLayoutDirectionFromLocale(Locale.getDefault()) == LayoutDirection.RTL) {
            editText?.setGravity(Gravity.START or Gravity.CENTER_VERTICAL)
        }
        if (LanguageManager.instance?.isLanguageForce == true) {
            ivEditRight.scaleX = -1f
        }

        editText?.setOnFocusChangeListener(OnFocusChangeListener { view, b ->
            if (b) {
                editBg?.setImageResource(R.drawable.bg_setting_button_2_beige)
                titleName?.setTextColor(Color.parseColor("#2C2E33"))
            } else {
                editBg?.setImageResource(R.drawable.bg_setting_button_salmon)
                titleName?.setTextColor(Color.parseColor("#AAAAAA"))
            }
        })
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        initView()
    }
}
