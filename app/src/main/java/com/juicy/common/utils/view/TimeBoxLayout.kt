package com.juicy.common.utils.view

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.widget.LinearLayout
import android.widget.TextView

class TimeBoxLayout : LinearLayout {
    private var timeText = "00:00:00"
    private var digitViews: MutableList<TextView>? = null
    private var colonViews: MutableList<TextView>? = null
    private var textSize = 40f

    constructor(context: Context?) : super(context) {
        init()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    private fun init() {
        orientation = HORIZONTAL
        digitViews = ArrayList()
        colonViews = ArrayList()
        createTimeViews()
    }

    private fun createTimeViews() {
        removeAllViews()
        digitViews!!.clear()
        colonViews!!.clear()

        // 创建数字背景drawable
        val digitBg = GradientDrawable()
        digitBg.setColor(BG_COLOR)
        digitBg.cornerRadius = dpToPx(CORNER_RADIUS)
        digitBg.setStroke(dpToPx(BORDER_WIDTH).toInt(), BORDER_COLOR)

        // 设置间距
        val margin = dpToPx(4f).toInt()

        for (i in 0..7) {
            if (i == 2 || i == 5) {
                // 创建冒号
                val colonView = createColonView()
                colonViews!!.add(colonView)
                addView(colonView)
            } else {
                // 创建数字框
                val digitView = createDigitView(digitBg)
                digitViews!!.add(digitView)
                addView(digitView)
            }
        }

        updateTime(timeText)
    }

    private fun createDigitView(background: GradientDrawable): TextView {
        val textView = TextView(context)
        val params = LayoutParams(
            LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT
        )
        params.setMargins(dpToPx(1f).toInt(), 0, dpToPx(1f).toInt(), 0)
        textView.layoutParams = params
        textView.setPadding(
            dpToPx(5f).toInt(),
            dpToPx(0f).toInt(),
            dpToPx(5f).toInt(),
            dpToPx(0f).toInt()
        )
        textView.setTextColor(TEXT_COLOR)
        textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
        textView.gravity = Gravity.CENTER
        textView.typeface =
            Typeface.create("sans-serif-medium", Typeface.NORMAL)
        textView.background = background.constantState!!.newDrawable()
        return textView
    }

    private fun createColonView(): TextView {
        val textView = TextView(context)
        val params = LayoutParams(
            LayoutParams.WRAP_CONTENT,
            LayoutParams.WRAP_CONTENT
        )
        params.setMargins(dpToPx(1f).toInt(), 0, dpToPx(1f).toInt(), 0)
        textView.layoutParams = params
        textView.text = ":"
        textView.setTextColor(TEXT_COLOR)
        textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
        textView.gravity = Gravity.CENTER
        textView.setPadding(0, 0, 0, dpToPx(2f).toInt())
        textView.typeface =
            Typeface.create("sans-serif", Typeface.BOLD)
        return textView
    }

    fun setTime(time: String?) {
        if (time == null || !time.matches("\\d{2}:\\d{2}:\\d{2}".toRegex())) {
            return
        }
        this.timeText = time
        updateTime(time)
    }

    private fun updateTime(time: String) {
        if (digitViews!!.size < 6) return

        val parts = time.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        if (parts.size != 3) return

        var digitIndex = 0
        for (part in parts) {
            if (part.length == 2) {
                digitViews!![digitIndex].text = part[0].toString()
                digitViews!![digitIndex + 1].text = part[1].toString()
                digitIndex += 2
            }
        }
    }

    fun setTextSize(sp: Float) {
        this.textSize = sp
        for (digitView in digitViews!!) {
            digitView.setTextSize(TypedValue.COMPLEX_UNIT_SP, sp)
        }
        for (colonView in colonViews!!) {
            colonView.setTextSize(TypedValue.COMPLEX_UNIT_SP, sp)
        }
    }

    private fun dpToPx(dp: Float): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            resources.displayMetrics
        )
    }

    companion object {
        private val TEXT_COLOR = Color.parseColor("#FF0B0B0B")
        private val BG_COLOR = Color.TRANSPARENT
        private val BORDER_COLOR = Color.TRANSPARENT
        private const val BORDER_WIDTH = 1f
        private const val CORNER_RADIUS = 4f
    }
}