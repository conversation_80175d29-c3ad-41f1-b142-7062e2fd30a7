package com.juicy.common.utils

import java.security.SecureRandom
import java.security.cert.X509Certificate
import javax.net.ssl.HostnameVerifier
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSession
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

/**
 * SSL
 * 证书处理
 */
object SSLUtil {
    @JvmStatic
    val sSLSocketFactory: SSLSocketFactory
        //获取这个SSLSocketFactory
        get() {
            try {
                val sslContext: SSLContext =
                    SSLContext.getInstance("SSL")
                sslContext.init(null, trustManagerALL, SecureRandom())
                return sslContext.getSocketFactory()
            } catch (e: Exception) {
                throw RuntimeException(e)
            }
        }

    private val trustManagerALL: Array<TrustManager>
        //获取TrustManager
        get() {
            val trustAllCerts: Array<TrustManager> =
                arrayOf(object :
                    X509TrustManager {
                    override fun checkClientTrusted(
                        chain: Array<X509Certificate>,
                        authType: String
                    ) {
                    }

                    override fun checkServerTrusted(
                        chain: Array<X509Certificate>,
                        authType: String
                    ) {
                        requireNotNull(chain) { "checkServerTrusted: X509Certificate array is null" }
                        require(chain.size > 0) { "checkServerTrusted: X509Certificate is empty" }
                    }

                    override fun getAcceptedIssuers(): Array<X509Certificate> {
                        return arrayOf()
                    }
                })
            return trustAllCerts
        }

    @JvmStatic
    val trustManager: X509TrustManager
        //获取TrustManager
        get() {
            return object : X509TrustManager {
                override fun checkClientTrusted(
                    chain: Array<X509Certificate>,
                    authType: String
                ) {
                }

                override fun checkServerTrusted(
                    chain: Array<X509Certificate>,
                    authType: String
                ) {
                    //1.对服务端返回的证书做判空处理
                    requireNotNull(chain) { "checkServerTrusted: X509Certificate array is null" }
                    require(chain.size > 0) { "checkServerTrusted: X509Certificate is empty" }
                }

                override fun getAcceptedIssuers(): Array<X509Certificate> {
                    return arrayOf()
                }
            }
        }

    @JvmStatic
    val hostnameVerifier: HostnameVerifier
        //获取HostnameVerifier
        get() {
            val hostnameVerifier: HostnameVerifier =
                object : HostnameVerifier {
                    override fun verify(
                        s: String,
                        sslSession: SSLSession
                    ): Boolean {
                        //输出
                        println("getHostnameVerifier: s = " + s)
                        println("getHostnameVerifier: sslSession = " + sslSession)
                        return true
                    }
                }
            return hostnameVerifier
        }
}

