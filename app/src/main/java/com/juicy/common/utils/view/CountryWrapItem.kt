package com.juicy.common.utils.view

import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import com.juicy.app.R
import com.juicy.common.utils.LanguageManager.Companion.instance
import com.yesterselga.countrypicker.Country

@SuppressLint("ViewConstructor")
class CountryWrapItem(context: Context?, @JvmField var country: Country, private val isSelect: Boolean) :
    RelativeLayout(context) {

    init {
        initView()
    }

    private fun initView() {
        val view = inflate(context, R.layout.item_grid, this)
        if (instance?.isLanguageForce == true) {
            view.layoutDirection = LAYOUT_DIRECTION_RTL
        }
        val labelContent = view.findViewById<AppCompatTextView>(R.id.footerOpen)
        val iconImgView = view.findViewById<ImageView>(R.id.timerEdit)
        val name = view.findViewById<TextView>(R.id.largeSurface)
        val bgView = view.findViewById<View>(R.id.calendarLower)

        //        View selView = view.findViewById(R.id.beyondSubmit);
        if (isSelect) {
            val drawableResId = R.drawable.bg_country_item_sel_orange // 设置要应用的背景资源ID
            bgView.setBackgroundResource(drawableResId)
        } else {
            val drawableResId = R.drawable.bg_country_item_nor_maroon // 设置要应用的背景资源ID
            bgView.setBackgroundResource(drawableResId)
        }
        var nameStr = country.name
        if (country.name.contains(",")) {
            val split =
                country.name.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            nameStr = split[0]
        }

        name.text = nameStr
        if (country.code === "") {
            iconImgView.visibility = GONE
        } else {
            iconImgView.setImageResource(country.flag)
        }
    }
}
