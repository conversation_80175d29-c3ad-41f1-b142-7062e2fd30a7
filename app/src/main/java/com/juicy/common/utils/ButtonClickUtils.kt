package com.juicy.common.utils

object ButtonClickUtils {
    private var lastClickTime: Long = 0 //记录最近一次点击时间
    private var interval: Long = 1000 //间隔为1秒
    private var lastButtonId = 0 //存放最近一次传入的按钮id

    // 如果需要不同的间隔时间，直接调用这个方法设置所需间隔毫秒数即可
    private const val MIN_CLICK_DELAY_TIME = 1000
    private var lastClickTime2: Long = 0
    @JvmStatic
    fun setInterval(interval: Long) {
        ButtonClickUtils.interval = interval
    }

    @JvmStatic
    val isFastClick: Boolean
        // 不需要传入任何参数 直接在点击事件下调用此方法即可
        get() {
            if (System.currentTimeMillis() - lastClickTime < interval) {
                return true
            }
            lastClickTime = System.currentTimeMillis()
            return false
        }

    // 需要传入ButtonId
    fun isFastClick(buttonId: Int): Boolean {
        if (lastButtonId == buttonId && System.currentTimeMillis() - lastClickTime < interval) {
            return true
        }
        lastClickTime = System.currentTimeMillis()
        lastButtonId = buttonId
        return false
    }

    val isFastClick2: Boolean
        get() {
            var flag = false
            val curClickTime = System.currentTimeMillis()
            if ((curClickTime - lastClickTime2) >= MIN_CLICK_DELAY_TIME) {
                flag = true
            }
            lastClickTime2 = curClickTime
            return flag
        }
}
