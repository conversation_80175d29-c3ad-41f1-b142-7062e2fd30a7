package com.juicy.common.utils.view

import android.content.Context
import android.util.AttributeSet
import android.util.DisplayMetrics
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatImageView
import com.blankj.utilcode.util.ActivityUtils
import com.juicy.app.R
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.LanguageManager.Companion.instance
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import io.rong.imkit.utils.StatusBarUtil
import kotlin.math.abs

class DraggableView : RelativeLayout {
    private var x = 0f
    private var y = 0f
    var contentView: FrameLayout? = null
        private set
    var camera: AppCompatImageView? = null
        private set
    var redPoint: View? = null
        private set

    private val lastX = 0f
    private val lastY = 0f
    private val originalX = 0f
    private val originalY = 0f
    private var hasMove = false
    private val isFirstTime = true

    constructor(context: Context?) : super(context) {
        initView()
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        initView()
    }

    private fun initView() {
        val view = inflate(context, R.layout.layout_input, this)
        contentView = view.findViewById(R.id.errorUnlocked)
        camera = view.findViewById(R.id.distantLast)
        if (instance!!.isLanguageForce) {
            camera?.setScaleX(-1f)
        }
        redPoint = view.findViewById(R.id.nestedTab)
        if (Cache.instance.userStratResult != null && Cache.instance?.userStratResult?.isCallRearCamera != null &&
            Cache.instance?.userStratResult?.isCallRearCamera == true
        ) {
            if (getBooleanVal(SpKeyPool.OPEN_CAMERA, false)!!) {
                redPoint?.setVisibility(GONE)
            } else {
                redPoint?.setVisibility(VISIBLE)
            }
        } else {
            redPoint?.setVisibility(GONE)
        }
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                x = event.rawX
                y = event.rawY
                hasMove = false
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                val variableX = event.rawX - x
                val variableY = event.rawY - y


                // 只有当移动距离超过阈值时才认为是移动
                if (abs(variableX.toDouble()) > 5 || abs(variableY.toDouble()) > 5) {
                    hasMove = true
                }

                var newX = getX() + variableX
                var newY = getY() + variableY


                // 边界检查
                val statusBarHeight = StatusBarUtil.getStatusBarHeight(context)
                val screenWidth = windowWidth
                val screenHeight = windowHeight


                // 限制左边界
                if (newX < 0) {
                    newX = 0f
                }
                // 限制右边界
                if (newX + width > screenWidth) {
                    newX = (screenWidth - width).toFloat()
                }
                // 限制上边界（考虑状态栏）
                if (newY < statusBarHeight) {
                    newY = statusBarHeight.toFloat()
                }
                // 限制下边界
                if (newY + height > screenHeight) {
                    newY = (screenHeight - height).toFloat()
                }

                setX(newX)
                setY(newY)

                x = event.rawX
                y = event.rawY
                return true
            }

            MotionEvent.ACTION_UP -> {
                if (hasMove) {
                    performClick()
                    parent.requestDisallowInterceptTouchEvent(true)
                    return true
                }
                return performClick()
            }
        }
        return false
    }

    override fun performClick(): Boolean {
        return super.performClick()
    }

    private val windowWidth: Int
        get() {
            val topActivity = ActivityUtils.getTopActivity() as BaseActivity
            val wm =
                topActivity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val dm = DisplayMetrics()
            // 从默认显示器中获取显示参数保存到dm对象中
            wm.defaultDisplay.getMetrics(dm)
            return dm.widthPixels // 返回屏幕的高度数值
        }

    private val windowHeight: Int
        get() {
            val topActivity = ActivityUtils.getTopActivity() as BaseActivity
            val wm =
                topActivity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val dm = DisplayMetrics()
            // 从默认显示器中获取显示参数保存到dm对象中
            wm.defaultDisplay.getMetrics(dm)
            return dm.heightPixels // 返回屏幕的高度数值
        }
}
