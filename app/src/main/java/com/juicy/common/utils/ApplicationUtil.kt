package com.juicy.common.utils

import android.app.Application
import android.content.Context
import android.content.pm.PackageInfo
import android.provider.Settings
import android.text.TextUtils
import java.lang.ref.SoftReference

/**
 * 描述:APP通用工具
 */
object ApplicationUtil {
    private val LOCK = Any()

    /**
     * 用于判断是否需要清理的后台应用---使用过1s即认为是后台应用
     */
    const val TOTAL_TIME_IN_FOREGROUND_THRESHOLD: Long = 1000L

    var mAppInfoList: SoftReference<List<PackageInfo>>? = null

    private var ANDROID_ID: String? = null

    fun getCurrentActivityThread(context: Context?): Any? {
        var currentActivityThread: Any? = null
        try {
            val activityThread = Class.forName("android.app.ActivityThread")
            val m = activityThread.getMethod("currentActivityThread")
            m.isAccessible = true
            currentActivityThread = m.invoke(null)
            if (currentActivityThread == null && context != null) {
                // In older versions of Android (prior to frameworks/base 66a017b63461a22842)
                // the currentActivityThread was built on thread locals, so we'll need to try
                // even harder
                val app = context.applicationContext as Application
                val mLoadedApk = app.javaClass.getField("mLoadedApk")
                mLoadedApk.isAccessible = true
                val apk = mLoadedApk[app]
                val mActivityThreadField = apk!!.javaClass.getDeclaredField("mActivityThread")
                mActivityThreadField.isAccessible = true
                currentActivityThread = mActivityThreadField[apk]
            }
        } catch (ignore: Exception) {
        }
        return currentActivityThread
    }

    @JvmStatic
    fun getAndroidId(context: Context?): String? {
        if (TextUtils.isEmpty(ANDROID_ID)) {
            ANDROID_ID =
                Settings.System.getString(context?.contentResolver, Settings.Secure.ANDROID_ID)
        }
        return ANDROID_ID
    }
}
