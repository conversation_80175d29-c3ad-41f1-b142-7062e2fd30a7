package com.juicy.common.utils

import android.app.Dialog

object DialogShowUtils {
    //显示弹窗，且点击弹窗外不可关闭弹窗
    @JvmStatic
    fun showDialogOutsideUnClose(dialog: Dialog?) {
        //创建弹窗
        dialog?.create()
        //设置弹窗背景为空
        dialog?.window?.setBackgroundDrawable(null)
        // 弹窗外部蒙层不可取消弹窗
        dialog?.setCanceledOnTouchOutside(false)
        //展示
        dialog?.show()
    }

    //显示弹窗，且点击弹窗外可关闭弹窗
    fun showDialogOutsideClose(dialog: Dialog) {
        //创建弹窗
        dialog.create()
        //设置弹窗背景为空
        dialog.window!!.setBackgroundDrawable(null)
        //展示
        dialog.show()
    }
}
