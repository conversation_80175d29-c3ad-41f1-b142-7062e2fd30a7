package com.juicy.common.utils

import java.util.regex.Matcher
import java.util.regex.Pattern

object TextUtils {
    private const val regEx: String = "[\u4e00-\u9fa5]" // 中文范围

    /**
     * 格式化字符串
     * @param string 原始输入字符串
     * @param maxCount 最大字符限制，中文算作2个字符，其他都算1个字符
     * @return
     */
    private fun formatText(string: String?, maxCount: Int): String? {
        var string: String? = string
        if ((string == null || string.length == 0)
            && getChCount(string!!) > maxCount
        ) {
            string = subStrByLen(string, maxCount - 1)
        }
        return string
    }

    /**
     *
     * 截取字符串，超出最大字数截断并显示"..."
     * @param str 原始字符串
     * @param length 最大字数限制（以最大字数限制7个为例，当含中文时，length应设为2*7，不含中文时设为7）
     * @return 处理后的字符串
     */
    @JvmStatic
    fun subStrByLen(str: String?, length: Int): String {
        if (str == null || str.length == 0) {
            return ""
        }
        val chCnt: Int = getStrLen(str)
        // 超出进行截断处理
        if (chCnt > length) {
            var cur: Int = 0
            var cnt: Int = 0
            val sb: StringBuilder = StringBuilder()
            while (cnt <= length && cur < str.length) {
                val nextChar: Char = str.get(cur)
                if (isChCharacter(nextChar.toString())) {
                    cnt += 1
                    //正确的中文算两个字符,想要实现中文也算一个字符,注释更下此计数方式为+1
                    //cnt += 2;
                } else {
                    cnt++
                }
                if (cnt <= length) {
                    sb.append(nextChar)
                } else {
                    return sb.toString() + "..."
                }
                cur++
            }
            return sb.toString() + "..."
        }
        // 未超出直接返回
        return str
    }

    /**
     * 获取字符串中的中文字数
     */
    private fun getChCount(str: String): Int {
        var cnt: Int = 0
        val pattern: Pattern = Pattern.compile(regEx)
        val matcher: Matcher = pattern.matcher(str)
        while (matcher.find()) {
            cnt++
        }
        return cnt
    }

    /**
     * 判断字符是不是中文
     */
    private fun isChCharacter(str: String?): Boolean {
        if (str == null || str.length == 0) {
            return false
        }
        if (str.length > 1) {
            return false
        }
        return Pattern.matches(regEx, str)
    }

    /**
     * 获取字符长度，中文算作2个字符，其他都算1个字符
     */
    @JvmStatic
    fun getStrLen(str: String?): Int {
        if (str == null || str.length == 0) {
            return 0
        }
        return str.length + getChCount(str)
    }
}
