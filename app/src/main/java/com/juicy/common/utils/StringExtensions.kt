package com.juicy.common.utils

object StringExtensions {
    /**
     * 裁剪字符串到指定长度，并在末尾添加省略号
     *
     * @param str   原始字符串
     * @param length 最大长度
     * @return 裁剪后的字符串
     */
    fun truncate(str: String?, length: Int): String? {
        // 检查字符串是否为空或长度小于等于指定长度
        if (str == null || str.length <= length) {
            return str
        }
        // 裁剪字符串并添加省略号
        return str.substring(0, length) + "..."
    }
}
