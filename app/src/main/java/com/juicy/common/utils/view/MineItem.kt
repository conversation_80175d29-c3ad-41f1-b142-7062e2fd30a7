package com.juicy.common.utils.view

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.juicy.app.R
import com.juicy.common.utils.LanguageManager.Companion.instance

class MineItem : RelativeLayout {
    @JvmField
    var name: AppCompatTextView? = null
    @JvmField
    var icon: AppCompatImageView? = null
    @JvmField
    var rightText: AppCompatTextView? = null
    var back: AppCompatImageView? = null
    var line: View? = null

    constructor(context: Context) : super(context) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initView(context)
    }


    private fun initView(context: Context) {
        val view = inflate(context, R.layout.item_tooltip, this)
        name = view.findViewById(R.id.stretchRight)
        icon = view.findViewById(R.id.mediumLast)
        rightText = view.findViewById(R.id.downloadCenter)
        back = view.findViewById(R.id.rigidChecked)
        line = view.findViewById(R.id.stretchLong)
        if (instance!!.isLanguageForce) {
            rightText?.setGravity(Gravity.START)
        }

        val scaleX = if (instance!!.isLanguageForce) -1f else 1f
        back?.setScaleX(scaleX)
    }
}
