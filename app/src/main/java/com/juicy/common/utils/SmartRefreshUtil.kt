package com.juicy.common.utils

import com.scwang.smartrefresh.layout.footer.ClassicsFooter
import com.scwang.smartrefresh.layout.header.ClassicsHeader

object SmartRefreshUtil {
    @JvmStatic
    fun initSmartRefresh() {
        // 设置下拉刷新文本
        ClassicsHeader.REFRESH_HEADER_PULLING =
            LanguageManager.Companion.instance?.getLocalTranslate("Pull_down_to_refresh")
        ClassicsHeader.REFRESH_HEADER_REFRESHING =
            LanguageManager.Companion.instance?.getLocalTranslate("Refreshing")
        ClassicsHeader.REFRESH_HEADER_LOADING =
            LanguageManager.Companion.instance?.getLocalTranslate("Loading")
        ClassicsHeader.REFRESH_HEADER_RELEASE =
            LanguageManager.Companion.instance?.getLocalTranslate("Release_to_refresh")
        ClassicsHeader.REFRESH_HEADER_FINISH =
            LanguageManager.Companion.instance?.getLocalTranslate("Refresh_completed")
        ClassicsHeader.REFRESH_HEADER_FAILED =
            LanguageManager.Companion.instance?.getLocalTranslate("Refresh_failed")
        ClassicsHeader.REFRESH_HEADER_UPDATE =
            LanguageManager.Companion.instance?.getLocalTranslate("Last_update_Md_HHmm")
        ClassicsHeader.REFRESH_HEADER_SECONDARY =
            LanguageManager.Companion.instance?.getLocalTranslate("Release_to_enter_floor")

        // 设置上拉加载文本
        ClassicsFooter.REFRESH_FOOTER_PULLING =
            LanguageManager.Companion.instance?.getLocalTranslate("Pull_up_to_load_more")
        ClassicsFooter.REFRESH_FOOTER_RELEASE =
            LanguageManager.Companion.instance?.getLocalTranslate("Release_to_load_more")
        ClassicsFooter.REFRESH_FOOTER_LOADING =
            LanguageManager.Companion.instance?.getLocalTranslate("Loading")
        ClassicsFooter.REFRESH_FOOTER_REFRESHING =
            LanguageManager.Companion.instance?.getLocalTranslate("Loading")
        ClassicsFooter.REFRESH_FOOTER_FINISH =
            LanguageManager.Companion.instance?.getLocalTranslate("Loading_successful")
        ClassicsFooter.REFRESH_FOOTER_FAILED =
            LanguageManager.Companion.instance?.getLocalTranslate("Load_failed")
        ClassicsFooter.REFRESH_FOOTER_NOTHING =
            LanguageManager.Companion.instance?.getLocalTranslate("No_more_data")
    }
}
