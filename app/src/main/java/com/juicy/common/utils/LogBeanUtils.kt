package com.juicy.common.utils

import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.LanguageUtils
import com.juicy.common.model.bean.LogInfoBean
import com.juicy.common.config.Cache
import java.util.UUID

/**
 * 支付流程日志打点上报
 */
object LogBeanUtils {
    //支付流程事件数组
    //1.事件类型     2.商品id   3.后台订单id  4.当前事物耗时  5.事物间隔总耗时  6.响应结果  7.响应码
    @JvmStatic
    fun createLogData(
        event: String?, shopCode: String?, orderNoCode: String?, durationTime: Long,
        elapsedTime: Long, result: String?, resultCode: Int
    ): LogInfoBean.DataBean {
        //List<LogBean.DataBean> listDataBean = new ArrayList<>();
        //单个事件bean
        val dataBean = LogInfoBean.DataBean()
        //事件类型
        dataBean.event = event
        //商品码
        dataBean.code = shopCode
        //uuid
        //dataBean.setUuid(mUUID);
        //订单号
        dataBean.orderId = orderNoCode
        //当前事物执行耗时
        //dataBean.setDurationTime(durationTime);
        //总耗时(从上一个时间之后,到这个时间之间的时间)
        //dataBean.setElapsedTime(elapsedTime);
        //结果(从Create_order开始，到各自日志点总耗时)
        dataBean.result = result
        //结果状态码
        dataBean.resultCode = resultCode
        //列表中存储所有事件类型内容
        return dataBean
    }

    //通话流程行为事件数组
    //1.通道名称  2.事件  3.详细信息一  4.详细信息二  5.时间戳
    fun createCallLogData(
        channelName: String?,
        action: String?,
        ext: String?,
        ext2: String?,
        tm: Long?
    ): com.juicy.common.model.bean.CallLogInfoBean.DataBean {
        val dataBean = com.juicy.common.model.bean.CallLogInfoBean.DataBean()
        dataBean.channelName = channelName
        dataBean.action = action
        dataBean.ext = ext
        dataBean.ext2 = ext2
        dataBean.tm = tm
        return dataBean
    }

    //完整的通话流程日志Bean数据
    fun submitCallLogData(callLogListBean: List<com.juicy.common.model.bean.CallLogInfoBean.DataBean>?): List<com.juicy.common.model.bean.CallLogInfoBean> {
        val dataBeanList: MutableList<com.juicy.common.model.bean.CallLogInfoBean> = ArrayList()
        val dataBean = com.juicy.common.model.bean.CallLogInfoBean()
        dataBean.typeName = "event"
        dataBean.subType = "livecall"
        dataBean.behavior = "event"
        dataBean.data = callLogListBean
        dataBeanList.add(dataBean)
        return dataBeanList
    }

    //完整的支付流程日志Bean数据
    fun submitLogData(listDataBean: List<LogInfoBean.DataBean>): List<LogInfoBean> {
        val logBeanList: MutableList<LogInfoBean> = ArrayList()
        for (i in listDataBean.indices) {
            //单次时间提交,设置同一个uuid
            listDataBean[i].uuid = UUID.randomUUID().toString()
        }
        val logBean = LogInfoBean()
        logBean.data = listDataBean //上报日志数据数组
        logBean.log_type = "event" //日志属性
        logBean.subtype = "purchase_detail" //日志类型
        logBean.behavior = "event" //日志属性
        //if(null!=CachePool.getInstance().IsNull(Constants.UTM_SOURCE)){
        //    logBean.setUtm_source(CachePool.getInstance().IsNull(Constants.UTM_SOURCE));//af返回的归因信息
        //}
        logBean.utm_source = "" //af返回的归因信息
        logBean.android_id = DeviceUtils.getAndroidID() //android设备id
        if (null != Cache.instance?.userInfo && null != Cache.instance?.userInfo?.country) {
            logBean.user_id = Cache.instance?.userInfo?.userId //用户id
        }
        logBean.pkg = AppUtils.getAppPackageName() //包名
        logBean.ver = AppUtils.getAppVersionCode().toString() //版本号
        logBean.platform = "30" //sdk ver
        logBean.model = DeviceUtils.getModel() //手机型号
        logBean.lan_id = UUID.randomUUID().toString() //客户端启动唯一标识,用于区分是否同一次启动
        logBean.sec_id = 0 //日志次数 0递增
        logBean.sys_lan = LanguageUtils.getSystemLanguage().toString() //手机系统语言
        if (null != Cache.instance?.userInfo && null != Cache.instance?.userInfo?.country) {
            logBean.country = Cache.instance?.userInfo?.country //用户国家
        }
        logBean.setIs_in_bg(!AppUtils.isAppForeground()) //应用是否位于后台
        if (null != Cache.instance?.userInfo && null != Cache.instance?.userInfo?.club) {
            logBean.setIs_in_bg(Cache.instance?.userInfo?.club?:false) //是否是主播
        }
        logBean.client_type = "common"
        logBean.bizver = ""
        logBean.tm = System.currentTimeMillis() //当前时间戳
        logBeanList.add(logBean)
        return logBeanList
    }
}
