package com.juicy.common.utils.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.Switch

class MySwitch : Switch {
    var listener: OnClickListener? = null

    constructor(context: Context?) : super(context)

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_UP) {
            if (listener != null) {
                listener!!.onClick(this)
            }
        }
        return true
    }

    override fun setOnClickListener(listener: OnClickListener?) {
        this.listener = listener
    }
}
