package com.juicy.common.utils

import android.app.Activity
import android.content.Context
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.juicy.app.R

object ImageLoadingUtils {
    @JvmStatic
    fun loadCircleImage(context: Context?, url: String?, imageView: ImageView) {
        loadImage(context, url, R.mipmap.img_empty_round, imageView, true)
    }

    @JvmStatic
    fun loadImage(context: Context?, url: String?, imageView: ImageView) {
        loadImage(context, url, R.drawable.img_empty_big_full_pearl, imageView, false)
    }

    @JvmStatic
    fun loadImage(
        context: Context?,
        url: String?,
        placeholderId: Int,
        imageView: ImageView,
        isCircleCrop: Boolean
    ) {
        var url = url
        if (context == null || (isActivityContext(context))) {
            return
        }
        if (url == null) {
            url = ""
        }
        val requestOptions = RequestOptions()
            .placeholder(placeholderId) // 设置占位图
            .error(placeholderId)
        // 设置错误图


        val lod = Glide.with(context).load(url)
        if (isCircleCrop) {
            requestOptions.transform(CircleCrop())
        }
        //渐变动画
        lod.apply(requestOptions).into(imageView)
    }

    fun isActivityContext(context: Context?): Boolean {
        if (context is Activity) {
            val activity = context
            // 判断 Activity 是否已经销毁
            return activity.isFinishing || activity.isDestroyed
        }
        return false // 不是 Activity
    }
}
