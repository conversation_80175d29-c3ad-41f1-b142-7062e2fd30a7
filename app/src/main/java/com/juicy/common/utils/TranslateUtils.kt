package com.juicy.common.utils

import android.content.Context
import android.content.SharedPreferences
import android.text.TextUtils
import android.util.Log
import android.util.LruCache
import com.google.cloud.translate.Translate
import com.google.cloud.translate.TranslateOptions
import com.google.cloud.translate.Translation
import com.google.gson.JsonArray
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import com.juicy.app.JuicyApplication
import com.juicy.app.MyBuildConfig
import com.juicy.common.config.SpKeyPool
import okhttp3.HttpUrl
import okhttp3.HttpUrl.Builder
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import org.json.JSONArray
import org.json.JSONException
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.util.concurrent.TimeUnit

/**
 * 微软翻译
 */
object TranslateUtils {
    private const val TAG: String = "TranslateUtil"
    private const val TRANSLATE_FILE: String = "translate_file"
    private const val CACHE_CODE_FLAG: String = "[:|;|;|:]"
    private val lruCache: LruCache<String, String> = LruCache(100)

    private val sharedPreferences: SharedPreferences
        get() {
            return JuicyApplication.juicyApplication!!.getSharedPreferences(
                TRANSLATE_FILE,
                Context.MODE_PRIVATE
            )
        }

    @JvmStatic
    fun initCache() {
        parseCacheJson()
    }

    fun md5(info: String): String {
        try {
            val md5: MessageDigest = MessageDigest.getInstance("MD5")
            md5.update(info.toByteArray(StandardCharsets.UTF_8))
            val encryption: ByteArray = md5.digest()

            val strBuf: StringBuffer = StringBuffer()
            for (i in encryption.indices) {
                if (Integer.toHexString(0xff and encryption.get(i).toInt()).length == 1) {
                    strBuf.append("0")
                        .append(Integer.toHexString(0xff and encryption.get(i).toInt()))
                } else {
                    strBuf.append(Integer.toHexString(0xff and encryption.get(i).toInt()))
                }
            }

            return strBuf.toString()
        } catch (e: Exception) {
            return ""
        }
    }

    // 子线程运行
    @JvmStatic
    @Synchronized
    fun textTrans(originalText: String): String? {
        if (MyBuildConfig.DEBUG && ThreadUtil.isInMainThread) {
            throw Error("The translation must runs in the work thread")
        }
        try {
            var key: String = md5(originalText)
            if (TextUtils.isEmpty(key)) {
                key =
                    String(originalText.toByteArray(StandardCharsets.UTF_8), StandardCharsets.UTF_8)
            }
            //从缓存中提取
            var cacheText: String? = lruCache.get(key)
            if (null == cacheText) cacheText = ""
            val flagCode: String? = getLanguageCode(false) //缓存翻译内容 语言code前缀标记 区分是否为当前语言翻译
            if (cacheText.contains(CACHE_CODE_FLAG)) {
                val code: String = cacheText.substring(0, cacheText.indexOf(CACHE_CODE_FLAG))
                if (!code.isEmpty() && code == flagCode) {
                    cacheText =
                        cacheText.substring(cacheText.indexOf(CACHE_CODE_FLAG) + CACHE_CODE_FLAG.length)
                    if (cacheText == "null") cacheText = ""
                } else {
                    cacheText = ""
                }
            }
            if (!TextUtils.isEmpty(cacheText)) {
                Log.d(TAG, "getCache key:" + key + ", value: " + cacheText)
                return cacheText
            }
            // 翻译并存入缓存
            if (SpSaveUtil.getIntValue(SpKeyPool.TRANSLATE_TYPE_KEY, 1) == 2) {
                cacheText = gpTrans(
                    SpSaveUtil.getStringValue(SpKeyPool.TRANSLATE_TYPE_GOOGLE, ""),
                    originalText
                )
            } else {
                cacheText = mfTrans(
                    SpSaveUtil.getStringValue(SpKeyPool.TRANSLATE_TYPE_MICROSOFT, "")!!,
                    originalText
                )
            }
            //            if(cacheText != null && !originalText.equals(cacheText)){
            lruCache.put(key, flagCode + CACHE_CODE_FLAG + cacheText)
            saveCacheFile()

            //            }
            return cacheText
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    /**
     * originalText:原始文本
     * apiKey : 谷歌翻译key
     */
    // 子线程运行
    fun gpTrans(apiKey: String?, originalText: String?): String? {
        var result: String? = null
        try {
            val targetLanguage: String? = getLanguageCode(true) //获取用户当前手机语言简称
            val translate: Translate = TranslateOptions.newBuilder()
                .setApiKey(apiKey) //翻译key
                .build()
                .getService()
            val translation: Translation =
                translate.translate(
                    originalText,
                    Translate.TranslateOption.sourceLanguage(""),
                    Translate.TranslateOption.targetLanguage(targetLanguage),
                    Translate.TranslateOption.model("nmt")
                )
            result = translation.getTranslatedText() //翻译结果
        } catch (e: Throwable) {
            e.printStackTrace()
        }
        return result
    }

    /**
     * originalText:原始文本
     * apiKey : 微软翻译key
     */
    // 子线程运行
    private fun mfTrans(apiKey: String, originalText: String): String? {
        var result: String? = null
        val code: String? = getLanguageCode(false)
        try {
            val url: HttpUrl = Builder()
                .scheme("https")
                .host("api.cognitive.microsofttranslator.com")
                .addPathSegment("/translate")
                .addQueryParameter("api-version", "3.0")
                .addQueryParameter("to", code)
                .build()

            // Instantiates the OkHttpClient.
            /***
             * import okhttp3.HttpUrl.Builder.addPathSegment
             * import okhttp3.HttpUrl.Builder.addQueryParameter
             * import okhttp3.HttpUrl.Builder.build
             * import okhttp3.HttpUrl.Builder.host
             * import okhttp3.HttpUrl.Builder.scheme
             * import okhttp3.MediaType
             * import okhttp3.MultipartBody.Builder.build
             * import okhttp3.OkHttpClient
             * import okhttp3.Request
             * import okhttp3.Request.Builder.addHeader
             * import okhttp3.Request.Builder.build
             * import okhttp3.Request.Builder.post
             * import okhttp3.Request.Builder.url
             */

            val client: OkHttpClient = OkHttpClient()
            val content: String = "[{" + "\"Text\":" + "\"" + originalText + "\"" + "}]"

            val mediaType: MediaType? = "application/json".toMediaTypeOrNull()
            val body: RequestBody = RequestBody.create(mediaType, content)
            val request: Request = Request.Builder().url(url).post(body)
                .addHeader("Ocp-Apim-Subscription-Key", apiKey)
                .addHeader("Content-type", "application/json")
                .build()
            val response: Response = client.newCall(request).execute()
            val parser: JsonParser = JsonParser()
            val json: JsonElement = parser.parse(response.body?.string())
            result = getResult(JSONArray(json.toString()))
        } catch (e: Throwable) {
            e.printStackTrace()
        }
        return result
    }

    fun getLanguageCode(isGP: Boolean): String? {
        return LanguageManager.Companion.instance?.translateLanguageCode

        //        String locale = LanguageTool.getLocaleLanguageCodeFunc(MyApplication.getApplication());
//        // 国内直翻繁体
//        if ("zh".equals(locale)) {
//            String country = LanguageTool.getLocaleLanguageCountryFunc(MyApplication.getApplication());
//            if (!country.isEmpty() && !"CN".equals(country)) {
//                locale = isGP ? "zh-TW" : "zh-Hant";
//            }
//        }
////        // 当前程序语言为默认英语 但系统语言不为英语 转换为翻译系统语言
////        if (locale.equals("en") && !sysLocale.equals(locale)) {
////            locale = sysLocale;
////        }
//        return locale;
    }

    private fun getResult(jsonArray: JSONArray): String? {
        try {
            jsonArray.getJSONObject(0)
            return jsonArray.getJSONObject(0).optJSONArray("translations").getJSONObject(0)
                .optString("text")
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return null
    }

    /**
     * 落地缓存
     */
    private var needCache: Boolean = false

    private fun saveCacheFile() {
        if (needCache) {
            return
        }
        needCache = true
        ThreadUtil.executeOnSingleThreadDelay(object : Runnable {
            override fun run() {
                val fileContent: String? = cacheJson
                val editor: SharedPreferences.Editor = sharedPreferences.edit()
                editor.putString(TRANSLATE_FILE, fileContent)
                // 子线程直接commit
                editor.commit()
                needCache = false
                Log.d(TAG, "fileContent: " + fileContent)
            }
        }, 10, TimeUnit.SECONDS)
    }

    val isAutoTranslate: Boolean
        /**
         * 是否自动翻译
         */
        get() {
            return SpSaveUtil.getBooleanVal(SpKeyPool.IS_AUTO_TRANSLATE, false)!!
        }

    private val cacheJson: String?
        get() {
            try {
                val map: Map<String, String> =
                    lruCache.snapshot()
                val array: JsonArray = JsonArray()
                val it: Iterator<Map.Entry<String, String>> =
                    map.entries.iterator()
                while (it.hasNext()) {
                    val entry: Map.Entry<String, String> =
                        it.next()
                    val jsonObject: JsonObject = JsonObject()
                    jsonObject.addProperty("k", entry.key)
                    jsonObject.addProperty("v", entry.value)
                    array.add(jsonObject)
                }
                return array.toString()
            } catch (e: Exception) {
            }
            return null
        }

    private fun parseCacheJson() {
        try {
            val cache: String =
                sharedPreferences.getString(TRANSLATE_FILE, "")!!
            if (!TextUtils.isEmpty(cache)) {
                val array: JSONArray = JSONArray(cache)
                for (i in 0 until array.length()) {
                    val key: String = array.getJSONObject(i).getString("k")
                    val value: String = array.getJSONObject(i).getString("v")
                    lruCache.put(key, value)
                }
            }
        } catch (e: Exception) {
        }
    }
}
