package com.juicy.common.utils

import android.content.Context
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.LinearGradient
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PixelFormat
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

object MyImageUtil {
    private const val TAG = "ImageUtil"

    /**
     * 缩放图片
     *
     * @param bitmap 源图片
     * @param w      新图片宽
     * @param h      新图片高
     * @return 新图片
     */
    fun zoomBitmap(bitmap: Bitmap?, w: Int, h: Int): Bitmap? {
        if (bitmap == null) {
            return null
        }
        val width = bitmap.width
        val height = bitmap.height
        val matrix = Matrix()
        val scaleWidht = (w.toFloat() / width)
        val scaleHeight = (h.toFloat() / height)
        matrix.postScale(scaleWidht, scaleHeight)
        val newbmp = BitmapUtil.createBitmap(bitmap, 0, 0, width, height, matrix, true)
        // bitmap.recycle();
        return newbmp
    }


    /**
     * 缩放图片
     *
     * @param drawable
     * @param w
     * @param h
     * @return
     */
    // public static Bitmap zoomBitmap(Drawable drawable, int w, int h) {
    // Bitmap bitmap = drawableToBitmap(drawable);
    // return zoomBitmap(bitmap, w, h);
    // }
    /**
     * 将Drawable转化为Bitmap
     *
     * @param paramDrawable [Drawable]
     * @return [Bitmap]
     */
    @JvmStatic
    fun drawableToBitmap(paramDrawable: Drawable?): Bitmap? {
        if (paramDrawable == null) {
            return null
        }
        val bitmap: Bitmap
        if (paramDrawable is BitmapDrawable) {
            val bitmapDrawable = paramDrawable
            if (bitmapDrawable.bitmap != null) return bitmapDrawable.bitmap
        }
        if (paramDrawable.intrinsicWidth <= 0 || paramDrawable.intrinsicHeight <= 0) {
            val config = if (paramDrawable.opacity != PixelFormat.OPAQUE) {
                Bitmap.Config.ARGB_4444
            } else {
                Bitmap.Config.RGB_565
            }
            bitmap = Bitmap.createBitmap(1, 1, config)
            val canvas1 = Canvas(bitmap)
            paramDrawable.setBounds(0, 0, canvas1.width, canvas1.height)
            paramDrawable.draw(canvas1)
            return bitmap
        }
        try {
            val i = paramDrawable.intrinsicWidth
            val j = paramDrawable.intrinsicHeight
            val config = if (paramDrawable.opacity != PixelFormat.OPAQUE) {
                Bitmap.Config.ARGB_4444
            } else {
                Bitmap.Config.RGB_565
            }
            bitmap = Bitmap.createBitmap(i, j, config)
        } catch (outOfMemoryError: OutOfMemoryError) {
            outOfMemoryError.printStackTrace()
            return null
        }
        val canvas = Canvas(bitmap)
        paramDrawable.setBounds(0, 0, canvas.width, canvas.height)
        paramDrawable.draw(canvas)
        return bitmap
    }

    /**
     * 将Drawable转化为Bitmap
     *
     * @param drawable [Drawable]
     * @return [Bitmap]
     */
    fun drawableToBitmap(drawable: Drawable?, config: Bitmap.Config): Bitmap? {
        if (drawable == null) {
            return null
        }
        val width = drawable.intrinsicWidth
        val height = drawable.intrinsicHeight
        val bitmap = BitmapUtil.createBitmap(width, height, config)
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, width, height)
        drawable.draw(canvas)
        return bitmap
    }

    fun colorToBitmap(color: Int, width: Int, height: Int): Bitmap? {
        return drawableToBitmap(object : ColorDrawable(color) {
            override fun getIntrinsicWidth(): Int {
                return width
            }

            override fun getIntrinsicHeight(): Int {
                return height
            }
        })
    }

    /**
     * 将Bitmap转为Drawable
     */
    fun bitmapToDrawable(bitmap: Bitmap?): Drawable? {
        if (bitmap == null) {
            return null
        }
        val bitmapDrawable = BitmapDrawable(bitmap)
        return bitmapDrawable
    }

    /**
     * 获得圆角图片的方法
     *
     * @param bitmap  源图片
     * @param roundPx 圆角半径
     * @return 新图片
     */
    fun getRoundedCornerBitmap(bitmap: Bitmap?, roundPx: Float): Bitmap? {
        if (bitmap == null) {
            return null
        }
        val output = BitmapUtil.createBitmap(
            bitmap.width, bitmap.height,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(output)
        val color = -0xbdbdbe
        val paint = Paint()
        val rect = Rect(0, 0, bitmap.width, bitmap.height)
        val rectF = RectF(rect)

        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        paint.color = color
        canvas.drawRoundRect(rectF, roundPx, roundPx, paint)
        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
        canvas.drawBitmap(bitmap, rect, rect, paint)
        return output
    }

    /**
     * 获得圆角图片的方法
     *
     * @param bitmap 源图片
     * @param radii  圆角半径
     * @return 新图片
     */
    fun getRoundedCornerBitmap(bitmap: Bitmap?, radii: FloatArray?): Bitmap? {
        if (bitmap == null || radii == null) {
            return null
        }
        val output = BitmapUtil.createBitmap(
            bitmap.width, bitmap.height,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(output)
        val color = -0xbdbdbe
        val paint = Paint()
        val rect = Rect(0, 0, bitmap.width, bitmap.height)
        val rectF = RectF(rect)
        val path = Path()
        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        paint.color = color
        path.addRoundRect(rectF, radii, Path.Direction.CCW)
        canvas.drawPath(path, paint)
        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
        canvas.drawBitmap(bitmap, rect, rect, paint)
        return output
    }

    /**
     * 获得圆形的方法
     *
     * @param bitmap  源图片
     * @param roundPx 圆角半径
     * @return 新图片
     */
    fun getRoundedBitmap(bitmap: Bitmap?, roundPx: Float): Bitmap? {
        if (bitmap == null) {
            return null
        }
        val output = BitmapUtil.createBitmap(
            bitmap.width, bitmap.height,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(output)
        val color = -0xbdbdbe
        val paint = Paint()
        val rect = Rect(0, 0, bitmap.width, bitmap.height)
        val rectF = RectF(rect)
        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        paint.color = color
        canvas.drawOval(rectF, paint)
        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
        canvas.drawBitmap(bitmap, rect, rect, paint)
        return output
    }

    /**
     * 获得带边框圆形的方法
     *
     * @param bitmap     源图片
     * @param roundPx    圆角半径
     * @param boderPx    背景边框宽度
     * @param boderColor 背景边框颜色
     * @return 新图片
     */
    fun getRoundedBitmapWithBorder(
        bitmap: Bitmap?, roundPx: Float, boderPx: Int,
        boderColor: Int
    ): Bitmap? {
        val tmpBitmap = getRoundedBitmap(bitmap, roundPx) ?: return null

        val output = BitmapUtil.createBitmap(
            tmpBitmap.width + boderPx * 2,
            tmpBitmap.height + boderPx * 2, Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(output)
        val borderpaint = Paint()
        val borderrect = Rect(
            0, 0, tmpBitmap.width + boderPx * 2,
            tmpBitmap.height + boderPx * 2
        )
        val borderrectF = RectF(borderrect)
        borderpaint.isAntiAlias = true
        borderpaint.color = boderColor
        // drawOval方法用于绘制一个包含borderrectF的圆
        canvas.drawOval(borderrectF, borderpaint)

        val paint = Paint()
        val src = Rect(0, 0, tmpBitmap.width, tmpBitmap.height)
        val dst = Rect(
            boderPx, boderPx, tmpBitmap.width + boderPx,
            tmpBitmap.height + boderPx
        )
        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        // src参数是图片原来的大小，dst参数是 绘画该图片需显示多大
        canvas.drawBitmap(tmpBitmap, src, dst, paint)
        return output
    }

    /**
     * 获得带倒影的图片方法
     *
     * @param bitmap 源图片
     * @return 带倒影图片
     */
    fun createReflectionImageWithOrigin(bitmap: Bitmap): Bitmap {
        val reflectionGap = 4
        val width = bitmap.width
        val height = bitmap.height
        val matrix = Matrix()
        matrix.preScale(1f, -1f)
        val reflectionImage = BitmapUtil.createBitmap(
            bitmap, 0, height / 2, width,
            height / 2, matrix, false
        )
        val bitmapWithReflection = BitmapUtil.createBitmap(
            width, (height + height / 2),
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmapWithReflection)
        canvas.drawBitmap(bitmap, 0f, 0f, null)
        val deafalutPaint = Paint()
        canvas.drawRect(
            0f,
            height.toFloat(),
            width.toFloat(),
            (height + reflectionGap).toFloat(),
            deafalutPaint
        )
        canvas.drawBitmap(reflectionImage, 0f, (height + reflectionGap).toFloat(), null)
        val paint = Paint()
        val shader = LinearGradient(
            0f, bitmap.height.toFloat(), 0f,
            (bitmapWithReflection.height + reflectionGap).toFloat(), 0x70ffffff, 0x00ffffff,
            Shader.TileMode.CLAMP
        )
        paint.setShader(shader)
        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.DST_IN))
        canvas.drawRect(
            0f,
            height.toFloat(),
            width.toFloat(),
            (bitmapWithReflection.height + reflectionGap).toFloat(),
            paint
        )
        return bitmapWithReflection
    }

    /**
     * 图片压缩--判断图片尺寸是否小于指定尺寸,如果大于则进行压缩，否则不压缩
     *
     * @param path     图片路径
     * @param savePath 保存路径
     * @param format   格式
     * @return
     */
    fun encodeImage(path: String?, savePath: String, format: Bitmap.CompressFormat): String {
        try {
            // 压缩图片
            val options = BitmapFactory.Options()
            options.inJustDecodeBounds = true
            var bgimage = BitmapFactory.decodeFile(path, options)
            var inSampleSize = 1
            inSampleSize = if (options.outHeight > options.outWidth) {
                Math.round(options.outHeight / 1024f)
            } else {
                Math.round(options.outWidth / 1024f)
            }
            inSampleSize = if (inSampleSize == 0) 1 else inSampleSize
            if (options.outWidth / inSampleSize > 1024 || options.outHeight / inSampleSize > 1024) {
                inSampleSize = inSampleSize + 1
            }
            if (inSampleSize > 1) {
                options.inSampleSize = inSampleSize
                options.inJustDecodeBounds = false
                bgimage = BitmapFactory.decodeFile(path, options)
                if (bgimage == null) {
                    return ""
                }
                val baos = ByteArrayOutputStream()
                // 得到输出流
                bgimage.compress(format, 100, baos)
                // 转输入流
                val isBm: InputStream = ByteArrayInputStream(baos.toByteArray())
                bgimage.recycle()
                bgimage = null
                val sendFilePath = File(savePath)
                writeToFile(sendFilePath, isBm)
            } else {
                if (bgimage != null && !bgimage.isRecycled) {
                    bgimage.recycle()
                    bgimage = null
                }
            }
            return savePath
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return savePath
    }

    /**
     * 写入图片
     *
     * @param saveFile
     * @param in
     * @return
     */
    fun writeToFile(saveFile: File?, `in`: InputStream?): Boolean {
        var fout: FileOutputStream? = null
        var success = true
        try {
            fout = FileOutputStream(saveFile)
            var len = -1
            val buff = ByteArray(4096)
            while ((`in`!!.read(buff).also { len = it }) != -1) {
                fout.write(buff, 0, len)
            }
        } catch (e: Exception) {
            success = false
            e.printStackTrace()
        } finally {
            if (fout != null) {
                try {
                    fout.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
            if (`in` != null) {
                try {
                    `in`.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return success
    }

    /**
     * 读取文件，如果图片出错，返回null
     */
    fun readDialogBgBitmap(pathName: String): Bitmap? {
        var bitmap: Bitmap? = null
        if (TextUtils.isEmpty(pathName)) {
            return null
        } else {
            if (MyFileTool.isExist(pathName)) {
                var `is`: InputStream? = null
                try {
                    try {
                        `is` = MyFileTool.getFileInputStream(pathName)
                        bitmap = BitmapUtil.decodeStream(`is`)
                    } finally {
                        `is`?.close()
                    }
                } catch (e: OutOfMemoryError) {
                    System.gc()
                    try {
                        try {
                            `is` = MyFileTool.getFileInputStream(pathName)
                            bitmap = BitmapUtil.decodeStream(`is`)
                        } finally {
                            `is`?.close()
                        }
                    } catch (e1: OutOfMemoryError) {
                    } catch (e3: Exception) {
                    }
                } catch (e2: Exception) {
                }
            }
        }
        return bitmap
    }

    private fun BOUND(x: Float, mn: Float, mx: Float): Float {
        return (if ((x) < (mn)) (mn) else (if ((x) > (mx)) (mx) else (x)))
    }

    // local function used in HLStoRGB
    private fun Value(n1: Float, n2: Float, hue: Float): Float {
        var hue = hue
        if (hue > 360.0) {
            hue -= 360.0.toFloat()
        } else if (hue < 0.0) {
            hue += 360.0.toFloat()
        }
        return if (hue < 60.0) {
            (n1 + (n2 - n1) * hue / 60.0f)
        } else if (hue < 180.0) {
            n2
        } else if (hue < 240.0) {
            (n1 + (n2 - n1) * (240.0f - hue) / 60.0f)
        } else {
            n1
        }
    }

    /**
     * 这个是对一个颜色算出灰阶
     *
     * @param r
     * @param g
     * @param b
     * @return 灰度
     */
    private fun RGB2Gray(r: Int, g: Int, b: Int): Int {
        var gray = 0
        gray = (((b) * 117 + (g) * 601 + (r) * 306) shr 10)

        return gray
    }

    /**
     * 判断是否使用亮色的文字
     *
     * @param
     * @return true, 使用亮色文字;flase,使用暗色文字.
     */
    fun IsUseLightFont(color: Int): Boolean {
        var useLightFont = false
        val gray = RGB2Gray(Color.red(color), Color.green(color), Color.blue(color))

        if (gray <= 150) {
            useLightFont = true
        }

        return useLightFont
    }

    /**
     * 计算出新图的亮度偏移值
     *
     * @return
     */
    private fun CalPowl(TargetL: Float): Float {
        var powl = 0.0
        if (TargetL - 50.0f > 0) {
            powl = (TargetL - 50.0f).pow(0.88)
        } else {
            powl = -(50.0f - TargetL).pow(0.88)
        }
        return powl.toFloat()
    }

    /**
     * 改变图片像素的颜色
     *
     * @param Source 目标颜色值 rgb,rgb各占一个int空间
     * @return
     */
    private fun CalMergeColor(
        Source: IntArray, Powl: Float, SelectH: Float, SelectS: Float,
        SelectL: Float
    ): IntArray {
        var DColor = IntArray(3)

        var hls = FloatArray(3)
        hls = RGBtoHLS(Source[0], Source[1], Source[2])
        // float h = hls[0];
        val l = hls[1]
        val s = hls[2]
        if (l >= 100.0f || l <= 0.0f) {
            DColor = Source
            return DColor
        }

        val DL = l
        val DS = s
        val DH = SelectH

        DColor = HLStoRGB(DH, DL, DS)

        return DColor
    }

    /**
     * 改变图片像素的颜色
     *
     * @param Source 颜色值
     * @param L      要改变的亮度值增量 （0.0f - 1。0f）
     * @return 改变后的颜色值
     */
    fun ChangeColorLight(Source: Int, L: Float): Int {
        var rgb = IntArray(3)
        rgb[0] = Color.red(Source)
        rgb[1] = Color.green(Source)
        rgb[2] = Color.blue(Source)
        val alpha = Color.alpha(Source)
        var hls = FloatArray(3)

        hls = RGBtoHLS(rgb[0], rgb[1], rgb[2])
        val h = hls[0]
        var l = hls[1]
        val s = hls[2]
        l += L
        if (l > 1.0f) {
            l = 1.0f
        } else if (l < 0.0f) {
            l = 0.0f
        }

        rgb = HLStoRGB(h, l, s)
        val DColor = Color.argb(alpha, rgb[0], rgb[1], rgb[2])
        return DColor
    }

    /**
     * 改变图片像素的颜色
     *
     * @param Source 颜色值
     * @return 改变后的颜色值
     */
    fun ChangeColorLightByPercent(Source: Int, L: Int): Int {
        var rgb = IntArray(3)
        rgb[0] = Color.red(Source)
        rgb[1] = Color.green(Source)
        rgb[2] = Color.blue(Source)
        val alpha = Color.alpha(Source)
        var hls = FloatArray(3)

        hls = RGBtoHLS(rgb[0], rgb[1], rgb[2])
        val h = hls[0]
        var l = hls[1]
        val s = hls[2]
        l += l * L / 100f
        if (l > 1.0f) {
            l = 1.0f
        } else if (l < 0.0f) {
            l = 0.0f
        }

        rgb = HLStoRGB(h, l, s)
        val DColor = Color.argb(alpha, rgb[0], rgb[1], rgb[2])
        return DColor
    }

    /**
     * 改变图片像素的颜色
     */
    fun changeImageColorWithAlpha(bitmap: Bitmap?, targetColor: Int, alpha: Float): Bitmap? {
        if (bitmap == null) {
            return null
        }

        val width = bitmap.width
        val height = bitmap.height
        val bmp = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val oldPx = IntArray(width * height) //用来存储旧的色素点的数组
        val newPx = IntArray(width * height) //用来存储新的像素点的数组
        var color: Int //用来存储原来颜色值
        var r: Int
        var g: Int
        var b: Int
        var a: Int //存储颜色的四个分量：红，绿，蓝，透明度

        bitmap.getPixels(oldPx, 0, width, 0, 0, width, height)
        for (x in 0 until width) {
            for (y in 0 until height) {
                val i = y * width + x
                color = oldPx[i]

                a = (Color.alpha(color) * alpha).toInt()
                r = Color.red(targetColor)
                g = Color.green(targetColor)
                b = Color.blue(targetColor)

                newPx[i] = Color.argb(a, r, g, b)
            }
        }
        bmp.setPixels(newPx, 0, width, 0, 0, width, height)
        return bmp
    }

    /**
     * @param h
     * @param l
     * @param s
     * @return
     */
    private fun HLStoRGB(h: Float, l: Float, s: Float): IntArray {
        val rgb = IntArray(3)
        val m1: Float
        val m2 = if (l <= 0.5) {
            l * (1.0f + s)
        } else {
            l + s - l * s
        }
        m1 = 2.0f * l - m2
        val R = Value(m1, m2, h + 120.0f)
        val G = Value(m1, m2, h)
        val B = Value(m1, m2, h - 120.0f)
        val iR = (R * 255.0).toInt()
        val iG = (G * 255.0).toInt()
        val iB = (B * 255.0).toInt()
        rgb[0] = BOUND(iR.toFloat(), 0f, 255f).toInt()
        rgb[1] = BOUND(iG.toFloat(), 0f, 255f).toInt()
        rgb[2] = BOUND(iB.toFloat(), 0f, 255f).toInt()
        return rgb
    }

    /**
     * @param r
     * @param g
     * @param b
     * @return
     */
    private fun RGBtoHLS(r: Int, g: Int, b: Int): FloatArray {
        val hls = FloatArray(3)
        val mx: Float
        val mn: Float
        val delta: Float
        val R = (r / 255.0).toFloat()
        val G = (g / 255.0).toFloat()
        val B = (b / 255.0).toFloat()
        mx = max(R.toDouble(), max(G.toDouble(), B.toDouble())).toFloat()
        mn = min(R.toDouble(), min(G.toDouble(), B.toDouble())).toFloat()
        hls[1] = ((mx + mn) / 2.0f).toFloat()
        if (mx == mn) {
            hls[2] = 0.0f
            hls[0] = 0.0f // undefined!
        } else {
            delta = (mx - mn).toFloat()
            if (hls[1] < 0.5) {
                hls[2] = (delta / (mx + mn)).toFloat()
            } else {
                hls[2] = (delta / (2.0f - mx - mn)).toFloat()
            }
            if (R == mx) {
                hls[0] = ((G - B) / delta).toFloat()
            } else if (G == mx) {
                hls[0] = (2.0f + (B - R) / delta).toFloat()
            } else if (B == mx) {
                hls[0] = (4.0f + (R - G) / delta).toFloat()
            }
            hls[0] *= 60.0.toFloat()
            if (hls[0] < 0.0) {
                hls[0] += 360.0.toFloat()
            } else if (hls[0] > 360.0) {
                hls[0] -= 360.0.toFloat()
            }
        }
        return hls
    }

    /**
     * 裁剪图片
     *
     * @param bitmap
     * @return
     */
    fun cropBitmap(bitmap: Bitmap?, width: Int, height: Int): Bitmap? {
        if (bitmap == null || bitmap.isRecycled) {
            return null
        }
        val newBitmap = BitmapUtil.createBitmap(bitmap, 0, 0, width, height, null, true)
        return newBitmap
    }

    /**
     * 从右边开始截取百分百为p的图片,并缩放
     *
     * @param bitmap
     * @param p
     * @param sx
     * @param sy
     * @return
     */
    fun cropBitmapFromRight(bitmap: Bitmap?, p: Float, sx: Float, sy: Float): Bitmap? {
        if (bitmap == null || bitmap.isRecycled) {
            return null
        }
        val sourceW = bitmap.width
        val sourceH = bitmap.height

        val targetW = (sourceW * p).toInt()

        val x = sourceW - targetW

        var m: Matrix? = null
        if (sourceW * p < 2 || targetW * p < 2) {
            m = null
        } else {
            m = Matrix()
            m.postScale(sx, sy)
        }

        val newBitmap = BitmapUtil.createBitmap(bitmap, x, 0, targetW, sourceH, m, true)
        return newBitmap
    }

    /**
     * 从底部开始截取百分百为p的图片,并缩放
     *
     * @param bitmap
     * @param p
     * @param sx
     * @param sy
     * @return
     */
    fun cropBitmapFromBottom(bitmap: Bitmap?, p: Float, sx: Float, sy: Float): Bitmap? {
        if (bitmap == null || bitmap.isRecycled) {
            return null
        }
        val sourceW = bitmap.width
        val sourceH = bitmap.height

        val targetH = (sourceH * p).toInt()

        val y = sourceH - targetH

        var m: Matrix? = null
        if (sourceW * p < 2 || targetH * p < 2) {
            m = null
        } else {
            m = Matrix()
            m.postScale(sx, sy)
        }

        val newBitmap = BitmapUtil.createBitmap(bitmap, 0, y, sourceW, targetH, m, true)
        return newBitmap
    }

    fun cropCenterBitmap(bitmap: Bitmap?, cropWidth: Int): Bitmap? {
        var cropWidth = cropWidth
        if (bitmap == null || bitmap.isRecycled) {
            return null
        }
        val width = bitmap.width
        val height = bitmap.height

        val cropMaxWidth = if (width < height) width else height

        if (cropWidth > cropMaxWidth) {
            cropWidth = cropMaxWidth
        }
        val cropX = (width - cropWidth) / 2
        val cropY = (height - cropWidth) / 2

        val newBitmap =
            BitmapUtil.createBitmap(bitmap, cropX, cropY, cropWidth, cropWidth, null, true)
        return newBitmap
    }

    /**
     * 图片平铺
     */
    fun bitmapCreateRepeater(width: Int, src: Bitmap): Bitmap {
        val count = (width + src.width - 1) / src.width
        val bitmap = Bitmap.createBitmap(width, src.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        for (idx in 0 until count) {
            canvas.drawBitmap(src, (idx * src.width).toFloat(), 0f, null)
        }
        return bitmap
    }

    /**
     * 按照bitmap的宽度先把原图等比缩放，然后裁剪处一个边长为宽度的矩形
     *
     * @param bitmap
     * @param w
     * @return
     */
    fun zoomAndCropSquareBitmap(bitmap: Bitmap?, w: Int): Bitmap? {
        if (bitmap == null) {
            return null
        }
        val width = bitmap.width
        val height = bitmap.height
        val matrix = Matrix()
        val scaleWidth = (w.toFloat() / width)
        matrix.postScale(scaleWidth, scaleWidth)
        val newbmpTmp = BitmapUtil.createBitmap(bitmap, 0, 0, width, height, matrix, true)
        if (newbmpTmp == null || newbmpTmp.isRecycled) {
            return null
        }
        val newBitmap = BitmapUtil.createBitmap(newbmpTmp, 0, 0, w, w, null, true)
        return newBitmap
    }

    fun centerCropImage(bitmap: Bitmap?, outWidth: Int, outHeight: Int): Bitmap? {
        return centerCropImage(bitmap, Bitmap.Config.RGB_565, outWidth, outHeight)
    }

    fun centerCropImage(
        bitmap: Bitmap?, config: Bitmap.Config, outWidth: Int,
        outHeight: Int
    ): Bitmap? {
        if (bitmap == null || outWidth <= 0 || outHeight <= 0) {
            return bitmap
        }

        val croppedImage = Bitmap.createBitmap(outWidth, outHeight, config)
        val canvas = Canvas(croppedImage)

        val srcRect = Rect(0, 0, bitmap.width, bitmap.height)
        val dstRect = Rect(0, 0, outWidth, outHeight)

        val dstRate = (dstRect.width() / dstRect.height()).toFloat()
        val srcRate = (srcRect.width() / srcRect.height()).toFloat()

        val deltaW: Int
        val deltaH: Int
        if (dstRate > srcRate) {
            deltaW = 0
            deltaH = ((srcRect.height() - srcRect.width().toFloat() / dstRate) / 2).toInt()
        } else {
            deltaW = ((srcRect.width() - dstRate * srcRect.height().toFloat()) / 2).toInt()
            deltaH = 0
        }

        srcRect.inset(deltaW, deltaH)
        canvas.drawBitmap(bitmap, srcRect, dstRect, null)

        return croppedImage
    }

    fun circleHead(
        source: Bitmap?, width: Int, height: Int, borderSize: Int,
        circleColor: Int
    ): Bitmap? {
        var ret: Bitmap? = null
        if (source != null) {
            val zoomBitmap = zoomBitmap(source, width, width)
            val roundedCornerBitmap = getRoundedBitmapWithBorder(
                zoomBitmap, 5.0f,
                borderSize, circleColor
            )
            ret = BitmapUtil.createBitmap(
                roundedCornerBitmap, 0, 0, width + borderSize * 2,
                height + borderSize * 2
            )
            zoomBitmap!!.recycle()
            roundedCornerBitmap!!.recycle()
        }
        return ret
    }

    /**
     * 把bitmap保存到SD卡上
     *
     * @param bitmap   源图片
     * @param savePath 保存路径
     * @param format   图片格式
     */
    fun writeBitmapToFile(
        bitmap: Bitmap?,
        savePath: String?,
        format: Bitmap.CompressFormat
    ): Boolean {
        if (bitmap == null || TextUtils.isEmpty(savePath)) {
            return false
        }
        var fos: FileOutputStream? = null
        try {
            fos = FileOutputStream(savePath)
            bitmap.compress(format, 100, fos)
        } catch (e: FileNotFoundException) {
            return false
        } finally {
            if (fos != null) {
                try {
                    fos.close()
                } catch (e: IOException) {
                    return false
                }
            }
        }
        return true
    }

    fun decodeResizedFile(pathName: String?, width: Int, height: Int): Bitmap? {
        var width = width
        var height = height
        var bitmap: Bitmap? = null

        try {
            val decodeOptions = BitmapFactory.Options()
            decodeOptions.inJustDecodeBounds = true
            BitmapFactory.decodeFile(pathName, decodeOptions)
            val actualWidth = decodeOptions.outWidth
            val actualHeight = decodeOptions.outHeight
            if (width == 0) {
                width = actualWidth
            }
            if (height == 0) {
                height = actualHeight
            }

            decodeOptions.inJustDecodeBounds = false

            val desiredWidth = getResizedDimension(width, height, actualWidth, actualHeight)
            val desiredHeight = getResizedDimension(width, height, actualHeight, actualWidth)
            decodeOptions.inSampleSize = findBestSampleSize(
                actualWidth, actualHeight,
                desiredWidth, desiredHeight
            )
            val tempBitmap = BitmapFactory.decodeFile(pathName, decodeOptions)
            if (tempBitmap != null &&
                (tempBitmap.width > desiredWidth || tempBitmap.height > desiredHeight)
                && desiredWidth > 0 && desiredHeight > 0
            ) {
                // desiredWidth和desiredHeight必须确保大于0
                // ,不然会报java.lang.IllegalArgumentException: width and height
                // must be > 0
                bitmap = Bitmap.createScaledBitmap(tempBitmap, desiredWidth, desiredHeight, true)
                tempBitmap.recycle()
            } else {
                bitmap = tempBitmap
            }
        } catch (e: OutOfMemoryError) {
            e.printStackTrace()
        }
        return bitmap
    }

    fun decodeResizedResource(res: Resources?, id: Int, width: Int, height: Int): Bitmap? {
        var width = width
        var height = height
        var bitmap: Bitmap? = null

        try {
            val decodeOptions = BitmapFactory.Options()
            decodeOptions.inJustDecodeBounds = true
            BitmapFactory.decodeResource(res, id, decodeOptions)
            val actualWidth = decodeOptions.outWidth
            val actualHeight = decodeOptions.outHeight
            if (width == 0) {
                width = actualWidth
            }
            if (height == 0) {
                height = actualHeight
            }

            decodeOptions.inJustDecodeBounds = false

            val desiredWidth = getResizedDimension(width, height, actualWidth, actualHeight)
            val desiredHeight = getResizedDimension(width, height, actualHeight, actualWidth)
            decodeOptions.inSampleSize = findBestSampleSize(
                actualWidth, actualHeight,
                desiredWidth, desiredHeight
            )
            val tempBitmap = BitmapFactory.decodeResource(res, id, decodeOptions)
            if (tempBitmap != null
                && (tempBitmap.width > desiredWidth ||
                        tempBitmap.height > desiredHeight)
            ) {
                bitmap = Bitmap.createScaledBitmap(tempBitmap, desiredWidth, desiredHeight, true)
                tempBitmap.recycle()
            } else {
                bitmap = tempBitmap
            }
        } catch (e: OutOfMemoryError) {
            e.printStackTrace()
        }
        return bitmap
    }

    fun getResizedDimension(
        maxPrimary: Int, maxSecondary: Int, actualPrimary: Int,
        actualSecondary: Int
    ): Int {
        // If no dominant value at all, just return the actual.
        if (maxPrimary == 0 && maxSecondary == 0) {
            return actualPrimary
        }

        // If primary is unspecified, scale primary to match secondary's scaling
        // ratio.
        if (maxPrimary == 0) {
            val ratio =
                maxSecondary.toDouble() / actualSecondary.toDouble() // parasoft-suppress BD.PB.ZERO-1
            return (actualPrimary * ratio).toInt()
        }

        if (maxSecondary == 0) {
            return maxPrimary
        }

        val ratio = actualSecondary.toDouble() / actualPrimary.toDouble()
        var resized = maxPrimary
        if (resized * ratio > maxSecondary) {
            resized = (maxSecondary / ratio).toInt()
        }
        return resized
    }

    fun findBestSampleSize(
        actualWidth: Int, actualHeight: Int, desiredWidth: Int,
        desiredHeight: Int
    ): Int {
        var n = 1.0f
        if (desiredWidth == 0 || desiredHeight == 0) {
            return n.toInt()
        }

        val wr = actualWidth.toDouble() / desiredWidth
        val hr = actualHeight.toDouble() / desiredHeight
        val ratio = min(wr, hr)
        while ((n * 2) <= ratio) {
            n *= 2f
        }

        return n.toInt()
    }

    fun rotateBitmap(bitmap: Bitmap?, degree: Float): Bitmap? {
        if (bitmap == null) {
            return null
        }

        val m = Matrix()
        m.setRotate(degree, bitmap.width.toFloat() / 2, bitmap.height.toFloat() / 2)
        try {
            val bm1 = Bitmap.createBitmap(
                bitmap, 0, 0, bitmap.width, bitmap.height,
                m, true
            )
            return bm1
        } catch (ex: OutOfMemoryError) {
        }
        return bitmap
    }

    fun getBitmapZoomByWidth(bmp: Bitmap, width: Int): Bitmap? {
        val scale = 1.0f * width / bmp.width
        val height = (scale * bmp.height).toInt()
        val zoomBmp = zoomBitmap(bmp, width, height)
        return zoomBmp
    }

    fun fastBlur(sentBitmap: Bitmap?, radius: Int, canReuseInBitmap: Boolean): Bitmap? {
        // Stack Blur v1.0 from
        // http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html
        //
        // Java Author: Mario Klingemann <mario at="" quasimondo.com="">
        // http://incubator.quasimondo.com
        // created Feburary 29, 2004
        // Android port : Yahel Bouaziz <yahel at="" kayenko.com="">
        // http://www.kayenko.com
        // ported april 5th, 2012

        // This is a compromise between Gaussian Blur and Box blur
        // It creates much better looking blurs than Box Blur, but is
        // 7x faster than my Gaussian Blur implementation.
        //
        // I called it Stack Blur because this describes best how this
        // filter works internally: it creates a kind of moving stack
        // of colors whilst scanning through the image. Thereby it
        // just has to add one new block of color to the right side
        // of the stack and remove the leftmost color. The remaining
        // colors on the topmost layer of the stack are either added on
        // or reduced by one, depending on if they are on the right or
        // on the left side of the stack.
        //
        // If you are using this algorithm in your code please add
        // the following line:
        //
        // Stack Blur Algorithm by Mario Klingemann <<EMAIL>>

        if (sentBitmap == null || sentBitmap.isRecycled) {
            return null
        }

        if (sentBitmap.width <= 0 || sentBitmap.height <= 0) {
            return null
        }
        val bitmap = if (canReuseInBitmap) {
            sentBitmap
        } else {
            sentBitmap.config?.let { sentBitmap.copy(it, true) }
        }

        if (bitmap == null) {
            return null
        }

        if (radius < 1) {
            return (null)
        }

        val w = bitmap.width
        val h = bitmap.height

        val pix = IntArray(w * h)
        bitmap.getPixels(pix, 0, w, 0, 0, w, h)

        val wm = w - 1
        val hm = h - 1
        val wh = w * h
        val div = radius + radius + 1

        val r = IntArray(wh)
        val g = IntArray(wh)
        val b = IntArray(wh)
        var rsum: Int
        var gsum: Int
        var bsum: Int
        var x: Int
        var y: Int
        var i: Int
        var p: Int
        var yp: Int
        var yi: Int
        val vmin = IntArray(max(w.toDouble(), h.toDouble()).toInt())

        var divsum = (div + 1) shr 1
        divsum *= divsum
        val dv = IntArray(256 * divsum)
        i = 0
        while (i < 256 * divsum) {
            dv[i] = (i / divsum)
            i++
        }

        yi = 0
        var yw = yi

        val stack = Array(div) { IntArray(3) }
        var stackpointer: Int
        var stackstart: Int
        var sir: IntArray
        var rbs: Int
        val r1 = radius + 1
        var routsum: Int
        var goutsum: Int
        var boutsum: Int
        var rinsum: Int
        var ginsum: Int
        var binsum: Int

        y = 0
        while (y < h) {
            bsum = 0
            gsum = bsum
            rsum = gsum
            boutsum = rsum
            goutsum = boutsum
            routsum = goutsum
            binsum = routsum
            ginsum = binsum
            rinsum = ginsum
            i = -radius
            while (i <= radius) {
                p = pix[(yi + min(
                    wm.toDouble(),
                    max(i.toDouble(), 0.0)
                )).toInt()]
                sir = stack[i + radius]
                sir[0] = (p and 0xff0000) shr 16
                sir[1] = (p and 0x00ff00) shr 8
                sir[2] = (p and 0x0000ff)
                rbs = (r1 - abs(i.toDouble())).toInt()
                rsum += sir[0] * rbs
                gsum += sir[1] * rbs
                bsum += sir[2] * rbs
                if (i > 0) {
                    rinsum += sir[0]
                    ginsum += sir[1]
                    binsum += sir[2]
                } else {
                    routsum += sir[0]
                    goutsum += sir[1]
                    boutsum += sir[2]
                }
                i++
            }
            stackpointer = radius

            x = 0
            while (x < w) {
                r[yi] = dv[rsum]
                g[yi] = dv[gsum]
                b[yi] = dv[bsum]

                rsum -= routsum
                gsum -= goutsum
                bsum -= boutsum

                stackstart = stackpointer - radius + div
                sir = stack[stackstart % div]

                routsum -= sir[0]
                goutsum -= sir[1]
                boutsum -= sir[2]

                if (y == 0) {
                    vmin[x] = min((x + radius + 1).toDouble(), wm.toDouble()).toInt()
                }
                p = pix[yw + vmin[x]]

                sir[0] = (p and 0xff0000) shr 16
                sir[1] = (p and 0x00ff00) shr 8
                sir[2] = (p and 0x0000ff)

                rinsum += sir[0]
                ginsum += sir[1]
                binsum += sir[2]

                rsum += rinsum
                gsum += ginsum
                bsum += binsum

                stackpointer = (stackpointer + 1) % div
                sir = stack[(stackpointer) % div]

                routsum += sir[0]
                goutsum += sir[1]
                boutsum += sir[2]

                rinsum -= sir[0]
                ginsum -= sir[1]
                binsum -= sir[2]

                yi++
                x++
            }
            yw += w
            y++
        }
        x = 0
        while (x < w) {
            bsum = 0
            gsum = bsum
            rsum = gsum
            boutsum = rsum
            goutsum = boutsum
            routsum = goutsum
            binsum = routsum
            ginsum = binsum
            rinsum = ginsum
            yp = -radius * w
            i = -radius
            while (i <= radius) {
                yi = (max(0.0, yp.toDouble()) + x).toInt()

                sir = stack[i + radius]

                sir[0] = r[yi]
                sir[1] = g[yi]
                sir[2] = b[yi]

                rbs = (r1 - abs(i.toDouble())).toInt()

                rsum += r[yi] * rbs
                gsum += g[yi] * rbs
                bsum += b[yi] * rbs

                if (i > 0) {
                    rinsum += sir[0]
                    ginsum += sir[1]
                    binsum += sir[2]
                } else {
                    routsum += sir[0]
                    goutsum += sir[1]
                    boutsum += sir[2]
                }

                if (i < hm) {
                    yp += w
                }
                i++
            }
            yi = x
            stackpointer = radius
            y = 0
            while (y < h) {
                // Preserve alpha channel: ( 0xff000000 & pix[yi] )
                pix[yi] =
                    (-0x1000000 and pix[yi]) or (dv[rsum] shl 16) or (dv[gsum] shl 8) or dv[bsum]

                rsum -= routsum
                gsum -= goutsum
                bsum -= boutsum

                stackstart = stackpointer - radius + div
                sir = stack[stackstart % div]

                routsum -= sir[0]
                goutsum -= sir[1]
                boutsum -= sir[2]

                if (x == 0) {
                    vmin[y] = (min((y + r1).toDouble(), hm.toDouble()) * w).toInt()
                }
                p = x + vmin[y]

                sir[0] = r[p]
                sir[1] = g[p]
                sir[2] = b[p]

                rinsum += sir[0]
                ginsum += sir[1]
                binsum += sir[2]

                rsum += rinsum
                gsum += ginsum
                bsum += binsum

                stackpointer = (stackpointer + 1) % div
                sir = stack[stackpointer]

                routsum += sir[0]
                goutsum += sir[1]
                boutsum += sir[2]

                rinsum -= sir[0]
                ginsum -= sir[1]
                binsum -= sir[2]

                yi += w
                y++
            }
            x++
        }

        bitmap.setPixels(pix, 0, w, 0, 0, w, h)

        return (bitmap)
    }

    //将图片缩放成屏幕宽高的比例的小图
    fun zoomtoScreenSize(mContext: Context?, mBackGroundBitmap: Bitmap): Bitmap {
        var result = mBackGroundBitmap
        val scrSize = AppDeviceUtils.getScreenSize(mContext)
        val width = mBackGroundBitmap.width
        val height = mBackGroundBitmap.height
        val p2 = height / (scrSize[1].toFloat())
        val p1 = width / (scrSize[0].toFloat())
        val scaleWidth: Int
        val scaleHeight: Int
        try {
            var startX = 0
            var startY = 0
            //图片宽高比例和屏幕宽高比例比较
            if (p1 > p2) {
                scaleHeight = height
                scaleWidth = scaleHeight * scrSize[0] / scrSize[1]
                startX = (width - scaleWidth) / 2
                startY = 0
            } else if (p1 < p2) {
                scaleWidth = width
                scaleHeight = scaleWidth * scrSize[1] / scrSize[0]
                startX = 0
                startY = (height - scaleHeight) / 2
            } else {
                return result
            }
            if (scaleWidth != 0 && scaleHeight != 0) {
                result = Bitmap.createBitmap(
                    mBackGroundBitmap, startX, startY, scaleWidth,
                    scaleHeight, null, false
                )
            }
        } catch (e: Exception) {
        }
        return result
    }

    /**
     * 创建圆角图片
     *
     * @param source
     * @param round
     * @param borderColor
     * @param borderWidth
     * @param rx
     * @param ry
     * @return
     */
    fun createTransformedBitmap(
        source: Bitmap, round: Boolean, borderColor: Int,
        borderWidth: Int, rx: Float, ry: Float
    ): Bitmap {
        val w = source.width
        val h = source.height
        val bitmap =
            Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)

        if (borderColor != Color.TRANSPARENT && borderWidth > 0) {
            val paint = Paint()
            paint.isAntiAlias = true
            paint.color = borderColor
            if (round) {
                val isWidthMinor = w < h
                val majorStart = (abs((w - h).toDouble()) / 2.0f).toFloat()
                val majorEnd = (abs((w + h).toDouble()) / 2.0f).toFloat()
                val left = if (isWidthMinor) 0f else majorStart
                val top = if (isWidthMinor) majorStart else 0f
                val right = if (isWidthMinor) w.toFloat() else majorEnd
                val bottom = if (isWidthMinor) majorEnd else h.toFloat()
                val oval = RectF(left, top, right, bottom)
                canvas.drawOval(oval, paint)
            } else {
                val rectF = RectF(0f, 0f, w.toFloat(), h.toFloat())
                canvas.drawRoundRect(rectF, rx, ry, paint)
            }
        }

        val paint = Paint()
        paint.isAntiAlias = true
        val shader = BitmapShader(source, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        paint.setShader(shader)
        if (round) {
            val radius = (if (w > h) h / 2.0f else w / 2.0f) - borderWidth
            canvas.drawCircle((w / 2).toFloat(), (h / 2).toFloat(), radius, paint)
        } else {
            val rectF = RectF(
                borderWidth.toFloat(),
                borderWidth.toFloat(),
                (w - borderWidth).toFloat(),
                (h - borderWidth).toFloat()
            )
            canvas.drawRoundRect(rectF, rx, ry, paint)
        }

        return bitmap
    }

    /**
     * 将图片的四角圆化
     *
     * @param bitmap      原图
     * @param roundPixels 圆滑率
     */
    fun getRoundCornerImage(bitmap: Bitmap, roundPixels: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        //创建一个和原始图片一样大小位图
        val roundConcerImage = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        //创建带有位图roundConcerImage的画布
        val canvas = Canvas(roundConcerImage)
        //创建画笔
        val paint = Paint()
        //创建一个和原始图片一样大小的矩形
        val rect = Rect(0, 0, bitmap.width, bitmap.height)
        val rectF = RectF(rect)
        // 去锯齿
        paint.isAntiAlias = true
        //画一个和原始图片一样大小的圆角矩形
        canvas.drawRoundRect(rectF, roundPixels.toFloat(), roundPixels.toFloat(), paint)
        //设置相交模式
        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
        //把图片画到矩形去
        canvas.drawBitmap(bitmap, null, rect, paint)
        return roundConcerImage
    }

    fun cropQRCodeSpace(bitmap: Bitmap?, blockColor: Int): Bitmap? {
        if (bitmap == null) {
            return null
        }
        // vertical
        val bmHeight = bitmap.height
        val bmWidth = bitmap.width
        // Top
        var topEnd = 0
        Top@ for (row in 0 until bmHeight) {
            for (column in 0 until bmWidth) {
                if (bitmap.getPixel(row, column) == blockColor) {
                    break@Top
                }
            }
            topEnd++
        }
        // Bottom
        var maxIndex = bmHeight - 1
        var bottomEnd = maxIndex
        Bottom@ for (row in maxIndex downTo 0) {
            for (column in 0 until bmWidth) {
                if (bitmap.getPixel(row, column) == blockColor) {
                    break@Bottom
                }
            }
            bottomEnd--
        }
        // Left
        var leftEnd = 0
        Left@ for (column in 0 until bmWidth) {
            for (row in 0 until bmHeight) {
                if (bitmap.getPixel(row, column) == blockColor) {
                    break@Left
                }
            }
            leftEnd++
        }
        // Right
        maxIndex = bmWidth - 1
        var rightEnd = maxIndex
        Right@ for (column in maxIndex downTo 0) {
            for (row in 0 until bmHeight) {
                if (bitmap.getPixel(row, column) == blockColor) {
                    break@Right
                }
            }
            rightEnd--
        }
        return Bitmap.createBitmap(
            bitmap, leftEnd, topEnd, rightEnd - leftEnd + 1,
            bottomEnd - topEnd + 1
        )
    }

    /**
     * 生成带边框圆形图片的方法
     *
     * @param bitmap
     * @param boderPx
     * @param boderColor
     * @return
     */
    fun makeRoundedBitmapWithBorder(bitmap: Bitmap?, boderPx: Int, boderColor: Int): Bitmap? {
        val tmpBitmap = makeRoundedBitmap(bitmap) ?: return null

        val output = createBitmap(
            tmpBitmap.width + boderPx * 2,
            tmpBitmap.height + boderPx * 2, Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(output)
        val borderpaint = Paint()
        val borderrect = Rect(
            0, 0,
            tmpBitmap.width + boderPx * 2,
            tmpBitmap.height + boderPx * 2
        )
        val borderrectF = RectF(borderrect)
        borderpaint.isAntiAlias = true
        borderpaint.color = boderColor
        // drawOval方法用于绘制一个包含borderrectF的圆
        canvas.drawOval(borderrectF, borderpaint)

        val paint = Paint()
        val src = Rect(
            0, 0,
            tmpBitmap.width,
            tmpBitmap.height
        )
        val dst = Rect(
            boderPx, boderPx,
            tmpBitmap.width + boderPx,
            tmpBitmap.height + boderPx
        )
        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        // src参数是图片原来的大小，dst参数是 绘画该图片需显示多大
        canvas.drawBitmap(tmpBitmap, src, dst, paint)
        return output
    }

    /**
     * 生成圆形图片
     *
     * @param bitmap
     * @return
     */
    fun makeRoundedBitmap(bitmap: Bitmap?): Bitmap? {
        if (bitmap == null) {
            return null
        }
        val output =
            createBitmap(bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(output)
        val paint = Paint()
        val rect = Rect(0, 0, bitmap.width, bitmap.height)
        val rectF = RectF(rect)
        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        paint.color = Color.GRAY
        canvas.drawOval(rectF, paint)
        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
        canvas.drawBitmap(bitmap, rect, rect, paint)
        return output
    }

    /**
     * 生成圆角图片
     *
     * @param bitmap
     * @param radiusPx 圆角半径
     * @return
     */
    fun makeRoundedCornerBitmap(bitmap: Bitmap?, radiusPx: Int): Bitmap? {
        if (bitmap == null) {
            return null
        }
        val output =
            createBitmap(bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(output)
        val paint = Paint()
        val rect = Rect(0, 0, bitmap.width, bitmap.height)
        val rectF = RectF(rect)
        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        paint.color = Color.GRAY
        canvas.drawRoundRect(rectF, radiusPx.toFloat(), radiusPx.toFloat(), paint)
        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
        canvas.drawBitmap(bitmap, rect, rect, paint)
        return output
    }

    /**
     * 将Drawable转化为Bitmap
     *
     * @param drawable [Drawable]
     * @return [Bitmap]
     */
    fun drawable2Bitmap(drawable: Drawable?): Bitmap? {
        if (drawable == null) {
            return null
        }
        val width = drawable.intrinsicWidth
        val height = drawable.intrinsicHeight
        val opacity = drawable.opacity
        val bmCfg = if ((opacity == PixelFormat.OPAQUE))
            Bitmap.Config.RGB_565
        else
            Bitmap.Config.ARGB_8888
        val bitmap = createBitmap(width, height, bmCfg)
        val canvas = Canvas(bitmap)
        drawable.setBounds(0, 0, width, height)
        drawable.draw(canvas)
        return bitmap
    }

    private fun createBitmap(width: Int, height: Int, config: Bitmap.Config): Bitmap {
        var bitmap: Bitmap? = null
        try {
            bitmap = Bitmap.createBitmap(width, height, config)
        } catch (e: OutOfMemoryError) {
            System.gc()
            try {
                bitmap = Bitmap.createBitmap(width, height, config)
            } catch (e1: OutOfMemoryError) {
            }
        } catch (e2: Exception) {
        }
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        }
        return bitmap
    }

    /**
     * 将Bitmap转为Drawable
     *
     * @param bitmap
     * @return
     */
    fun bitmap2Drawable(context: Context, bitmap: Bitmap?): Drawable? {
        if (bitmap == null) {
            return null
        }
        return BitmapDrawable(context.resources, bitmap)
    }

    private fun createBitmap(
        source: Bitmap?, x: Int, y: Int, width: Int, height: Int,
        m: Matrix
    ): Bitmap? {
        var bitmap: Bitmap? = null
        if (source != null && !source.isRecycled) {
            try {
                bitmap = Bitmap.createBitmap(source, x, y, width, height, m, true)
            } catch (err: OutOfMemoryError) {
                System.gc()
                try {
                    bitmap = Bitmap.createBitmap(source, x, y, width, height, m, true)
                } catch (e1: OutOfMemoryError) {
                }
            } catch (e2: Exception) {
            }
        }
        return bitmap
    }

    /**
     * 释放view中使用的bitmap资源
     *
     * @param view
     */
    @Suppress("deprecation")
    fun recycleBitmap(view: View?) {
        if (view != null) {
            // recycle background
            val backgroundDrawable = view.background
            if (backgroundDrawable != null && backgroundDrawable is BitmapDrawable) {
                val bitmap = backgroundDrawable.bitmap
                view.setBackgroundDrawable(null)
                if (bitmap != null && !bitmap.isRecycled) {
                    bitmap.recycle()
                }
            }
            // recycle bitmap drawable
            var viewDrawable: Drawable? = null
            if (view is ImageView) {
                viewDrawable = view.drawable
                if (viewDrawable != null && viewDrawable is BitmapDrawable) {
                    val bitmap = viewDrawable.bitmap
                    view.setImageDrawable(null)
                    if (bitmap != null && !bitmap.isRecycled) {
                        bitmap.recycle()
                    }
                }
            }
        }
    }

    /**
     * 从资源文件中加载不超过指定宽高的bitmap
     *
     * @param context
     * @param rid
     * @param reqWidth  单位：像素
     * @param reqHeight 单位：像素
     * @return
     */
    fun loadBitmap(context: Context?, rid: Int, reqWidth: Int, reqHeight: Int): Bitmap? {
        var bitmap: Bitmap? = null
        if (context != null) {
            val opts = BitmapFactory.Options()
            if (reqWidth > 0 && reqHeight > 0) {
                opts.inJustDecodeBounds = true
                BitmapFactory.decodeResource(context.resources, rid, opts)
                opts.inSampleSize = calcInSampleSize(opts, reqWidth, reqHeight)
                opts.inJustDecodeBounds = false
            }
            try {
                bitmap = BitmapFactory.decodeResource(context.resources, rid, opts)
            } catch (e: Exception) {
                e.printStackTrace()
            } catch (e: OutOfMemoryError) {
                e.printStackTrace()
            }
        }
        return bitmap
    }

    /**
     * 从文件中加载不超过指定宽高的bitmap
     *
     * @param picFile
     * @param reqWidth  单位：像素
     * @param reqHeight 单位：像素
     * @return
     */
    fun loadBitmap(picFile: File?, reqWidth: Int, reqHeight: Int): Bitmap? {
        var bitmap: Bitmap? = null
        if (picFile != null && picFile.exists()) {
            val path = picFile.path
            val opts = BitmapFactory.Options()
            if (reqWidth > 0 && reqHeight > 0) {
                opts.inJustDecodeBounds = true
                BitmapFactory.decodeFile(path, opts)
                opts.inSampleSize = calcInSampleSize(opts, reqWidth, reqHeight)
                opts.inJustDecodeBounds = false
            }
            try {
                bitmap = BitmapFactory.decodeFile(path, opts)
            } catch (e: Exception) {
                e.printStackTrace()
            } catch (e: OutOfMemoryError) {
                e.printStackTrace()
            }
        }
        return bitmap
    }

    /**
     * 计算图片缩放比例
     *
     * @param opts
     * @param reqWidth
     * @param reqHeight
     * @return
     */
    private fun calcInSampleSize(opts: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val width = opts.outWidth
        val height = opts.outHeight
        var inSampleSize = 1
        if (width > reqWidth || height > reqHeight) {
            val wRate = width / reqWidth
            val hRate = height / reqHeight
            val bigRate = if ((wRate > hRate)) wRate else hRate
            inSampleSize = 2.pow((Math.round(bigRate.toFloat()) - 1).toDouble()) as Int
        }
        return inSampleSize
    }

    fun saveBitmapToFile(
        bitmap: Bitmap?, savePath: String?, format: Bitmap.CompressFormat,
        quality: Int
    ): Boolean {
        var quality = quality
        if (bitmap == null || TextUtils.isEmpty(savePath)) {
            return false
        }
        if (quality > 100) {
            quality = 100
        } else if (quality < 50) {
            quality = 50
        }

        var fos: FileOutputStream? = null
        try {
            val file = File(savePath)
            if (!file.exists()) {
                val parentPath = file.parent
                val parent = File(parentPath)
                if (!parent.exists()) {
                    parent.mkdirs()
                }
                file.createNewFile()
            }
            fos = FileOutputStream(file)
            bitmap.compress(format, quality, fos)
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        } finally {
            if (fos != null) {
                try {
                    fos.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
        return true
    }

    /**
     * 保存bitmap
     *
     * @param bitmap   源图片
     * @param savePath 保存路径
     * @param format   图片格式
     */
    fun saveBitmap(bitmap: Bitmap?, savePath: String?, format: Bitmap.CompressFormat): Boolean {
        return saveBitmapToFile(bitmap, savePath, format, 80)
    }

    fun bmpToByteArray(bmp: Bitmap, needRecycle: Boolean): ByteArray {
        val output = ByteArrayOutputStream()
        bmp.compress(Bitmap.CompressFormat.JPEG, 80, output)
        if (needRecycle) {
            bmp.recycle()
        }

        val result = output.toByteArray()
        try {
            output.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return result
    }

    fun readBitmap(filePath: String): Bitmap? {
        return if (TextUtils.isEmpty(filePath) || !MyFileTool.isExist(filePath)) {
            null
        } else {
            BitmapUtil.decodeFile(filePath)
        }
    }

    // 黑白效果
    fun changeToGray(bitmap: Bitmap?): Bitmap? {
        if (bitmap == null) {
            return null
        }
        val width = bitmap.width
        val height = bitmap.height
        val grayBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(grayBitmap)
        val paint = Paint()
        paint.isAntiAlias = true // 设置抗锯齿
        val colorMatrix = ColorMatrix()
        colorMatrix.setSaturation(0f)
        val filter = ColorMatrixColorFilter(colorMatrix)
        paint.setColorFilter(filter)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        return grayBitmap
    }

    fun changeColor(bitmap: Bitmap?, src: FloatArray?): Bitmap? {
        if (bitmap == null) {
            return null
        }
        val width = bitmap.width
        val height = bitmap.height
        val grayBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(grayBitmap)
        val paint = Paint()
        paint.isAntiAlias = true // 设置抗锯齿
        val colorMatrix = ColorMatrix(src)
        colorMatrix.setSaturation(0f)
        val filter = ColorMatrixColorFilter(colorMatrix)
        paint.setColorFilter(filter)
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        return grayBitmap
    }

    fun changeToGray(context: Context, resId: Int): Bitmap? {
        val resources = context.resources
        val bitmap = BitmapFactory.decodeResource(resources, resId)
        return changeToGray(bitmap)
    }

    /**
     * 生成带圆角的图片
     *
     * @param bitmap 原图片
     * @param pixels 圆角弧度
     * @return
     */
    fun getRoundedCornerBitmap(bitmap: Bitmap, pixels: Int): Bitmap {
        val output = Bitmap.createBitmap(
            bitmap.width, bitmap
                .height, Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(output)

        val color = -0xbdbdbe
        val paint = Paint()
        val rect = Rect(0, 0, bitmap.width, bitmap.height)
        val rectF = RectF(rect)
        val roundPx = pixels.toFloat()

        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        paint.color = color
        canvas.drawRoundRect(rectF, roundPx, roundPx, paint)

        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
        canvas.drawBitmap(bitmap, rect, rect, paint)

        return output
    }

    /**
     * 获取镜像图片
     *
     * @param src
     * @return
     */
    fun getMirrorBitmap(src: Bitmap, recycle: Boolean): Bitmap {
        val w = src.width
        val h = src.height
        var newb: Bitmap
        synchronized(decodeLock) {
            newb = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888) // 创建一个新的和SRC长度宽度一样的位图
            val m = Matrix()
            //m.postScale(1, -1);   //镜像垂直翻转
            m.postScale(-1f, 1f) //镜像水平翻转
            //m.postRotate(-90);  //旋转-90度
            val new2 = Bitmap.createBitmap(src, 0, 0, w, h, m, true)
            val cv = Canvas(newb)
            cv.drawBitmap(
                new2, Rect(0, 0, new2.width, new2.height),
                Rect(0, 0, w, h), null
            )
            if (recycle) {
                src.recycle()
            }
        }
        return newb
    }

    /**
     * 将bitmap压缩到指定大小
     *
     * @param bitmap  原始bitmap
     * @param maxSize 压缩后大小
     * @param quality 每次压缩的质量值
     * @return
     */
    @JvmOverloads
    fun compress(
        bitmap: Bitmap, maxSize: Int, quality: Int,
        format: Bitmap.CompressFormat = Bitmap.CompressFormat.JPEG
    ): ByteArrayOutputStream {
        val byteBuffer = ByteArrayOutputStream()
        var mBitmap = bitmap
        synchronized(decodeLock) {
            mBitmap.compress(format, COMPRESS_QUALITY_100, byteBuffer)
            while (byteBuffer.size() >= maxSize) {
                mBitmap = decode(byteBuffer.toByteArray())
                byteBuffer.reset()
                mBitmap.compress(format, quality, byteBuffer)
            }
        }
        bitmap.recycle()
        //saveToFile(byteBuffer, FxConstant.IMAGE_USER_IMG_FOLDER + new Date().toString() + "_compressed.jpg");
        return byteBuffer
    }

    const val COMPRESS_QUALITY_100: Int = 100

    const val COMPRESS_QUALITY_90: Int = 90

    const val MAX_SIZE_2M: Int = 2 * 1024 * 1024

    const val MAX_WIDTH_OR_HEIGHT_2048_PX: Int = 2048

    /**
     * 根据option解析bitmap
     *
     * @param options
     * @param inputStream
     * @return
     */
    fun decode(options: BitmapFactory.Options, inputStream: InputStream?): Bitmap {
        var bitmap: Bitmap
        val w = options.outWidth
        val h = options.outHeight
        synchronized(decodeLock) {
            val tmp = if (options == null) {
                BitmapFactory.decodeStream(inputStream)
            } else {
                BitmapFactory.decodeStream(inputStream, null, options)
            }
            bitmap = scale(tmp!!, w, h)
        }
        return bitmap
    }

    /**
     * bitmap缩放
     *
     * @param bitmap
     * @param targetW
     * @param targetH
     * @return
     */
    fun scale(bitmap: Bitmap, targetW: Int, targetH: Int): Bitmap {
        val b = Bitmap.createScaledBitmap(bitmap, targetW, targetH, false)
        return b
    }

    /**
     * 根据指定分辨率大小decode option
     *
     * @param inputStream
     * @param maxWidthOrHeight 高宽最大值
     * @return
     */
    fun getDecodeOpt(
        inputStream: InputStream,
        maxWidthOrHeight: Int
    ): BitmapFactory.Options? {
        var options: BitmapFactory.Options? = null
        synchronized(decodeLock) {
            try {
                options = BitmapFactory.Options()
                options!!.inJustDecodeBounds = true
                BitmapFactory.decodeStream(inputStream, null, options)
                val actualWidth = options!!.outWidth
                val actualHeight = options!!.outHeight
                val maxWidth = if ((actualWidth > actualHeight)) actualWidth else actualHeight
                if (maxWidth > maxWidthOrHeight) {
                    //高宽任意一个大于目标值就需要处理。
                    if (actualWidth > actualHeight) {
                        //宽大于高
                        options!!.outWidth = maxWidthOrHeight
                        options!!.outHeight =
                            (actualHeight.toFloat() * options!!.outWidth.toFloat() / actualWidth.toFloat()).toInt()
                    } else {
                        options!!.outHeight = maxWidthOrHeight
                        options!!.outWidth =
                            (actualWidth.toFloat() * options!!.outHeight.toFloat() / actualHeight.toFloat()).toInt()
                    }
                }
                options!!.inSampleSize = getBestInSample(
                    actualWidth, actualHeight, options!!.outWidth,
                    options!!.outHeight
                )
                options!!.inJustDecodeBounds = false
                options!!.inPurgeable = true
                options!!.inInputShareable = true
                inputStream.close()
                return options
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return options
    }

    fun decode(data: ByteArray): Bitmap {
        return BitmapFactory.decodeByteArray(data, 0, data.size)
    }

    /**
     * 从uri中获取流
     *
     * @return
     */
    fun getInputStreamFromUri(mContext: Context, uri: Uri): InputStream? {
        try {
            return mContext.contentResolver.openInputStream(uri)
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        }
        return null
    }

    /**
     * 获取解析bitmap统一的lock，防止同一时间同时解析多个图片
     *
     * @return ImageRequest.sDecodeLock
     */
    private val decodeLock = Any()

    /**
     * 改动自Volley ImageRequest获取最佳Options.inSampleSize的方法。
     *
     * @param actualWidth
     * @param actualHeight
     * @param desiredWidth
     * @param desiredHeight
     * @return
     */
    private fun getBestInSample(
        actualWidth: Int,
        actualHeight: Int,
        desiredWidth: Int,
        desiredHeight: Int
    ): Int {
//		try {
//			Method mMethod = ImageRequest.class.getDeclaredMethod("findBestSampleSize", int.class, int.class, int.class, int.class);
//			mMethod.setAccessible(true);
//			return (int) mMethod.invoke(ImageRequest.class, actualWidth, actualHeight, desiredWidth, desiredHeight);
//		} catch (NoSuchMethodException e) {
//			e.printStackTrace();
//		} catch (InvocationTargetException e) {
//			e.printStackTrace();
//		} catch (IllegalAccessException e) {
//			e.printStackTrace();
//		}
//		return 0;
        val wr = actualWidth.toDouble() / desiredWidth
        val hr = actualHeight.toDouble() / desiredHeight
        val ratio = min(wr, hr)
        var n = 1.0f
        while ((n * 2) <= ratio) {
            n *= 2f
        }

        return n.toInt()
        /*double wr = (double) actualWidth / desiredWidth;
		double hr = (double) actualHeight / desiredHeight;
		double ratio = Math.min(wr, hr);
		float n = 1.0f;
		while (n <= ratio) {
			n *= 2;
		}

		return (int) n;*/
    }

    /**
     * @param bitmap     原图
     * @param edgeLength 希望得到的正方形部分的边长
     * @return 缩放截取正中部分后的位图。
     */
    fun centerSquareScaleBitmap(bitmap: Bitmap?, edgeLength: Int): Bitmap? {
        if (null == bitmap || edgeLength <= 0) {
            return null
        }

        var result: Bitmap = bitmap
        val widthOrg = bitmap.width
        val heightOrg = bitmap.height
        if (widthOrg > edgeLength && heightOrg > edgeLength) {
            //压缩到一个最小长度是edgeLength的bitmap
            val longerEdge =
                ((edgeLength * max(
                    widthOrg.toDouble(),
                    heightOrg.toDouble()
                ) / min(
                    widthOrg.toDouble(),
                    heightOrg.toDouble()
                )).toInt())
            val scaledWidth = if (widthOrg > heightOrg) longerEdge else edgeLength
            val scaledHeight = if (widthOrg > heightOrg) edgeLength else longerEdge
            val scaledBitmap: Bitmap

            try {
                scaledBitmap = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true)
            } catch (e: OutOfMemoryError) {
                return null
            } catch (e: Exception) {
                return null
            }

            //从图中截取正中间的正方形部分。
            val xTopLeft = (scaledWidth - edgeLength) / 2
            val yTopLeft = (scaledHeight - edgeLength) / 2

            try {
                result = Bitmap.createBitmap(
                    scaledBitmap, xTopLeft, yTopLeft, edgeLength,
                    edgeLength
                )
                scaledBitmap.recycle()
            } catch (e: OutOfMemoryError) {
                return null
            } catch (e: Exception) {
                return null
            }
        } else if (widthOrg > edgeLength || heightOrg > edgeLength) { //缩放处理
            val width = widthOrg
            val height = heightOrg
            // 设置想要的大小
            val newWidth = edgeLength
            val newHeight = edgeLength
            // 计算缩放比例
            val scaleWidth = (newWidth.toFloat()) / width
            val scaleHeight = (newHeight.toFloat()) / height
            // 取得想要缩放的matrix参数
            val matrix = Matrix()
            matrix.postScale(scaleWidth, scaleHeight)
            try {
                result = Bitmap.createBitmap(
                    bitmap, 0, 0, width, height, matrix,
                    true
                )
            } catch (e: OutOfMemoryError) {
                return null
            } catch (e: Exception) {
                return null
            }
        }

        return result
    }

    /**
     * 根据图片，自动算出匹配的颜色值
     *
     * @param bm 图片对象
     * @return 颜色值
     */
    fun getImageMainColor(bm: Bitmap?): Int {
        if (bm == null) {
            return Color.WHITE
        }
        var w = bm.width
        var h = bm.height
        var RGB: IntArray?
        if (w > 100 && h > 100) {
            val temp = zoomBitmap(bm, w / 8, h / 8)
            w = temp!!.width
            h = temp.height
            RGB = IntArray(w * h)
            temp.getPixels(RGB, 0, w, 0, 0, w, h)
            temp.recycle()
        } else {
            RGB = IntArray(w * h)
            bm.getPixels(RGB, 0, w, 0, 0, w, h)
        }

        val rgb = IntArray(3)

        var ignore = 0
        val MaxPosNumber = 10000 // 最多取一万个点求平均值，
        ignore = RGB.size / MaxPosNumber
        if (ignore == 0) {
            ignore = 1
        }
        var totalr = 0
        var totalg = 0
        var totalb = 0
        var PosNumber = 0
        val len = RGB.size - 1
        var i = len
        while (i >= 0) {
            rgb[0] = (RGB[i] and 0x00ff0000) shr 16
            rgb[1] = (RGB[i] and 0x0000ff00) shr 8
            rgb[2] = (RGB[i] and 0x000000ff)

            //剔除白色系颜色
            if (rgb[0] == rgb[1] && rgb[1] == rgb[2] && rgb[0] > 100 ||
                (rgb[0] > 200 && rgb[1] > 200 && rgb[2] > 200)
            ) {
                i -= ignore
                continue
            }

            totalr += rgb[0]
            totalg += rgb[1]
            totalb += rgb[2]

            PosNumber++
            i -= ignore
        }

        if (PosNumber > 0) {
            rgb[0] = totalr / PosNumber
            rgb[1] = totalg / PosNumber
            rgb[2] = totalb / PosNumber
        }

        val returnColor = Color.rgb(rgb[0], rgb[1], rgb[2])
        RGB = null
        return returnColor
    }

    /**
     * 以CenterCrop方式resize图片,并以math_parent的方式填充
     *
     * @param src        原始图片
     * @param destWidth  目标图片宽度
     * @param destHeight 目标图片高度
     * @return
     */
    fun fillBitmapCenterCrop(src: Bitmap?, destWidth: Int, destHeight: Int): Bitmap? {
        if (src == null || destWidth == 0 || destHeight == 0) {
            return null
        }
        val bitmapWidth = src.width
        val bitmapHeight = src.height

        if ((bitmapWidth < destWidth || bitmapHeight < destHeight) ||
            (bitmapWidth > destWidth && bitmapHeight > destHeight)
        ) {
            val ratio = max(
                (destWidth * 1.0f / bitmapWidth).toDouble(),
                (destHeight * 1.0f / bitmapHeight).toDouble()
            ).toFloat()
            val scaleBitmap = scaleBitmap(src, ratio)
            return resizeBitmapByCenterCrop(scaleBitmap, destWidth, destHeight)
        }
        return resizeBitmapByCenterCrop(src, destWidth, destHeight)
    }

    /**
     * 以CenterCrop方式resize图片
     *
     * @param src        原始图片
     * @param destWidth  目标图片宽度
     * @param destHeight 目标图片高度
     * @return
     */
    fun resizeBitmapByCenterCrop(src: Bitmap?, destWidth: Int, destHeight: Int): Bitmap? {
        if (src == null || destWidth == 0 || destHeight == 0) {
            return null
        }
        val bitmapWidth = src.width
        val bitmapHeight = src.height
        var clipWidth = bitmapWidth
        var clipHeight = bitmapHeight
        var startX = 0
        var startY = 0

        val temp = (destHeight * 1.0f / destWidth) - (bitmapHeight * 1.0f / bitmapWidth)

        if (temp > 0) {
            clipWidth = (bitmapHeight * destWidth) / destHeight
            startX = abs(((bitmapWidth - clipWidth) / 2).toDouble()).toInt()
            if (clipHeight > destHeight) {
                startY = (clipHeight - destHeight) / 2
            }
            if (clipWidth > destWidth) {
                startX += (clipWidth - destWidth) / 2
            }
        } else {
            clipHeight = (destHeight * bitmapWidth) / destWidth
            startY = (bitmapHeight - clipHeight) / 2
            if (clipWidth > destWidth) {
                startX = (clipWidth - destWidth) / 2
            }

            if (clipHeight > destHeight) {
                startY += (clipHeight - destHeight) / 2
            }
        }

        return Bitmap.createBitmap(src, startX, startY, clipWidth, clipHeight)
    }

    /**
     * 按比例缩放图片
     *
     * @param origin 原图
     * @param ratio  比例
     * @return 新的bitmap
     */
    private fun scaleBitmap(origin: Bitmap?, ratio: Float): Bitmap? {
        if (origin == null) {
            return null
        }
        val width = origin.width
        val height = origin.height
        val matrix = Matrix()
        matrix.preScale(ratio, ratio)
        return Bitmap.createBitmap(origin, 0, 0, width, height, matrix, false)
    }


    /**
     * @param src        原始图片
     * @param destWidth  目标图片宽度
     * @param destHeight 目标图片高度
     * @return
     */
    fun getScaleRatio(src: Bitmap?, destWidth: Int, destHeight: Int): Float {
        if (src == null || destWidth == 0 || destHeight == 0) {
            return 1.0f
        }
        val bitmapWidth = src.width
        val bitmapHeight = src.height

        if ((bitmapWidth < destWidth || bitmapHeight < destHeight) ||
            (bitmapWidth > destWidth && bitmapHeight > destHeight)
        ) {
            val ratio = max(
                (destWidth * 1.0f / bitmapWidth).toDouble(),
                (destHeight * 1.0f / bitmapHeight).toDouble()
            ).toFloat()
            return ratio
        }
        return 1.0f
    }

    class BitmapEntity(var mBitmap: Bitmap, var url: String)
}

private fun Int.pow(toDouble: Double): Any {
    return pow(toDouble)
}

private fun Float.pow(d: Double): Double {
   return pow(d)
}
