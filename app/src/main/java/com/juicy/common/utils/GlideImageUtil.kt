package com.juicy.common.utils

import android.content.Context
import android.widget.ImageView

object GlideImageUtil {
    // 加载普通图片
    @JvmStatic
    fun load(context: Context?, dataPath: String?, imageView: ImageView) {
        ImageLoadingUtils.loadCircleImage(context, dataPath, imageView)
        //        Glide.with(context)
//                .load(dataPath)
//                .placeholder(com.juicy.utils.R.drawable.img_small_empty_indigo)
//                .into(imageView);
    }
}
