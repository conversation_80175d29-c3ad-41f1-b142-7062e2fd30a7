package com.juicy.common.utils

import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.drawable.GradientDrawable
import android.media.ExifInterface
import android.media.ThumbnailUtils
import android.util.Base64
import androidx.core.graphics.ColorUtils
import com.juicy.app.JuicyApplication
import java.io.BufferedInputStream
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.IOException
import java.io.InputStream
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sqrt

object BitmapUtil {
    fun calculateInSampleSize(
        options: BitmapFactory.Options, reqWidth: Int,
        reqHeight: Int
    ): Int {
        // Raw height and width of image
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            // Calculate ratios of height and width to requested height and
            // width

            val heightRatio = Math.round(height.toFloat() / reqHeight.toFloat())
            val widthRatio = Math.round(width.toFloat() / reqWidth.toFloat())

            // Choose the smallest ratio as inSampleSize value, this will
            // guarantee
            // a final image with both dimensions larger than or equal to the
            // requested height and width.
            inSampleSize = if (heightRatio < widthRatio) heightRatio else widthRatio
        }

        return inSampleSize
    }

    private fun computeInitialSampleSize(
        options: BitmapFactory.Options, minSideLength: Int,
        maxNumOfPixels: Int
    ): Int {
        val w = options.outWidth.toDouble()
        val h = options.outHeight.toDouble()

        val lowerBound = if ((maxNumOfPixels == -1)) 1 else ceil(
            sqrt(
                w * h
                        / maxNumOfPixels
            )
        ) as Int
        val upperBound = if ((minSideLength == -1)) 128 else min(
            floor(w / minSideLength), floor(h / minSideLength)
        ) as Int

        if (upperBound < lowerBound) {
            // return the larger one when there is no overlapping zone.
            return lowerBound
        }

        return if ((maxNumOfPixels == -1) && (minSideLength == -1)) {
            1
        } else if (minSideLength == -1) {
            lowerBound
        } else {
            upperBound
        }
    }

    fun getBitmapWidth(id: Int): Int {
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeResource(JuicyApplication.juicyApplication?.resources, id, options)
        return options.outWidth
    }

    fun getBitmapHeight(id: Int): Int {
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeResource(JuicyApplication.juicyApplication?.resources, id, options)
        return options.outHeight
    }

    private fun computeSampleSize(
        options: BitmapFactory.Options, minSideLength: Int,
        maxNumOfPixels: Int
    ): Int {
        val initialSize = computeInitialSampleSize(options, minSideLength, maxNumOfPixels)
        var roundedSize: Int
        if (initialSize <= 8) {
            roundedSize = 1
            while (roundedSize < initialSize) {
                roundedSize = roundedSize shl 1
            }
        } else {
            roundedSize = (initialSize + 7) / 8 * 8
        }

        return roundedSize
    }

    fun createBitmap(source: Bitmap?, x: Int, y: Int, width: Int, height: Int): Bitmap {
        var bitmap: Bitmap? = null
        if (source != null && !source.isRecycled) {
            try {
                bitmap = Bitmap.createBitmap(source, x, y, width, height)
            } catch (e: OutOfMemoryError) {
                System.gc()
                try {
                    bitmap = Bitmap.createBitmap(source, x, y, width, height)
                } catch (e1: OutOfMemoryError) {
                }
            } catch (e2: Exception) {
            }
        }
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        }
        return bitmap
    }

    fun createBitmap(
        source: Bitmap?, x: Int, y: Int, width: Int, height: Int, m: Matrix?,
        filter: Boolean
    ): Bitmap {
        var bitmap: Bitmap? = null
        if (source != null && !source.isRecycled) {
            try {
                bitmap = Bitmap.createBitmap(source, x, y, width, height, m, true)
            } catch (e: OutOfMemoryError) {
                System.gc()
                try {
                    bitmap = Bitmap.createBitmap(source, 0, 0, width, height, m, true)
                } catch (e1: OutOfMemoryError) {
                }
            } catch (e2: Exception) {
            }
        }
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        }
        return bitmap
    }

    fun createBitmap(width: Int, height: Int, config: Bitmap.Config): Bitmap {
        var bitmap: Bitmap? = null
        try {
            bitmap = Bitmap.createBitmap(width, height, config)
        } catch (e: OutOfMemoryError) {
            System.gc()
            try {
                bitmap = Bitmap.createBitmap(width, height, config)
            } catch (e1: OutOfMemoryError) {
            }
        } catch (e2: Exception) {
        }
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        }
        return bitmap
    }

    fun createBitmap(colors: IntArray, width: Int, height: Int, config: Bitmap.Config?): Bitmap {
        var bitmap: Bitmap? = null
        try {
            bitmap = Bitmap.createBitmap(colors, width, height, config)
        } catch (e: OutOfMemoryError) {
            System.gc()
            try {
                bitmap = Bitmap.createBitmap(colors, width, height, config)
            } catch (e1: OutOfMemoryError) {
            }
        } catch (e2: Exception) {
        }
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        }
        return bitmap
    }

    fun decodeFile(pathName: String): Bitmap {
        var bitmap: Bitmap? = null
        if (MyFileTool.isExist(pathName)) {
            var `is`: InputStream? = null
            try {
                try {
                    `is` = BufferedInputStream(MyFileTool.getFileInputStream(pathName))
                    bitmap = decodeStream(`is`)
                } finally {
                    `is`?.close()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        }
        return bitmap
    }

    /**
     * 从图片文件加载Bitmap，加载异常返回纯色Bitmap
     * @param pathName  图片文件路径
     * @param defaultColor 异常情况下的颜色
     * @return
     */
    fun decodeFileWithColor(pathName: String, defaultColor: Int): Bitmap {
        var bitmap: Bitmap? = null
        if (MyFileTool.isExist(pathName)) {
            var `is`: InputStream? = null
            try {
                `is` = MyFileTool.getFileInputStream(pathName)
                bitmap = try {
                    BitmapFactory.decodeStream(`is`)
                } catch (e: OutOfMemoryError) {
                    null
                }
            } catch (e: Exception) {
                bitmap = null
            } finally {
                MyFileTool.closeIS(`is`)
            }
        }
        if (bitmap == null) {
            bitmap = createColorBitmap(defaultColor)
        }
        return bitmap
    }

    fun decodeFile(pathName: String?, inSampleSize: Int): Bitmap {
        var bitmap: Bitmap? = null
        if (MyFileTool.isExist(pathName)) {
            val opts = BitmapFactory.Options()
            opts.inSampleSize = inSampleSize
            opts.inJustDecodeBounds = false
            try {
                bitmap = BitmapFactory.decodeFile(pathName, opts)
            } catch (e: OutOfMemoryError) {
                System.gc()
                try {
                    bitmap = BitmapFactory.decodeFile(pathName, opts)
                } catch (e1: OutOfMemoryError) {
                }
            } catch (e2: Exception) {
            }
        }
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        }
        return bitmap
    }

    fun decodeFile(pathName: String?, width: Int, height: Int): Bitmap {
        var bitmap: Bitmap? = null
        if (MyFileTool.isExist(pathName)) {
            val opts = BitmapFactory.Options()
            opts.inJustDecodeBounds = true
            BitmapFactory.decodeFile(pathName, opts)
            opts.inSampleSize = computeSampleSize(opts, -1, width * height)
            opts.inJustDecodeBounds = false
            try {
                bitmap = BitmapFactory.decodeFile(pathName, opts)
            } catch (e: OutOfMemoryError) {
                System.gc()
                try {
                    bitmap = BitmapFactory.decodeFile(pathName, opts)
                } catch (e1: OutOfMemoryError) {
                }
            } catch (e2: Exception) {
            }
        }
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        }
        return bitmap
    }

    fun decodeResource(res: Resources, id: Int): Bitmap {
        var bitmap: Bitmap? = null
        var `is`: InputStream? = null
        try {
            try {
                `is` = BufferedInputStream(res.openRawResource(id))
                bitmap = decodeStream(`is`)
            } finally {
                `is`?.close()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        }
        return bitmap
    }

    fun createColorBitmap(color: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888)
        bitmap.setPixel(0, 0, color)
        return bitmap
    }

    fun createColorBitmap(color: Int, width: Int, height: Int): Bitmap {
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        bitmap.eraseColor(color)
        return bitmap
    }

    fun createColorBitmap(
        startColor: Int, endColor: Int, orientation: GradientDrawable.Orientation?,
        width: Int, height: Int
    ): Bitmap {
        val gradientDrawable = GradientDrawable(orientation, intArrayOf(startColor, endColor))
        gradientDrawable.setBounds(0, 0, width, height)
        val config = Bitmap.Config.ARGB_8888
        val bitmap = Bitmap.createBitmap(width, height, config)
        val canvas = Canvas(bitmap)
        gradientDrawable.setSize(width, height)
        gradientDrawable.setDither(true)
        gradientDrawable.draw(canvas)
        return bitmap
    }

    fun decodeSampledBitmapFromFile(resPath: String, maxWidth: Int): Bitmap? {
        if (maxWidth <= 0) {
            return null
        }
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeFile(resPath, options)
        val oldHeight = options.outHeight
        val oldWidth = options.outWidth
        val oldMaxWidth = if ((oldHeight > oldWidth)) oldHeight else oldWidth

        if (oldMaxWidth > maxWidth) {
            options.inSampleSize = Math.round(oldMaxWidth.toFloat() / maxWidth.toFloat())
        } else {
            options.inSampleSize = 1
        }
        options.inJustDecodeBounds = false
        return rotateBitmap(BitmapFactory.decodeFile(resPath, options), readPictureDegree(resPath))
    }

    fun decodeSampledBitmapFromFile(resPath: String?, reqWidth: Int, reqHeight: Int): Bitmap {
        // First decode with inJustDecodeBounds=true to check dimensions
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeFile(resPath, options)

        // Calculate inSampleSize
        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)

        // Decode bitmap with inSampleSize set
        options.inJustDecodeBounds = false
        return BitmapFactory.decodeFile(resPath, options)
    }

    /**
     * mImageView.setImageBitmap(
     * decodeSampledBitmapFromResource(getResources(), R.id.myimage, 100, 100));
     *
     * @param res
     * @param resId
     * @param reqWidth
     * @param reqHeight
     * @return
     */
    fun decodeSampledBitmapFromResource(
        res: Resources?, resId: Int, reqWidth: Int,
        reqHeight: Int
    ): Bitmap {
        // First decode with inJustDecodeBounds=true to check dimensions

        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeResource(res, resId, options)

        // Calculate inSampleSize
        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)

        // Decode bitmap with inSampleSize set
        options.inJustDecodeBounds = false
        return BitmapFactory.decodeResource(res, resId, options)
    }

    fun decodeStream(`is`: InputStream?): Bitmap {
        var bitmap: Bitmap? = null
        if (`is` != null) {
            try {
                bitmap = BitmapFactory.decodeStream(`is`)
            } catch (e: OutOfMemoryError) {
                System.gc()
                try {
                    bitmap = BitmapFactory.decodeStream(`is`)
                } catch (e1: OutOfMemoryError) {
                }
            } catch (e2: Exception) {
            }
        }
        if (bitmap == null) {
            bitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ALPHA_8)
        }
        return bitmap
    }

    fun imageZoom(bitMap: Bitmap, maxSize: Double): Bitmap {
        // 将bitmap放至数组中，意在bitmap的大小（与实际读取的原文件要大）
        var bitMap = bitMap
        val baos = ByteArrayOutputStream()
        bitMap.compress(Bitmap.CompressFormat.JPEG, 100, baos)
        val b = baos.toByteArray()
        // 将字节换成KB
        val mid = (b.size / 1024).toDouble()
        // 判断bitmap占用空间是否大于允许最大空间 如果大于则压缩 小于则不压缩
        if (mid > maxSize) {
            // 获取bitmap大小 是允许最大大小的多少倍
            val i = mid / maxSize
            val height = bitMap.height / sqrt(i)
            val width = bitMap.width / sqrt(i)
            // 开始压缩 此处用到平方根 将宽带和高度压缩掉对应的平方根倍
            // （1.保持刻度和高度和原bitmap比率一致，压缩后也达到了最大大小占用空间的大小）
            bitMap = zoomImage(
                bitMap, width,
                if (height > (2 * width)) (2 * width) else height
            )
        }
        return bitMap
    }

    /**
     * 设置图片 Bitmap任意透明度
     * @param sourceImg
     * @param number
     * @return
     */
    fun getTransparentBitmap(sourceImg: Bitmap, number: Int): Bitmap {
        var sourceImg = sourceImg
        var number = number
        val argb = IntArray(sourceImg.width * sourceImg.height)

        sourceImg.getPixels(
            argb, 0, sourceImg.width, 0, 0, sourceImg

                .width, sourceImg.height
        ) // 获得图片的ARGB值

        number = number * 255 / 100

        for (i in argb.indices) {
            argb[i] = (number shl 24) or (argb[i] and 0x00FFFFFF)
        }

        sourceImg = Bitmap.createBitmap(
            argb, sourceImg.width, sourceImg

                .height, Bitmap.Config.ARGB_8888
        )

        return sourceImg
    }

    fun recycleBitmap(vararg bms: Bitmap?) {
        for (bm in bms) {
            recycleBitmap(bm)
        }
    }

    fun recycleBitmap(bm: Bitmap?) {
        if (bm != null && !bm.isRecycled) {
            bm.recycle()
        }
    }

    /**
     * 在图片上盖一层颜色的蒙层
     *
     */
    fun overColorToBitmap(blurBitmap: Bitmap, color: Int): Bitmap {
        val srcR = Rect(0, 0, blurBitmap.width, blurBitmap.height)
        val overlayBitmap =
            Bitmap.createBitmap(srcR.width(), srcR.height(), Bitmap.Config.ARGB_8888)
        val canvas = Canvas()
        canvas.setBitmap(overlayBitmap)
        canvas.drawBitmap(blurBitmap, srcR, srcR, Paint())
        canvas.drawColor(color)
        canvas.setBitmap(null)
        return overlayBitmap
    }

    //居中裁剪
    fun scaleBitmap(bitmap: Bitmap, w: Float, h: Float): Bitmap? {
        val width = bitmap.width.toFloat()
        val height = bitmap.height.toFloat()
        var x = 0f
        var y = 0f
        var scaleWidth = width
        var scaleHeight = height
        val newbmp: Bitmap
        //Log.e("gacmy","width:"+width+" height:"+height);
        if (w > h) { //比例宽度大于高度的情况
            val scale = w / h
            val tempH = width / scale
            if (height > tempH) { //
                x = 0f
                y = (height - tempH) / 2
                scaleWidth = width
                scaleHeight = tempH
            } else {
                scaleWidth = height * scale
                x = (width - scaleWidth) / 2
                y = 0f
            }
            //            Log.e("gacmy","scale:"+scale+" scaleWidth:"+scaleWidth+" scaleHeight:"+scaleHeight);
        } else if (w < h) { //比例宽度小于高度的情况
            val scale = h / w
            val tempW = height / scale
            if (width > tempW) {
                y = 0f
                x = (width - tempW) / 2
                scaleWidth = tempW
                scaleHeight = height
            } else {
                scaleHeight = width * scale
                y = (height - scaleHeight) / 2
                x = 0f
                scaleWidth = width
            }
        } else { //比例宽高相等的情况
            if (width > height) {
                x = (width - height) / 2
                y = 0f
                scaleHeight = height
                scaleWidth = height
            } else {
                y = (height - width) / 2
                x = 0f
                scaleHeight = width
                scaleWidth = width
            }
        }
        try {
            newbmp = Bitmap.createBitmap(
                bitmap,
                x.toInt(),
                y.toInt(),
                scaleWidth.toInt(),
                scaleHeight.toInt(),
                null,
                false
            ) // createBitmap()方法中定义的参数x+width要小于或等于bitmap.getWidth()，y+height要小于或等于bitmap.getHeight()
            //bitmap.recycle();
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
        return newbmp
    }

    /**
     *
     * @param bm 图像
     * @param hue 色相
     * @param saturation 饱和度
     * @param lum 亮度
     * @return new bitmap
     */
    fun handleImageEffect(bm: Bitmap, hue: Float, saturation: Float, lum: Float): Bitmap {
        val bmp = Bitmap.createBitmap(bm.width, bm.height, Bitmap.Config.ARGB_8888)

        val canvas = Canvas(bmp)

        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        val hueMatrix = ColorMatrix()
        hueMatrix.setRotate(0, hue) // R
        hueMatrix.setRotate(1, hue) // G
        hueMatrix.setRotate(2, hue) // B

        val saturationMatrix = ColorMatrix()
        saturationMatrix.setSaturation(saturation)

        val lumMatrix = ColorMatrix()
        lumMatrix.setScale(lum, lum, lum, 1f)

        //融合
        val imageMatrix = ColorMatrix()
        imageMatrix.postConcat(hueMatrix)
        imageMatrix.postConcat(saturationMatrix)
        imageMatrix.postConcat(lumMatrix)

        paint.setColorFilter(ColorMatrixColorFilter(imageMatrix))
        canvas.drawBitmap(bm, 0f, 0f, paint)

        return bmp
    }

    /***
     * 图片的缩放方法
     *
     * @param bgimage
     * ：源图片资源
     * @param newWidth
     * ：缩放后宽度
     * @param newHeight
     * ：缩放后高度
     * @return
     */
    fun zoomImage(
        bgimage: Bitmap, newWidth: Double,
        newHeight: Double
    ): Bitmap {
        // 获取这个图片的宽和高
        val width = bgimage.width.toFloat()
        val height = bgimage.height.toFloat()
        // 创建操作图片用的matrix对象
        val matrix = Matrix()
        // 计算宽高缩放率
        val scaleWidth = (newWidth.toFloat()) / width
        val scaleHeight = (newHeight.toFloat()) / height
        // 缩放图片动作
        matrix.postScale(scaleWidth, scaleHeight)
        val bitmap = Bitmap.createBitmap(
            bgimage, 0, 0, width.toInt(),
            height.toInt(), matrix, true
        )
        return bitmap
    }

    /**
     * 将图片变成圆形
     *
     * @param bitmap
     * @return
     */
    fun makeRoundCorner(bitmap: Bitmap): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        var left = 0
        var top = 0
        var right = width
        var bottom = height
        var roundPx = height / 2
        if (width > height) {
            left = (width - height) / 2
            top = 0
            right = left + height
            bottom = height
        } else if (height > width) {
            left = 0
            top = (height - width) / 2
            right = width
            bottom = top + width
            roundPx = width / 2
        }
        val output = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(output)
        val color = -0xbdbdbe
        val paint = Paint()
        val rect = Rect(left, top, right, bottom)
        val rectF = RectF(rect)
        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        paint.color = color
        canvas.drawRoundRect(rectF, roundPx.toFloat(), roundPx.toFloat(), paint)
        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
        canvas.drawBitmap(bitmap, rect, rect, paint)
        return output
    }


    fun getOptions(path: String): BitmapFactory.Options? {
        val mFile = File(path)
        if (!mFile.exists()) {
            return null
        }
        try {
            val options = BitmapFactory.Options()
            options.inJustDecodeBounds = true
            BitmapFactory.decodeFile(path, options)

            return options
        } catch (e: OutOfMemoryError) {
            e.printStackTrace()
        }
        return null
    }

    /**
     * 读取图片属性：旋转的角度
     *
     * @param path 拍摄图片的完整路径
     * @return degree旋转的角度
     */
    fun readPictureDegree(path: String): Int {
        var degree = 0
        try {
            val exifInterface = ExifInterface(path)
            val orientation = exifInterface.getAttributeInt(
                ExifInterface.TAG_ORIENTATION,
                ExifInterface.ORIENTATION_NORMAL
            )
            when (orientation) {
                ExifInterface.ORIENTATION_ROTATE_90 -> degree = 90
                ExifInterface.ORIENTATION_ROTATE_180 -> degree = 180
                ExifInterface.ORIENTATION_ROTATE_270 -> degree = 270
            }
        } catch (e: IOException) {
            e.printStackTrace()
            return degree
        }
        return degree
    }

    /**
     * 旋转图片，使图片保持正确的方向。
     *
     * @param bitmap  原始图片
     * @param degrees 原始图片的角度
     * @return Bitmap 旋转后的图片
     */
    fun rotateBitmap(bitmap: Bitmap?, degrees: Int): Bitmap? {
        if (degrees == 0 || null == bitmap) {
            return bitmap
        }
        val matrix = Matrix()
        matrix.setRotate(
            degrees.toFloat(),
            (bitmap.width / 2).toFloat(),
            (bitmap.height / 2).toFloat()
        )
        val bmp = Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
        return bmp
    }

    fun getThumbnailBitmap(bitmap: Bitmap?, targetWidth: Int, targetHeight: Int): Bitmap? {
        if (null == bitmap) {
            return bitmap
        }
        val bmp = ThumbnailUtils.extractThumbnail(
            bitmap,
            targetWidth,
            targetHeight,
            ThumbnailUtils.OPTIONS_RECYCLE_INPUT
        )
        return bmp
    }

    fun bitmapUsable(bitmap: Bitmap?): Boolean {
        return bitmap != null && !bitmap.isRecycled
    }

    /**
     * 圆角图片
     *
     * @param toTransform
     * @param radiusArray
     * @return
     */
    fun roundRectBitmap(toTransform: Bitmap, radiusArray: FloatArray): Bitmap {
        val srcR = RectF(0f, 0f, toTransform.width.toFloat(), toTransform.height.toFloat())
        val path = Path()
        path.addRoundRect(srcR, radiusArray, Path.Direction.CW)

        val bitmap = Bitmap.createBitmap(
            srcR.width().toInt(),
            srcR.height().toInt(),
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        val paint = Paint()
        paint.isAntiAlias = true
        val shader = BitmapShader(toTransform, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        paint.setShader(shader)
        canvas.drawPath(path, paint)
        return bitmap
    }

    /**
     * 设置四个角的圆角半径
     */
    fun getRadius(
        leftTop: Float,
        rightTop: Float,
        rightBottom: Float,
        leftBottom: Float
    ): FloatArray {
        val radiusArray = FloatArray(8)
        radiusArray[0] = leftTop
        radiusArray[1] = leftTop
        radiusArray[2] = rightTop
        radiusArray[3] = rightTop
        radiusArray[4] = rightBottom
        radiusArray[5] = rightBottom
        radiusArray[6] = leftBottom
        radiusArray[7] = leftBottom
        return radiusArray
    }

    /**
     * 图片叠加
     * @param background
     * @param foreground
     * @return
     */
    fun superimposedBitmap(background: Bitmap, foreground: Bitmap): Bitmap? {
        var foreground = foreground
        if (!bitmapUsable(background) && !bitmapUsable(foreground)) {
            return null
        }
        var bgWidth = 0
        var bgHeight = 0
        if (bitmapUsable(background)) {
            bgWidth = background.width
            bgHeight = background.height
        } else if (bitmapUsable(foreground)) {
            bgWidth = background.width
            bgHeight = background.height
        } else {
            return null
        }

        if (bgWidth <= 0 || bgHeight <= 0) {
            return null
        }
        val newbmp = Bitmap.createBitmap(bgWidth, bgHeight, Bitmap.Config.ARGB_8888)
        val cv = Canvas(newbmp)
        if (bitmapUsable(background)) {
            cv.drawBitmap(background, 0f, 0f, null)
        }
        if (bitmapUsable(foreground)) {
            foreground = zoomImage(foreground, bgWidth.toDouble(), bgHeight.toDouble())
            cv.drawBitmap(foreground, 0f, 0f, null)
        }
        cv.save()
        cv.restore()
        return newbmp
    }


    /**
     * 融合图裁剪
     * @param big  底图
     * @param small 小图
     * @param xInRatio 小图在底图中的横坐标（相对于底图宽的比例）
     * @param yInRatio 小图在底图中的纵坐标（相对于底图高的比例）
     * @param alpha 小图的透明度 0~255
     * @return
     */
    fun mergeWithCrop(
        big: Bitmap,
        small: Bitmap,
        xInRatio: Float,
        yInRatio: Float,
        alpha: Int
    ): Bitmap {
        val smallW = small.width
        val smallH = small.height
        val bigW = big.width
        val bigH = big.height

        val smallX = (bigW * xInRatio).toInt()
        val smallY = (bigH * yInRatio).toInt()
        val bigX = 0
        val bigY = 0

        val sRect = Rect(smallX, smallY, smallX + smallW, smallY + smallH)
        val bRect = Rect(bigX, bigY, bigX + bigW, bigY + bigH)
        val overlap = intersect(sRect, bRect)?:Rect()

        var overlayW = overlap.right - overlap.left
        var overlayH = overlap.bottom - overlap.top

        //防止红外图完全覆盖可见光图时报错。
        if (overlap.left == 0 && overlap.top == 0) {
            overlayW--
            overlayH--
        }

        val newBmp = Bitmap.createBitmap(big, overlap.left, overlap.top, overlayW, overlayH)
        big.recycle()

        val canvas = Canvas(newBmp)
        val paint = Paint()
        //设置画笔为取交集模式
        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
        paint.alpha = alpha
        canvas.drawBitmap(
            small,
            (if (smallX >= 0) 0 else smallX).toFloat(),
            (if (smallY >= 0) 0 else smallY).toFloat(),
            paint
        )
        small.recycle()

        return newBmp
    }

    /**
     * 求矩形的重叠区域
     * @param r1
     * @param r2
     * @return
     */
    fun intersect(r1: Rect, r2: Rect): Rect? {
        var r: Rect? = Rect()
        r!!.left = max(r1.left.toDouble(), r2.left.toDouble()).toInt()
        r.top = max(r1.top.toDouble(), r2.top.toDouble()).toInt()
        r.right = min(r1.right.toDouble(), r2.right.toDouble()).toInt()
        r.bottom = min(r1.bottom.toDouble(), r2.bottom.toDouble()).toInt()

        if (r.left > r.right || r.top > r.bottom) r = null

        return r
    }

    fun compressBitmap(bitmap: Bitmap, sizeLimit: Long): Bitmap? {
        val baos = ByteArrayOutputStream()
        var quality = 100
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos)

        // 循环判断压缩后图片是否超过限制大小
        while (baos.toByteArray().size / 1024 > sizeLimit) {
            // 清空baos
            baos.reset()
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, baos)
            quality -= 10
        }

        val newBitmap =
            BitmapFactory.decodeStream(ByteArrayInputStream(baos.toByteArray()), null, null)

        return newBitmap
    }

    fun base642bitmap(base64: String): Bitmap {
        var base64 = base64
        val index = base64.indexOf(";base64,")
        if (index >= 0) {
            base64 = base64.substring(index + ";base64,".length)
        }
        val iconBytes = Base64.decode(base64, Base64.DEFAULT)
        return BitmapFactory.decodeByteArray(iconBytes, 0, iconBytes.size)
    }

    /**
     * 合成多张图片，生成长图
     * @param width
     * @param bitmaps
     * @return
     */
    fun compositeLongBitmap(width: Int, vararg bitmaps: Bitmap): Bitmap? {
        if (bitmaps == null || width <= 0) {
            return null
        }
        var height = 0
        for (bitmap in bitmaps) {
            if (bitmapUsable(bitmap)) {
                height += bitmap.height * width / bitmap.width
            }
        }
        if (height <= 0) {
            return null
        }
        val newBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val cv = Canvas(newBitmap)
        val src = Rect()
        val dst = Rect()
        var curHeight = 0
        for (bitmap in bitmaps) {
            if (bitmapUsable(bitmap)) {
                val h = bitmap.height * width / bitmap.width
                src[0, 0, bitmap.width] = bitmap.height
                dst[0, curHeight, width] = curHeight + h
                cv.drawBitmap(bitmap, src, dst, null)
                curHeight += h
            }
        }
        cv.save()
        cv.restore()
        return newBitmap
    }

    /**
     * 图片居中显示
     */
    fun createDefaultBitmap(width: Int, height: Int, bitmap: Bitmap, dw: Int, dh: Int): Bitmap? {
        if (!bitmapUsable(bitmap) || width <= 0 || height <= 0 || width < dw || height < dh) {
            return null
        }
        val newBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val cv = Canvas(newBitmap)
        val src = Rect()
        val dst = Rect()
        src[0, 0, bitmap.width] = bitmap.height
        val t = (width - dw) / 2
        val l = (height - dh) / 2
        dst[t, l, t + dw] = l + dh
        cv.drawBitmap(bitmap, src, dst, null)
        return newBitmap
    }

    fun tintBitmap(inBitmap: Bitmap?, tintColor: Int): Bitmap? {
        if (inBitmap == null) {
            return null
        }
        val outBitmap =
            inBitmap.config?.let { Bitmap.createBitmap(inBitmap.width, inBitmap.height, it) }
        val canvas = outBitmap?.let { Canvas(it) }
        val paint = Paint()
        paint.setColorFilter(PorterDuffColorFilter(tintColor, PorterDuff.Mode.SRC_IN))
        canvas?.drawBitmap(inBitmap, 0f, 0f, paint)
        return outBitmap
    }

    /**
     * 按比例缩放图片
     *
     * @param origin 原图
     * @param ratio  比例
     * @return 新的bitmap
     */
    fun scaleBitmap(origin: Bitmap?, ratio: Float): Bitmap? {
        if (origin == null) {
            return null
        }
        val width = origin.width
        val height = origin.height
        val matrix = Matrix()
        matrix.preScale(ratio, ratio)
        val newBM = Bitmap.createBitmap(origin, 0, 0, width, height, matrix, false)
        if (newBM == origin) {
            return newBM
        }
        origin.recycle()
        return newBM
    }


    fun changeBitmapColor(toTransform: Bitmap, colorMapping: Map<Int?, Int?>): Bitmap {
        val src = toTransform
        val width = src.width
        val height = src.height
        val pixels = IntArray(width * height)
        //get pixels
        src.getPixels(pixels, 0, width, 0, 0, width, height)

        for (x in pixels.indices) {
            pixels[x] =
                if ((colorMapping.containsKey(pixels[x]))) colorMapping[pixels[x]]!! else pixels[x]
        }
        // create result bitmap output
        val result = src.config?.let { Bitmap.createBitmap(width, height, it) }
        //set pixels
        result?.setPixels(pixels, 0, width, 0, 0, width, height)
        return result!!
    }

    /**
     * 裁剪图片图片
     *
     */
    fun cropBitmap(bitmap: Bitmap?, x: Int, y: Int, width: Int, height: Int): Bitmap? {
        if (bitmap == null) {
            return null
        }
        val rawW = bitmap.width
        val rawH = bitmap.height
        if (x >= 0 && y >= 0 && width > 0 && height > 0 && x < rawW && y < rawH && width <= rawW && height <= rawH) {
            val newBitmap = Bitmap.createBitmap(bitmap, x, y, width, height)
            return newBitmap
        }
        return bitmap
    }

    /**
     * 纵向透明度渐变
     */
    fun changeBitmapAlphaVerticalRange(
        bitmap: Bitmap?,
        percents: FloatArray,
        alphas: FloatArray
    ): Bitmap? {
        return changeBitmapAlpha(bitmap, percents, alphas, DIRECTION_TOP_BOTTOM)
    }


    var DIRECTION_LEFT_RIGHT: Int = 0
    var DIRECTION_LEFTTOP_RIGHTBOTTOM: Int = 1
    var DIRECTION_TOP_BOTTOM: Int = 2
    var DIRECTION_RIGHTTOP_LEFTBOTTOM: Int = 3

    /**
     * 纵向透明度渐变
     */
    fun changeBitmapAlpha(
        bitmap: Bitmap?,
        percents: FloatArray,
        alphas: FloatArray,
        direction: Int
    ): Bitmap? {
        if (bitmap == null) {
            return null
        }

        val width = bitmap.width
        val height = bitmap.height
        val bmp = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val oldPx = IntArray(width * height) //用来存储旧的色素点的数组
        val newPx = IntArray(width * height) //用来存储新的像素点的数组
        var color: Int //用来存储原来颜色值
        var r: Int
        var g: Int
        var b: Int
        var a: Int //存储颜色的四个分量：红，绿，蓝，透明度

        var currentLevel = 0
        var currentStart = 0
        var currentEnd = 0
        var currentStartAlpha = 0f
        var currentEndAlpha = 0f

        bitmap.getPixels(oldPx, 0, width, 0, 0, width, height)
        val ratio = width.toFloat() / height
        var totalLength = 0
        if (direction == DIRECTION_TOP_BOTTOM) {
            totalLength = height
        } else if (direction == DIRECTION_LEFT_RIGHT) {
            totalLength = width
        } else if (direction == DIRECTION_LEFTTOP_RIGHTBOTTOM
            || direction == DIRECTION_RIGHTTOP_LEFTBOTTOM
        ) {
            totalLength = sqrt((width * width + height * height).toDouble()) as Int
        }

        for (x in 0 until width) {
            currentLevel = 0
            for (y in 0 until height) {
                val i = y * width + x
                color = oldPx[i]

                r = Color.red(color)
                g = Color.green(color)
                b = Color.blue(color)
                a = Color.alpha(color)

                var distance = 0
                if (direction == DIRECTION_TOP_BOTTOM) {
                    distance = y
                } else if (direction == DIRECTION_LEFT_RIGHT) {
                    distance = x
                } else if (direction == DIRECTION_LEFTTOP_RIGHTBOTTOM) {
                    distance = sqrt((x * x + y * y).toDouble()) as Int
                } else if (direction == DIRECTION_RIGHTTOP_LEFTBOTTOM) {
                    distance = sqrt(((width - x) * (width - x) + y * y).toDouble()) as Int
                }

                if (percents.size > currentLevel + 1) {
                    currentStart = (totalLength * percents[currentLevel]).toInt()
                    currentEnd = (totalLength * percents[currentLevel + 1]).toInt()
                    currentStartAlpha = alphas[currentLevel]
                    currentEndAlpha = alphas[currentLevel + 1]

                    if (distance >= currentStart && distance <= currentEnd) {
                        val offset = distance - currentStart
                        if (currentEnd - currentStart != 0) {
                            val offsetPercent = offset.toFloat() / (currentEnd - currentStart)
                            val targetAlpha =
                                currentStartAlpha + offsetPercent * (currentEndAlpha - currentStartAlpha)

                            a = (a * targetAlpha).toInt()
                            if (a > 255) {
                                a = 255
                            } else if (a < 0) {
                                a = 0
                            }
                        }
                    }
                    if (distance == currentEnd) {
                        currentLevel++
                    }
                }

                color = Color.argb(a, r, g, b)
                newPx[i] = color
            }
        }
        bmp.setPixels(newPx, 0, width, 0, 0, width, height)
        return bmp
    }

    /**
     * 纵向混合颜色
     */
    fun changeBitmapMixColorVerticalRange(
        bitmap: Bitmap?,
        percents: FloatArray,
        colors: IntArray,
        useBackAlpha: Boolean
    ): Bitmap? {
        if (bitmap == null) {
            return null
        }

        val width = bitmap.width
        val height = bitmap.height
        val bmp = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val oldPx = IntArray(width * height) //用来存储旧的色素点的数组
        val newPx = IntArray(width * height) //用来存储新的像素点的数组
        var color: Int //用来存储原来颜色值
        var r: Int
        var g: Int
        var b: Int
        var a: Int //存储颜色的四个分量：红，绿，蓝，透明度

        var currentLevel = 0
        var currentStartY = 0
        var currentEndY = 0

        var currentStartA = 0
        var currentStartR = 0
        var currentStartG = 0
        var currentStartB = 0
        var currentEndA = 0
        var currentEndR = 0
        var currentEndG = 0
        var currentEndB = 0

        bitmap.getPixels(oldPx, 0, width, 0, 0, width, height)
        for (x in 0 until width) {
            currentLevel = 0
            for (y in 0 until height) {
                val i = y * width + x
                color = oldPx[i]

                a = Color.alpha(color)

                var foreColor = color

                if (percents.size > currentLevel + 1) {
                    currentStartY = (height * percents[currentLevel]).toInt()
                    currentEndY = (height * percents[currentLevel + 1]).toInt()

                    val currentStartColor = colors[currentLevel]
                    val currentEndColor = colors[currentLevel + 1]

                    currentStartA = Color.alpha(currentStartColor)
                    currentEndA = Color.alpha(currentEndColor)
                    currentStartR = Color.red(currentStartColor)
                    currentEndR = Color.red(currentEndColor)
                    currentStartG = Color.green(currentStartColor)
                    currentEndG = Color.green(currentEndColor)
                    currentStartB = Color.blue(currentStartColor)
                    currentEndB = Color.blue(currentEndColor)

                    if (y >= currentStartY && y <= currentEndY) {
                        val offsetY = y - currentStartY
                        if (currentEndY - currentStartY != 0) {
                            val offsetPercent = offsetY.toFloat() / (currentEndY - currentStartY)
                            val targetA =
                                currentStartA + (offsetPercent * (currentEndA - currentStartA)).toInt()
                            val targetR =
                                currentStartR + (offsetPercent * (currentEndR - currentStartR)).toInt()
                            val targetG =
                                currentStartG + (offsetPercent * (currentEndG - currentStartG)).toInt()
                            val targetB =
                                currentStartB + (offsetPercent * (currentEndB - currentStartB)).toInt()
                            foreColor = Color.argb(
                                if (useBackAlpha) a else targetA,
                                targetR,
                                targetG,
                                targetB
                            )
                        }
                    }
                    if (y == currentEndY) {
                        currentLevel++
                    }
                }

                color = ColorUtils.compositeColors(foreColor, color)
                newPx[i] = color
            }
        }
        bmp.setPixels(newPx, 0, width, 0, 0, width, height)
        return bmp
    }
}
