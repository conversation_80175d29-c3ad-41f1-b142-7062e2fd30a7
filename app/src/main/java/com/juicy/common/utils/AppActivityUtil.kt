package com.juicy.common.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import java.lang.reflect.InvocationTargetException

object AppActivityUtil {
    @JvmStatic
    val currentActivity: Activity?
        @SuppressLint("PrivateApi", "DiscouragedPrivateApi")
        get() {
            try {
                val activityThreadClass =
                    Class.forName("android.app.ActivityThread")
                val activityThread =
                    activityThreadClass.getMethod("currentActivityThread").invoke(
                        null
                    )
                val activitiesField = activityThreadClass.getDeclaredField("mActivities")
                activitiesField.isAccessible = true
                val activities =
                    activitiesField[activityThread] as Map<*, *>
                for (activityRecord in activities.values) {
                    val javaClass = activityRecord?.javaClass
                    val pausedField =
                        javaClass?.getDeclaredField("paused")
                    pausedField?.isAccessible = true
                    if (pausedField?.getBoolean(activityRecord) == false) {
                        val activityField =
                            javaClass.getDeclaredField("activity")
                        activityField.isAccessible = true
                        val activity =
                            activityField[activityRecord] as? Activity
                        return activity
                    }
                }
            } catch (e: ClassNotFoundException) {
                e.printStackTrace()
            } catch (e: InvocationTargetException) {
                e.printStackTrace()
            } catch (e: NoSuchMethodException) {
                e.printStackTrace()
            } catch (e: NoSuchFieldException) {
                e.printStackTrace()
            } catch (e: IllegalAccessException) {
                e.printStackTrace()
            }
            return null
        }

    //解决在dialog强制转换成activity
    fun scanForActivity(cont: Context?): Activity? {
        if (cont == null) return null
        else if (cont is Activity) return cont
        else if (cont is ContextWrapper) return scanForActivity(
            cont.baseContext
        )
        return null
    }

    /**
     * 判断Activity是否Destroy
     */
    @JvmStatic
    fun isDestroy(mActivity: Activity?): Boolean {
        return null == mActivity || mActivity.isFinishing || mActivity.isDestroyed
    }

    fun startActivity(activity: Class<*>?) {
        if (!currentActivity!!.isDestroyed) {
            val intent = Intent(currentActivity, activity)
            currentActivity!!.startActivity(intent)
        }
    }
}
