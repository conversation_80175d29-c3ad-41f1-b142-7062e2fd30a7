package com.juicy.common.utils

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import android.text.format.Time
import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.GregorianCalendar
import java.util.Locale
import java.util.TimeZone
import java.util.concurrent.TimeUnit

object DateMetaUtil {
    val zodiacArr: Array<String> = arrayOf(
        "猴", "鸡", "狗", "猪", "鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊"
    )

    val constellationArr: Array<String> = arrayOf(
        "水瓶座",
        "双鱼座",
        "白羊座",
        "金牛座",
        "双子座",
        "巨蟹座",
        "狮子座",
        "处女座",
        "天秤座",
        "天蝎座",
        "射手座",
        "魔羯座"
    )

    val constellationEdgeDay: IntArray = intArrayOf(
        20, 19, 21, 21, 21, 22, 23, 23, 23, 23, 22, 22
    )

    val enMonth: Array<String> = arrayOf(
        "JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"
    )

    val dayOfWeek: Array<String> = arrayOf(
        "周日", "周一", "周二", "周三", "周四", "周五", "周六"
    )

    val dayOfWeekEng: Array<String> = arrayOf(
        "SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"
    )

    const val DAY_TIME_MILLIS: Long = 24L * 60 * 60 * 1000

    /**
     * 根据时间获取英文月份
     *
     * @param time
     * @return
     */
    fun date2EnMonth(time: Calendar): String {
        val month = time[Calendar.MONTH]
        return enMonth[month]
    }

    /**
     * 根据时间获取周几
     *
     * @param time
     * @return
     */
    fun date2Week(time: Calendar): String {
        val locale = Locale.getDefault()
        Locale.setDefault(Locale.CHINA)
        val week = time[Calendar.DAY_OF_WEEK]
        val day2Week = dayOfWeek[week - 1]
        Locale.setDefault(locale)
        return day2Week
    }

    fun date2WeekEng(time: Calendar): String {
        val locale = Locale.getDefault()
        Locale.setDefault(Locale.CHINA)
        val week = time[Calendar.DAY_OF_WEEK]
        val day2Week = dayOfWeekEng[week - 1]
        Locale.setDefault(locale)
        return day2Week
    }


    /**
     * 根据日期获取星座
     *
     * @param time
     * @return
     */
    fun date2Constellation(time: Calendar): String {
        var month = time[Calendar.MONTH]
        val day = time[Calendar.DAY_OF_MONTH]
        if (day < constellationEdgeDay[month]) {
            month = month - 1
        }
        if (month >= 0) {
            return constellationArr[month]
        }
        // default to return 魔羯
        return constellationArr[11]
    }

    /**
     * 根据日期获取生肖
     *
     * @return
     */
    fun date2Zodica(time: Calendar): String {
        return zodiacArr[time[Calendar.YEAR] % 12]
    }

    @SuppressLint("SimpleDateFormat")
    @JvmStatic
    fun formatDate(date: Date, format: String?): String {
        val formater = SimpleDateFormat(format)
        return formater.format(date)
    }

    /**
     * 格式化日期
     *
     * @param datetime
     * @param format
     * @return
     */
    @SuppressLint("SimpleDateFormat")
    fun formatDate(datetime: String, format: String?): String {
        val formater = SimpleDateFormat(format)
        try {
            val date = formater.parse(datetime)
            return formater.format(date?: Date())
        } catch (e: ParseException) {
            return datetime
        }
    }

    // 1) 当前所处的自然十年（decade）不足5年，则不聚合；＞5年，则聚合第一个五年，不聚合剩余年份
    //
    // 如：当前2023年，则显示 2023    2022    2021
    // 当前2029年，则显示 2029    2028    2027    2026    2021-2025
    //
    // 2) 上一个十年拆成五年显示，其余年份每10年一聚合
    fun formatDateYear(dateTime: String): String {
        try {
            val nowYear = curYear
            val recentTenYear = nowYear - nowYear % 10 - 10
            val recentFiveYear = nowYear - nowYear % 5
            var publishYear = formatDate(dateTime, "yyyy").toInt()
            val moreTenYears =
                nowYear - nowYear % 10 - publishYear >= 10 || recentTenYear >= publishYear
            val moreFiveYears = nowYear - publishYear >= 5 || recentFiveYear >= publishYear
            if (moreTenYears) {
                publishYear = if (publishYear % 10 == 0) publishYear - 1 else publishYear
                val less = publishYear - publishYear % 10 + 1
                val more = publishYear - publishYear % 10 + 10
                return "$less-$more"
            } else if (moreFiveYears) {
                publishYear = if (publishYear % 5 == 0) publishYear - 1 else publishYear
                val less = publishYear - publishYear % 5 + 1
                val more = publishYear - publishYear % 5 + 5
                return "$less-$more"
            } else {
                return publishYear.toString()
            }
        } catch (e: NumberFormatException) {
            return dateTime
        }
    }

    /**
     *
     * @param datetime 完整日期格式 例：2020-06-04 20:14:33
     * @param format
     * @return
     */
    fun formatDateWithCompleteDate(datetime: String, format: String?): String {
        try {
            return SimpleDateFormat(format?:"", Locale.CHINA).format(
                SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA).parse(datetime)?: Date()
            )
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return datetime
    }

    /**
     * 格式化日期
     *
     * @param datetime
     * @param format
     * @return
     */
    @SuppressLint("SimpleDateFormat")
    fun formatStringToDate(datetime: String, format: String?): Date? {
        val formater = SimpleDateFormat(format)
        try {
            val date = formater.parse(datetime)
            return date
        } catch (e: Exception) {
            return null
        }
    }

    /**
     * 格式化日期
     *
     * @param datetime     输入的日期
     * @param inputFormat  输入的日期格式
     * @param outputFormat 输出的日期格式
     * @return
     */
    @SuppressLint("SimpleDateFormat")
    fun formatDate(datetime: String, inputFormat: String?, outputFormat: String?): String {
        val formater = SimpleDateFormat(inputFormat)
        try {
            val date = formater.parse(datetime)
            return formatDate(date?:Date(), outputFormat)
        } catch (e: ParseException) {
            return ""
        }
    }

    val curDateTime: String
        get() = formatDate(Date(), "yyyy-MM-dd HH:mm:ss")

    val monthDate: String
        get() = formatDate(Date(), "yyyyMM")

    fun getDate(calendar: Calendar): String {
        return formatDate(calendar.time, "yyyy-MM-dd")
    }

    val curDate: String
        get() = formatDate(Date(), "yyyy-MM-dd")

    val curDateLong: Long
        /**
         * 返回可比较
         * @return
         */
        get() = formatDate(Date(), "yyyyMMdd").toLong()

    val curZeroTime: Long
        //今天零点零分零秒的毫秒数
        get() {
            val calendar = Calendar.getInstance()
            calendar.timeZone = TimeZone.getTimeZone("GMT+8:00")
            calendar.timeInMillis = System.currentTimeMillis()
            calendar[Calendar.HOUR_OF_DAY] = 0
            calendar[Calendar.MINUTE] = 0
            calendar[Calendar.SECOND] = 0
            val zero = calendar.time
            return zero.time
        }

    @SuppressLint("SimpleDateFormat")
    fun dateToString(data: Date, formatType: String?): String {
        return SimpleDateFormat(formatType).format(data)
    }

    @SuppressLint("SimpleDateFormat")
    @Throws(ParseException::class)
    fun stringToDate(strTime: String, formatType: String?): Date? {
        val formatter = SimpleDateFormat(formatType)
        val date = formatter.parse(strTime)
        return date
    }

    fun getAgeFromBirthday(birthday: String): String {
        if (TextUtils.isEmpty(birthday)) {
            return ""
        }
        try {
            val date = stringToDate(birthday, "yyyy-MM-dd")
            val year = ((Calendar.getInstance().get(Calendar. YEAR) - 1900) + 1900).toString()
            if (year.length != 4) {
                return ""
            }
            val tens = year.substring(2, 3)
            val units = year.substring(3, 4)
            return if (units.toInt() >= 5) {
                tens + "5后"
            } else {
                tens + "0后"
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    fun getDayWeek(date: Date): String {
        val weeks = arrayOf(
            "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"
        )
        val cal = Calendar.getInstance()
        cal.time = date
        val dayinweek = cal[Calendar.DAY_OF_WEEK] - 1
        return weeks[dayinweek]
    }

    fun getAgeOnlyFromBirthday(birthday: String): String {
        if (TextUtils.isEmpty(birthday)) {
            return ""
        }
        try {
            val date = stringToDate(birthday, "yyyy-MM-dd")
            val age = Date().year - date!!.year
            return age.toString() + "岁"
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    /**
     * 返回指定格式的日期
     *
     * @param format
     * @param milliseconds 毫秒数
     * @return
     */
    @SuppressLint("SimpleDateFormat")
    @JvmStatic
    fun getDate(format: String?, milliseconds: Long): String {
        try {
            val formater = SimpleDateFormat(format)
            return formater.format(Date(milliseconds))
        } catch (e: Exception) {
            return ""
        }
    }

    /**
     * 将毫秒转换成小时：分钟：秒格式
     */
    @JvmStatic
    fun ms2HMS(_ms: Long): String {
        var _ms = _ms
        val HMStime: String
        _ms /= 1000
        val hour = (_ms / 3600).toInt()
        val mint = ((_ms % 3600) / 60).toInt()
        val sed = (_ms % 60).toInt()
        var hourStr = hour.toString()
        if (hour < 10) {
            hourStr = "0$hourStr"
        }
        var mintStr = mint.toString()
        if (mint < 10) {
            mintStr = "0$mintStr"
        }
        var sedStr = sed.toString()
        if (sed < 10) {
            sedStr = "0$sedStr"
        }
        HMStime = "$hourStr:$mintStr:$sedStr"
        return HMStime
    }

    val currentDayMillis: Long
        /**
         * 获取今天0点的时间戳
         * @return
         */
        get() {
            val now = System.currentTimeMillis()
            val sdfOne = SimpleDateFormat("yyyy-MM-dd")
            try {
                return sdfOne.parse(sdfOne.format(now)).time
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return -1
        }

    val currentWeek: Int
        get() {
            val calendar = Calendar.getInstance()
            val week = calendar[Calendar.DAY_OF_WEEK] - 1
            return week
        }

    // public static String getAppStartTime() {
    // return formatDate(new Date(BackAppWrapper.mStartTime == -1 ?
    // ForeAppWrapper.sStartTime
    // : BackAppWrapper.mStartTime), "yyyy-MM-dd HH:mm:ss");
    // }
    /**
     * 生成一个日期
     *
     * @param datetime 　基准日期
     * @param format   　日期格式
     * @param days     　与基准日期相差的天数
     * @return
     */
    fun getDate(datetime: String, format: String?, days: Int): String {
        var newDate = ""
        val formater = SimpleDateFormat("yyyy-MM-dd")
        try {
            val date = formater.parse(datetime)
            val calendar = Calendar.getInstance()
            calendar.time = date
            calendar.add(Calendar.DATE, days)
            newDate = formater.format(calendar.time)
        } catch (e: ParseException) {
        }
        return newDate
    }

    /**
     * 根据一个日期，取得时分秒等信息
     *
     * @param time
     * @param field 　Calendar字段
     * @return
     */
    fun getDateField(time: Long, field: Int): Int {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = time
        return calendar[field]
    }

    /**
     * 检测第一个时间是否在第二个时间之前
     *
     * @param firstDate
     * @param secondDate
     * @return 第一个时间在第二个时间之前则返回true
     */
    fun isFirstDateBeforeSecondDate(firstDate: Long, secondDate: Long): Boolean {
        val firstCalendar = Calendar.getInstance()
        firstCalendar.timeInMillis = firstDate
        val secondCalendar = Calendar.getInstance()
        secondCalendar.timeInMillis = secondDate
        val result = firstCalendar.compareTo(secondCalendar)
        return result <= 0
    }

    /**
     * 检测是否在兩個時間段內
     *
     * @param start
     * @param end
     * @return
     */
    fun isBetweenDates(start: Date, end: Date): Boolean {
        val firstCalendar = Calendar.getInstance()
        firstCalendar.time = start
        val secondCalendar = Calendar.getInstance()
        secondCalendar.time = end
        val target = Calendar.getInstance()
        target.time = Date(System.currentTimeMillis())

        val result1 = firstCalendar.compareTo(target)
        val result2 = secondCalendar.compareTo(target)
        return result1 <= 0 && result2 >= 0
    }

    /**
     * 判断某一时间是否在一个区间内(24h)
     * @param sourceTime 时间区间,如10:00-20:00(包含左右值)
     * @param curTime 需要判断的时间 ms
     * @throws IllegalArgumentException
     */
    fun isInTime(sourceTime: String, curTime: Long): Boolean {
        require(!(sourceTime == null || !sourceTime.contains("-") || !sourceTime.contains(":"))) { "Illegal Argument arg:$sourceTime" }
        require(curTime >= 0) { "Illegal Argument arg:$curTime" }
        val args = sourceTime.split("-".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        try {
            if (args[1] == "00:00") {
                args[1] = "24:00"
            }
            val sdf = SimpleDateFormat("HH:mm", Locale.CHINA)
            val now = sdf.parse(sdf.format(curTime)).time
            val start = sdf.parse(args[0]).time
            val end = sdf.parse(args[1]).time
            return if (end < start) {
                now <= end || now >= start
            } else {
                now >= start && now <= end
            }
        } catch (e: Throwable) {
            e.printStackTrace()
            throw IllegalArgumentException("Illegal Argument arg:$sourceTime")
        }
    }

    /**
     * 时间转化字符串
     *
     * @param context
     * @param secs    秒
     * @return
     */
    fun makeTimeString(context: Context?, secs: Long): String {
        val durationformat = if (secs < 3600) "%2$02d:%5$02d" else "%1\$d:%3$02d:%5$02d"

        val timeArgs = arrayOfNulls<Any>(5)
        timeArgs[0] = secs / 3600
        timeArgs[1] = secs / 60
        timeArgs[2] = (secs / 60) % 60
        timeArgs[3] = secs
        timeArgs[4] = secs % 60

        return String.format(durationformat, *timeArgs)
    }

    /**
     * 时间转化字符串
     *
     * @param secs    00:00秒
     * @return
     */
    fun makeTimeColonString(secs: Long): String {
        val durationformat = "%2$02d:%5$02d"

        val timeArgs = arrayOfNulls<Any>(5)
        timeArgs[1] = secs / 60
        timeArgs[4] = secs % 60
        return String.format(durationformat, *timeArgs)
    }

    /**
     * 时间转化字符串 5'12''格式
     * 超过小时也不转为小时
     *
     * @param secs 秒
     * @return
     */
    @SuppressLint("DefaultLocale")
    fun makeTimeString(secs: Int): String {
        val durationformat = "%2\$d'%5$02d''"
        val timeArgs = arrayOfNulls<Any>(5)
        //        timeArgs[0] = secs / 3600;
        timeArgs[1] = secs / 60
        //        timeArgs[2] = (secs / 60) % 60;
//        timeArgs[3] = secs;
        timeArgs[4] = secs % 60

        return String.format(durationformat, *timeArgs)
    }

    fun checkIsToday(`when`: Long): Boolean {
        val time = Time()
        time.set(`when`)

        val thenYear = time.year
        val thenMonth = time.month
        val thenMonthDay = time.monthDay

        time.set(System.currentTimeMillis())
        return (thenYear == time.year)
                && (thenMonth == time.month)
                && (thenMonthDay == time.monthDay)
    }

    fun checkSystemTimeIsToday(`when`: Long): Boolean {
        val time = Time()
        time.set(`when`)

        val thenYear = time.year
        val thenMonth = time.month
        val thenMonthDay = time.monthDay

        time.set(System.currentTimeMillis())
        return (thenYear == time.year)
                && (thenMonth == time.month)
                && (thenMonthDay == time.monthDay)
    }


    @SuppressLint("SimpleDateFormat")
    @JvmOverloads
    fun parseDateTime(datetime: String?, pattern: String? = "yyyy-MM-dd HH:mm:ss"): Long {
        val dateFormat = SimpleDateFormat(pattern)
        try {
            if (!datetime.isNullOrEmpty()) {
                return dateFormat.parse(datetime)?.time ?: -1
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return -1
    }


    fun formatDateWipeYear(birthday: String): String {
        if (TextUtils.isEmpty(birthday)) {
            return ""
        }
        try {
            val date = stringToDate(birthday, "yyyy-MM-dd")
            val format = SimpleDateFormat("MMdd")
            return format.format(date)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    val curDateMMdd: String
        get() {
            try {
                val format = SimpleDateFormat("MMdd")
                return format.format(Date())
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return ""
        }


    /**
     * 格式化 MM-dd HH:mm
     *
     * @param time 时间戳，单位是秒
     */
    fun formatDate(time: Int): String {
        if (time <= 0) {
            return ""
        }
        val nowTime = Date(time * 1000L)
        val format = SimpleDateFormat("MM-dd HH:mm")
        return format.format(nowTime)
    }

    /**
     * 格式化 MM-dd HH:mm
     *
     * @param time 时间戳，单位是毫秒
     */
    @JvmStatic
    fun formatDate(time: Long): String {
        if (time <= 0) {
            return ""
        }
        val nowTime = Date(time)
        val format = SimpleDateFormat("MM-dd HH:mm")
        return format.format(nowTime)
    }

    /***
     *
     * @param time 时间戳，单位是毫秒
     * @param fd 日期格式
     * @return
     */
    fun formatDate(time: Long, fd: String?): String {
        if (time <= 0) {
            return ""
        }
        val nowTime = Date(time)
        val format = SimpleDateFormat(fd)
        return format.format(nowTime)
    }

    /**
     * 格式化 毫秒数: 00:00
     */
    @JvmOverloads
    fun formatMillisSecond(l: Long, withHourIfZeroHour: Boolean = false): String {
        if (l == 0L) {
            return "00:00"
        }
        var time = l
        val h = TimeUnit.MILLISECONDS.toHours(time)
        time -= TimeUnit.HOURS.toMillis(h)
        val m = TimeUnit.MILLISECONDS.toMinutes(time)
        time -= TimeUnit.MINUTES.toMillis(m)
        val s = TimeUnit.MILLISECONDS.toSeconds(time)
        if (h > 0) {
            return String.format("%02d:%02d:%02d", h, m, s)
        } else {
            if (withHourIfZeroHour) {
                return String.format("%02d:%02d:%02d", 0, m, s)
            }
            return String.format("%02d:%02d", m, s)
        }
    }

    /**
     * 格式化 秒数: 00:00
     */
    fun formatSecond(second: Long): String {
        val h = second / (60 * 60)
        val m = (second - 60 * 60 * h) / 60
        val s = second % 60
        var result = ""
        result = if (h > 0) {
            String.format("%02d:%02d:%02d", h, m, s)
        } else {
            String.format("%02d:%02d", m, s)
        }
        return result
    }

    /**
     * 格式化 秒数: 00:00 不转成小时 只有分:秒
     */
    fun formatNoHoursSecond(second: Long): String {
        val m = second / 1000 / 60
        val s = second / 1000 % 60
        var result = ""
        result = String.format("%02d:%02d", m, s)
        return result
    }

    /**
     * 格式化 秒数: 00:00 倒计时
     *
     * @param time
     * @return
     */
    fun phpTimeTransform(time: Long): String {
        val diff = System.currentTimeMillis() / 1000 - time
        return timeTransform(diff.toInt().toLong())
    }

    /**
     * 根据时间转换成相应的字符串提示语
     *
     * @param time 时间（单位:秒）
     * @return
     */
    fun timeTransform(time: Long): String {
        var s = "刚刚"
        if (time < 60) {
            s = "刚刚"
        } else if (time > 59 && time < 3600) {
            s = (time / 60).toString() + "分钟前"
        } else if (time > 3599 && time < 3600 * 24) {
            s = (time / 3600).toString() + "小时前"
        } else if (time > 86399) {
            s = (time / 86400).toString() + "天前"
        }
        return s
    }

    /**
     * @param startDay    需要比较的时间 不能为空(null),需要正确的日期格式 ,如：2009-09-12
     * @param endDay      被比较的时间  为空(null)则为当前时间
     * @param stype       返回值类型   0为多少天，1为多少个月，2为多少年
     * @param formatStyle 格式化类型
     * @return 举例：
     * compareDate("2009-09-12", null, 0);//比较天
     * compareDate("2009-09-12", null, 1);//比较月
     * compareDate("2009-09-12", null, 2);//比较年
     */
    @SuppressLint("SimpleDateFormat")
    fun compareDate(startDay: String, endDay: String?, stype: Int, formatStyle: String?): Int {
        var endDay = endDay
        var n = 0
        endDay = endDay ?: formatDate(Date(), formatStyle)
        val df: DateFormat = SimpleDateFormat(formatStyle)
        val c1 = Calendar.getInstance()
        val c2 = Calendar.getInstance()
        try {
            c1.time = df.parse(startDay)?: Date()
            c2.time = df.parse(endDay)?:Date()
        } catch (e3: Exception) {
            println("wrong occured")
        }
        while (!c1.after(c2)) {                   // 循环对比，直到相等，n 就是所要的结果
            n++
            if (stype == 1) {
                c1.add(
                    Calendar.MONTH,
                    1
                ) //System.out.println(startDay + " -- " + endDay + " 相差多少" + u[stype] + ":" + n); 比较月份，月份+1
            } else {
                c1.add(Calendar.DATE, 1) // 比较天数，日期+1
            }
        }
        n = n - 1
        if (stype == 2) {
            n = n / 365
        }
        return n
    }

    val systemTimeToMins: Int
        /**
         * 当前系统时分换算成分钟数
         *
         * @return
         */
        get() {
            val c = Calendar.getInstance()
            val hourSystem = c[Calendar.HOUR_OF_DAY]
            val minSystem = c[Calendar.MINUTE]
            val systemTime = (hourSystem * 60) + minSystem
            return systemTime
        }


    const val YYYY_MM_DD_I: String = "yyyy/MM/dd"
    const val YYYY_MM_DD_HH_MM_SS: String = "yyyy-MM-dd hh:mm:ss"
    const val YYYY_MM_DD_HH_MM_SS_SSS: String = "yyyy-MM-dd HH:mm:ss.SSS"
    const val YYYY_MM_DD: String = "yyyy-MM-dd"
    const val YYYY_MM_DD_HH_MM: String = "yyyy-MM-dd hh:mm"
    const val MMDDHHMMSS: String = "MMddhhmmss"
    const val MMDD_HHMM: String = "MM/dd HH:mm" //10/05 19:00

    /**
     * * 格式化日期模式
     *
     * @param dataTime   yyyy-MM-dd hh:mm:ss 或 yyyy-MM-dd的格式
     * @param outPattern
     * @return yyyy-MM-dd的格式
     */
    fun autoFormatDate(dataTime: String, outPattern: String?): String {
        if (TextUtils.isEmpty(dataTime)) {
            return ""
        }
        var data = formatDate(dataTime, YYYY_MM_DD_HH_MM_SS, outPattern)
        if (!TextUtils.isEmpty(data)) {
            return data
        }
        data = formatDate(dataTime, YYYY_MM_DD, outPattern)
        if (!TextUtils.isEmpty(data)) {
            return data
        }
        data = formatDate(dataTime, YYYY_MM_DD_I, outPattern)
        if (!TextUtils.isEmpty(data)) {
            return data
        }
        data = formatDate(dataTime, YYYY_MM_DD_HH_MM, outPattern)
        return if (!TextUtils.isEmpty(data)) {
            data
        } else {
            dataTime
        }
    }

    fun formatToTimestamp(dateTime: String, inputFormat: String?): Long {
        try {
            val format = SimpleDateFormat(inputFormat, Locale.CHINA)
            val date = format.parse(dateTime)
            return date!!.time
        } catch (e: Exception) {
        }
        return -1
    }

    fun checkDateFormat(dataTime: String?): Boolean {
        if (TextUtils.isEmpty(dataTime)) {
            return false
        }
        try {
            val format = SimpleDateFormat("yyyy-MM-dd")
            format.isLenient = false
            val date = format.parse(dataTime)
        } catch (ex: Exception) {
            ex.printStackTrace()
            return false
        }
        return true
    }

    fun autoFormatDate(dataTime: String): String {
        return autoFormatDate(dataTime, YYYY_MM_DD)
    }

    fun autoFormatDateI(dataTime: String): String {
        return autoFormatDate(dataTime, YYYY_MM_DD_I)
    }

    fun getmmddhhmmss(): String {
        return formatDate(Date(), MMDDHHMMSS)
    }


    fun isSameDay(startDay: Long, endDay: Long): Boolean {
        val date0 = Date(startDay)
        val date1 = Date(endDay)
        val ca0 = GregorianCalendar()
        val ca1 = GregorianCalendar()
        ca0.time = date0
        ca1.time = date1
        return ca0[GregorianCalendar.YEAR] == ca1[GregorianCalendar.YEAR] && ca0[GregorianCalendar.MONTH] == ca1[GregorianCalendar.MONTH] && ca0[GregorianCalendar.DAY_OF_MONTH] == ca1[GregorianCalendar.DAY_OF_MONTH]
    }

    fun getNewSongOrderDateDesc(serviceTime: Long, publishTime: Long): String {
        val fromCalendar = Calendar.getInstance()
        fromCalendar.timeInMillis = serviceTime
        fromCalendar[Calendar.HOUR_OF_DAY] = 0
        fromCalendar[Calendar.MINUTE] = 0
        fromCalendar[Calendar.SECOND] = 0
        fromCalendar[Calendar.MILLISECOND] = 0

        val toCalendar = Calendar.getInstance()
        toCalendar.timeInMillis = publishTime
        toCalendar[Calendar.HOUR_OF_DAY] = 0
        toCalendar[Calendar.MINUTE] = 0
        toCalendar[Calendar.SECOND] = 0
        toCalendar[Calendar.MILLISECOND] = 0

        val day = ((toCalendar.time.time - fromCalendar.time.time) / (1000L * 60 * 60 * 24)).toInt()
        return if (day == 0) {
            "今天"
        } else if (day == 1) {
            "明天"
        } else if (toCalendar[Calendar.YEAR] == fromCalendar[Calendar.YEAR]) {
            (toCalendar[Calendar.MONTH] + 1).toString() + "月" + toCalendar[Calendar.DAY_OF_MONTH] + "日"
        } else {
            toCalendar[Calendar.YEAR].toString() + "年" + (toCalendar[Calendar.MONTH] + 1) + "月" + toCalendar[Calendar.DAY_OF_MONTH] + "日"
        }
    }


    // 判断是否是闰年
    fun isLeap(year: Int): Boolean {
        return if (year % 4 == 0 && year % 100 != 0) {
            true
        } else year % 100 == 0 && year % 400 == 0
    }

    // 判断是合法日期
    fun isValidDate(year: Int, month: Int, day: Int): Boolean {
        if (year > 0 && month > 0 && month < 13 && day > 0 && day < 32) {
            if (month == 2) {
                if (isLeap(year) && day < 30) {
                    return true
                }
                return !isLeap(year) && day < 29
            } else if ((month == 4 || month == 6 || month == 9 || month == 11) && day < 31) {
                return true
            } else return month == 1 || month == 3 || month == 5 || month == 7 || month == 8 || month == 10 || month == 12
        } else {
            return false
        }
    }

    /**
     *     * 获取两个日期之间的间隔天数
     *     * @return
     *     
     */
    fun getDayInterval(startTime: Long, endTime: Long): Int {
        val fromCalendar = Calendar.getInstance()
        fromCalendar.timeInMillis = startTime
        fromCalendar[Calendar.HOUR_OF_DAY] = 0
        fromCalendar[Calendar.MINUTE] = 0
        fromCalendar[Calendar.SECOND] = 0
        fromCalendar[Calendar.MILLISECOND] = 0

        val toCalendar = Calendar.getInstance()
        toCalendar.timeInMillis = endTime
        toCalendar[Calendar.HOUR_OF_DAY] = 0
        toCalendar[Calendar.MINUTE] = 0
        toCalendar[Calendar.SECOND] = 0
        toCalendar[Calendar.MILLISECOND] = 0

        return ((toCalendar.time.time - fromCalendar.time.time) / (1000L * 60 * 60 * 24)).toInt()
    }

    val curYear: Int
        get() {
            val calendar = Calendar.getInstance()
            calendar.time = Date()
            return calendar[Calendar.YEAR]
        }

    fun isExpiredTime(date: String): Boolean {
        val calendar = Calendar.getInstance()
        try {
            calendar.time = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date)
            val targetDate = calendar.timeInMillis
            return System.currentTimeMillis() > targetDate
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return false
    }

    /**
     * 将一个时间(不是时刻),如666秒钟,转化成格式 00:00或者00:00:00,
     *
     * @param time 单位 秒
     * @return
     */
    fun secToTime(time: Int): String {
        var timeStr: String? = null
        var hour = 0
        var minute = 0
        var second = 0
        if (time <= 0) return "00:00"
        else {
            minute = time / 60
            if (minute < 60) {
                second = time % 60
                timeStr = unitFormat(minute) + ":" + unitFormat(second)
            } else {
                hour = minute / 60
                if (hour > 99) return "99:59:59"
                minute = minute % 60
                second = time - hour * 3600 - minute * 60
                timeStr = unitFormat(hour) + ":" + unitFormat(minute) + ":" + unitFormat(second)
            }
        }
        return timeStr
    }

    /**
     * 保留两位显示整数  如6会显示成06,11显示成11
     */
    fun unitFormat(i: Int): String {
        var retStr: String? = null
        retStr = if (i >= 0 && i < 10) "0$i"
        else "" + i
        return retStr
    }

    /**
     * 根据时间转换成相应的字符串提示语
     *
     * @param time 时间（单位:秒）
     * @return
     */
    fun timeTransform2(time: Long): String {
        var s = ""
        val d = Date(time)
        val calendar = Calendar.getInstance()
        val a = calendar[Calendar.YEAR]
        calendar.time = d
        val b = calendar[Calendar.YEAR]
        val df = if (a == b) { //是本年
            SimpleDateFormat("MM月dd日")
        } else {
            SimpleDateFormat("yyyy年MM月dd日")
        }
        s = df.format(d) + getWhenStr(d)
        return s
    }

    /**
     * 获取时间对应的时分名称
     *
     * @param date
     * @return
     */
    fun getWhenStr(date: Date): String {
        val df = SimpleDateFormat("HH")
        val str = df.format(date)
        val a = str.toInt()
        if (a >= 0 && a <= 6) {
            return "凌晨"
        }
        if (a > 6 && a <= 12) {
            return "上午"
        }
        if (a > 12 && a <= 13) {
            return "中午"
        }
        if (a > 13 && a <= 18) {
            return "下午"
        }
        if (a > 18 && a <= 24) {
            return "晚上"
        }
        return ""
    }

    /**
     * 过去是否与现在相隔n天以内
     *
     * @param n    多少天
     * @param time 过去的时间点
     * @return
     */
    fun isLastNDay(n: Int, time: Long): Boolean {
        val curTime = System.currentTimeMillis()
        val betweenTime = curTime - time
        if (betweenTime < 0) {
            return false
        }
        return betweenTime / 1000 / 60 / 60 / 24 < n
    }

    fun isCurrentInTimeScope(
        startHour: Int,
        endHour: Int,
        hour: Int,
        endMinute: Int,
        minute: Int,
        startMinute: Int
    ): Boolean {
        var isInTimeScope = false
        if (startHour <= endHour) {
            if (startHour <= hour && hour <= endHour) {
                if (endHour == hour && startHour == hour && endMinute >= minute && startMinute <= minute) {
                    isInTimeScope = true
                } else if (endHour == hour && startHour != hour && endMinute >= minute) {
                    isInTimeScope = true
                } else if (startHour == hour && endHour != hour && startMinute <= minute) {
                    isInTimeScope = true
                } else if (startHour != hour && endHour != hour) {
                    isInTimeScope = true
                }
            }
        } else {
            if (startHour <= hour && hour <= 24 || hour <= endHour) {
                if (startHour == hour && startMinute <= minute) {
                    isInTimeScope = true
                } else if (endHour == hour && endMinute >= minute) {
                    isInTimeScope = true
                } else if (startHour != hour && endHour != hour) {
                    isInTimeScope = true
                }
            }
        }
        return isInTimeScope
    }
}
