package com.juicy.common.utils

import android.content.Context
import android.opengl.GLES20
import android.os.Build
import android.os.Environment
import android.util.Log
import java.io.File

/**
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 *
 * Copyright (C) 2013, <PERSON><PERSON> (http://www.skoumal.net)
 * 在原Repo基础上增加了一些判定
 */
object EmulatorDetector {
    private const val TAG = "EmulatorDetector"

    private var rating = -1

    fun isEmulatorAbsoluly(context: Context): Boolean {
        if (mayOnEmulatorViaQEMU(context)) {
            return true
        }

        if (Build.PRODUCT.contains("sdk") ||
            Build.PRODUCT.contains("sdk_x86") ||
            Build.PRODUCT.contains("sdk_google") ||
            Build.PRODUCT.contains("Andy") ||
            Build.PRODUCT.contains("Droid4X") ||
            Build.PRODUCT.contains("nox") ||
            Build.PRODUCT.contains("vbox86p") ||
            Build.PRODUCT.contains("aries")
        ) {
            return true
        }
        if (Build.MANUFACTURER == "Genymotion" ||
            Build.MANUFACTURER.contains("Andy") ||
            Build.MANUFACTURER.contains("nox") ||
            Build.MANUFACTURER.contains("TiantianVM")
        ) {
            return true
        }
        if (Build.BRAND.contains("Andy")) {
            return true
        }
        if (Build.DEVICE.contains("Andy") ||
            Build.DEVICE.contains("Droid4X") ||
            Build.DEVICE.contains("nox") ||
            Build.DEVICE.contains("vbox86p") ||
            Build.DEVICE.contains("aries")
        ) {
            return true
        }
        if (Build.MODEL.contains("Emulator") ||
            Build.MODEL == "google_sdk" ||
            Build.MODEL.contains("Droid4X") ||
            Build.MODEL.contains("TiantianVM") ||
            Build.MODEL.contains("Andy") ||
            Build.MODEL == "Android SDK built for x86_64" ||
            Build.MODEL == "Android SDK built for x86"
        ) {
            return true
        }
        if (Build.HARDWARE == "vbox86" ||
            Build.HARDWARE.contains("nox") ||
            Build.HARDWARE.contains("ttVM_x86")
        ) {
            return true
        }
        if (Build.FINGERPRINT.contains("generic/sdk/generic") ||
            Build.FINGERPRINT.contains("generic_x86/sdk_x86/generic_x86") ||
            Build.FINGERPRINT.contains("Andy") ||
            Build.FINGERPRINT.contains("ttVM_Hdragon") ||
            Build.FINGERPRINT.contains("generic/google_sdk/generic") ||
            Build.FINGERPRINT.contains("vbox86p") ||
            Build.FINGERPRINT.contains("generic/vbox86p/vbox86p")
        ) {
            return true
        }

        return false
    }

    /**
     * Detects if app is currenly running on emulator, or real device.
     *
     * @return true for emulator, false for real devices
     */
    fun isEmulator(context: Context): Boolean {
        if (isEmulatorAbsoluly(context)) {
            return true
        }
        var newRating = 0
        if (rating < 0) {
            if (Build.PRODUCT.contains("sdk") ||
                Build.PRODUCT.contains("Andy") ||
                Build.PRODUCT.contains("ttVM_Hdragon") ||
                Build.PRODUCT.contains("google_sdk") ||
                Build.PRODUCT.contains("Droid4X") ||
                Build.PRODUCT.contains("nox") ||
                Build.PRODUCT.contains("sdk_x86") ||
                Build.PRODUCT.contains("sdk_google") ||
                Build.PRODUCT.contains("vbox86p") ||
                Build.PRODUCT.contains("aries")
            ) {
                newRating++
            }

            if (Build.MANUFACTURER == "unknown" ||
                Build.MANUFACTURER == "Genymotion" ||
                Build.MANUFACTURER.contains("Andy") ||
                Build.MANUFACTURER.contains("MIT") ||
                Build.MANUFACTURER.contains("nox") ||
                Build.MANUFACTURER.contains("TiantianVM")
            ) {
                newRating++
            }

            if (Build.BRAND == "generic" ||
                Build.BRAND == "generic_x86" ||
                Build.BRAND == "TTVM" ||
                Build.BRAND.contains("Andy")
            ) {
                newRating++
            }

            if (Build.DEVICE.contains("generic") ||
                Build.DEVICE.contains("generic_x86") ||
                Build.DEVICE.contains("Andy") ||
                Build.DEVICE.contains("ttVM_Hdragon") ||
                Build.DEVICE.contains("Droid4X") ||
                Build.DEVICE.contains("nox") ||
                Build.DEVICE.contains("generic_x86_64") ||
                Build.DEVICE.contains("vbox86p") ||
                Build.DEVICE.contains("aries")
            ) {
                newRating++
            }

            if (Build.MODEL == "sdk" ||
                Build.MODEL.contains("Emulator") ||
                Build.MODEL == "google_sdk" ||
                Build.MODEL.contains("Droid4X") ||
                Build.MODEL.contains("TiantianVM") ||
                Build.MODEL.contains("Andy") ||
                Build.MODEL == "Android SDK built for x86_64" ||
                Build.MODEL == "Android SDK built for x86"
            ) {
                newRating++
            }

            if (Build.HARDWARE == "goldfish" ||
                Build.HARDWARE == "vbox86" ||
                Build.HARDWARE.contains("nox") ||
                Build.HARDWARE.contains("ttVM_x86")
            ) {
                newRating++
            }

            if (Build.FINGERPRINT.contains("generic/sdk/generic") ||
                Build.FINGERPRINT.contains("generic_x86/sdk_x86/generic_x86") ||
                Build.FINGERPRINT.contains("Andy") ||
                Build.FINGERPRINT.contains("ttVM_Hdragon") ||
                Build.FINGERPRINT.contains("generic_x86_64") ||
                Build.FINGERPRINT.contains("generic/google_sdk/generic") ||
                Build.FINGERPRINT.contains("vbox86p") ||
                Build.FINGERPRINT.contains("generic/vbox86p/vbox86p")
            ) {
                newRating++
            }

            try {
                val opengl = GLES20.glGetString(GLES20.GL_RENDERER)
                if (opengl != null) {
                    if (opengl.contains("Bluestacks") ||
                        opengl.contains("Translator")
                    ) {
                        newRating += 10
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            try {
                val sharedFolder = File(
                    (Environment
                        .getExternalStorageDirectory().toString()
                            + File.separatorChar
                            + "windows"
                            + File.separatorChar
                            + "BstSharedFolder")
                )

                if (sharedFolder.exists()) {
                    newRating += 10
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            rating = newRating
        }
        return rating > 3 //不能再少了，否则有可能误判，若增减了新的嫌疑度判定属性，要重新评估该值
    }


    private fun mayOnEmulatorViaQEMU(context: Context): Boolean {
        val qemu = getProp(context, "ro.kernel.qemu")
        return "1" == qemu
    }

    //    /**
    //     * 有权限可以打开这个判断
    //     * @param context
    //     * @return
    //     */
    //    private static final boolean mayOnEmulatorViaTelephonyDeviceId(Context context) {
    //        TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
    //        if (tm == null) {
    //            return false;
    //        }
    //
    //        String deviceId = tm.getDeviceId();
    //        if (TextUtils.isEmpty(deviceId)) {
    //            return false;
    //        }
    //
    //        /**
    //         * device id of telephony likes '0*'
    //         */
    //        for (int i = 0; i < deviceId.length(); i++) {
    //            if (deviceId.charAt(i) != '0') {
    //                return false;
    //            }
    //        }
    //
    //        return true;
    //    }
    val deviceListing: String
        /**
         * Returns string with human-readable listing of Build.* parameters used in [.isEmulator] method.
         *
         * @return all involved Build.* parameters and its values
         */
        get() = """
             Build.PRODUCT: ${Build.PRODUCT}
             Build.MANUFACTURER: ${Build.MANUFACTURER}
             Build.BRAND: ${Build.BRAND}
             Build.DEVICE: ${Build.DEVICE}
             Build.MODEL: ${Build.MODEL}
             Build.HARDWARE: ${Build.HARDWARE}
             Build.FINGERPRINT: ${Build.FINGERPRINT}
             Build.TAGS: ${Build.TAGS}
             GL_RENDERER: ${GLES20.glGetString(GLES20.GL_RENDERER)}
             GL_VENDOR: ${GLES20.glGetString(GLES20.GL_VENDOR)}
             GL_VERSION: ${GLES20.glGetString(GLES20.GL_VERSION)}
             GL_EXTENSIONS: ${GLES20.glGetString(GLES20.GL_EXTENSIONS)}
             
             """.trimIndent()


    private fun getProp(context: Context, property: String): String? {
        try {
            val cl = context.classLoader
            val SystemProperties = cl.loadClass("android.os.SystemProperties")
            val method = SystemProperties.getMethod("get", String::class.java)
            val params = arrayOfNulls<Any>(1)
            params[0] = property
            return method.invoke(SystemProperties, *params) as String
        } catch (e: Exception) {
            return null
        }
    }

    /**
     * Prints all Build.* parameters used in [.isEmulator] method to logcat.
     */
    fun logcat() {
        Log.d(TAG, deviceListing)
    }
}
