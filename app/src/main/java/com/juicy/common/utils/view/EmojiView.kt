package com.juicy.common.utils.view

import android.content.Context
import android.util.AttributeSet
import android.widget.RelativeLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.juicy.app.R
import com.juicy.app.modules.base.adapter.EmojiAdapter
import com.juicy.common.utils.AppDeviceUtils.dip2px
import io.rong.imkit.picture.decoration.GridSpacingItemDecoration

class EmojiView : RelativeLayout {
    var emojiList: List<String>? = null
    private var callBack: EmojiCallBack? = null
    private var emojiAdapter: EmojiAdapter? = null

    constructor(context: Context?) : super(context) {
        init()
    }


    private fun init() {
        val view = inflate(context, R.layout.layout_label, this)
        val emojiRc = view.findViewById<RecyclerView>(R.id.pasteContent)
        val gridLayoutManager = GridLayoutManager(
            context, 7
        )
        emojiRc.layoutManager = gridLayoutManager
        emojiRc.addItemDecoration(GridSpacingItemDecoration(7, dip2px(10f), true))
        emojiAdapter = EmojiAdapter(R.layout.item_timer)
        emojiAdapter?.setNewInstance(emojiList?.toMutableList())
        emojiRc.adapter = emojiAdapter
        emojiRc.isNestedScrollingEnabled = false
        emojiAdapter?.addChildClickViewIds(R.id.filterWrap)
        emojiAdapter?.setOnItemChildClickListener { adapter, view, position ->
            callBack?.onCallBack(
                adapter.data[position].toString()
            )
        }
    }


    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    fun setData(emojiList: MutableList<String?>) {
        emojiAdapter?.setNewInstance(emojiList)
    }

    fun setCallBack(emojiCallBack: EmojiCallBack?) {
        this.callBack = emojiCallBack
    }

    interface EmojiCallBack {
        fun onCallBack(text: String?)
    }
}
