package com.juicy.common.utils

import android.app.Activity
import android.text.TextUtils
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener

object ImageSelectHelper {
    @JvmStatic
    fun selectImage(
        activity: Activity?,
        maxSelectNum: Int,
        enableCrop: <PERSON><PERSON><PERSON>,
        listener: OnImageSelectListener?
    ) {
        PictureSelector.create(activity)
            .openGallery(SelectMimeType.ofImage())
            .setImageEngine(GlideEngine.Companion.createGlideEngine())
            .setMaxSelectNum(maxSelectNum)
            .setImageSpanCount(4)
            .isDisplayCamera(true)
            .setLanguage(-1) // 使用系统语言
            .isGif(false)
            .forResult(object : OnResultCallbackListener<LocalMedia> {
                override fun onResult(result: ArrayList<LocalMedia>) {
                    val paths: MutableList<String> = ArrayList()
                    for (media in result) {
                        // 优先使用压缩后的路径

                        var path = media.realPath
                        if (TextUtils.isEmpty(path)) {
                            // 如果没有压缩，使用原图路径
                            path = media.path
                        }
                        paths.add(path)
                    }
                    listener?.onSelected(paths)
                }

                override fun onCancel() {
                    listener?.onCancel()
                }
            })
    }

    interface OnImageSelectListener {
        fun onSelected(paths: List<String>?)
        fun onCancel()
    }
}