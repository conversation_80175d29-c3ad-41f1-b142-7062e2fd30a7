package com.juicy.common.utils.RongCloud

import android.net.Uri
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import io.reactivex.rxjava3.disposables.Disposable
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.userinfo.UserDataProvider
import io.rong.imlib.model.UserInfo

class RongUserMessageProvider : UserDataProvider.UserInfoProvider {
    override fun getUserInfo(userId: String): UserInfo? {
        getUserInfo(userId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                super.onNext(azBaseBean)
                if (azBaseBean != null && azBaseBean.isOk) {
                    if (azBaseBean.data != null) {
                        val az = azBaseBean.data
                        val userInfo = if (az!!.avatarThumbUrl != null) {
                            UserInfo(
                                az.userId, az.nickname, Uri.parse(
                                    az.avatarThumbUrl
                                )
                            )
                        } else {
                            UserInfo(
                                az.userId,
                                az.nickname,
                                Uri.parse("")
                            )
                        }
                        RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
                    }
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }

            override fun onComplete() {
                super.onComplete()
            }
        })
        return null
    }
}
