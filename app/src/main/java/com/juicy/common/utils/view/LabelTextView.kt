package com.juicy.common.utils.view

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.juicy.app.R
import com.juicy.common.utils.AppDeviceUtils.dip2px

class LabelTextView : AppCompatTextView {
    private val colorList: MutableList<String> = ArrayList()
    private val colorBgList:MutableList<Int> = ArrayList()

    fun isSelect(): Boolean {
        return isSelect
    }

    private var isSelect = false

    constructor(context: Context) : super(context) {
        colorList.add("#FF000000")
        colorList.add("#FF000000")
        colorList.add("#FF000000")
        colorList.add("#FF000000")
        colorBgList.add(R.drawable.bg_imp_bg1_sage)
        colorBgList.add(R.drawable.bg_imp_bg2_purple)
        colorBgList.add(R.drawable.bg_imp_bg3_white)
        colorBgList.add(R.drawable.bg_imp_bg4_indigo)
        setSelect(isSelect)
        includeFontPadding = false
        setPadding(
            dip2px(8f),
            dip2px(4f),
            dip2px(8f),
            dip2px(4f)
        )
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)


    fun setSelect(isSelect: Boolean) {
        this.isSelect = isSelect
        if (isSelect) {
            var colorNum = (Math.random() * 3).toInt()
            if (colorNum < 0 || colorNum > 3) colorNum = 0
            setBackgroundResource(colorBgList[colorNum])
//            background = create()
//                .corner(dip2px(16f).toFloat())
//                .bgColor(Color.parseColor("#3FA9FD"))
//                .build()
        } else {
//            background = create()
//                .corner(dip2px(16f).toFloat())
//                .bgColor(Color.parseColor("#979999"))
//                .build()
            var colorNum = (Math.random() * 3).toInt()
            if (colorNum < 0 || colorNum > 3) colorNum = 0
            setBackgroundResource(colorBgList[colorNum])
        }
    }
}
