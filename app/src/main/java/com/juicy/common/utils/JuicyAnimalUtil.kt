package com.juicy.common.utils

import android.view.View
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.AnimationSet
import android.view.animation.RotateAnimation
import android.view.animation.ScaleAnimation
import android.widget.ImageView

object JuicyAnimalUtil {
    @JvmStatic
    fun startGiftAnimal(imageView: ImageView, isBg: Boolean) {
        imageView.visibility = View.VISIBLE
        val set1 = AnimationSet(false)
        val alphaAnimation1 = AlphaAnimation(0f, 1f)
        val scaleAnimation1 = ScaleAnimation(
            0.5f, 1f, 0.5f, 1f,
            Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f
        )
        set1.addAnimation(alphaAnimation1)
        set1.addAnimation(scaleAnimation1)
        set1.duration = 500
        val set2 = AnimationSet(false)
        set2.duration = 1000
        var alphaAnimation2: AlphaAnimation? = null
        var rotateAnimation: RotateAnimation? = null
        if (isBg) {
            alphaAnimation2 = AlphaAnimation(0.7f, 1f)
            rotateAnimation = RotateAnimation(
                0f,
                180f,
                Animation.RELATIVE_TO_SELF,
                0.5f,
                Animation.RELATIVE_TO_SELF,
                0.5f
            )
        } else {
            alphaAnimation2 = AlphaAnimation(1f, 1f)
            rotateAnimation = RotateAnimation(
                0f,
                0f,
                Animation.RELATIVE_TO_SELF,
                0.5f,
                Animation.RELATIVE_TO_SELF,
                0.5f
            )
        }
        set2.addAnimation(alphaAnimation2)
        set2.addAnimation(rotateAnimation)
        val set3 = AnimationSet(false)
        val alphaAnimation3 = AlphaAnimation(1f, 0f)
        val scaleAnimation3 = ScaleAnimation(
            1f, 0.5f, 1f, 0.5f,
            Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f
        )
        set3.addAnimation(alphaAnimation3)
        set3.addAnimation(scaleAnimation3)
        set3.duration = 500
        imageView.startAnimation(set1)
        set1.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
            }

            override fun onAnimationEnd(animation: Animation) {
                imageView.clearAnimation()
                imageView.startAnimation(set2)
            }

            override fun onAnimationRepeat(animation: Animation) {
            }
        })
        set2.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
            }

            override fun onAnimationEnd(animation: Animation) {
                imageView.clearAnimation()
                imageView.startAnimation(set3)
            }

            override fun onAnimationRepeat(animation: Animation) {
            }
        })
        set3.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation) {
            }

            override fun onAnimationEnd(animation: Animation) {
                imageView.clearAnimation()
                imageView.visibility = View.GONE
            }

            override fun onAnimationRepeat(animation: Animation) {
            }
        })
    }
}
