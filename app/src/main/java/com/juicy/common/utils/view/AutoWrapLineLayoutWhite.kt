package com.juicy.common.utils.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import com.juicy.app.R
import com.juicy.common.utils.LanguageManager.Companion.instance
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.max

class AutoWrapLineLayoutWhite : ViewGroup {
    private var mVerticalGap = 0
    private var mHorizontalGap = 0

    private var mFillMode = MODE_FILL_PARENT

    private var childOfLine: CopyOnWriteArrayList<Int>? = null //Save the count of child views of each line;
    private val mOriginWidth: MutableList<Int>

    constructor(context: Context?) : super(context) {
        mOriginWidth = ArrayList()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        mOriginWidth = ArrayList()
        val ta = context.obtainStyledAttributes(attrs, R.styleable.autoWrapLineLayout)
        mHorizontalGap = ta.getDimensionPixelSize(R.styleable.autoWrapLineLayout_horizontalGap, 0)
        mVerticalGap = ta.getDimensionPixelSize(R.styleable.autoWrapLineLayout_verticalGap, 0)
        mFillMode = ta.getInteger(R.styleable.autoWrapLineLayout_fillMode, 0)
        ta.recycle()
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        mOriginWidth = ArrayList()
        val ta = context.obtainStyledAttributes(attrs, R.styleable.autoWrapLineLayout)
        mHorizontalGap = ta.getDimensionPixelSize(R.styleable.autoWrapLineLayout_horizontalGap, 0)
        mVerticalGap = ta.getDimensionPixelSize(R.styleable.autoWrapLineLayout_verticalGap, 0)
        mFillMode = ta.getInteger(R.styleable.autoWrapLineLayout_fillMode, 0)
        ta.recycle()
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        val isRtl = instance!!.isLanguageForce
        if (mFillMode == MODE_FILL_PARENT) {
            layoutModeFillParent(isRtl)
        } else {
            layoutWrapContent(isRtl)
        }
    }

    @SuppressLint("DrawAllocation")
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        childOfLine = CopyOnWriteArrayList()
        val childCount = childCount
        var totalHeight = 0
        val totalWidth = MeasureSpec.getSize(widthMeasureSpec)
        var curLineChildCount = 0
        var curLineWidth = 0
        var maxHeight = 0
        for (i in 0 until childCount) {
            val childItem = getChildAt(i)
            if (mFillMode == MODE_FILL_PARENT) {
                if (mOriginWidth.size <= i) {
                    measureChild(childItem, widthMeasureSpec, heightMeasureSpec)
                    mOriginWidth.add(childItem.measuredWidth)
                } else {
                    childItem.measure(
                        MeasureSpec.makeMeasureSpec(mOriginWidth[i], MeasureSpec.EXACTLY),
                        MeasureSpec.makeMeasureSpec(childItem.measuredHeight, MeasureSpec.EXACTLY)
                    )
                }
            } else {
                measureChild(childItem, widthMeasureSpec, heightMeasureSpec)
            }
            val childHeight = childItem.measuredHeight
            val childWidth = childItem.measuredWidth
            if (curLineWidth + childWidth + curLineChildCount * mHorizontalGap <= totalWidth) {
                curLineWidth += childWidth
                maxHeight = max(childHeight.toDouble(), maxHeight.toDouble()).toInt()
                curLineChildCount++
            } else {
                childOfLine?.add(curLineChildCount)
                curLineWidth = childWidth
                curLineChildCount = 1
                totalHeight += maxHeight
                maxHeight = childHeight
            }
        }
        childOfLine?.add(curLineChildCount)
        val list = childOfLine ?: CopyOnWriteArrayList()
        for (item in list) {
            if (item == 0) {
                list.remove(item)
            }
        }
        totalHeight += (mVerticalGap * (list?.size?:2 - 1) + maxHeight)
        setMeasuredDimension(totalWidth, totalHeight)
    }

    private fun layoutModeFillParent(isRtl: Boolean) {
        var index = 0
        val width = measuredWidth
        var curHeight = 0
        for (i in childOfLine!!.indices) {
            var childCount = childOfLine!![i]
            var maxHeight = 0
            var lineWidth = 0
            for (j in 0 until childCount) {
                lineWidth += getChildAt(j + index).measuredWidth
            }
            if (childCount == 0)return
            var padding = (width - lineWidth - mHorizontalGap * (childCount - 1)) / childCount / 2
            lineWidth = 0
            if (padding == 0) padding = 10
            val target = index + childCount
            while (index < target) {
                val item = getChildAt(index)
                maxHeight =
                    max(maxHeight.toDouble(), item.measuredHeight.toDouble()).toInt()
                var left: Int
                var right: Int
                if (isRtl) {
                    right = width - lineWidth - padding
                    left = right - item.measuredWidth
                } else {
                    left = lineWidth + padding
                    right = left + item.measuredWidth
                }
                item.setPadding(
                    padding, item.paddingTop,
                    padding, item.paddingBottom
                )
                item.measure(
                    MeasureSpec.makeMeasureSpec(
                        item.measuredWidth + padding * 2,
                        MeasureSpec.EXACTLY
                    ),
                    MeasureSpec.makeMeasureSpec(item.measuredHeight, MeasureSpec.EXACTLY)
                )
                item.layout(left, curHeight, right, curHeight + item.measuredHeight)
                lineWidth += item.measuredWidth + mHorizontalGap
                index++
            }
            curHeight += maxHeight + mVerticalGap
        }
    }

    private fun layoutWrapContent(isRtl: Boolean) {
        var index = 0
        var curHeight = 0
        for (i in childOfLine!!.indices) {
            val childCount = childOfLine!![i]
            var maxHeight = 0
            var lineWidth = 0
            val target = index + childCount
            while (index < target) {
                val item = getChildAt(index)
                maxHeight =
                    max(maxHeight.toDouble(), item.measuredHeight.toDouble()).toInt()
                var left: Int
                var right: Int
                if (isRtl) {
                    right = measuredWidth - lineWidth
                    left = right - item.measuredWidth
                } else {
                    left = lineWidth
                    right = left + item.measuredWidth
                }
                item.layout(left, curHeight, right, curHeight + item.measuredHeight)
                lineWidth += item.measuredWidth + mHorizontalGap
                index++
            }
            curHeight += maxHeight + mVerticalGap
        }
    }

    override fun addView(child: View) {
        super.addView(child)
    }

    fun setFillMode(fillMode: Int) {
        this.mFillMode = fillMode
    }

    fun setHorizontalGap(horizontalGap: Int) {
        this.mHorizontalGap = horizontalGap
    }

    fun setVerticalGap(verticalGap: Int) {
        this.mVerticalGap = verticalGap
    }

    companion object {
        const val MODE_FILL_PARENT: Int = 0
        const val MODE_WRAP_CONTENT: Int = 1
    }
}