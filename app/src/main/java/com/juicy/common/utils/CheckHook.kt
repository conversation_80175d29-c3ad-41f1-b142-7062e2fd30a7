package com.juicy.common.utils

import android.content.Context
import android.os.Process
import android.util.Log
import java.io.BufferedReader
import java.io.FileReader

/**
 * Created by Ben on 2018/1/29.
 */
object CheckHook {
    fun isHook(context: Context?): <PERSON><PERSON>an {
        return isHookCheckStack(context) || isHookByJar
    }


    fun isHookCheckStack(context: Context?): Boolean {
        var isHook = false
        try {
            val msg = "blah"
            throw Exception(msg)
        } catch (e: Exception) {
            var zygoteInitCallCount = 0
            for (stackTraceElement in e.stackTrace) {
                val className = stackTraceElement.className
                val methodName = stackTraceElement.methodName
                if (className == "com.android.internal.os.ZygoteInit") {
                    zygoteInitCallCount++
                    if (zygoteInitCallCount == 2) {
                        Log.wtf("HookDetection", "Substrate is active on the device.")
                        isHook = true
                    }
                }
                if (className == "com.saurik.substrate.MS$2" && methodName == "invoked") {
                    Log.wtf(
                        "HookDetection",
                        "A method on the stack trace has been hooked using Substrate."
                    )
                    isHook = true
                }
                if (className == "de.robv.android.xposed.XposedBridge" && methodName == "main") {
                    Log.wtf("HookDetection", "Xposed is active on the device.")
                    isHook = true
                }
                if (className == "de.robv.android.xposed.XposedBridge" && methodName == "handleHookedMethod") {
                    Log.wtf(
                        "HookDetection",
                        "A method on the stack trace has been hooked using Xposed."
                    )
                    isHook = true
                }
            }
        }
        return isHook
    }

    val isHookByJar: Boolean
        get() {
            var isHook = false
            val libraries = libraries
            isHook = isHookBySubstrate(libraries) || isHookByXposed(libraries)
            return isHook
        }

    private val libraries: MutableSet<String>
        get() {
            val libraries: MutableSet<String> =
                mutableSetOf<String>()
            val mapsFilename = "/proc/" + Process.myPid() + "/maps"
            try {
                val reader =
                    BufferedReader(FileReader(mapsFilename))
                var line: String
                while ((reader.readLine().also { line = it }) != null) {
                    if (line.endsWith(".so") || line.endsWith(".jar")) {
                        val n = line.lastIndexOf(" ")
                        libraries.add(line.substring(n + 1))
                    }
                }
                reader.close()
            } catch (e: Exception) {
                Log.wtf("HookDetection", e.toString())
            }
            return libraries?: mutableSetOf<String>()
        }

    private fun isHookBySubstrate(libraries: Set<String>): Boolean {
        for (library in libraries) {
            if (library.contains("com.saurik.substrate")) {
                Log.wtf("HookDetection", "Substrate shared object found: $library")
                return true
            }
        }
        return false
    }

    private fun isHookByXposed(libraries: Set<String>): Boolean {
        for (library in libraries) {
            if (library.contains("XposedBridge.jar")) {
                Log.wtf("HookDetection", "Xposed JAR found: $library")
                return true
            }
        }
        return false
    }
}