package com.juicy.common.utils

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner

class ProcessLifecycleObserver(private var callback: Callback?) :
    DefaultLifecycleObserver {
    override fun onStart(owner: LifecycleOwner) {
        if (callback != null) {
            callback!!.onAppForeground()
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        if (callback != null) {
            callback!!.onAppBackground()
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        callback = null
    }

    interface Callback {
        fun onAppBackground()

        fun onAppForeground()
    }
}
