package com.juicy.common.utils

import android.content.Context
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import com.juicy.app.JuicyApplication

object VibratorUtils {
     var vibrator: Vibrator? = null
    private var vibrationEffect: VibrationEffect? = null

    init {
        vibrator =
            JuicyApplication.juicyApplication!!.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator?
    }

    /**
     * 开始持续震动，直到调用 stopVibration() 停止震动。
     * 持续震动的频率为 500 毫秒震动，200 毫秒停止，然后重复。
     */
    fun startVibration() {
        if (vibrator != null && vibrator?.hasVibrator() == true) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // 定义一个震动模式，500ms 震动，200ms 停止，持续重复
                val timings: LongArray = longArrayOf(0, 500, 200, 500)
                val amplitudes: IntArray = intArrayOf(
                    0,
                    VibrationEffect.DEFAULT_AMPLITUDE,
                    0,
                    VibrationEffect.DEFAULT_AMPLITUDE
                )

                vibrationEffect = VibrationEffect.createWaveform(timings, amplitudes, 0)
                vibrator?.vibrate(vibrationEffect)
            } else {
                // 低于 API 26 的设备使用旧方法实现震动
                val pattern: LongArray = longArrayOf(0, 500, 200, 500)
                vibrator?.vibrate(pattern, 0)
            }
        }
    }

    /**
     * 停止震动。
     */
    fun stopVibration() {
        if (vibrator != null) {
            vibrator?.cancel()
        }
    }
}
