package com.juicy.common.utils

import android.content.Context
import android.text.TextUtils
import com.juicy.common.config.SpKeyPool
import java.util.Locale

object LanguageTool {
    var list: ArrayList<LanguageEntity> = ArrayList()

    val languagesFunc: ArrayList<LanguageEntity>
        get() {
            if (list.isEmpty()) {
                list.add(LanguageEntity("en", "English"))
                list.add(LanguageEntity("ar", "بالعربية"))
                list.add(LanguageEntity("es", "Español"))
                list.add(LanguageEntity("tr", "Türkçe"))
                list.add(LanguageEntity("ko", "한국어"))
                list.add(LanguageEntity("de", "Deutsch"))
                list.add(LanguageEntity("ja", "日本語"))
                list.add(LanguageEntity("it", "Italiano"))
                list.add(LanguageEntity("hi", "हिन्दी"))
                list.add(LanguageEntity("zh", "中文（繁體）", "TW"))
                list.add(LanguageEntity("th", "ภาษาไทย"))
                list.add(LanguageEntity("fr", "français"))
            }
            return list
        }

    @JvmStatic
    val localeLanguageCodeCountryFunc: String
        get() {
            var curCode = Locale.getDefault().language
            val curCountry = Locale.getDefault().country
            if (!curCountry.isEmpty()) {
                curCode = "$curCode-$curCountry"
            }
            return curCode
        }

    fun getLocaleLanguageCodeFunc(context: Context): String {
        var nativeLangCode = SpSaveUtil.getStringValue(SpKeyPool.LANGUAGE, "")
        if (!SpSaveUtil.getBooleanVal(SpKeyPool.CHANGE_LANGUAGE, false)!! || TextUtils.isEmpty(
                nativeLangCode
            )
        ) {
            val locale = context.resources.configuration.locale
            nativeLangCode = locale.language
        }
        return nativeLangCode!!
    }

    @JvmStatic
    fun getLocaleLanguageCountryFunc(context: Context): String {
        var nativeCountryCode = SpSaveUtil.getStringValue(SpKeyPool.LANGUAGE_COUNTRY, "")
        if (!SpSaveUtil.getBooleanVal(SpKeyPool.CHANGE_LANGUAGE, false)!! || TextUtils.isEmpty(
                nativeCountryCode
            )
        ) {
            val locale = context.resources.configuration.locale
            nativeCountryCode = locale.country
        }
        return nativeCountryCode!!
    }


    fun getLocaleLanguageDisplayNameFunc(context: Context): String {
        var nativeDisplayName = SpSaveUtil.getStringValue(SpKeyPool.LANGUAGE_NAME, "")
        if (!SpSaveUtil.getBooleanVal(SpKeyPool.CHANGE_LANGUAGE, false)!! || TextUtils.isEmpty(
                nativeDisplayName
            )
        ) {
            val locale = context.resources.configuration.locale
            nativeDisplayName = locale.displayName
        }
        return nativeDisplayName!!
    }

    @JvmStatic
    val defaultLanguageCodeFunc: String
        get() {
            var nativeLangCode = SpSaveUtil.getStringValue(SpKeyPool.LANGUAGE, "")
            if (TextUtils.isEmpty(nativeLangCode)) {
                nativeLangCode = Locale.getDefault().language
            }
            return nativeLangCode!!
        }

    @JvmStatic
    val deviceLanguage: String
        get() {
            var nativeLangCode = Locale.getDefault().language
            val scriptCode = Locale.getDefault().script
            if (!TextUtils.isEmpty(scriptCode)) {
                nativeLangCode = nativeLangCode + "-" + Locale.getDefault().script
            }

            return nativeLangCode
        }

    //获取语言国家编码
    fun getDeviceLanguageCountry(context: Context): String {
        val countryCode = getLocaleLanguageCountryFunc(context)
        var nativeLangCode = Locale.getDefault().language
        val scriptCode = Locale.getDefault().script
        if (!TextUtils.isEmpty(scriptCode)) {
            nativeLangCode = "$nativeLangCode-$scriptCode"
        }
        if (!TextUtils.isEmpty(countryCode)) {
            nativeLangCode = "$nativeLangCode-$countryCode"
        }

        return nativeLangCode
    }

    fun changeLanguageFunc(context: Context?, language: String, country: String) {
        if (context == null || TextUtils.isEmpty(language) || language == "auto") {
            return
        }
        val resources = context.resources
        val config = resources.configuration
        config.locale = Locale(language, country)
        resources.updateConfiguration(config, null)
    }

    class LanguageEntity {
        var code: String
        var name: String
        var country: String = ""

        constructor(code: String, name: String) {
            this.code = code
            this.name = name
            this.country = ""
        }

        constructor(code: String, name: String, country: String) {
            this.code = code
            this.name = name
            this.country = country
        }
    }
}
