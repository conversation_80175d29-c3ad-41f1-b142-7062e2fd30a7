package com.juicy.common.utils

import android.Manifest
import android.app.Activity
import android.app.AppOpsManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.res.Resources
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Process
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import com.juicy.app.JuicyApplication
import com.juicy.app.modules.base.dialog.SettingDialog
import com.juicy.app.modules.base.activity.BaseActivity
import io.rong.imkit.utils.PermissionCheckUtil

class PermissionUtil {
    var hx: String = "baimo"
    fun setHx() {
        Log.i("dsasaasd", "Dsadd")
    }

    interface PermissionCallback {
        fun complete()
    }

    interface PermissionCallingback {
        fun onGranted()
        fun onDenied()
    }

    open class SimplePermissionCallback {
        open fun onGranted() {
        }

        open fun onDenied() {
        }

        open fun OnNotAsk(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
//            if (null != this.activity) {
//                showRequestPermissionFailedAlter(this.activity, permissions, grantResults);
////                PermissionCheckUtil.showRequestPermissionFailedAlter(this.activity, permissions, grantResults);
//            }
        }
    }

    companion object {
        // Java
        const val STORAGE_REQUEST_CODE: Int = 1
        const val VIDEO_PERMISSION_CODE: Int = 2

        //开启视频时需要的权限、音频  相册
        val VIDEO_REQUESTED_PERMISSIONS: Array<String> = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            arrayOf(
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.CAMERA,
                Manifest.permission.BLUETOOTH_CONNECT
            )
        } else {
            arrayOf(
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.CAMERA,
            )
        }

        val VIDEO_REQUESTED_PERMISSIONSMin: Array<String> = arrayOf(
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.CAMERA
        )

        val STORAGE_PERMISSIONS: Array<String> = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )


        val PHOTO_PERMISSIONS: Array<String> = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA
        )

        fun checkAndRequestPermission(
            activity: Activity,
            permissions: Array<String>,
            requestCode: Int,
            permissionCallback: SimplePermissionCallback?
        ) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val result = PermissionCheckUtil.checkPermissions(activity, permissions)
                if (result) {
                    permissionCallback?.onGranted()
                } else {
                    requestPermissions(activity, permissions, requestCode, permissionCallback)
                }
            } else {
                permissionCallback?.onGranted()
            }
        }

        fun checkPermission(
            activity: Activity?,
            permissions: Array<String>,
            requestCode: Int,
            permissionCallback: SimplePermissionCallback?
        ) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val result = PermissionCheckUtil.checkPermissions(activity, permissions)
                if (result) {
                    permissionCallback?.onGranted()
                } else {
                    permissionCallback!!.onDenied()
                }
            } else {
                permissionCallback?.onGranted()
            }
        }

        @JvmStatic
        fun checkPhotoPermission(activity: BaseActivity, permissionCallback: PermissionCallback) {
            checkStoragePermission(activity, object : SimplePermissionCallback() {
                override fun onGranted() {
                    permissionCallback.complete()
                }


                override fun onDenied() {
                    showPhotoRequestPermissionFailedAlter(permissionCallback)
                }

                override fun OnNotAsk(
                    requestCode: Int,
                    permissions: Array<String>,
                    grantResults: IntArray
                ) {
                    showPhotoRequestPermissionFailedAlter(permissionCallback)
                }
            })
        }

        fun checkStoragePermission(
            activity: BaseActivity,
            permissionCallback: SimplePermissionCallback?
        ) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13及以上需要所有媒体权限
                checkAndRequestPermission(
                    activity,
                    arrayOf(
                        Manifest.permission.READ_MEDIA_IMAGES,
                    ),
                    STORAGE_REQUEST_CODE,
                    permissionCallback
                )
            } else {
                checkAndRequestPermission(
                    activity, arrayOf(
                        Manifest.permission.READ_EXTERNAL_STORAGE,
                    ), STORAGE_REQUEST_CODE, permissionCallback
                )
            }
            //        else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
//            // Android 11及以上使用 MANAGE_EXTERNAL_STORAGE 权限
//            if (Environment.isExternalStorageManager()) {
//                permissionCallback.onGranted();
//            } else {
//                Intent intent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
//                activity.startActivityForResult(intent, STORAGE_REQUEST_CODE);
//            }
//        } else {
//            // Android 10及以下使用传统存储权限
//            checkAndRequestPermission(activity, STORAGE_PERMISSIONS, STORAGE_REQUEST_CODE, permissionCallback);
//        }
        }

        fun toSetJust() {
            val handler = Handler()
            // 延迟 1 秒（1000 毫秒）执行代码
            handler.postDelayed(Runnable { // 这里是要执行的代码
                // 跳转到应用设置页面
                val currentActivity: Activity = AppActivityUtil.currentActivity as Activity
                    ?: return@Runnable
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = Uri.fromParts("package", currentActivity.packageName, null)
                intent.setData(uri)
                currentActivity.startActivity(intent)
            }, 100) // 延迟时间
        }

        var videoPermissions: Array<String> = arrayOf()
        @JvmStatic
        fun checkVideoPermission(activity: Activity, permissionCallback: PermissionCallback) {
            var permissions = VIDEO_REQUESTED_PERMISSIONS
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // 判断是否是Android TIRAMISU版本
            } else {
                permissions = VIDEO_REQUESTED_PERMISSIONSMin
            }
            videoPermissions = permissions
            checkAndRequestPermission(
                activity,
                permissions,
                VIDEO_PERMISSION_CODE,
                object : SimplePermissionCallback() {
                    override fun onGranted() {
                        permissionCallback.complete()
                    }

                    override fun onDenied() {
                        if (videoPermissions.size == 3 && videoPermissions[videoPermissions.size - 1] == Manifest.permission.BLUETOOTH_CONNECT) {
                            showVideoRequestPermissionFailedAlter(
                                "Dear Sir, you do not allow us to access your camera, microphone, and nearby devices. If you would like to fully utilize the functionality of our application, it is best to enable your camera, microphone, and nearby device permissions.",
                                permissionCallback
                            )
                        } else {
                            showVideoRequestPermissionFailedAlter(
                                "\"Dear Sir,  you didn't allow us to access your camera and microphone  if you want to use our app fuction comprehensively, you better turn on  your permissions of camera and  microphone.\"",
                                permissionCallback
                            )
                        }
                    }

                    override fun OnNotAsk(
                        requestCode: Int,
                        permissions: Array<String>,
                        grantResults: IntArray
                    ) {
                        super.OnNotAsk(requestCode, permissions, grantResults)
                        if (permissions.isNotEmpty() && permissions[permissions.size - 1] == Manifest.permission.BLUETOOTH_CONNECT && grantResults[permissions.size - 1] == -1) {
                            showVideoRequestPermissionFailedAlter(
                                "Dear Sir, you do not allow us to access your camera, microphone, and nearby devices. If you would like to fully utilize the functionality of our application, it is best to enable your camera, microphone, and nearby device permissions.",
                                permissionCallback
                            )
                        } else {
                            showVideoRequestPermissionFailedAlter(
                                "\"Dear Sir,  you didn't allow us to access your camera and microphone  if you want to use our app fuction comprehensively, you better turn on  your permissions of camera and  microphone.\"",
                                permissionCallback
                            )
                        }

                        //弹框
                    }
                })
        }

        //在视频里面使用
        @JvmStatic
        fun checkCallingCamaraPermission(
            activity: Activity?,
            permissionCallback: PermissionCallingback
        ) {
            val list = arrayOf(
                Manifest.permission.CAMERA
            )

            checkPermission(
                activity,
                list,
                VIDEO_PERMISSION_CODE,
                object : SimplePermissionCallback() {
                    override fun onGranted() {
                        permissionCallback.onGranted()
                    }

                    override fun onDenied() {
                        permissionCallback.onDenied()
                    }

                    override fun OnNotAsk(
                        requestCode: Int,
                        permissions: Array<String>,
                        grantResults: IntArray
                    ) {
                        super.OnNotAsk(requestCode, permissions, grantResults)

                        permissionCallback.onDenied()
                    }
                })
        }

        @JvmStatic
        fun checkCallingAudioPermission(
            activity: Activity?,
            permissionCallback: PermissionCallingback
        ) {
            val list = arrayOf(
                Manifest.permission.RECORD_AUDIO
            )

            checkPermission(
                activity,
                list,
                VIDEO_PERMISSION_CODE,
                object : SimplePermissionCallback() {
                    override fun onGranted() {
                        permissionCallback.onGranted()
                    }

                    override fun onDenied() {
                        permissionCallback.onDenied()
                    }

                    override fun OnNotAsk(
                        requestCode: Int,
                        permissions: Array<String>,
                        grantResults: IntArray
                    ) {
                        super.OnNotAsk(requestCode, permissions, grantResults)

                        permissionCallback.onDenied()
                    }
                })
        }

        fun showPhotoRequestPermissionFailedAlter(permissionCallback: PermissionCallback?) {
            val handler = Handler()
            // 延迟 1 秒（1000 毫秒）执行代码
            handler.postDelayed(Runnable { // 这里是要执行的代码
                // 跳转到应用设置页面
                val currentActivity =
                    (AppActivityUtil.currentActivity as Activity)
                if (AppActivityUtil.isDestroy(currentActivity) && currentActivity !is AppCompatActivity) return@Runnable
                val settingDialog = SettingDialog(
                    currentActivity,
                    LanguageManager.Companion.instance?.getLocalTranslate("Unable_to_obtain_album_or_storage_permissions_Please_go_to_the_application_settings_page_to_obtain_permissions")?:"",
                    object : SettingDialog.ConfirmBack {
                        override fun onConfirmBack() {
                            toSetJust()
                        }

                        override fun onCancelBack() {
                        }
                    })
                settingDialog.show(
                    (currentActivity as AppCompatActivity).supportFragmentManager,
                    ""
                )
            }, 100) // 延迟时间
        }

        fun showVideoRequestPermissionFailedAlter(
            message: String,
            permissionCallback: PermissionCallback
        ) {
            val handler = Handler()
            // 延迟 1 秒（1000 毫秒）执行代码
            handler.postDelayed(Runnable { // 这里是要执行的代码
                // 跳转到应用设置页面
                val currentActivity =
                    (AppActivityUtil.currentActivity as Activity)
                if (AppActivityUtil.isDestroy(currentActivity) && currentActivity !is AppCompatActivity) return@Runnable
                val settingDialog = SettingDialog(
                    currentActivity,
                    LanguageManager.Companion.instance?.getLocalTranslate(message)?:"",
                    object : SettingDialog.ConfirmBack {
                        override fun onConfirmBack() {
                            toSetJust()
                        }

                        override fun onCancelBack() {
                            permissionCallback.complete()
                        }
                    })
                settingDialog.show(
                    (currentActivity as AppCompatActivity).supportFragmentManager,
                    ""
                )
            }, 100) // 延迟时间
        }

        @JvmStatic
        fun showMicrophoneRequestPermissionFailedAlter() {
            val handler = Handler()
            // 延迟 1 秒（1000 毫秒）执行代码
            handler.postDelayed(Runnable { // 这里是要执行的代码
                // 跳转到应用设置页面
                val currentActivity =
                    (AppActivityUtil.currentActivity as Activity)
                if (AppActivityUtil.isDestroy(currentActivity) && currentActivity !is AppCompatActivity) return@Runnable
                val settingDialog = SettingDialog(
                    currentActivity,
                    LanguageManager.Companion.instance?.getLocalTranslate("Unable_to_retrieve_microphone_Please_go_to_the_application_settings_page_to_obtain_permissions")?:"",
                    object : SettingDialog.ConfirmBack {
                        override fun onConfirmBack() {
                            toSetJust()
                        }

                        override fun onCancelBack() {
                        }
                    })
                settingDialog.show(
                    (currentActivity as AppCompatActivity).supportFragmentManager,
                    ""
                )
            }, 100) // 延迟时间
        }

        /**
         * 检查视频通话权限
         */
        //    public static void checkVideoPermission(Activity activity, SimplePermissionCallback permissionCallback) {
        //        checkAndRequestPermission(activity, VIDEO_REQUESTED_PERMISSIONS, VIDEO_PERMISSION_CODE, permissionCallback);
        //    }
        fun hasPermission(permission: String): Boolean {
            return ContextCompat.checkSelfPermission(
                JuicyApplication.juicyApplication!!,
                permission
            ) == PackageManager.PERMISSION_GRANTED
        }

        fun requestPermissions(
            activity: Activity,
            permissions: Array<String>,
            requestCode: Int,
            permissionCallback: SimplePermissionCallback?
        ) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (activity is BaseActivity) {
                    activity.setPermissionCallback(permissionCallback)
                } else {
                    permissionCallback!!.onDenied()
                }
                ActivityCompat.requestPermissions(activity, permissions, requestCode)
            } else {
                permissionCallback?.onGranted()
            }
        }

        /**
         * 是否允许后台弹出界面
         *
         * @param context
         * @return
         */
        fun isAllowedBackPop(context: Context): Boolean {
            val ops = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
            try {
                val op = 10021
                val method = ops.javaClass.getMethod(
                    "checkOpNoThrow",
                    Int::class.javaPrimitiveType,
                    Int::class.javaPrimitiveType,
                    String::class.java
                )
                val result: Int? =
                    method.invoke(ops, op, Process.myUid(), context.packageName) as Int
                return result == AppOpsManager.MODE_ALLOWED
            } catch (e: Exception) {
            }
            return false
        }

        /**
         * 是否开启通知栏权限
         *
         * @return
         */
        fun isNotifyEnabled(context: Context): Boolean {
            val manager = NotificationManagerCompat.from(context)
            // areNotificationsEnabled方法的有效性官方只最低支持到API 19，低于19的仍可调用此方法不过只会返回true，即默认为用户已经开启了通知。
            val isOpened = manager.areNotificationsEnabled()
            return isOpened
        }


        fun checkPermissionResultIncompatible(
            permissions: Array<String>?,
            grantResults: IntArray?
        ): Boolean {
            return grantResults == null || grantResults.size == 0 || permissions == null || permissions.size != grantResults.size
        }

        //    public static void showRequestPermissionFailedAlter(final Context context, @NonNull String[] permissions, @NonNull int[] grantResults) {
        //        if (context != null) {
        //            String content = getNotGrantedPermissionMsg(context, permissions, grantResults);
        //            content = LanguageManager.getInstance().getLocalTranslate(content);
        //            if (!TextUtils.isEmpty(content)) {
        //                DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
        //                    public void onClick(DialogInterface dialog, int which) {
        //                        switch (which) {
        //                            case -1:
        //                                Intent intent = new Intent("android.settings.APPLICATION_DETAILS_SETTINGS");
        //                                Uri uri = Uri.fromParts("package", context.getPackageName(), (String)null);
        //                                intent.setData(uri);
        //                                context.startActivity(intent);
        //                            case -2:
        //                            default:
        //                        }
        //                    }
        //                };
        //                if (Build.VERSION.SDK_INT >= 21) {
        //                    (new AlertDialog.Builder(context, 16974394)).setMessage(content).setPositiveButton(LanguageManager.getInstance().getLocalTranslate("Confirm"), listener).setNegativeButton(LanguageManager.getInstance().getLocalTranslate("Cancel"), listener).setCancelable(false).create().show();
        //                } else {
        //                    (new AlertDialog.Builder(context)).setMessage(content).setPositiveButton(LanguageManager.getInstance().getLocalTranslate("Confirm"), listener).setNegativeButton(LanguageManager.getInstance().getLocalTranslate("Cancel"), listener).setCancelable(false).create().show();
        //                }
        //
        //            }
        //        }
        //    }
        private fun getNotGrantedPermissionMsg(
            context: Context,
            permissions: Array<String>,
            grantResults: IntArray
        ): String {
            if (checkPermissionResultIncompatible(permissions, grantResults)) {
                return ""
            } else {
                try {
                    val permissionNameList: MutableList<String?> = ArrayList<String>(permissions.size).toMutableList()

                    for (i in permissions.indices) {
                        if (grantResults[i] == -1) {
                            val permissionName = context.getString(
                                context.resources.getIdentifier(
                                    "rc_" + permissions[i],
                                    "string",
                                    context.packageName
                                ), *arrayOf<Any>(0)
                            )
                            if (!permissionNameList.contains(permissionName)) {
                                permissionNameList.add(permissionName)
                            }
                        }
                    }

                    val builder: StringBuilder = StringBuilder(
                        LanguageManager.Companion.instance?.getLocalTranslate("You_need_to_enable_read_and_write_permissions_in_the_settings")?:""
                    )
                    return builder.append("(").append(
                        LanguageManager.Companion.instance?.getLocalTranslate(
                            TextUtils.join(" ", permissionNameList)
                        )
                    ).append(")").toString()
                } catch (var6: Resources.NotFoundException) {
//                RLog.e(TAG, "One of the permissions is not recognized by SDK." + Arrays.toString(permissions));
                    return ""
                }
            }
        }
    }
}
