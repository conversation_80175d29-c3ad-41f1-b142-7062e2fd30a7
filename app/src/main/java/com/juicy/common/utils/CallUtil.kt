package com.juicy.common.utils

import android.os.Handler
import androidx.appcompat.app.AppCompatActivity
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.modules.base.dialog.HalfShoppingDialog
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppUtil.checkNetworkToast
import com.juicy.common.utils.LanguageManager.Companion.instance
import com.juicy.common.utils.throttle.Throttle.throttle

object CallUtil {
    @JvmStatic
    fun callHandle(callCoins: Int, source: Int, userId: String?, status: String?, entry: String?) {
        if (checkNetworkToast()) return
        if (callCoins == -1) {
            ToastUtils.showShort(instance?.getLocalTranslate("Data_is_loading_please_try_again_later"))
            return
        }

        if (status != null && (status == Constant.ONLINE || status == Constant.AVAILABLE)) {
            //能拨打
            var canCall = false
            if (Cache.instance.reviewPkg) {
                ARouter.getInstance()
                    .build(Constant.VIDEO_CALL_ACTIVITY_ROUTE)
                    .withString(Constant.CALL_SOURCE, source.toString())
                    .withString(Constant.CALL_STATUS, Constant.CALL_CREATE)
                    .withString(Constant.ANCHOR_ID, userId)
                    .navigation()
            } else {
                if (Cache.instance.userInfo != null && Cache.instance.userInfo?.availableCoins != null) {
                    if ((Cache.instance.userInfo?.availableCoins ?: 0) >= callCoins) {
                        canCall = true
                    }

                    if (canCall) {
                        //防抖
                        throttle("call") {
                            ARouter.getInstance()
                                .build(Constant.VIDEO_CALL_ACTIVITY_ROUTE)
                                .withString(
                                    Constant.CALL_SOURCE,
                                    source.toString()
                                )
                                .withString(
                                    Constant.CALL_STATUS,
                                    Constant.CALL_CREATE
                                )
                                .withString(Constant.ANCHOR_ID, userId)
                                .navigation()
                        }
                    } else {
                        showAlterCoins(entry?:"")
                    }
                }
            }
        } else {
            //不能拨打
            if (Cache.instance.reviewPkg) {
            } else {
                if (Cache.instance.userInfo != null && Cache.instance.userInfo?.availableCoins != null) {
                    if ((Cache.instance?.userInfo?.availableCoins ?: 0) < callCoins) { //金币不足
//                        ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate("Coins_Not_Enough"));
                        showAlterCoins(entry?:"")
                    } else {
                        if (status != null && status == Constant.OFFLINE) {
                            ToastUtils.showShort(instance?.getLocalTranslate("The_anchor_is_not_online"))
                        } else if (status != null && status == Constant.BUSY) {
                            ToastUtils.showShort(instance?.getLocalTranslate("User_is_Busy"))
                        } else if (status != null && status == Constant.IN_CALL) {
                            ToastUtils.showShort(instance?.getLocalTranslate("The_user_is_on_a_call"))
                        }
                    }
                }
            }
        }
    }

    fun showAlterCoins(entry: String) {
        val handler = Handler()
        // 延迟 1 秒（1000 毫秒）执行代码
        handler.postDelayed({
            // 这里是要执行的代码
            // 例如，更新 UI 或执行其他操作
            val halfShoppingDialog = HalfShoppingDialog()
            halfShoppingDialog.entry = entry
            val currentActivity =
                currentActivity as AppCompatActivity?
            if (currentActivity != null) {
                halfShoppingDialog.show(currentActivity.supportFragmentManager, "")
            }
            val handler = Handler()

            // 延迟 1 秒（1000 毫秒）执行代码
            handler.postDelayed({ // 这里是要执行的代码
                // 例如，更新 UI 或执行其他操作
                ToastUtils.showShort(
                    instance?.getLocalTranslate(
                        "Coins Not Enough"
                    )
                )
            }, 100) // 延迟时间
        }, 100) // 延迟时间
    }
}
