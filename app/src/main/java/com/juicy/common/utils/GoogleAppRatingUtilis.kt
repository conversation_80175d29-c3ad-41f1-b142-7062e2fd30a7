package com.juicy.common.utils

import android.app.Activity
import com.google.android.gms.tasks.Task
import com.google.android.play.core.review.ReviewInfo
import com.google.android.play.core.review.ReviewManagerFactory

//import com.google.android.play.core.tasks.Task;
object GoogleAppRatingUtilis {
    @JvmStatic
    fun start(context: Activity) {
        val reviewManager = ReviewManagerFactory.create(context)
        val reviewInfoTask = reviewManager.requestReviewFlow()
        reviewInfoTask.addOnCompleteListener { task: Task<ReviewInfo?> ->
            if (task.isSuccessful) {
                if (task.result != null) {
                    reviewManager.launchReviewFlow(context, task.result!!)
                }
            }
        }
    }
}
