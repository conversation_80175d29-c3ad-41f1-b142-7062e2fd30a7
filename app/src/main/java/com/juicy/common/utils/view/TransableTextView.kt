package com.juicy.common.utils.view

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView

class TransableTextView : AppCompatTextView {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    override fun refreshDrawableState() {
        super.refreshDrawableState()

        alpha = if (isEnabled) {
            if (isPressed) {
                0.4f
            } else {
                1.0f
            }
        } else {
            0.4f
        }
    }
}
