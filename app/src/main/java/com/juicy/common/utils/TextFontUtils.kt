package com.juicy.common.utils

import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.text.Html
import android.widget.TextView

/**
 * <P>
 * 字体字符串工具类
</P> *
 */
object TextFontUtils {
    /**
     * 高亮字体的颜色
     */
    var HIGHLIGHT_COLOR: String = "#0fc264"

    /**
     * 使指定的字符串显示不同的颜色
     * @param regexStr  高亮字符串
     * @param targetStr 原字符串
     * @param textView  文本框
     */
    fun setHighlightFont(regexStr: String, targetStr: String, textView: TextView) {
        var targetStr: String = targetStr
        targetStr = targetStr.replace(
            regexStr.toRegex(),
            "<font color='" + HIGHLIGHT_COLOR + "'>" + regexStr + "</font>"
        )
        textView.setText(Html.fromHtml(targetStr))
    }

    /**
     * TextView 字体渐变
     * @param textView      文本框
     * @param startColor    起始颜色
     * @param endColor      终止颜色
     */
    fun setGradientFont(textView: TextView, startColor: String?, endColor: String?) {
        // Shader.TileMode.CLAMP：如果着色器超出原始边界范围，会复制边缘颜色
        val gradient: LinearGradient = LinearGradient(
            0f, 0f, 0f,
            textView.getPaint().getTextSize(),
            Color.parseColor(startColor), Color.parseColor(endColor),
            Shader.TileMode.CLAMP
        )

        textView.getPaint().setShader(gradient)
        // 直接调用invalidate()方法，请求重新draw()，但只会绘制调用者本身
        textView.invalidate()
    }
}