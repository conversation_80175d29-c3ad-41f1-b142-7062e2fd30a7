package com.juicy.common.utils.PayUtils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.android.billingclient.api.AcknowledgePurchaseParams
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingFlowParams.ProductDetailsParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ConsumeParams
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.ProductDetailsResponseListener
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams
import com.appsflyer.AFInAppEventParameterName
import com.appsflyer.AFInAppEventType
import com.appsflyer.AppsFlyerLib
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ToastUtils
import com.facebook.appevents.AppEventsLogger
import com.google.common.collect.ImmutableList
import com.juicy.app.JuicyApplication.Companion.juicyApplication
import com.juicy.app.modules.base.dialog.DerivativeDialog
import com.juicy.app.modules.base.dialog.DerivativeDialog.DismissCallBack
import com.juicy.app.modules.base.dialog.LoadDialog
import com.juicy.app.modules.base.dialog.PayDialog
import com.juicy.app.modules.base.dialog.PayDialog.SummitCallBack
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.app.modules.base.activity.WebViewActivity
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.UserIdParamsBean
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.model.bean.PayResultBean
import com.juicy.common.model.bean.ExtraBean.ChannelInfoBean
import com.juicy.common.model.bean.LogInfoBean
import com.juicy.common.model.event.PromotionPayStatusChangeEvent
import com.juicy.common.model.event.PayResultEvent
import com.juicy.common.model.event.PayResultStatusChangeEvent
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.networks.delegate.GetCoinGoodsSearchInterface.getCoinGoodsSearch
import com.juicy.common.networks.delegate.LogNetworkUploadInterface
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.CommonUtils.scanForActivity
import com.juicy.common.utils.LanguageManager.Companion.instance
import com.juicy.common.utils.LogBeanUtils
import com.juicy.common.utils.LogBeanUtils.createLogData
import com.juicy.common.utils.SpSaveUtil.getIntValue
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.SpSaveUtil.putIntValue
import com.juicy.common.utils.SpSaveUtil.putStringValue
import com.juicy.common.utils.ThreadUtil.execute
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.functions.Consumer
import io.reactivex.rxjava3.schedulers.Schedulers
import org.greenrobot.eventbus.EventBus
import java.io.IOException
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.Currency
import java.util.concurrent.TimeUnit


class PayUtils {
    private var good: com.juicy.common.model.bean.ActivityInfoBean? = null

    //事件类型列表
    private val listDataBean: MutableList<LogInfoBean.DataBean> = ArrayList()
    private var msg: String? = null
    private var listen: SuccessOnListen? = null
    private var failOnListen: FailOnListen? = null
    private var loadingDialog: LoadDialog? = null
    private var isOpen = false //是否打开购买弹窗过；
    private var invitationId = ""
    private var isAnchorRecharge = false
    private val context: Context?
    private var isVIP = false

    private var entry = ""

    constructor(
        context: Context?,
        entry: String,
        isVIP: Boolean,
        good: com.juicy.common.model.bean.ActivityInfoBean?,
        listen: SuccessOnListen?,
        failOnListen: FailOnListen?
    ) {
        this.entry = entry
        this.good = good
        this.listen = listen
        this.failOnListen = failOnListen
        this.context = context
        this.isVIP = isVIP
    }


    constructor(
        context: Context?,
        entry: String,
        good: com.juicy.common.model.bean.ActivityInfoBean?,
        listen: SuccessOnListen?,
        failOnListen: FailOnListen?,
        invitationId: String,
        isAnchorRecharge: Boolean
    ) {
        this.good = good
        this.entry = entry
        this.listen = listen
        this.failOnListen = failOnListen
        this.invitationId = invitationId
        this.isAnchorRecharge = isAnchorRecharge
        this.context = context
    }

    constructor(context: Context?) {
        this.context = context
    }

    @SuppressLint("CheckResult")
    fun openPayDialog() {
        if (isVIP) {
            if (showGuide()) return
            if (good == null || good!!.code == null) return
            loadingDialog = LoadDialog(context!!)
            loadingDialog!!.show()
            createOrder(null, good!!)
        } else {
            try {
                if (Cache.instance.payChannelList != null) {
                    if (Cache.instance?.payChannelList?.size?:0 > 0) {
                        if (Cache.instance?.payChannelList?.size?:0 == 1
                            && Cache.instance?.payChannelList!![0]?.payChannel?:"" == "GP") { // 直接拉起谷歌支付流程
                            //Log.e("传递选择支付方式的数据为(商品支付码)", String.valueOf(goodsResults.get(position).getCode()));
                            if (showGuide()) return
                            loadingDialog = LoadDialog(context!!)
                            loadingDialog!!.show()
                            createOrder(
                                Cache.instance.payChannelList!![0],
                                good!!
                            )
                        } else {
                            showThreePartyPayment(good!!, Cache.instance?.payChannelList!!.toMutableList())
                            if (loadingDialog != null) {
                                loadingDialog!!.dismiss()
                            }
                        }
                    } else {
                        //如果为空时,也需要拉起谷歌支付
                        //Log.e("传递选择支付方式的数据为(商品支付码)", String.valueOf(goodsResults.get(position).getCode()));
                        if (showGuide()) return
                        loadingDialog = LoadDialog(context!!)
                        loadingDialog!!.show()
                        createOrder(null, good!!)
                    }
                } else {
                    loadingDialog = LoadDialog(context!!)
                    loadingDialog!!.show()
                    RetrofitManage.instance.create(Service::class.java)
                        .payChannel()
                        .subscribeOn(Schedulers.newThread())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(Consumer<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ExtraBean>> { bpBaseBean ->
                            if (null != bpBaseBean) {
                                val listData = bpBaseBean.data!!.channelList
                                Cache.instance.lastPayChannelModel = bpBaseBean.data
                                Cache.instance.payChannelList = listData
                                if (null != listData && listData.size > 0) {
                                    if (listData.size == 1 && listData[0].payChannel == "GP") { // 直接拉起谷歌支付流程
                                        //Log.e("传递选择支付方式的数据为(商品支付码)", String.valueOf(goodsResults.get(position).getCode()));
                                        if (showGuide()) {
                                            if (loadingDialog != null) {
                                                loadingDialog!!.dismiss()
                                            }
                                            return@Consumer
                                        }
                                        createOrder(listData[0], good!!)
                                    } else {
                                        showThreePartyPayment(good!!, listData.toMutableList())
                                        if (loadingDialog != null) {
                                            loadingDialog!!.dismiss()
                                        }
                                    }
                                } else {
                                    //如果为空时,也需要拉起谷歌支付
                                    //Log.e("传递选择支付方式的数据为(商品支付码)", String.valueOf(goodsResults.get(position).getCode()));
                                    if (showGuide()) {
                                        if (loadingDialog != null) {
                                            loadingDialog!!.dismiss()
                                        }
                                        return@Consumer
                                    }
                                    createOrder(null, good!!)
                                }
                            } else {
                                ToastUtils.showShort(
                                    instance!!.getLocalTranslate(
                                        "Recharge failed, Please try again later"
                                    )
                                )
                            }
                        }, Consumer {
                            ToastUtils.showShort(
                                instance!!.getLocalTranslate(
                                    "Recharge failed, Please try again later"
                                )
                            )
                            if (loadingDialog != null) {
                                loadingDialog!!.dismiss()
                            }
                        })
                }
            } catch (e: Exception) {
                Log.e("PayUtils", "openPayDialog: " + e.message)
            }
        }
    }

    private fun showGuide(): Boolean {
        if (Cache.instance.userStratResult != null &&
            Cache.instance?.userStratResult?.isSwitchStrongGuide == true && Cache.instance.guide != null
            && Cache.instance.guide?.nativeRechargeRedirect == 0
        ) {
            val dialog = DerivativeDialog()
            dialog.setDismiss(object : DismissCallBack {
                override fun onDismiss() {
                }

            })
            if (currentActivity != null) {
                if (currentActivity is AppCompatActivity) {
                    val activity = currentActivity as AppCompatActivity?
                    dialog.show(activity!!.supportFragmentManager, "")
                }
            }
            EventBus.getDefault().post(PayResultStatusChangeEvent(2))
            return true
        }
        return false
    }

    private fun showThreePartyPayment(good: com.juicy.common.model.bean.ActivityInfoBean, listData: MutableList<ChannelInfoBean>) {
        var gpNum = 110
        for (i in listData.indices) {
            if (listData[i].payChannel == "GP") {
                gpNum = i
            }
        }
        if (gpNum != 110) {
            val bean = listData[gpNum]
            listData.remove(listData[gpNum])
            listData.add(bean)
        }
        val payDialog = PayDialog(listData, good,
            object : SummitCallBack {
                override fun onCallBack(channelInfoBean: ChannelInfoBean?) {
                    when (channelInfoBean?.payChannel) {
                        "GP" -> {
                            if (showGuide()) {
                                if (loadingDialog != null) {
                                    loadingDialog!!.dismiss()
                                }
                                return
                            }
                            createOrder(null, good)
                        }

                        "PP" -> if (null != Cache.instance.userInfo && null != Cache.instance.userInfo?.userId) {
                            requestPaypal(
                                good,
                                Cache.instance.userInfo?.userId?:""
                            )
                        }

                        else -> otherPayment(good, channelInfoBean?.payChannel?:"", channelInfoBean?.jumpType?:0)


                    }
                }

            })

        val activity = scanForActivity(context) as BaseActivity?
        payDialog.show(activity!!.supportFragmentManager, "")
    }

    @SuppressLint("CheckResult")
    private fun otherPayment(good: com.juicy.common.model.bean.ActivityInfoBean, payChannel: String, jumpType: Int) {
        RetrofitManage.instance.create(Service::class.java)
            .rechargeCreate(
                RetrofitManage.requestBody(
                    com.juicy.common.model.bean.PayInfoBean(
                        Cache.instance.userInfo?.email ?: "",
                        entry,
                        good.code!!, payChannel,
                        if (isAnchorRecharge) invitationId else good.invitationId
                    )
                )
            )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ bkBaseBean ->
                if (loadingDialog != null) {
                    loadingDialog!!.dismiss()
                }
                if (bkBaseBean.isOk) {
                    Cache.instance.thirdPayGRLogMap[bkBaseBean.data!!.orderNo] =
                        good
                    //    toastAFFBBuyTestEvent("other_pay_url", good);
                    val url = bkBaseBean.data!!.requestUrl
                    if (jumpType == 1) {
                        val joinUrl = url
                        val uri = Uri.parse(joinUrl)
                        Log.v(
                            Constant.GlobalTppEvent.CLICK,
                            "source：" + Constant.GlobalTppSource.IM + "open th：out browser"
                        )
                        val i = Intent(Intent.ACTION_VIEW, uri)
                        context!!.startActivity(i)
                    } else {
                        // TODO: 2023/4/8 应用内web
                        val intent = Intent(
                            context,
                            WebViewActivity::class.java
                        )
                        intent.putExtra("urlPath", url)
                        intent.putExtra("orderNo", bkBaseBean.data!!.orderNo)
                        intent.putExtra("mResultPage", "CoreActivity")
                        scanForActivity(context)!!.startActivityForResult(intent, 1000)
                    }
                    EventBus.getDefault().post(PayResultStatusChangeEvent(3))
                } else {
                    ToastUtils.showShort(instance!!.getLocalTranslate("The_payment_method_is_not_available_nowPlease_try_another_one"))
                    EventBus.getDefault().post(PayResultStatusChangeEvent(2))
                }
            }, {
                if (loadingDialog != null) {
                    loadingDialog!!.dismiss()
                }
                EventBus.getDefault().post(PayResultStatusChangeEvent(2))
            })
    }

    private fun requestPaypal(good: com.juicy.common.model.bean.ActivityInfoBean, userId: String) {
        //1.传tppDialog  2.商品编号  3.true  4. 邀请id  5.用户id
        RetrofitManage.instance.create(Service::class.java).paypalCreate(
            RetrofitManage.requestBody(
                com.juicy.common.model.bean.PplBean(
                    "tppDialog",
                    good.code!!,
                    true,
                    if (isAnchorRecharge) invitationId else good.invitationId,
                    userId
                )
            )
        )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(Consumer<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.PayUrlBean>> { bqBaseBean ->
                if (loadingDialog != null) {
                    loadingDialog!!.dismiss()
                }
                if (bqBaseBean.isOk) {
                    //赋值订单号、当前传入为支付订单号
                    Cache.instance.thirdPayGRLogMap[bqBaseBean.data?.orderNo?:""] =
                        good
                    //  toastAFFBBuyTestEvent("paypal_url", good);
                    // TODO: 2023/4/8 应用内web
                    val intent = Intent(
                        context,
                        WebViewActivity::class.java
                    )
                    intent.putExtra("urlPath", bqBaseBean.data!!.url)
                    intent.putExtra("orderNo", bqBaseBean.data!!.orderNo)
                    intent.putExtra("mResultPage", "CoreActivity")
                    scanForActivity(context)!!.startActivityForResult(intent, 1000)
                    EventBus.getDefault().post(PayResultStatusChangeEvent(3))
                } else {
                    ToastUtils.showShort(instance!!.getLocalTranslate("The_payment_method_is_not_available_nowPlease_try_another_one"))
                    EventBus.getDefault().post(PayResultStatusChangeEvent(2))
                }
            }, Consumer {
                if (loadingDialog != null) {
                    loadingDialog!!.dismiss()
                }
                EventBus.getDefault().post(PayResultStatusChangeEvent(2))
            })
    }

    private fun createOrder(channelInfoBean: ChannelInfoBean?, good: com.juicy.common.model.bean.ActivityInfoBean) {
        //Log步骤一、创建订单

        listDataBean.add(createLogData(Constant.CREATE_ORDER, good.code, "", 0, 0, "成功", 0))
        submitLog()
        RetrofitManage.instance
            .create(Service::class.java) //1.支付渠道   2.商品code    3.特殊商品需要传sourceId
            .rechargeCreate(
                RetrofitManage.requestBody(
                    com.juicy.common.model.bean.CreateOrderBean(
                        "GP",
                        good.code!!,
                        if (isAnchorRecharge) invitationId else good.invitationId,
                        entry
                    )
                )
            )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ bkBaseBean ->
                var requestStatus = ""
                if (null != bkBaseBean) {
                    val data = bkBaseBean.data
                    putStringValue(
                        Constant.TC_PREFIX + data!!.goodsCode,
                        data.paidAmount
                    )
                    // 创建订单成功后，调用google支付
                    goodsDetailsParams(data)
                    requestStatus = "成功"
                    EventBus.getDefault().post(PayResultStatusChangeEvent(3))
                } else {
                    requestStatus = "失败"
                    ToastUtils.showShort(
                        instance!!.getLocalTranslate(
                            "Recharge failed, Please try again later"
                        )
                    )
                    EventBus.getDefault().post(PayResultStatusChangeEvent(2))
                }
                if (loadingDialog != null) {
                    loadingDialog!!.dismiss()
                }

                //Log步骤二、创建订单回调
                listDataBean.add(
                    createLogData(
                        Constant.CONSUME_ORDER_RESPONSE,
                        bkBaseBean.data!!.goodsCode,
                        if (null != bkBaseBean.data) bkBaseBean.data!!.orderNo else "",
                        0,
                        0,
                        requestStatus,
                        bkBaseBean.code!!
                    )
                )
                submitLog()
            }, { throwable ->
                Log.e(
                    "googlePayConnect",
                    "googlePayConnect: " + throwable.message,
                    throwable
                )
                EventBus.getDefault().post(PayResultStatusChangeEvent(2))
                if (loadingDialog != null) {
                    loadingDialog!!.dismiss()
                }
                showPayFailed()
            })
    }

    /**显示支付失败Toast */
    private fun showPayFailed() {
        try {
            //主线程执行
            scanForActivity(context)!!.runOnUiThread {
                ToastUtils.showShort(
                    instance!!.getLocalTranslate("Recharge_failed_Please_try_again_later")
                )
            }
        } catch (e: Exception) {
        }
    }

    private val billingClient: BillingClient?
        get() = MyBillingClient.Companion.instance.billingClient

    //购买回调监听
    private fun goodsDetailsParams(data: PayResultBean) {
        googlePayConnect(data.goodsCode, data.orderNo)
    }

    @SuppressLint("CheckResult")
    private fun onSuccessPay(
        purchase: Purchase,
        goodsCode: String,
        orderNo: String,
        paidAmount: String
    ) {
        listDataBean.add(createLogData("verify_order", goodsCode, orderNo, 0, 0, "成功", 0))
        submitLog()
        // 调用接口验证订单是否有效
        RetrofitManage.instance.create(Service::class.java)
            .rechargePayment(
                RetrofitManage.requestBody(
                    com.juicy.common.model.bean.CheckOrderParamBean(
                        orderNo,
                        purchase.originalJson,
                        purchase.signature
                    )
                )
            )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ baseBean: com.juicy.common.model.bean.NetResponseBean<*>? ->
                if (baseBean!!.data is Boolean) {
                    if ((baseBean.data as Boolean?)!!) {
                        if (billingClient != null) {
                            EventBus.getDefault().post(PromotionPayStatusChangeEvent())
                            //                                listen.onListen();
                            updateUser()
                            if (isVIP) {
                                acknowledge(purchase, goodsCode, orderNo, paidAmount)
                            } else {
                                consume(purchase, goodsCode, orderNo, paidAmount)
                            }
                        }
                        val orderStatus: String
                        if (baseBean.isOk) {
                            orderStatus = "成功"
                            ToastUtils.showShort(instance!!.getLocalTranslate("Payment_successful"))
                            EventBus.getDefault().post(PayResultEvent(true))
                        } else {
                            orderStatus = "失败"
                            ToastUtils.showShort(instance!!.getLocalTranslate("Recharge_failed_Please_try_again_later"))
                        }
                        listDataBean.add(
                            createLogData(
                                Constant.VERIFY_ORDER_RESPONSE,
                                goodsCode, orderNo, 0, 0, orderStatus, baseBean.code!!
                            )
                        )
                        submitLog()
                    }
                }
            },
                { throwable ->
                    Log.e(
                        "googlePayConnect",
                        "googlePayConnect: " + throwable.message,
                        throwable
                    )
                })
    }

    private fun updateUser() {
        execute {
            try {
                val execute = RetrofitManage
                    .instance
                    .create(Service::class.java)
                    .getUserInfo2(
                        UserIdParamsBean(
                            getStringValue(
                                SpKeyPool.USER_ID_DEVICE,
                                ""
                            )!!
                        )
                    )
                    .execute()
                if (execute.isSuccessful) {
                    if (execute.body()!!.data != null) {
                        Cache.instance.userInfo = execute.body()!!.data
                    }
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    private fun acknowledge(
        purchase: Purchase,
        goodsCode: String,
        orderNo: String,
        paidAmount: String
    ) {
        listDataBean.add(createLogData("acknowledged_order", goodsCode, orderNo, 0, 0, "成功", 0))
        submitLog()
        acknowledgeCommodity(purchase, goodsCode, orderNo, paidAmount)
    }

    //筛选订阅和商品的本地化数据
    fun batchQueryPurchases() {
//        new PayUtils(ActivityUtils.getTopActivity()).batchQueryPurchasesWithList(false,Cache.getInstance().coinGoods);
        if (Cache.instance.subscribeGood != null) {
            PayUtils(ActivityUtils.getTopActivity()).batchQueryPurchasesWithList(
                true, listOf<com.juicy.common.model.bean.ActivityInfoBean>(
                        Cache.instance.subscribeGood?: com.juicy.common.model.bean.ActivityInfoBean()
                    )?: listOf<com.juicy.common.model.bean.ActivityInfoBean>()
                )

        }
    }

    fun batchQueryPurchasesWithList(isSubscription: Boolean, list: List<com.juicy.common.model.bean.ActivityInfoBean>) {
        val codes: MutableList<String?> = ArrayList()
        for (goods in list) {
            codes.add(goods.code)
        }
        batchQueryPurchases(isSubscription, codes)
    }

    //批量查询GooglePay商品
    fun batchQueryPurchases(isSubscription: Boolean, list: List<String?>) {
        if (billingClient == null) {
            return
        }
        //判断是否审核模式
        if (!Cache.instance.reviewPkg) {
            return
        }
        billingClient!!.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                val code = billingResult.responseCode
                if (code != BillingClient.BillingResponseCode.OK) {
                    return
                }
                //list 转  List<Product>
                val products: MutableList<QueryProductDetailsParams.Product> = ArrayList()
                for (goodsCode in list) {
                    products.add(
                        QueryProductDetailsParams.Product.newBuilder().setProductId(
                            goodsCode!!
                        )
                            .setProductType(if (isSubscription) BillingClient.ProductType.SUBS else BillingClient.ProductType.INAPP)
                            .build()
                    )
                }
                billingClient?.queryProductDetailsAsync(QueryProductDetailsParams.newBuilder()
                    .setProductList(products).build(),
                    ProductDetailsResponseListener { billingResult, list ->
                        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                            for (productDetails in list) {
                                Log.d("PayUtils", "productDetails : $productDetails")
                                //判断是否订阅
                                if (productDetails.productType == BillingClient.ProductType.SUBS) {
                                    Cache.instance.storePriceMap[productDetails.productId] =
                                        productDetails.subscriptionOfferDetails!![0].pricingPhases.pricingPhaseList[0].priceCurrencyCode + "" + productDetails.subscriptionOfferDetails!![0].pricingPhases.pricingPhaseList[0].formattedPrice
                                    EventBus.getDefault()
                                        .post(com.juicy.common.model.bean.LocalPriceChangeBean())
                                } else {
                                    Cache.instance.storePriceMap[productDetails.productId] =
                                        productDetails.oneTimePurchaseOfferDetails!!
                                            .priceCurrencyCode + productDetails.oneTimePurchaseOfferDetails!!
                                            .formattedPrice
                                }
                            }
                        }
                    })
            }

            //连接失败
            override fun onBillingServiceDisconnected() {
                Log.e("PayUtils", "connect GooglePay failed，please retry")
            }
        })
    }

    private fun acknowledgeCommodity(
        purchase: Purchase,
        goodsCode: String,
        orderNo: String,
        paidAmount: String
    ) {
        val purchaseToken = purchase.purchaseToken
        if (billingClient == null) {
            return
        }
        Log.d("PayUtils", "consumeGPProduct : $purchaseToken")
        billingClient!!.acknowledgePurchase(
            AcknowledgePurchaseParams.newBuilder().setPurchaseToken(purchaseToken).build()
        ) { billingResult -> //1.消费失败时,重试   2.消费成功处理用户状态刷新,金币刷新等购买时间打点等
            if (billingResult.responseCode != BillingClient.BillingResponseCode.OK) {
                Log.d("PayUtils", "consumeAsync fail : $purchaseToken")
            }
            val orderStatus: String
            if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                orderStatus = "成功"
                val fb = getStringValue(SpKeyPool.HACK_REVENUE_FACTOR_FB, "")
                var fbPrice = BigDecimal(paidAmount)
                if (null != fb && !fb.isEmpty()) {
                    fbPrice =
                        BigDecimal(paidAmount).multiply(BigDecimal(fb))
                }
                fbPrice = fbPrice.setScale(3, RoundingMode.HALF_UP)
                logFaceBookPurchaseEvent(
                    "purchase_success",
                    fbPrice.toString(),
                    purchase.orderId,
                    orderNo
                )

                val af = getStringValue(SpKeyPool.HACK_REVENUE_FACTOR_AF, "")
                var afPrice = BigDecimal(paidAmount)
                if (null != af && !af.isEmpty()) {
                    afPrice =
                        BigDecimal(paidAmount).multiply(BigDecimal(af))
                }
                afPrice = afPrice.setScale(3, RoundingMode.HALF_UP)
                logAppsFlyerPurchaseEvent(afPrice.toString(), purchase.orderId)
                if (getIntValue(SpKeyPool.MY_RECHARGE_LOG, 0) == 0) {
                    // TODO: 2023/4/8 引导弹窗
                    putIntValue(
                        SpKeyPool.MY_RECHARGE_LOG,
                        getIntValue(SpKeyPool.MY_RECHARGE_LOG, 0) + 1
                    )
                }
            } else {
                orderStatus = "失败"
            }
            listDataBean.add(
                createLogData(
                    "acknowledged_order_resp",
                    goodsCode,
                    orderNo,
                    0,
                    0,
                    orderStatus,
                    billingResult.responseCode
                )
            )
            //提交日志打点上报
            submitLog()
        }
    }

    private fun consume(
        purchase: Purchase,
        goodsCode: String,
        orderNo: String,
        paidAmount: String
    ) {
        listDataBean.add(createLogData("consume_order", goodsCode, orderNo, 0, 0, "成功", 0))
        submitLog()
        consumeGPProduct(purchase, goodsCode, orderNo, paidAmount, 0)
    }

    fun logAppsFlyerPurchaseEvent(price: String, googleOrderId: String?) {
        var priceDouble = 0.00
        try {
            priceDouble = price.toDouble()
        } catch (e: NumberFormatException) {
        }
        try {
            val eventValue: MutableMap<String, Any?> = HashMap()
            eventValue[AFInAppEventParameterName.REVENUE] = priceDouble
            eventValue[AFInAppEventParameterName.PRICE] = priceDouble
            eventValue[AFInAppEventParameterName.CURRENCY] = "USD"
            eventValue["google_orderId"] = googleOrderId
            AppsFlyerLib.getInstance().logEvent(juicyApplication, AFInAppEventType.PURCHASE, eventValue)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun logFaceBookPurchaseEvent(
        event: String?,
        price: String,
        orderId: String?,
        orderNo: String?
    ) {
        try {
            val logger = AppEventsLogger.newLogger(context)
            val parameters = Bundle()
            // 自定义的购买事件中绑定谷歌订单id以及后台订单号
            parameters.putString("google_order_id", orderId)
            parameters.putString("order_no", orderNo)
            logger.logEvent(event, price.toDouble(), parameters)
            logger.logPurchase(BigDecimal(price), Currency.getInstance("USD"))
        } catch (t: Throwable) {
            t.printStackTrace()
        }
    }

    private fun consumeGPProduct(
        purchase: Purchase,
        goodsCode: String,
        orderNo: String,
        paidAmount: String,
        retryCount: Int
    ) {
        if (billingClient == null) {
            return
        }
        billingClient!!.consumeAsync(
            ConsumeParams.newBuilder().setPurchaseToken(purchase.purchaseToken).build()
        ) { billingResult: BillingResult, purchaseToken1: String ->
            //1.消费失败时,重试   2.消费成功处理用户状态刷新,金币刷新等购买时间打点等
            if (billingResult.responseCode != BillingClient.BillingResponseCode.OK) {
                Log.d("PayUtils", "consumeAsync fail : $purchaseToken1")
                // 若重试次数小于3次,则重试
                if (retryCount < 3) {
                    // 延迟2秒重试
                    try {
                        TimeUnit.SECONDS.sleep(2)
                    } catch (ignored: InterruptedException) {
                    }
                    consumeGPProduct(purchase, goodsCode, orderNo, paidAmount, retryCount + 1)
                }
                // 若重试次数大于等于3次,则不再重试,等用户再次打开app后通过补单流程，再次尝试消费
                if (retryCount >= 3) return@consumeAsync
                return@consumeAsync
            }
            EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
            val orderStatus = "成功"
            val fb = getStringValue(SpKeyPool.HACK_REVENUE_FACTOR_FB, "")
            var fbPrice = BigDecimal(paidAmount)
            if (null != fb && !fb.isEmpty()) {
                fbPrice = BigDecimal(paidAmount).multiply(BigDecimal(fb))
            }
            fbPrice = fbPrice.setScale(3, RoundingMode.HALF_UP)
            logFaceBookPurchaseEvent(
                "purchase_success",
                fbPrice.toString(),
                purchase.orderId,
                orderNo
            )

            val af = getStringValue(SpKeyPool.HACK_REVENUE_FACTOR_AF, "")
            var afPrice = BigDecimal(paidAmount)
            if (null != af && !af.isEmpty()) {
                afPrice = BigDecimal(paidAmount).multiply(BigDecimal(af))
            }
            afPrice = afPrice.setScale(3, RoundingMode.HALF_UP)
            logAppsFlyerPurchaseEvent(afPrice.toString(), purchase.orderId)
            Log.e(
                "purchase_success",
                "googleOrderId:" + purchase.orderId + " orderNo: " + orderNo + " goodsCode: " + goodsCode + " paidAmount: " + paidAmount
            )
            if (getIntValue(SpKeyPool.MY_RECHARGE_LOG, 0) == 0) {
                putIntValue(
                    SpKeyPool.MY_RECHARGE_LOG,
                    getIntValue(SpKeyPool.MY_RECHARGE_LOG, 0) + 1
                )
            }
            listDataBean.add(
                createLogData(
                    "consume_order_resp",
                    goodsCode,
                    orderNo,
                    0,
                    0,
                    orderStatus,
                    billingResult.responseCode
                )
            )
            //提交日志打点上报
            submitLog()
        }
    }

    @SuppressLint("CheckResult")
    private fun submitLog() {
        Observable.fromCallable {
            LogNetworkUploadInterface.LogResult(LogBeanUtils.submitLogData(listDataBean))
        }.subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object :Consumer<Any>{
                override fun accept(t: Any) {
                    val me = t as? Boolean ?: false
                    if (me) {
                        Log.e("PayUtils", "log uploaded, listDataBean: " + listDataBean);
                        listDataBean.clear();
                    } else
                        Log.e("PayUtils", "log upload false");
                }

            }, object : Consumer<Throwable> {
                override fun accept(t: Throwable) {
                    TODO("Not yet implemented")
                }

            })
    }

    //    private void toastAFFBBuyTestEvent(String event, BhBean goodsResult) {
    //        if (BuildConfig.DEBUG) {
    //            String fb = SpSaveUtil.getStringValue(SpKeyPool.HACK_REVENUE_FACTOR_FB, "");
    //            BigDecimal fbPrice = new BigDecimal(goodsResult.getPrice().toString());
    //            if (null != fb && !fb.isEmpty()) {
    //                fbPrice = new BigDecimal(goodsResult.getPrice().toString()).multiply(new BigDecimal(fb));
    //            } else {
    //                fb = "为空";
    //            }
    //            fbPrice = fbPrice.setScale(3, RoundingMode.HALF_UP);
    //            ToastUtils.showShort(event + " 商品原价: " + goodsResult.getPrice() + " FB系数: " + fb + " 处理后价格: " + fbPrice.toString());
    //            String af = SpSaveUtil.getStringValue(SpKeyPool.HACK_REVENUE_FACTOR_AF, "");
    //            BigDecimal afPrice = new BigDecimal(goodsResult.getPrice().toString());
    //            if (null != af && !af.isEmpty()) {
    //                afPrice = new BigDecimal(goodsResult.getPrice().toString()).multiply(new BigDecimal(af));
    //            } else {
    //                af = "为空";
    //            }
    //            afPrice = afPrice.setScale(3, RoundingMode.HALF_UP);
    //            ToastUtils.showShort(event + " 商品原价: " + goodsResult.getPrice() + " AF系数: " + af + " 处理后价格: " + afPrice.toString(), 1500L);
    //        }
    //    }
    private fun googlePayConnect(goodsCode: String, orderNo: String) {
        billingClient!!.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                val code = billingResult.responseCode
                if (code != BillingClient.BillingResponseCode.OK) {
                    msg = billingResult.debugMessage
                    ToastUtils.showShort(instance!!.getLocalTranslate("Failed_to_connect_to_Google_Pay_please_try_again_later"))
                    Log.e("PayUtils", "Connect Fail    code = $code    msg = $msg")
                    FailFunction(msg!!)
                    return
                }
                Log.e("PayUtils", "Connect Success")
                //Log步骤三、请求库存列表
                listDataBean.add(
                    createLogData(
                        Constant.REVIEW_ORDER,
                        goodsCode,
                        orderNo,
                        0,
                        0,
                        "成功",
                        0
                    )
                )
                submitLog()
                checkProductDetails(goodsCode, orderNo)
            }

            //连接失败
            override fun onBillingServiceDisconnected() {
                Log.e("PayUtils", "connect GooglePay failed，please retry")
                showPayFailed()
            }
        })
    }

    private fun checkProductDetails(goodsCode: String, orderNo: String) {
        val queryProductDetailsParams =
            QueryProductDetailsParams.newBuilder().setProductList(
                ImmutableList.of(
                    QueryProductDetailsParams.Product.newBuilder()
                        .setProductId(goodsCode)
                        .setProductType(if (isVIP) BillingClient.ProductType.SUBS else BillingClient.ProductType.INAPP)
                        .build()
                )
            ).build()
        billingClient!!.queryProductDetailsAsync(
            queryProductDetailsParams
        ) { billingResult: BillingResult, list: List<ProductDetails> ->
            val code = billingResult.responseCode
            Log.e(
                "PayUtils",
                "purchase    code = " + code + "    msg = " + billingResult.debugMessage
            )
            if (code != BillingClient.BillingResponseCode.OK || list.isEmpty()) {
                msg = billingResult.debugMessage
                Log.e("PayUtils", "purchase failed   code = $code    msg = $msg")
                listDataBean.add(
                    createLogData(
                        "review_order_resp",
                        goodsCode,
                        orderNo,
                        0,
                        0,
                        "失败",
                        -1
                    )
                )
                submitLog()
                showPayFailed()
                FailFunction(msg!!)
                return@queryProductDetailsAsync
            }
            Log.e("PayUtils", "query goods success")
            listDataBean.add(
                createLogData(
                    "review_order_resp",
                    goodsCode,
                    orderNo,
                    0,
                    0,
                    "成功",
                    0
                )
            )
            submitLog()
            //调起谷歌支付
            callBuy(list[0], goodsCode, orderNo, isVIP)
        }
    }

    private fun callBuy(
        productDetails: ProductDetails,
        goodsCode: String,
        orderNo: String,
        isVIP: Boolean
    ) {
        //Log步骤五、调起支付、调起支付前打点
        listDataBean.add(createLogData(Constant.LAUNCH_PAY, goodsCode, orderNo, 0, 0, "成功", 0))
        submitLog()
        var productDetailsParamsList: List<ProductDetailsParams>? = null
        if (isVIP) {
            //订阅
            var offToken = ""
            if (productDetails.subscriptionOfferDetails != null && !productDetails.subscriptionOfferDetails!!
                    .isEmpty()
            ) {
                offToken = productDetails.subscriptionOfferDetails!![0].offerToken
            }


            // 构建产品详情参数List
            productDetailsParamsList = ImmutableList.of(
                ProductDetailsParams.newBuilder()
                    .setProductDetails(productDetails).setOfferToken(offToken).build()
            )
        } else {
            //非订阅
            // 构建产品详情参数List
            productDetailsParamsList = ImmutableList.of(
                ProductDetailsParams.newBuilder()
                    .setProductDetails(productDetails).build()
            )
        }


        try {
            // 设置产品详情参数及订单号
            val billingFlowParams = BillingFlowParams.newBuilder()
                .setProductDetailsParamsList(productDetailsParamsList)
                .setObfuscatedAccountId(orderNo).build()
            val billingResult =
                billingClient!!.launchBillingFlow((context as Activity?)!!, billingFlowParams)
            val code = billingResult.responseCode
            if (code != BillingClient.BillingResponseCode.OK) {
                msg = billingResult.debugMessage
                Log.e("PayUtils", "query goods failed code = $code    msg = $msg")
                showPayFailed()
                FailFunction(msg!!)
                return
            }
            Log.e("PayUtils", "pay goods$productDetails")
        } catch (e: Exception) {
            //支付失败
        }
        // 后续支付结果在onPurchasesUpdated中回调
    }

    private fun FailFunction(msg: String) {
        if (!isOpen) {
            if (failOnListen != null) {
                failOnListen!!.onListen()
                isOpen = true
            }
        }
    }

    interface SuccessOnListen {
        fun onListen()
    }

    fun interface FailOnListen {
        fun onListen()
    }

    fun startPurchaseListen() {
        // 支付后进入的监听流程，有可消耗的订单，就会进入该流程
        val billingClient = BillingClient.newBuilder(context!!)
            .setListener { billingResult: BillingResult, list: List<Purchase>? ->
                val code = billingResult.responseCode
                msg = billingResult.debugMessage
                Log.i("PayUtils", "onPurchasesUpdated：code = $code    msg = $msg")
                // 无商品的list，不做处理，这种情况也不会出现，以防万一之举，避免下面流程的空指针异常
                if (list == null || list.isEmpty()) {
                    Log.e("PayUtils", "onPurchasesUpdated：list is null or empty")
                    return@setListener
                }
                // APP中一次购买对应一个订单，一个订单只包含一个商品，所以这里直接取第一个商品即可
                val purchase = list[0]
                val goodsCode = purchase.products[0]
                // 购买时，要求必须要设置obfuscatedAccountId，这里取出来，用于后面的消耗
                val accountIdentifiers = purchase.accountIdentifiers
                if (accountIdentifiers == null) {
                    Log.e("PayUtils", "onPurchasesUpdated：accountIdentifiers is null")
                    listDataBean.add(
                        createLogData(
                            "launch_pay_resp",
                            goodsCode,
                            "",
                            0,
                            0,
                            "onPurchasesUpdated：accountIdentifiers is null",
                            1
                        )
                    )
                    submitLog()
                    return@setListener
                }
                when (code) {
                    BillingClient.BillingResponseCode.OK -> {
                        val paidAmount = getPaidAmountByGoodsCode(goodsCode)
                        listDataBean.add(
                            createLogData(
                                "launch_pay_resp",
                                goodsCode,
                                accountIdentifiers.obfuscatedAccountId,
                                0,
                                0,
                                "成功",
                                code
                            )
                        )
                        submitLog()
                        // 进行验证订单，消耗等操作
                        onSuccessPay(
                            purchase, goodsCode,
                            accountIdentifiers.obfuscatedAccountId!!, paidAmount
                        )
                    }

                    BillingClient.BillingResponseCode.USER_CANCELED -> {
                        listDataBean.add(
                            createLogData(
                                "launch_pay_resp",
                                goodsCode,
                                accountIdentifiers.obfuscatedAccountId,
                                0,
                                0,
                                "取消",
                                code
                            )
                        )
                        submitLog()
                        FailFunction(msg!!)
                        showPayFailed()
                    }

                    else -> {
                        listDataBean.add(
                            createLogData(
                                "launch_pay_resp",
                                goodsCode,
                                accountIdentifiers.obfuscatedAccountId,
                                0,
                                0,
                                "失败",
                                code
                            )
                        )
                        submitLog()
                        FailFunction(msg!!)
                        showPayFailed()
                        Log.e("PayUtils", "pay failed：code = $code    msg = $msg")
                    }
                }
            }.enablePendingPurchases().build()
        MyBillingClient.Companion.instance.billingClient = (billingClient)
        this.billingClient!!.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                val code = billingResult.responseCode
                if (code != BillingClient.BillingResponseCode.OK) {
                    msg = billingResult.debugMessage
                    ToastUtils.showShort(instance!!.getLocalTranslate("Failed_to_connect_to_Google_Pay_please_try_again_later"))
                    Log.e("PayUtils", "Connect Fail    code = $code    msg = $msg")
                    FailFunction(msg!!)
                    return
                }
                Log.e("PayUtils", "Connect Success")
                //Log步骤三、请求库存列表
                submitLog()
                // 执行补单流程
                restorePurchases()
            }

            //连接失败
            override fun onBillingServiceDisconnected() {
                Log.e("PayUtils", "connect GooglePay failed，please retry")
            }
        })
        resetGoodData()
    }

    private fun resetGoodData() {
        if (context == null) {
            return
        }
        getCoinGoodsSearch(
            context,
            PlayParamsBean(true),
            object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>() {
                override fun onNext(goodsData: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>) {
                    super.onNext(goodsData)
                    if (goodsData.data != null && !goodsData.data!!.isEmpty()) {
                        val coinGoods: MutableList<com.juicy.common.model.bean.ActivityInfoBean> = ArrayList()
                        for (i in goodsData.data!!.indices) {
                            val bhBean = goodsData.data!![i]
                            if (bhBean.type == "1") { //订阅
                                Cache.instance.subscribeGood = bhBean
                            } else {
                                coinGoods.add(bhBean)
                            }
                        }
                        Cache.instance.coinGoods = coinGoods
                        PayUtils(ActivityUtils.getTopActivity()).batchQueryPurchases()
                    }
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                }
            })
    }

    // 补单流程
    fun restorePurchases() {
        val params =
            QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.INAPP)
        if (null == billingClient) return
        //Log步骤三、请求库存列表
        listDataBean.add(createLogData("review_order", "", "", 0, 0, "启动查询，进入补单流程", 0))
        billingClient!!.queryPurchasesAsync(params.build()) { billingResult: BillingResult, list: List<Purchase?> ->
            val code = billingResult.responseCode
            if (code != BillingClient.BillingResponseCode.OK) {
                val msg = billingResult.debugMessage
                Log.e(
                    "PayUtils",
                    "get not Consume Purchases failed code = $code  msg = $msg"
                )
                FailFunction(msg)
                listDataBean.add(
                    createLogData(
                        "review_order_resp", "",
                        "", 0, 0, "启动查询失败$msg", code
                    )
                )
                submitLog()
                showPayFailed()
                return@queryPurchasesAsync
            }
            Log.e("PayUtils", "get not Consume Purchases success")
            listDataBean.add(
                createLogData(
                    "review_order_resp", "",
                    "", 0, 0, "查询到待补订单", code
                )
            )
            submitLog()
            if (list.isEmpty()) {
                Log.e("PayUtils", "not Consume Purchases isEmpty")
                return@queryPurchasesAsync
            }
            for (purchase in list) {
                if (null != purchase) {
                    var orderNo: String? = ""
                    if (null != purchase.accountIdentifiers) {
                        orderNo = purchase.accountIdentifiers!!.obfuscatedAccountId
                    }
                    var goodsCode = ""
                    if (!purchase.products.isEmpty()) {
                        goodsCode = purchase.products[0]
                    }
                    val paidAmount = getPaidAmountByGoodsCode(goodsCode)
                    onSuccessPay(purchase, goodsCode, orderNo!!, paidAmount)
                }
            }
        }
    }

    private fun getPaidAmountBySubscribeGoodsCode(goodsCode: String): String {
        if (null != Cache.instance.subscribeGoods) {
            for (goods in Cache.instance.subscribeGoods!!) {
                if (!goodsCode.isEmpty() && null != goods && null != goods.code && goods.code == goodsCode) {
                    return goods.price.toString()
                }
            }
        }
        return "0"
    }

    private fun getPaidAmountByGoodsCode(goodsCode: String): String {
        val paidAmount = getStringValue(Constant.TC_PREFIX + goodsCode, null)
        if (paidAmount != null) {
            return paidAmount
        }
        if (null != Cache.instance.coinGoods) {
            for (goods in Cache.instance.coinGoods!!) {
                if (!goodsCode.isEmpty() && null != goods && null != goods.code && goods.code == goodsCode) {
                    return goods.price.toString()
                }
            }
        }
        return "0"
    }

    // 三方支付埋点上报
    fun purchaseFinishedEvent(paidAmount: String?, orderNo: String?) {
        //Facebook 购买事件打点
        val fb = getStringValue(SpKeyPool.HACK_REVENUE_FACTOR_FB, "")
        var fbPrice = BigDecimal(paidAmount)
        if (null != fb && !fb.isEmpty()) {
            fbPrice = BigDecimal(paidAmount).multiply(BigDecimal(fb))
        }
        fbPrice = fbPrice.setScale(3, RoundingMode.HALF_UP)
        logFaceBookPurchaseEvent("purchase_success", fbPrice.toString(), null, orderNo)

        val af = getStringValue(SpKeyPool.HACK_REVENUE_FACTOR_AF, "")
        var afPrice = BigDecimal(paidAmount)
        if (null != af && !af.isEmpty()) {
            afPrice = BigDecimal(paidAmount).multiply(BigDecimal(af))
        }
        afPrice = afPrice.setScale(3, RoundingMode.HALF_UP)
        logAppsFlyerPurchaseEvent(afPrice.toString(), null)
    }
}



