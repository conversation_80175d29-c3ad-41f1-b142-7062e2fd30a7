package com.juicy.common.utils

import android.util.Log
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.DeviceUtils
import com.juicy.common.model.bean.LogInfoBean
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import java.util.UUID

object LogBeanFactory {
    @JvmField
    val TAG: String = LogBeanFactory::class.java.simpleName

    //支付流程事件数组
    //1.事件类型     2.商品id   3.后台订单id  4.当前事物耗时  5.事物间隔总耗时  6.响应结果  7.响应码
    fun getCreatePayLog(
        event: String, shopCode: String, orderNoCode: String, durationTime: Long,
        elapsedTime: Long, result: String, resultCode: Int
    ): LogInfoBean.DataBean {
        //单个事件bean
        //列表中存储所有事件类型内容
        return getDataBean(event, shopCode, orderNoCode, result, resultCode)
    }

    private fun getDataBean(
        event: String,
        shopCode: String,
        orderNoCode: String,
        result: String,
        resultCode: Int
    ): LogInfoBean.DataBean {
        val dataBean = LogInfoBean.DataBean()
        //事件类型
        dataBean.event = event
        //商品码
        dataBean.code = shopCode
        //uuid
        //dataBean.setUuid(mUUID);
        //订单号
        dataBean.orderId = orderNoCode
        //当前事物执行耗时
        //dataBean.setDurationTime(durationTime);
        //总耗时(从上一个时间之后,到这个时间之间的时间)
        //dataBean.setElapsedTime(elapsedTime);
        //结果(从Create_order开始，到各自日志点总耗时)
        dataBean.result = result
        //结果状态码
        dataBean.resultCode = resultCode
        return dataBean
    }

    //通话流程行为事件数组
    //1.通道名称  2.事件  3.详细信息一  4.详细信息二  5.时间戳
    @JvmStatic
    fun getCreateCallLog(
        channelName: String,
        action: String,
        ext: String,
        ext2: String,
        tm: Long
    ): com.juicy.common.model.bean.CallLogInfoBean.DataBean {
        return getDataBean(channelName, action, ext, ext2, tm)
    }

    private fun getDataBean(
        channelName: String,
        action: String,
        ext: String,
        ext2: String,
        tm: Long
    ): com.juicy.common.model.bean.CallLogInfoBean.DataBean {
        val dataBean = com.juicy.common.model.bean.CallLogInfoBean.DataBean()
        dataBean.channelName = channelName
        dataBean.action = action
        dataBean.ext = ext
        dataBean.ext2 = ext2
        dataBean.tm = tm
        return dataBean
    }

    //完整的通话流程日志Bean数据
    fun getSubmitCallLog(callLogListBean: List<com.juicy.common.model.bean.CallLogInfoBean.DataBean>): List<com.juicy.common.model.bean.CallLogInfoBean> {
        val dataBeanList: MutableList<com.juicy.common.model.bean.CallLogInfoBean> = ArrayList()
        val mj = getMj(callLogListBean)
        val add = dataBeanList.add(mj)
        if (!add) Log.e(TAG, "add " + com.juicy.common.model.bean.CallLogInfoBean::class.java.simpleName + " failed")
        return dataBeanList
    }

    private fun getMj(callLogListBean: List<com.juicy.common.model.bean.CallLogInfoBean.DataBean>): com.juicy.common.model.bean.CallLogInfoBean {
        val dataBean = com.juicy.common.model.bean.CallLogInfoBean()
        dataBean.typeName = Constant.EVENT
        dataBean.subType = Constant.LIVE_CALL
        dataBean.behavior = Constant.EVENT
        dataBean.data = callLogListBean
        return dataBean
    }

    //完整的支付流程日志Bean数据
    fun getSubmitPayLog(listDataBean: List<LogInfoBean.DataBean>): List<LogInfoBean> {
        val logBeanList: MutableList<LogInfoBean> = ArrayList()
        for (i in listDataBean.indices) {
            //单次时间提交,设置同一个uuid
            listDataBean[i].uuid = UUID.randomUUID().toString()
        }
        val bi = getNi(listDataBean)
        logBeanList.add(bi)
        return logBeanList
    }

    private fun getNi(listDataBean: List<LogInfoBean.DataBean>): LogInfoBean {
        val logBean = LogInfoBean()
        logBean.data = listDataBean //上报日志数据数组
        logBean.log_type = Constant.EVENT //日志属性
        logBean.subtype = Constant.PURCHASE_DETAIL //日志类型
        logBean.behavior = Constant.EVENT //日志属性
        //if(null!=CommonPool.getInstance().IsNull(Constants.UTM_SOURCE)){
        //    logBean.setUtm_source(CommonPool.getInstance().IsNull(Constants.UTM_SOURCE));//af返回的归因信息
        //}
        logBean.utm_source = "" //af返回的归因信息
        logBean.android_id = Constant.ANDROID_ID //android设备id
        if (null != Cache.instance?.userInfo && null != Cache.instance?.userInfo?.userId) {
            logBean.user_id = Cache.instance?.userInfo?.userId //用户id
        }
        logBean.pkg = Constant.PACKAGE_NAME //包名
        logBean.ver = Constant.VERSION_CODE.toString() //版本号
        logBean.platform = Constant.PLATFORM_SDK_VER //sdk ver
        logBean.model = DeviceUtils.getModel() //手机型号
        logBean.lan_id = UUID.randomUUID().toString() //客户端启动唯一标识,用于区分是否同一次启动
        logBean.sec_id = 0 //日志次数 0递增
        logBean.sys_lan = AppMessageUtil.defaultLanguageCode //手机系统语言
        if (null != Cache.instance?.userInfo && null != Cache.instance?.userInfo?.country) {
            logBean.country = Cache.instance?.userInfo?.country //用户国家
        }
        logBean.setIs_in_bg(!AppUtils.isAppForeground()) //应用是否位于后台
        if (null != Cache.instance?.userInfo && null != Cache.instance?.userInfo?.club) {
            logBean.setIs_in_bg(Cache.instance?.userInfo?.club?:false) //是否是主播
        }
        logBean.client_type = Constant.COMMON
        logBean.bizver = ""
        logBean.tm = System.currentTimeMillis() //当前时间戳
        return logBean
    }
}
