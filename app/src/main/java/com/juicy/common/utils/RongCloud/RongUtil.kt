package com.juicy.common.utils.RongCloud

import android.app.Application
import android.content.Context
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.ImageView
import androidx.fragment.app.FragmentActivity
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.ActivityUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CircleCrop
import com.bumptech.glide.request.RequestOptions
import com.google.gson.Gson
import com.juicy.app.modules.base.dialog.ExtremelyCallDialogFragment
import com.juicy.app.modules.base.dialog.MessageNoticationDialog
import com.juicy.common.networks.Service
import com.juicy.common.model.event.DialogShowStatusEvent
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.networks.request.SocketUtil.SocketUtils
import com.juicy.common.rcMessage.NFMsg
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.MyFileTool.APP_FILE_PATH
import com.juicy.common.utils.MyFileTool.isExist
import com.juicy.common.utils.SpSaveUtil.getIntValue
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.SpSaveUtil.putIntValue
import com.juicy.common.utils.SpSaveUtil.putStringValue
import com.juicy.common.utils.ThreadUtil.runDelayOnUIThread
import com.juicy.common.utils.ThreadUtil.runOnUIThread
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.functions.Consumer
import io.reactivex.rxjava3.schedulers.Schedulers
import io.rong.imkit.GlideKitImageEngine
import io.rong.imkit.IMCenter
import io.rong.imkit.RongIM
import io.rong.imkit.config.ConversationClickListener
import io.rong.imkit.config.ConversationListBehaviorListener
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversation.extension.RongExtensionManager
import io.rong.imkit.conversationlist.model.BaseUiConversation
import io.rong.imkit.notification.DefaultInterceptor
import io.rong.imkit.notification.RongNotificationManager
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.InitOption
import io.rong.imlib.model.Message
import io.rong.imlib.model.UserInfo
import io.rong.message.CommandMessage
import io.rong.message.TextMessage
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import java.io.File

object RongUtil {
    var TAG: String = "RongUtils"
    var loadNum: Int = 0

    @JvmStatic
    fun init(application: Application?, connectCallBack: ConnectCallBack) {
        var areaCode: InitOption.AreaCode? = InitOption.AreaCode.BJ
        if (Cache.instance?.areaCode != null) {
            areaCode = Cache.instance?.areaCode
        }
        val initOption = InitOption.Builder()
            .setAreaCode(areaCode)
            .build()

        var appkey = getStringValue(SpKeyPool.RCK_KEY, "")
        val newAppkey = getStringValue(SpKeyPool.RC_APP_KEY, "")
        if (!TextUtils.isEmpty(newAppkey)) {
            appkey = newAppkey
        }

        //初始化之前先做数据融合
        checkOldToNew()
        //保存最后迁移key
        putStringValue(SpKeyPool.LAST_RONG_KEY, appkey)


        IMCenter.init(application, appkey, initOption)
        if (IMCenter.getInstance().isInitialized) {
            IMCenter.init(application, appkey, initOption)
        }

        RongNotificationManager.getInstance().init(application)
        RongExtensionManager.getInstance().init(application, appkey)
        connectCallBack.connectSuccessful()


        RongConfigCenter.notificationConfig().setInterceptor(object : DefaultInterceptor() {
            override fun isNotificationIntercepted(message: Message): Boolean {
                return excludeOfficial(message.targetId)
            }
        })

        //私聊界面头像设置
        RongConfigCenter.featureConfig().kitImageEngine = object : GlideKitImageEngine() {
            override fun loadConversationPortrait(
                context: Context,
                url: String,
                imageView: ImageView,
                message: Message
            ) {
                Glide.with(imageView).load(url)
                    .apply(RequestOptions.bitmapTransform(CircleCrop()))
                    .into(imageView)
            }
        }

        RongUserInfoManager.getInstance().setUserInfoProvider(RongUserMessageProvider(), false)

        RongIM.setConversationClickListener(object : ConversationClickListener {
            override fun onUserPortraitClick(
                context: Context,
                conversationType: Conversation.ConversationType,
                user: UserInfo,
                targetId: String
            ): Boolean {
                if (excludeOfficial(user.userId)) return true
                //跳转
                var isMoreBar = true
                val userIds = Cache.instance?.userStratResult?.topOfficialUserIds
                val list = userIds?.indices?.toMutableList() ?: mutableListOf<String>()
                for (item in list) {
                    if (item == targetId) {
                        isMoreBar = false
                        break
                    }
                }
                if (Cache.instance?.userStratResult != null && Cache.instance?.userStratResult?.userServiceAccountId != null &&
                    targetId == Cache.instance?.userStratResult?.userServiceAccountId
                ) {
                    isMoreBar = false
                }

                if (isMoreBar) {
                    startToAnchorInfo(context, user.userId)
                }

                return true
            }


            override fun onUserPortraitLongClick(
                context: Context,
                conversationType: Conversation.ConversationType,
                user: UserInfo,
                targetId: String
            ): Boolean {
                return false
            }

            override fun onMessageClick(context: Context, view: View, message: Message): Boolean {
                return false
            }

            override fun onMessageLongClick(
                context: Context,
                view: View,
                message: Message
            ): Boolean {
                return false
            }

            override fun onMessageLinkClick(
                context: Context,
                link: String,
                message: Message
            ): Boolean {
                return false
            }

            override fun onReadReceiptStateClick(context: Context, message: Message): Boolean {
                return false
            }
        })

        RongIM.setConversationListBehaviorListener(object : ConversationListBehaviorListener {
            override fun onConversationPortraitClick(
                context: Context,
                conversationType: Conversation.ConversationType,
                targetId: String
            ): Boolean {
                if (excludeOfficial(targetId)) return true
                //跳转
                var isMoreBar = true
                val userIds = Cache.instance?.userStratResult?.topOfficialUserIds?.toMutableList() ?: mutableListOf<String>()
                for (j in userIds.indices) {
                    if (userIds[j] == targetId) {
                        isMoreBar = false
                        break
                    }
                }
                if (Cache.instance?.userStratResult != null && Cache.instance?.userStratResult?.userServiceAccountId != null &&
                    targetId == Cache.instance?.userStratResult?.userServiceAccountId
                ) {
                    isMoreBar = false
                }

                if (isMoreBar) {
                    startToAnchorInfo(context, targetId)
                }

                return true
            }

            override fun onConversationPortraitLongClick(
                context: Context,
                conversationType: Conversation.ConversationType,
                targetId: String
            ): Boolean {
                return false
            }

            override fun onConversationLongClick(
                context: Context,
                view: View,
                conversation: BaseUiConversation
            ): Boolean {
                return false
            }

            override fun onConversationClick(
                context: Context,
                view: View,
                conversation: BaseUiConversation
            ): Boolean {
                RouteUtils.routeToConversationActivity(
                    context,
                    Conversation.ConversationType.PRIVATE,
                    conversation.mCore.targetId
                )
                return true
            }
        })
        IMCenter.getInstance()
            .addOnReceiveMessageListener(object : RongIMClient.OnReceiveMessageWrapperListener() {
                override fun onReceived(
                    message: Message,
                    i: Int,
                    b: Boolean,
                    b1: Boolean
                ): Boolean {
                    val currentTime = System.currentTimeMillis()
                    var messageTime = message.sentTime
                    if (messageTime <= 9999999999L) {
                        messageTime = messageTime * 1000
                    }
                    var isCurrentOff = false
                    if ((currentTime - messageTime) > 2 * 60 * 1000) {
                        isCurrentOff = true
                    }

                    if (message.content is TextMessage) {
                        val textMessage = message.content as TextMessage
                        if (textMessage.extra != null && textMessage.extra == "compensation") {
                            //弹出异常通话补偿
                            val finalIsCurrentOff = isCurrentOff
                            runOnUIThread(Runnable {
                                Log.d(TAG, "run: 异常弹框" + message.messageId)
                                val messageId = getIntValue("ExtremelyCallDialogFragment", 0)
                                if (messageId != message.messageId && !message.isOffline && Cache.instance?.reviewPkg == false && !finalIsCurrentOff) {
                                    putIntValue(
                                        "ExtremelyCallDialogFragment",
                                        message.messageId
                                    )
                                    val currentActivity =
                                        ActivityUtils.getTopActivity() as FragmentActivity
                                    if (currentActivity.isFinishing) {
                                        Log.e(
                                            "ExtremelyCallDialogFragment",
                                            "Activity 被销毁"
                                        )
                                        return@Runnable
                                    }
                                    val fragmentManager =
                                        currentActivity.supportFragmentManager

                                    val ExtremelyCallDialog =
                                        fragmentManager.findFragmentByTag("ExtremelyCallDialogFragment") as ExtremelyCallDialogFragment?
                                    if (ExtremelyCallDialog == null && fragmentManager != null) {
                                        val evaluateDialog =
                                            ExtremelyCallDialogFragment(textMessage.content)

                                        evaluateDialog.show(
                                            fragmentManager,
                                            "ExtremelyCallDialogFragment"
                                        )
                                    }
                                }
                            })
                        }
                    }


                    if (message.content is CommandMessage) {
                        val commandMessage = message.content as CommandMessage
                        if ("responseEvent" == commandMessage.name) {
                            if (!message.isOffline && !isCurrentOff) {
                                try {
                                    val json = JSONObject(commandMessage.data)
                                    SocketUtils.mEmitterListener.call(json)
                                } catch (e: Exception) {
                                    Log.w(TAG, "responseEvent to json fail: " + e.message)
                                }
                            }
                        } else if ("onGiftAsk" == commandMessage.name) {
                            //被主播索取礼物消息类型
                            if (!message.isOffline && !isCurrentOff) {
                                try {
                                    val json = JSONObject(commandMessage.data)
                                    SocketUtils.mEmitterListener.call(json)
                                    //EventBus.getDefault().post(SocketIoUtils.GiftAskEvent2Object());
//                            Log.e("被索取的礼物信息为(融云内部发送前):", String.valueOf(json));
                                } catch (e: Exception) {
                                    Log.w(TAG, "responseEvent to json fail: " + e.message)
                                }
                            }
                        } else if ("messageEvent" == commandMessage.name) {
                            if (!message.isOffline && !isCurrentOff) {
                                try {
                                    val json = JSONObject(commandMessage.data)
                                    SocketUtils.mEmitterListener.call(json)
                                } catch (e: Exception) {
                                    Log.w(TAG, "responseEvent to json fail: " + e.message)
                                }
                            }
                        }
                    } else if (message.content !is NFMsg) {
                        MessageNoticationDialog.show(message)
                    }


                    if (Cache.instance?.reviewPkg == true) {
                        if (excludeOfficial(message.targetId)) {
                            IMCenter.getInstance().removeConversation(
                                Conversation.ConversationType.PRIVATE,
                                message.targetId, object : RongIMClient.ResultCallback<Boolean>() {
                                    override fun onSuccess(aBoolean: Boolean) {
                                        if (aBoolean) {
                                            Log.e("office", "delete office true" + message.targetId)
                                        } else {
                                            Log.e("office", "delete office false")
                                        }
                                    }

                                    override fun onError(errorCode: RongIMClient.ErrorCode) {
                                        Log.e("office", errorCode.message)
                                    }
                                })
                        }
                    }
                    //判断消息类型是否是NoneFlagMsg并且内容类型为SPECIAL_OFFER,返回主界面时进行优惠商品请求
                    if (message.content is NFMsg) {
                        val NFMsg = message.content as NFMsg
                        val extra = Gson().fromJson(
                            NFMsg.extra,
                            com.juicy.common.model.bean.InvitationInfoBean::class.java
                        )
                        if (extra.contentType == Constant.SPECIAL_OFFER) {
//                        Log.v(TAG, "接收到noneFlagMsg消息：" + "recordId:" + extra.getRecordId() + " contentType:" + extra.getContentType());
                            putStringValue(SpKeyPool.INVITATION_ID, extra.invitationId)
                            EventBus.getDefault().post(DialogShowStatusEvent(true))
                        }
                    }

                    EventBus.getDefault().post(UnReadCount())
                    return false
                }
            })
    }

    @JvmStatic
    fun rongLogot() {
        RongIM.getInstance().logout()
        SocketUtils.disconnect()
    }

    @JvmStatic
    fun getRongcloudToken(callBack: RongTokenCallBack) {
        val newAppkey = getStringValue(SpKeyPool.RC_APP_KEY, "")

        RetrofitManage.instance.create(Service::class.java).getRongToken(
            com.juicy.common.model.bean.ImAppKeyBean(
                (if (TextUtils.isEmpty(newAppkey)) null else newAppkey)!!
            )
        )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(Consumer { rongReslut ->
                if (rongReslut.data != null && rongReslut.isOk) {
                    val rongToken = rongReslut.data
                    putStringValue(SpKeyPool.MY_RTC_KEY, rongToken)
                    callBack.successful
                } else {
                    callBack.unSuccessful
                }
            }, Consumer { throwable ->
                Log.e(TAG, "accept: " + throwable.message)
                callBack.unSuccessful
            })
    }

    @JvmStatic
    fun RongConnect() {
        val rongToken = getStringValue(SpKeyPool.MY_RTC_KEY, "")
        if (TextUtils.isEmpty(rongToken)) {
            getRongcloudToken(object : RongTokenCallBack {
                override val successful: Unit
                    get() {
                        RongConnect()
                    }

                override val unSuccessful: Unit
                    get() {
                        runDelayOnUIThread({ RongConnect() }, 10000)
                    }
            })
            return
        }

        RongIM.connect(rongToken, object : RongIMClient.ConnectCallback() {
            override fun onSuccess(s: String) {
                EventBus.getDefault().post(UnReadCount())
            }


            override fun onError(connectionErrorCode: RongIMClient.ConnectionErrorCode) {
                if (connectionErrorCode == RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_INCORRECT) {
                    runDelayOnUIThread({
                        putStringValue(SpKeyPool.MY_RTC_KEY, "")
                        getRongcloudToken(object : RongTokenCallBack {
                            override val successful: Unit
                                get() {
                                    RongConnect()
                                }

                            override val unSuccessful: Unit
                                get() {
                                    runDelayOnUIThread(
                                        { RongConnect() },
                                        10000
                                    )
                                }
                        })
                    }, 10000)
                }
            }

            override fun onDatabaseOpened(databaseOpenStatus: RongIMClient.DatabaseOpenStatus) {
            }
        })
    }


    private fun startToAnchorInfo(context: Context, userId: String) {
        ARouter.getInstance().build(Constant.ANCHOR_INFO_ACTIVITY_ROUTE)
            .withString(Constant.USER_ID, userId)
            .navigation()
    }


    //    public static void connect(ConnectCallBack connectCallBack) {
    //        RongIM.connect(SpSaveUtil.getStringValue(SpKeyPool.RTC_COULD, ""), new RongIMClient.ConnectCallback() {
    //            @Override
    //            public void onSuccess(String s) {
    //                if (connectCallBack != null) {
    //                    connectCallBack.connectSuccessful();
    //                }
    //                EventBus.getDefault().post(new UnReadCount());
    //            }
    //
    //
    //            @Override
    //            public void onError(RongIMClient.ConnectionErrorCode connectionErrorCode) {
    //                loadNum++;
    //                if (loadNum < 3) {
    //                    connect(connectCallBack);
    //                }
    //            }
    //
    //            @Override
    //            public void onDatabaseOpened(RongIMClient.DatabaseOpenStatus databaseOpenStatus) {
    //
    //            }
    //        });
    //    }
    fun excludeOfficial(id: String): Boolean {
        val userIds = Cache.instance?.userStratResult?.topOfficialUserIds
        if (userIds != null) {
            for (i in userIds.indices) {
                if (userIds[i] == id) {
                    return true
                }
            }
        }
        val nativeUserIds = Cache.instance?.userStratResult?.officialBlacklistUserIds
        if (null != nativeUserIds) {
            for (i in nativeUserIds.indices) {
                if (nativeUserIds[i] == id) {
                    return true
                }
            }
        }
        return null != Cache.instance?.userInfo && Cache.instance?.userInfo?.userId == id
    }


    //迁移目录
    fun checkOldToNew() {
        try {
            var newAppKey = getStringValue(SpKeyPool.RCK_KEY, "")
            val newAppkey_new = getStringValue(SpKeyPool.RC_APP_KEY, "")
            if (!TextUtils.isEmpty(newAppkey_new)) {
                newAppKey = newAppkey_new
            }
            if (TextUtils.isEmpty(newAppKey)) {
                return
            }


            var oldAppKey = getStringValue(SpKeyPool.LAST_RONG_KEY, null)
            if (TextUtils.isEmpty(oldAppKey)) {
                oldAppKey = getStringValue(SpKeyPool.RCK_KEY, "")
            }

            if (TextUtils.isEmpty(oldAppKey)) {
                debugPrint("融云appKey为空，不迁移")
                return
            }

            if (newAppKey == oldAppKey) {
                debugPrint("新key == 旧key")
                return
            }

            val oldDir = getRongCloudStorageDocDir(oldAppKey)
            if (TextUtils.isEmpty(oldDir)) {
                return
            }

            val oldStorage = isExist(oldDir)

            if (!oldStorage) {
                //旧目录不存在
                return
            }

            val newDir = getRongCloudStorageDocDir(newAppKey)
            if (TextUtils.isEmpty(newDir)) {
                return
            }
            val storegeFile = getNewRongCloudStorageFile(newAppKey, "storage")
            val storegeshm = getNewRongCloudStorageFile(newAppKey, "storage-shm")
            val storegewal = getNewRongCloudStorageFile(newAppKey, "storage-wal")

            if (!isExist(storegeFile) &&
                !isExist(storegeshm) &&
                !isExist(storegewal)
            ) {
                moveRongCloudStorageDir(oldDir, newDir)
            } else {
                debugPrint("目录文件已存在，不迁移")
            }
        } catch (e: Exception) {
            debugPrint("迁移融云旧目录失败===>" + e.message)
        }
    }

    private fun moveRongCloudStorageDir(oldDir: String, newDir: String) {
        val newStorage = File(newDir)
        if (!newStorage.exists()) {
            newStorage.mkdirs()
        }

        val oldDirFile = File(oldDir)
        val oldFiles = oldDirFile.listFiles()

        if (oldFiles != null) {
            for (file in oldFiles) {
                val fileName = file.name

                if (fileName == "storage" || fileName == "storage-shm" || fileName == "storage-wal") {
                    val newFilePath = newDir + File.separator + fileName

                    try {
                        val newFile = File(newFilePath)
                        if (file.renameTo(newFile)) {
                            println("File moved successfully.")
                            file.delete() // 删除旧文件
                        } else {
                            println("Failed to move file: " + file.absolutePath)
                        }
                    } catch (e: Exception) {
                        println("Error while moving file: " + e.message)
                    }
                }
            }
        }
    }

    private fun getRongCloudStorageDocDir(newAppKey: String?): String {
        val userId = getStringValue(SpKeyPool.USER_ID_DEVICE, "")

        val directory: String = APP_FILE_PATH ?: return ""
        return "$directory/$newAppKey/$userId"
    }

    private fun getNewRongCloudStorageFile(newAppKey: String?, fileName: String): String {
        val userId = getStringValue(SpKeyPool.USER_ID_DEVICE, "")

        val directory: String = APP_FILE_PATH ?: return ""
        return "$directory/$newAppKey/$userId/$fileName"
    }

    private fun debugPrint(message: String) {
        println(message)
    }

    class UnReadCount

    interface ConnectCallBack {
        fun connectSuccessful()
    }

    interface RongTokenCallBack {
        val successful: Unit
        val unSuccessful: Unit
    }
}
