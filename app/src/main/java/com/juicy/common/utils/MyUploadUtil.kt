package com.juicy.common.utils

import android.text.TextUtils
import android.util.Log
import com.juicy.common.model.bean.OssPolicyINfoBean
import com.juicy.common.config.Cache
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import java.io.File

object MyUploadUtil {
    val ossHost: String?
        get() {
            return if (null != Cache.instance.ossPolicy) Cache.instance?.ossPolicy?.host else ""
        }

    /**
     * 创建表单
     *
     * @param filePath 图片的文件路径
     * import okhttp3.HttpUrl.Builder.build
     * import okhttp3.MultipartBody
     * import okhttp3.MultipartBody.Builder.addFormDataPart
     * import okhttp3.MultipartBody.Builder.build
     * import okhttp3.MultipartBody.Builder.setType
     * import okhttp3.Request.Builder.build
     */
    fun getUploadAvatarBody(filePath: String): MultipartBody {
        val userOssPolicy: OssPolicyINfoBean? = Cache.instance.ossPolicy
        if (userOssPolicy != null) Log.v("taf", "asda")
        val body: MultipartBody.Builder = MultipartBody.Builder().setType(MultipartBody.FORM)
        val fileBody: com.juicy.common.model.bean.FileUploadParamsBean = getFileBody(filePath)
        body.addFormDataPart("ossaccessKeyId", userOssPolicy?.accessKeyId?:"")
        body.addFormDataPart("policy", userOssPolicy?.policy?:"")
        body.addFormDataPart("signature", userOssPolicy?.signature?:"")
        body.addFormDataPart("callback", userOssPolicy?.callback?:"")
        body.addFormDataPart("key", getOssKey(userOssPolicy?.dir?:"", fileBody.fileName))
        body.addFormDataPart(fileBody.fileId, fileBody.fileName, fileBody.fileBody)
        return body.build()
    }

    fun getFileBody(filePath: String): com.juicy.common.model.bean.FileUploadParamsBean {
        val file: File = File(filePath) //localFile 是当前用户选择的图片
        val body: RequestBody = RequestBody.create("application/octet-stream".toMediaTypeOrNull(), file)
        //        Log.v("图片", file.getName());
        return com.juicy.common.model.bean.FileUploadParamsBean(
            "file",
            file.getName(),
            body
        )
    }

    //保存路径，拼接路径和文件名
    fun getOssKey(dir: String?, fileName: String): String {
        if (TextUtils.isEmpty(dir) || TextUtils.isEmpty(fileName)) return ""
        //lastIndexOf表示从字符串下标为0开始找
        val suffixIndex: Int = fileName.lastIndexOf(".")
        //获取到文件格式
        var fileType: String = ""
        if (suffixIndex > 0) {
            fileType = fileName.substring(suffixIndex)
        }
        val ossKey: String = dir + System.currentTimeMillis() + fileType
        //        Log.v("osskey", ossKey);
        return ossKey
    }
}
