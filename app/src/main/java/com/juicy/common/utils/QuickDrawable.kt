package com.juicy.common.utils

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.StateListDrawable
import android.util.Log
import android.view.View
import com.juicy.app.JuicyApplication

class QuickDrawable {
    var hx: String = "baimo"
    fun setHx() {
        Log.i("dsasaasd", "Dsadd")
    }

    //默认矩形
    private var shape: Int = GradientDrawable.RECTANGLE

    //背景颜色
    private var backgroundColor: Int = Color.WHITE

    //背景颜色渐变
    private var backgroundColors: IntArray = intArrayOf()
    private var backgroundColorsOrientation: GradientDrawable.Orientation? = null

    //圆角半径
    private val defaultRadius: Float = 0f
    private var topLeftRadius: Float = defaultRadius
    private var topRightRadius: Float = defaultRadius
    private var bottomLeftRadius: Float = defaultRadius
    private var bottomRightRadius: Float = defaultRadius

    //边框线宽度,颜色
    private var borderWidth: Int = 0
    private var borderColor: Int = Color.GRAY

    //边框线长度,间隔距离(用于虚线)
    private var dashWidth: Float = 0f
    private var dashGap: Float = 0f

    //存放stateSet
    var selectorDrawableMap: MutableMap<Int?, Drawable?>? = null

    private fun createDrawable(): Drawable {
        var drawable: GradientDrawable = GradientDrawable()
        if (backgroundColorsOrientation != null && backgroundColors != null) {
            drawable = GradientDrawable(backgroundColorsOrientation, backgroundColors)
        } else {
            drawable.setColor(backgroundColor)
        }
        drawable.setShape(shape)
        drawable.setStroke(borderWidth, borderColor, dashWidth, dashGap)
        drawable.setCornerRadii(
            floatArrayOf(
                topLeftRadius, topLeftRadius,
                topRightRadius, topRightRadius,
                bottomRightRadius, bottomRightRadius,
                bottomLeftRadius, bottomLeftRadius
            )
        )
        return drawable
    }

    fun build(): Drawable {
        if (selectorDrawableMap != null && selectorDrawableMap!!.size > 0) {
            val selectorDrawable: StateListDrawable = StateListDrawable()
            for (entry: Map.Entry<Int?, Drawable?> in selectorDrawableMap!!.entries) {
                selectorDrawable.addState(intArrayOf(entry.key!!), entry.value)
            }
            return selectorDrawable
        } else {
            return createDrawable()
        }
    }

    fun shape(shape: Int): QuickDrawable {
        this.shape = shape
        return this
    }

    fun intoBackground(view: View) {
        view.setBackgroundDrawable(build())
    }


    fun bgColor(color: Int): QuickDrawable {
        this.backgroundColor = color
        return this
    }

    fun bgColorId(colorid: Int): QuickDrawable {
        val color: Int = JuicyApplication.juicyApplication!!.getResources().getColor(colorid)
        this.backgroundColor = color
        return this
    }

    fun bgColor(orientation: GradientDrawable.Orientation?, colors: IntArray?): QuickDrawable {
        this.backgroundColorsOrientation = orientation
        this.backgroundColors = colors?: intArrayOf()
        return this
    }

    fun corner(radius: Float): QuickDrawable {
        corner(radius, radius, radius, radius)
        return this
    }

    fun cornerDp(radiusDp: Int): QuickDrawable {
        val radius: Int = AppDeviceUtils.dip2px(radiusDp.toFloat())
        corner(radius.toFloat(), radius.toFloat(), radius.toFloat(), radius.toFloat())
        return this
    }

    fun corner(
        topLeft: Float,
        topRight: Float,
        bottomLeft: Float,
        bottomRight: Float
    ): QuickDrawable {
        this.topLeftRadius = topLeft
        this.topRightRadius = topRight
        this.bottomLeftRadius = bottomLeft
        this.bottomRightRadius = bottomRight
        return this
    }

    fun border(width: Int): QuickDrawable {
        this.borderWidth = width
        return this
    }

    fun borderColor(color: Int): QuickDrawable {
        if (borderWidth == 0 && dashWidth == 0f) {
            borderWidth = 1
        }
        this.borderColor = color
        return this
    }

    fun dashWidth(width: Int): QuickDrawable {
        this.dashWidth = width.toFloat()
        return this
    }

    fun dashGap(gap: Int): QuickDrawable {
        this.dashGap = gap.toFloat()
        return this
    }

    /**
     * 设置selector-state的效果
     *
     * @param state    如android:state_pressed,android:state_selected等
     * @param drawable 对应状态的drawable
     */
    @SuppressLint("UseSparseArrays")
    fun stateDrawable(state: Int, drawable: Drawable?): QuickDrawable {
        if (selectorDrawableMap == null) selectorDrawableMap = HashMap()
        selectorDrawableMap!!.put(state, drawable)
        return this
    }

    companion object {
        @JvmStatic
        fun create(): QuickDrawable {
            return QuickDrawable()
        }
    }
}
