package com.juicy.common.utils.view

import android.content.Context
import android.graphics.Color
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatTextView
import com.juicy.app.R

class AutoWrapItem(context: Context?, private var labelText: String) : RelativeLayout(context) {
    private val labelName: String? = null
    private var labelCount: String? = null
    private val colorList: MutableList<String> = ArrayList()
    private val colorBgList: MutableList<Int> = ArrayList()

    init {
//        colorList.add("#FFB138")
//        colorList.add("#3FA9FD")
//        colorList.add("#FD7151")
        colorList.add("#FF000000")
        colorBgList.add(R.drawable.bg_imp_bg1_sage)
        colorBgList.add(R.drawable.bg_imp_bg2_purple)
        colorBgList.add(R.drawable.bg_imp_bg3_white)
        colorBgList.add(R.drawable.bg_imp_bg4_indigo)
        initView()
    }

    private fun initView() {
        val view = inflate(context, R.layout.item_status, this)
        val labelContent = view.findViewById<AppCompatTextView>(R.id.footerOpen)
        val labelNum = view.findViewById<AppCompatTextView>(R.id.frontExpand)
        val labelItem = view.findViewById<LinearLayout>(R.id.collapseWide)
        if (!labelText.isEmpty()) {
            val split = labelText.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            labelText = split[0]
            labelCount = split[1]
        }
        labelContent.text = labelText
        val stringBuilder = StringBuilder()
        stringBuilder.append("x").append(labelCount)
        labelNum.text = stringBuilder
        val colorNum = (Math.random() * 3).toInt()
        labelContent.setTextColor(Color.parseColor(colorList[0]))
        if (colorNum <= colorBgList.size - 1) {
            labelItem.setBackgroundResource(colorBgList[colorNum])
            labelNum.setBackgroundResource(R.drawable.bg_imp_txt_bg_cyan)
        }
    }
}
