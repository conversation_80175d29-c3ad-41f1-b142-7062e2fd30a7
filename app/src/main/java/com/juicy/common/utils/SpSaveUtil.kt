package com.juicy.common.utils

import android.content.Context
import android.content.SharedPreferences
import com.juicy.app.JuicyApplication
import com.juicy.common.config.Cache

object SpSaveUtil {
    val sharedPreferences: SharedPreferences
        get() {
            return JuicyApplication.juicyApplication!!.getSharedPreferences(
                Cache.instance.appName,
                Context.MODE_PRIVATE
            )
        }
    private val cacheSpMap: HashMap<String, Any?> = HashMap()

    @JvmStatic
    fun putStringValue(key: String, value: String?) {
        cacheSpMap.put(key, value)
        sharedPreferences.edit().putString(key, value).apply()
    }

    @JvmStatic
    fun putBooleanValue(key: String, value: Boolean) {
        cacheSpMap.put(key, value)
        sharedPreferences.edit().putBoolean(key, value).apply()
    }

    @JvmStatic
    fun putIntValue(key: String, value: Int) {
        cacheSpMap.put(key, value)
        sharedPreferences.edit().putInt(key, value).apply()
    }

    @JvmStatic
    fun putLongValue(key: String, value: Long) {
        cacheSpMap.put(key, value)
        sharedPreferences.edit().putLong(key, value).apply()
    }

    @JvmStatic
    fun getIntValue(key: String, defaultValue: Int): Int {
        if (cacheSpMap.get(key) is Int) {
            return cacheSpMap.get(key) as Int
        }
        val value: Int = sharedPreferences.getInt(key, defaultValue)
        cacheSpMap.put(key, value)
        return value
    }

    @JvmStatic
    fun getLongValue(key: String, defaultValue: Long): Long {
        if (cacheSpMap.get(key) is Long) {
            return cacheSpMap.get(key) as Long
        }
        val value: Long = sharedPreferences.getLong(key, defaultValue)
        cacheSpMap.put(key, value)
        return value
    }

    @JvmStatic
    fun getBooleanVal(key: String, defaultValue: Boolean): Boolean? {
        if (cacheSpMap.get(key) is Boolean) {
            return cacheSpMap.get(key) as Boolean?
        }
        val value: Boolean = sharedPreferences.getBoolean(key, defaultValue)
        cacheSpMap.put(key, value)
        return value
    }

    @JvmStatic
    fun getStringValue(key: String, defaultValue: String?): String? {
        if (cacheSpMap.get(key) is String) {
            return cacheSpMap.get(key) as String?
        }
        val value: String? = sharedPreferences.getString(key, defaultValue)
        cacheSpMap.put(key, value)
        return value
    }
}
