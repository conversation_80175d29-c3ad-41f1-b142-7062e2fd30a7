package com.juicy.common.utils

import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.media.MediaScannerConnection
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.text.TextUtils
import com.juicy.app.JuicyApplication
import java.io.ByteArrayOutputStream
import java.io.Closeable
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.nio.channels.FileChannel

object MyFileTool {
    @JvmField
    val EXT_FILE_PATH: String = JuicyApplication.juicyApplication!!.getExternalFilesDir(null)!!.path

    @JvmField
    val APP_FILE_PATH: String = JuicyApplication.juicyApplication!!.filesDir.absolutePath

    /**
     * 判断文件或文件夹是否存在
     *
     * @param path
     * @return true 文件存在
     */
    @JvmStatic
    fun isExist(path: String?): Boolean {
        if (TextUtils.isEmpty(path)) {
            return false
        }
        var exist = false
        try {
            val file = File(path)
            exist = file.exists()
        } catch (e: Exception) {
            return false
        }
        return exist
    }

    /**
     * 获取文件的输入流
     *
     * @param path
     * @return
     */
    fun getFileInputStream(path: String): FileInputStream? {
        var fis: FileInputStream? = null
        try {
            val file = File(path)
            if (file.exists()) {
                fis = FileInputStream(file)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return fis
    }

    fun closeIS(`is`: Closeable?) {
        try {
            `is`?.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    /**
     * 根据文件路径拷贝文件
     *
     * @param src         源文件
     * @param destPath    目标文件路径，含文件名
     * @param deleteExits 存在是否删除
     * @return boolean 成功true、失败false
     */
    fun copyFile(src: File?, destPath: String?, deleteExits: Boolean): Boolean {
        var result = false
        if ((src == null) || (destPath == null)) {
            return result
        }
        val dest = File(destPath)
        if (dest != null && dest.exists()) {
            if (!deleteExits) {
                // 不删除---即--即可复制成功
                return true
            }
            dest.delete() // delete file
        }
        try {
            val parent = dest.parentFile
            if (!parent!!.exists()) {
                parent.mkdirs()
            }
            dest.createNewFile()
        } catch (e: IOException) {
            e.printStackTrace()
        }

        var srcChannel: FileChannel? = null
        var dstChannel: FileChannel? = null

        try {
            srcChannel = FileInputStream(src).channel
            dstChannel = FileOutputStream(dest).channel
            srcChannel.transferTo(0, srcChannel.size(), dstChannel)
            result = true
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
            return result
        } catch (e: IOException) {
            e.printStackTrace()
            return result
        }
        try {
            srcChannel.close()
            dstChannel.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return result
    }

    /**
     * 输出流复制文件
     *
     * @param sourceFilePath
     * @param insertUri
     * @return
     */
    fun copyFile(sourceFilePath: String, insertUri: Uri?): Boolean {
        if (insertUri == null) {
            return false
        }
        val resolver = JuicyApplication.juicyApplication!!.contentResolver
        var `is`: InputStream? = null //输入流
        var os: OutputStream? = null //输出流
        try {
            os = resolver.openOutputStream(insertUri)
            if (os == null) {
                return false
            }
            val sourceFile = File(sourceFilePath)
            if (sourceFile.exists()) { // 文件存在时
                `is` = FileInputStream(sourceFile) // 读入原文件
                //输入流读取文件，输出流写入指定目录
                return copyFileWithStream(os, `is`)
            }
            return false
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        } finally {
            try {
                `is`?.close()
                os?.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    private fun copyFileWithStream(os: OutputStream?, `is`: InputStream?): Boolean {
        if (os == null || `is` == null) {
            return false
        }
        var read = 0
        while (true) {
            try {
                val buffer = ByteArray(1444)
                while ((`is`.read(buffer).also { read = it }) != -1) {
                    os.write(buffer, 0, read)
                    os.flush()
                }
                return true
            } catch (e: IOException) {
                e.printStackTrace()
                return false
            } finally {
                try {
                    os.close()
                    `is`.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
    }

    /**
     * AndroidQ以上保存图片到公共目录
     *
     * @param imageName    图片名称
     * @param mimeType     类型
     * @param relativePath 缓存路径
     */
    fun insertImageFileIntoMediaStore(
        imageName: String?, mimeType: String?,
        relativePath: String?
    ): Uri? {
        if (TextUtils.isEmpty(relativePath)) {
            return null
        }
        var insertUri: Uri? = null
        val resolver = JuicyApplication.juicyApplication!!.contentResolver
        //设置文件参数到ContentValues中
        val values = ContentValues()
        //设置文件名
        values.put(MediaStore.Images.Media.DISPLAY_NAME, imageName)
        //设置文件描述，这里以文件名代替
        values.put(MediaStore.Images.Media.DESCRIPTION, imageName)
        //设置文件类型为image/*
        values.put(MediaStore.Images.Media.MIME_TYPE, mimeType)
        // 时间
        val date = System.currentTimeMillis() / 1000
        values.put(MediaStore.Images.Media.DATE_ADDED, date)
        values.put(MediaStore.Images.Media.DATE_MODIFIED, date)
        val external: Uri
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // >= Build.VERSION_CODES.Q
            //注意：MediaStore.Images.Media.RELATIVE_PATH需要targetSdkVersion=29,
            //故该方法只可在Android10的手机上执行
            values.put(MediaStore.Images.Media.RELATIVE_PATH, relativePath)
            values.put(MediaStore.Images.Media.IS_PENDING, 1)
            external = MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)
        } else {
            values.put(MediaStore.Images.Media.DATA, relativePath)
            //EXTERNAL_CONTENT_URI代表外部存储器 （<Q 使用这个）
            external = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
        }
        //insertUri表示文件保存的uri路径
        insertUri = resolver.insert(external, values)
        return insertUri
    }

    /**
     * AndroidQ以上保存视频到公共目录
     *
     * @param imageName    图片名称
     * @param mimeType     类型
     * @param relativePath 缓存路径
     */
    fun insertVideoFileIntoMediaStore(
        imageName: String?, mimeType: String?,
        relativePath: String?
    ): Uri? {
        if (TextUtils.isEmpty(relativePath)) {
            return null
        }
        var insertUri: Uri? = null
        val resolver = JuicyApplication.juicyApplication!!.contentResolver
        //设置文件参数到ContentValues中
        val values = ContentValues()
        //设置文件名
        values.put(MediaStore.Video.Media.DISPLAY_NAME, imageName)
        //设置文件描述，这里以文件名代替
        values.put(MediaStore.Video.Media.DESCRIPTION, imageName)
        //设置文件类型为image/*
        values.put(MediaStore.Video.Media.MIME_TYPE, mimeType)
        // 时间
        val date = System.currentTimeMillis() / 1000
        values.put(MediaStore.Video.Media.DATE_ADDED, date)
        values.put(MediaStore.Video.Media.DATE_MODIFIED, date)
        val external: Uri
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // >= Build.VERSION_CODES.Q
            //注意：MediaStore.Video.Media.RELATIVE_PATH需要targetSdkVersion=29,
            //故该方法只可在Android10的手机上执行
            values.put(MediaStore.Video.Media.RELATIVE_PATH, relativePath)
            values.put(MediaStore.Video.Media.IS_PENDING, 1)
            external = MediaStore.Video.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)
        } else {
            values.put(MediaStore.Images.Media.DATA, relativePath)
            //EXTERNAL_CONTENT_URI代表外部存储器 （<Q 使用这个）
            external = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
        }
        //insertUri表示文件保存的uri路径
        insertUri = resolver.insert(external, values)
        return insertUri
    }

    /**
     * 删除资源文件media数据库
     *
     * @param filePath
     * @param mimeType
     */
    fun deleteFileFromMediaStore(filePath: String, mimeType: String, isVedio: Boolean) {
        val mContentResolver = JuicyApplication.juicyApplication!!.contentResolver
        val uri: Uri
        val where: String
        if (isVedio) {
            where = MediaStore.Video.Media.DATA + "='" + filePath + "'"
            uri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
        } else {
            where = MediaStore.Images.Media.DATA + "='" + filePath + "'"
            uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
        }
        //删除图片
        mContentResolver.delete(uri, where, null)
        MediaScannerConnection.scanFile(
            JuicyApplication.juicyApplication, arrayOf(filePath), arrayOf(mimeType)
        ) { path: String?, uri1: Uri? ->
            val mediaScanIntent =
                Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE)
            mediaScanIntent.setData(uri1)
            JuicyApplication.juicyApplication!!.sendBroadcast(mediaScanIntent)
        }
    }

    /**
     * 删除文件或文件夹(包括目录下的文件)
     *
     * @param filePath
     */
    fun deleteFileOrFloder(filePath: String?) {
        if (TextUtils.isEmpty(filePath)) {
            return
        }
        try {
            val f = File(filePath)
            if (f.exists()) {
                if (f.isDirectory) {
                    val delFiles = f.listFiles()
                    if (delFiles != null) {
                        for (i in delFiles.indices) {
                            deleteFileOrFloder(delFiles[i].absolutePath)
                        }
                    }
                }
                f.delete()
            }
        } catch (e: Exception) {
        }
    }

    /**
     * 获取文件名字
     *
     * @param filePath
     * @return
     */
    fun getNameWithSuffix(filePath: String?): String {
        if (filePath == null) return ""
        val lastIndex = filePath.lastIndexOf(File.separator)
        if (lastIndex == -1) {
            return filePath
        }
        return filePath.substring(lastIndex + 1)
    }


    /**
     * 读取文件转为字符串
     *
     * @param filePath
     * @return
     */
    fun readFileToString(filePath: String): String? {
        var str = ""
        var inStream: FileInputStream? = null
        try {
            val readFile = File(filePath)
            if (!readFile.exists()) {
                return null
            }
            inStream = FileInputStream(readFile)
            val stream = ByteArrayOutputStream()
            val buffer = ByteArray(1024)
            var length = -1
            while ((inStream.read(buffer).also { length = it }) != -1) {
                stream.write(buffer, 0, length)
            }
            str = stream.toString()
            stream.close()
            return str
        } catch (e: FileNotFoundException) {
            return null
        } catch (e: IOException) {
            return null
        } finally {
            if (inStream != null) {
                try {
                    inStream.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }
    }

    /**
     * 保存文本内容
     *
     * @param path    保存路径
     * @param content 需要保存的文本内容
     */
    fun writeData(path: String, content: String) {
        writeData(path, content.toByteArray())
    }

    /**
     * 写入新文件
     */
    fun writeData(path: String, data: ByteArray?) {
        var fos: FileOutputStream? = null
        try {
            val file = File(path)
            if (!file.exists()) {
                createParentIfNecessary(path)
            }
            fos = FileOutputStream(file, false)
            fos.write(data)
            fos.flush()
        } catch (e: Exception) {
        } finally {
            if (fos != null) {
                try {
                    fos.close()
                } catch (e: IOException) {
                }
            }
        }
    }

    fun createParentIfNecessary(path: String) {
        val file = File(path)
        if (!file.exists()) {
            val parent = file.parentFile
            if (!parent!!.exists()) {
                parent.mkdirs()
            }
        }
    }

    /**
     * 9  *
     * 10  * @param myContext
     * 11  * @param ASSETS_NAME 要复制的文件名
     * 12  * @param savePath 要保存的路径
     * 13  * @param saveName 复制后的文件名
     * 14  * testCopy(Context context)是一个测试例子。
     * 15
     */
    fun copy(myContext: Context, ASSETS_NAME: String, savePath: String, saveName: String) {
        val filename = "$savePath/$saveName"
        val dir = File(savePath)
        // 如果目录不中存在，创建这个目录
        if (!dir.exists()) dir.mkdir()
        try {
            if (!(File(filename)).exists()) {
                val `is` = myContext.resources.assets.open(ASSETS_NAME)
                val fos = FileOutputStream(filename)
                val buffer = ByteArray(7168)
                var count = 0
                while ((`is`.read(buffer).also { count = it }) > 0) {
                    fos.write(buffer, 0, count)
                }
                fos.close()
                `is`.close()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
