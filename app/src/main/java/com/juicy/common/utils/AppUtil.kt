package com.juicy.common.utils

import android.content.Context
import android.content.pm.PackageInfo
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.provider.Settings
import android.text.TextUtils
import com.blankj.utilcode.util.ToastUtils
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.google.gson.Gson
import com.juicy.app.BuildConfig
import com.juicy.app.JuicyApplication
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import java.lang.ref.SoftReference
import java.net.InetSocketAddress
import java.net.ProxySelector
import java.net.URI
import java.nio.charset.StandardCharsets
import java.util.Base64
import java.util.TimeZone
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * 描述:APP通用工具
 *
 */
object AppUtil {
    private val LOCK = Any()

    /**
     * 用于判断是否需要清理的后台应用---使用过1s即认为是后台应用
     */
    const val TOTAL_TIME_IN_FOREGROUND_THRESHOLD: Long = 1000L

    var mAppInfoList: SoftReference<List<PackageInfo>>? = null

    private var ANDROID_ID: String? = null

    @JvmStatic
    val pkgName: String
        get() {
            return BuildConfig.APPLICATION_ID
        }

    @JvmStatic
    val phoneSystemVesion: String
        get() {
//        String osVersion = Build.VERSION.RELEASE;
            val sdkVersion = Build.VERSION.SDK_INT
            return sdkVersion.toString()
        }

    @JvmStatic
    val timeZone: String
        get() {
            val timeZone = TimeZone.getDefault().id
            return timeZone
        }


    //是否开启VPN
    fun getIsEnableVpn(context: Context): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val connectivityManager =
                context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            if (connectivityManager != null) {
                val activeNetwork = connectivityManager.activeNetwork
                if (activeNetwork != null) {
                    val networkCapabilities =
                        connectivityManager.getNetworkCapabilities(activeNetwork)
                    if (networkCapabilities != null) {
                        return networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN)
                    }
                }
            }
        }
        return false
    }

    //获取sim卡国家二字码
    fun getSimCountryCode(context: Context?): String {
        return ""
    }

    //获取是否自动设置时区
    fun isAutoTimeZoneEnabled(context: Context): Boolean {
        try {
            val autoTimeZoneValue =
                Settings.Global.getInt(context.contentResolver, Settings.Global.AUTO_TIME_ZONE)
            return autoTimeZoneValue == 1
        } catch (e: Settings.SettingNotFoundException) {
            e.printStackTrace()
        }
        return false
    }

    //是否开启代理
    fun isProxyEnabled(context: Context): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val connectivityManager =
                context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            if (connectivityManager != null) {
                val activeNetwork = connectivityManager.activeNetwork
                if (activeNetwork != null) {
                    val proxyList =
                        ProxySelector.getDefault().select(URI.create("http://www.example.com"))
                    for (proxy in proxyList) {
                        val proxyAddress = proxy.address() as? InetSocketAddress
                        if (proxyAddress != null) {
                            val proxyHost = proxyAddress?.hostName
                            if (proxyHost != null && !proxyHost.isEmpty()) {
                                return true
                            }
                        }
                    }
                }
            }
        }
        return false
    }

    @JvmStatic
    val safeInfoString: String
        get() {
            var aesKey: String? = ""
            if (Cache.instance?.appconfig?.riskConfigBean != null) {
                aesKey =
                    Cache.instance?.appconfig?.riskConfigBean!!.k_factor
            }

            if (TextUtils.isEmpty(aesKey)) {
                return ""
            }

            val infoMap: MutableMap<String, String?> =
                HashMap()
            infoMap["platform"] = "Android"
            infoMap["pkg"] = pkgName
            infoMap["ver"] = Cache.instance?.buildVersionName
            infoMap["platform_ver"] = phoneSystemVesion
            infoMap["model"] = Build.MODEL
            infoMap["user_id"] = SpSaveUtil.getStringValue(SpKeyPool.USER_ID_DEVICE, "")
            infoMap["device-id"] = ApplicationUtil.getAndroidId(JuicyApplication.juicyApplication!!)
            infoMap["is_enable_vpn"] =
                if (getIsEnableVpn(JuicyApplication.juicyApplication!!)) "1" else "0"
            infoMap["is_enable_proxy"] =
                if (isProxyEnabled(JuicyApplication.juicyApplication!!)) "1" else "0"
            infoMap["system_language"] =
                LanguageTool.getDeviceLanguageCountry(JuicyApplication.juicyApplication!!)
            infoMap["sim_country"] = getSimCountryCode(JuicyApplication.juicyApplication)
            infoMap["time_zone"] = if (TextUtils.isEmpty(timeZone)) "" else timeZone
            infoMap["is_auto_time_zone"] =
                if (isAutoTimeZoneEnabled(JuicyApplication.juicyApplication!!)) "1" else "0"
            infoMap["is_emulator"] =
                if (EmulatorDetector.isEmulator(JuicyApplication.juicyApplication!!)) "1" else "0"
            infoMap["is_run_in_virtual"] = if (CheckVirtual.isRunInVirtual) "1" else "0"
            infoMap["is_rooted"] = if (CheckRoot.isDeviceRooted) "1" else "0"
            infoMap["is_hook"] = if (CheckHook.isHook(JuicyApplication.juicyApplication)) "1" else "0"
            // 转换为字节数组
            val gson = Gson()
            val infoString = gson.toJson(infoMap)

            //        infoString = "{\"platform\":\"Android\",\"pkg\":\"com.xxxxx.dev\",\"ver\":\"1.0.0\",\"platform_ver\":29,\"model\":\"HMA-AL00\",\"device-id\":\"fk55e6192b00374637a9178da11111111\",\"is_enable_vpn\":0,\"is_enable_proxy\":1,\"system_language\":\"zh\",\"sim_country\":\"CN\",\"time_zone\":\"Asia/Shanghai\",\"is_auto_time_zone\":1}";
//        ToastUtils.showLong(infoString);
            val infoBytes =
                infoString.toByteArray(StandardCharsets.UTF_8)
            //        aesKey = "u980qm4hqqtt48dn";
            // 加密
            val aesKeyBytes =
                aesKey?.toByteArray(StandardCharsets.UTF_8)
            val encryptedSafeInfo = encryptSafeInfo(infoBytes, aesKeyBytes)

            return encryptedSafeInfo
        }


    fun encryptSafeInfo(infoString: ByteArray?, aesKey: ByteArray?): String {
        try {
            val key = SecretKeySpec(aesKey, "AES")
            val cipher = Cipher.getInstance("AES/ECB/PKCS5Padding")
            cipher.init(Cipher.ENCRYPT_MODE, key)
            val encryptedData = cipher.doFinal(infoString)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val encodedData =
                    Base64.getUrlEncoder().withoutPadding().encodeToString(encryptedData)
                return encodedData
            }
        } catch (e: Exception) {
        }
        return ""
    }


    fun getAndroidId(context: Context?): String? {
        if (TextUtils.isEmpty(ANDROID_ID)) {
            ANDROID_ID =
                Settings.System.getString(context?.contentResolver, Settings.Secure.ANDROID_ID)
        }
        return ANDROID_ID
    }

    @JvmStatic
    var gid: String? = null
        get() {
            if (TextUtils.isEmpty(field)) {
                try {
                    field =
                        AdvertisingIdClient.getAdvertisingIdInfo(JuicyApplication.juicyApplication!!).id
                } catch (ignored: Exception) {
                }
                if (TextUtils.isEmpty(field)) {
                    field = "00000000-0000-0000-0000-00000000000"
                }
            }
            return field
        }
        private set

    @JvmStatic
    fun checkNetworkNoToast(): Boolean {
        // 检查网络连接状态
        val cm =
            JuicyApplication.juicyApplication?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork = cm.activeNetworkInfo
        val isConnected = activeNetwork != null && activeNetwork.isConnectedOrConnecting

        return !isConnected
    }

    //检测无网络Toast
    @JvmStatic
    fun checkNetworkToast(): Boolean {
        // 检查网络连接状态
        val cm =
            JuicyApplication.juicyApplication?.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork = cm.activeNetworkInfo
        val isConnected = activeNetwork != null && activeNetwork.isConnectedOrConnecting
        if (!isConnected) {
            showNetworkToast()
        }
        return !isConnected
    }

    //显示无网络Toast
    fun showNetworkToast() {
        ToastUtils.showShort(
            LanguageManager.Companion.instance?.getLocalTranslate("The_network_request_failed")?:""
        )
    }
}
