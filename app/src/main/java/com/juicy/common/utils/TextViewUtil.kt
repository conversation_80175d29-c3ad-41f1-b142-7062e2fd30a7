package com.juicy.common.utils

import android.content.Context
import android.graphics.LinearGradient
import android.graphics.Rect
import android.graphics.Shader
import android.widget.TextView
import androidx.annotation.ColorInt

object TextViewUtil {
    @JvmStatic
    fun setTextColorGradient(
        textView: TextView?,
        @ColorInt startColor: Int,
        @ColorInt endColor: Int
    ) {
        if (textView == null || textView.getContext() == null) {
            return
        }
        val context: Context = textView.getContext()

        val textWidth: Float = textView.getPaint().measureText(textView.getText().toString())
        val gradient: LinearGradient = LinearGradient(
            0f, 0f, textWidth, 0f,
            startColor, endColor,
            Shader.TileMode.CLAMP
        )
        textView.getPaint().setShader(gradient)
        textView.invalidate()
    }

    fun setTextColorGradient(textView: TextView?, colors: IntArray, positions: FloatArray?) {
        if (textView == null || textView.getContext() == null) {
            return
        }

        val text: String = textView.getText().toString()
        // 方法1：getTextBounds
        val rect: Rect = Rect()
        textView.getPaint().getTextBounds(text, 0, text.length, rect)
        // 方法2：measureText
//        float measuredWidth = textView.getPaint().measureText(text);
        val textWidth: Float = rect.width().toFloat()
        val linearGradient: LinearGradient = LinearGradient(
            0f, 0f, textWidth, 0f,
            colors,
            positions,
            Shader.TileMode.REPEAT
        )
        textView.getPaint().setShader(linearGradient)
        textView.invalidate()
    }


    fun clearTextColorGradient(textView: TextView) {
        textView.getPaint().setShader(null)
        textView.invalidate()
    }
}