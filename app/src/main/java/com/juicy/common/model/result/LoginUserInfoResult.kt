package com.juicy.common.model.result

import androidx.annotation.Keep
import com.juicy.common.model.bean.JuicyUserInfoBean

@Keep
class LoginUserInfoResult {
    fun getUserId(): String? {
        return mUserId
    }
    var firstRegister: Boolean? = null
        get() = field != null && field as Boolean
        set(value) {
            field = value
        }
    @JvmField
    val token: String? = null
    @JvmField
    val userInfo: com.juicy.common.model.bean.JuicyUserInfoBean? = null

    private var mUserId: String? = null



    override fun toString(): String {
        return "LoginUserInfoResult(firstRegister=$firstRegister, token=$token, userInfo=$userInfo , mUserId=$mUserId)"
    }
}
