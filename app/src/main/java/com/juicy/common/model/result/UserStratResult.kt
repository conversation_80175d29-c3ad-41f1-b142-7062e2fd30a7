package com.juicy.common.model.result

import androidx.annotation.Keep
import com.juicy.common.model.bean.AnchorTagItemBean
import com.juicy.common.model.bean.FreeCallConBean
import com.juicy.common.model.bean.MaleConfigBean
import com.juicy.common.model.bean.PopUpInfoBean

@Keep
class UserStratResult {
    val matchCallFree: Boolean? = null
    @JvmField
    val initTab: Int? = null
    val showMatchGender: Boolean? = null
    @JvmField
    val genderMatchCoin: com.juicy.common.model.bean.MaleConfigBean? = null
    val isReviewPkg: Boolean? = null
    val showLP: Boolean? = null
    val lpDiscount: Int? = null
    val lpPromotionDiscount: Int? = null
    val payChannels: List<String>? = null
    val maskOpen: Boolean? = null
    val showBroadcasterRank: Boolean? = null
    val autoAccept: Boolean? = null
    val broadcasterWallTags: List<String>? = null
    val tabType: Int? = null
    val openFlashChat: Boolean? = null
    val videoStreamCategory: List<String>? = null
    @JvmField
    val flashChatConfig: com.juicy.common.model.bean.FreeCallConBean? = null
    val showMatch: Boolean? = null
    val newTppUsable: Boolean? = null
    private var mSlide: Int? = null
    private var mVolume: Int? = null
    val userInvitation: com.juicy.common.model.bean.PopUpInfoBean? = null
    @JvmField
    val topOfficialUserIds: List<String>? = null
    @JvmField
    val reviewOfficialBlacklistUserIds: List<String>? = null
    @JvmField
    val officialBlacklistUserIds: List<String>? = null
    val imIncentiveBlacklistUserIds: List<String>? = null
    @JvmField
    val broadcasterFollowOfficialUserIds: List<String>? = null
    val displayNotDisturbCall: Boolean? = null
    val displayNotDisturbIm: Boolean? = null
    @JvmField
    val imSessionBalance: Int? = null
    val showFlowInfo: Boolean? = null
    @JvmField
    var broadcasterWallTagList: List<com.juicy.common.model.bean.AnchorTagItemBean>? = null
    val showDeletedButton: Boolean? = null
    val freeUserCallStaySecond: String? = null
    val freeUserImStaySecond: String? = null
    val rechargeUserCallStaySecond: String? = null
    val rechargeUserImStaySecond: String? = null
    val randomUploadPaidEvents: Boolean? = null
    val isSwitchIMLimit: Boolean? = null
    val switchOneKeyFollow: Boolean? = null
    val switchIMIncentive: Boolean? = null
    val switchClub: Boolean? = null
    val showRookieGuide: Boolean? = null
    val isSwitchStrongGuide: Boolean? = null
    val isCallRearCamera: Boolean? = null
    val showAutoTranslate: Boolean? = null
    val silence: Boolean? = null
    val rearCamera: Boolean? = null
    val switchInstruct: Boolean? = null
    val forceEvaluationInstruct: Boolean? = null
    val switchExtraCategory: Boolean? = null
    val switchMultipleCall: Boolean? = null
    val timestamp: String? = null
    @JvmField
    val userServiceAccountId: String? = null
    @JvmField
    val broadcasterWallRegions: List<String>? = null
    @JvmField
    val imSessionBroadcasterIds: List<Long>? = null
    //加入多余字段
    private var mBrightness: Int? = null
    private var mSpeed: String? = null

    fun getSlide(): Int? {
        return mSlide
    }

    override fun toString(): String {
        return "UserStratResult{" +
                "matchCallFree=" + matchCallFree +
                ", initTab=" + initTab +
                ", showMatchGender=" + showMatchGender +
                ", genderMatchCoin=" + genderMatchCoin +
                ", isReviewPkg=" + isReviewPkg +
                ", showLP=" + showLP +
                ", lpDiscount=" + lpDiscount +
                ", lpPromotionDiscount=" + lpPromotionDiscount +
                ", payChannels=" + payChannels +
                ", maskOpen=" + maskOpen +
                ", showBroadcasterRank=" + showBroadcasterRank +
                ", autoAccept=" + autoAccept +
                ", broadcasterWallTags=" + broadcasterWallTags +
                ", tabType=" + tabType +
                ", openFlashChat=" + openFlashChat +
                ", videoStreamCategory=" + videoStreamCategory +
                ", flashChatConfig=" + flashChatConfig +
                ", showMatch=" + showMatch +
                ", newTppUsable=" + newTppUsable +
                ", userInvitation=" + userInvitation +
                ", topOfficialUserIds=" + topOfficialUserIds +
                ", reviewOfficialBlacklistUserIds=" + reviewOfficialBlacklistUserIds +
                ", officialBlacklistUserIds=" + officialBlacklistUserIds +
                ", imIncentiveBlacklistUserIds=" + imIncentiveBlacklistUserIds +
                ", broadcasterFollowOfficialUserIds=" + broadcasterFollowOfficialUserIds +
                ", displayNotDisturbCall=" + displayNotDisturbCall +
                ", displayNotDisturbIm=" + displayNotDisturbIm +
                ", imSessionBalance=" + imSessionBalance +
                ", showFlowInfo=" + showFlowInfo +
                ", broadcasterWallTagList=" + broadcasterWallTagList +
                ", showDeletedButton=" + showDeletedButton +
                ", freeUserCallStaySecond=" + freeUserCallStaySecond +
                ", freeUserImStaySecond=" + freeUserImStaySecond +
                ", rechargeUserCallStaySecond=" + rechargeUserCallStaySecond +
                ", rechargeUserImStaySecond=" + rechargeUserImStaySecond +
                ", randomUploadPaidEvents=" + randomUploadPaidEvents +
                ", isSwitchIMLimit=" + isSwitchIMLimit +
                ", switchOneKeyFollow=" + switchOneKeyFollow +
                ", switchIMIncentive=" + switchIMIncentive +
                ", switchClub=" + switchClub +
                ", showRookieGuide=" + showRookieGuide +
                ", isSwitchStrongGuide=" + isSwitchStrongGuide +
                ", isCallRearCamera=" + isCallRearCamera +
                ", showAutoTranslate=" + showAutoTranslate +
                ", silence=" + silence +
                ", rearCamera=" + rearCamera +
                ", switchInstruct=" + switchInstruct +
                ", forceEvaluationInstruct=" + forceEvaluationInstruct +
                ", switchExtraCategory=" + switchExtraCategory +
                ", switchMultipleCall=" + switchMultipleCall +
                ", timestamp=" + timestamp +
                ", userServiceAccountId=" + userServiceAccountId +
                ", broadcasterWallRegions=" + broadcasterWallRegions +
                ", imSessionBroadcasterIds=" + imSessionBroadcasterIds +
                ", mSlide=" + mSlide +
                ", mVolume=" + mVolume +
                ", mBrightness=" + mBrightness +
                ", mSpeed='" + mSpeed + '\'' +
                '}'.toString()
    }

}
