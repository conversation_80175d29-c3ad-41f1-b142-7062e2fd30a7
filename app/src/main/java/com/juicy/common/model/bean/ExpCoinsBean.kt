package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class ExpCoinsBean {
    val bottomDocument: String? = null //底部文案
    val title: String? = null //标题
    @JvmField
    val coins: Int? = null //赠送金币

    private var mBottomDocument: String? = null //af的底部文案
    private var mLabel: String? = null //af的标题
    private var mAge: Int? = null //af的赠送金币

    override fun toString(): String {
        return "ExpCoinsBean{" +
                "bottomDocument='$bottomDocument', mBottomDocument='$mBottomDocument', " +
                "title='$title', mLabel='$mLabel', " +
                "coins=$coins, mAge=$mAge" +
                '}'
    }

}
