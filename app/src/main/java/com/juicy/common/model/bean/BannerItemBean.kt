package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class BannerItemBean {
    @JvmField
    var bizType: String? = null //业务类型 ：1.三方支付，2.老虎机 3.导量强化

    val isFirstLottery: Boolean = false //是否首次抽奖(老虎机特有)
    private var mIsFirstLottery: Boolean = false //af的is_first_lottery

    @JvmField
    var jumpUrl: String? = null //跳转链接
    private var mJumpUrl: String = "" //af的jump_url

    @JvmField
    val pic: String? = null //图片
    @JvmField
    var type: String? = null //跳转类型 1.应用内webView打开 4.跳转外部浏览器
    private var mType: String = "" //af的type

    override fun toString(): String {
        return "BannerItemBean(bizType='$bizType', mType='$mType', isFirstLottery=$isFirstLottery, mIsFirstLottery=$mIsFirstLottery, jumpUrl='$jumpUrl', mJumpUrl='$mJumpUrl', pic='$pic', type='$type', mType='$mType')"
    }
}
