package com.juicy.common.model.bean

import androidx.annotation.Keep
import com.juicy.common.config.Cache

@Keep
class ActivityInfoBean {
    @JvmField
    var capableRechargeNum: Int? = null //能够充值次数
    @JvmField
    var code: String? = null //商品编号
    private var mName: String? = null //
    @JvmField
    var discount: Number? = null //折扣
    private var mDiscount: Number? = null //折扣
    @JvmField
    var exchangeCoin: Int? = null //兑换金币数
    @JvmField
    var extraCoin: Int? = null //额外的金币数量
    @JvmField
    var extraCoinPercent: Int? = null //额外的金币比例
    var goodsId: String? = null //商品id
    private var mGoodsId: String? = null //商品id
    var icon: String? = null //商品图标

    @JvmField
    var isVipItem: Boolean = false

    @JvmField
    var invitationId: String = "" //邀请链接id
    var promotion: Boolean? = null //是否促销
    var originalCode: String? = null //升级消费的商品code
    @JvmField
    var originalPrice: Number? = null //原价
    var originalPriceRupee: Number? = null //原价(卢比)
    @JvmField
    var price: Number? = null //当前价格
    var priceRupee: Number? = null //当前价格(卢比)
    @JvmField
    var rechargeNum: Int? = null //充值次数
    private var mRechargeNum: Int? = null //充值次数

    @JvmField
    var remainMilliseconds: Long = 0 // 剩余毫秒秒数
    private var mRemainMilliseconds: Long = 0 // 剩余毫秒秒数

    @JvmField
    var surplusMillisecond: Long = 0 //剩余毫秒数
    @JvmField
    var tags: String? = null //商品标签
    @JvmField
    var type: String? = null //商品类型
    var validity: Int? = null //订阅有效期
    var validityUnit: String? = null //订阅有效期单位
    @JvmField
    var activityName: String? = null //活动名称

    @JvmField
    var activityPic: String? = null //活动大图
    var activitySmallPic: String? = null //活动小图
    private var mActivityName: String? = null //活动名称
    private var mActivityPic: String? = null //活动大图
    private var mActivitySmallPic: String? = null //活动小图

    @JvmField
    var thirdpartyCoinPercent: Int? = null //三方充值赠送比例
    var localPaymentPriceRupee: Int? = null

    val localPrice: String
        //获取本地化价格
        get() {
            val product =
                Cache.instance.storePriceMap[code]
            if (product != null) {
                return product
            }
            return "$ $price"
        }

    fun setSurplusMillisecond(surplusMillisecond: Long) {
        this.surplusMillisecond = surplusMillisecond
    }

    fun setRemainMilliseconds(remainMilliseconds: Long) {
        this.remainMilliseconds = remainMilliseconds
    }

    override fun toString(): String {
        return "ActivityInfoBean{" +
                "capableRechargeNum=" + capableRechargeNum +
                ", code='" + code + '\'' +
                ", discount=" + discount +
                ", exchangeCoin=" + exchangeCoin +
                ", extraCoin=" + extraCoin +
                ", extraCoinPercent=" + extraCoinPercent +
                ", goodsId='" + goodsId + '\'' +
                ", icon='" + icon + '\'' +
                ", isVipItem=" + isVipItem +
                ", invitationId='" + invitationId + '\'' +
                ", promotion=" + promotion +
                ", originalCode='" + originalCode + '\'' +
                ", originalPrice=" + originalPrice +
                ", originalPriceRupee=" + originalPriceRupee +
                ", price=" + price +
                ", priceRupee=" + priceRupee +
                ", rechargeNum=" + rechargeNum +
                ", remainMilliseconds=" + remainMilliseconds +
                ", surplusMillisecond=" + surplusMillisecond +
                ", tags='" + tags + '\'' +
                ", type='" + type + '\'' +
                ", validity=" + validity +
                ", validityUnit='" + validityUnit + '\'' +
                ", activityName='" + activityName + '\'' +
                ", activityPic='" + activityPic + '\'' +
                ", activitySmallPic='" + activitySmallPic + '\'' +
                ", thirdpartyCoinPercent=" + thirdpartyCoinPercent +
                ", localPaymentPriceRupee=" + localPaymentPriceRupee +
                '}'
    }   

}
