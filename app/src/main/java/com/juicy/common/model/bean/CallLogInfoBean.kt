package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class CallLogInfoBean {
    var data: List<com.juicy.common.model.bean.CallLogInfoBean.DataBean>? = null
    @JvmField
    var typeName: String? = null
    private var mTypeName: String = "" //af的type_name

    @JvmField
    var subType: String? = null
    @JvmField
    var behavior: String? = null
    private var mSubType: String = "" //af的sub_type

    private var mBehavior: String = "" //af的behavior

    override fun toString(): String {
        return "CallLogInfoBean{" +
                "data=" + data +
                ", typeName='" + typeName + '\'' +
                ", subType='" + subType + '\'' +
                ", behavior='" + behavior + '\'' +
                ", mTypeName='" + mTypeName + '\'' +
                ", mSubType='" + mSubType + '\'' +
                ", mBehavior='" + mBehavior + '\'' +
                '}'
    }

    class DataBean {
        @JvmField
        var channelName: String? = null
        private var mChannelName: String = "" //af的channel_name

        @JvmField
        var action: String? = null
        private var mAction: String = "" //af的action

        @JvmField
        var ext: String? = null
        @JvmField
        var ext2: String? = null
        private var mExt: String = "" //af的ext
        private var mExt2: String = "" //af的ext2

        @JvmField
        var tm: Long? = null

        override fun toString(): String {
            return "DataBean{" +
                    "channelName='" + channelName + '\'' +
                    ", action='" + action + '\'' +
                    ", ext='" + ext + '\'' +
                    ", ext2='" + ext2 + '\'' +
                    ", tm=" + tm +
                    ", mChannelName='" + mChannelName + '\'' +
                    ", mAction='" + mAction + '\'' +
                    ", mExt='" + mExt + '\'' +
                    ", mExt2='" + mExt2 + '\'' +
                    '}'
        }
    }
}
