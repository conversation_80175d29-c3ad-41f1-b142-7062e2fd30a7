package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class SimpleUserInfoBean {
    private var age: Int? = null
    @JvmField
    var avatar: String? = null
    @JvmField
    var broadcasterId: String? = null
    var gender: Int = 0
    @JvmField
    var nickName: String? = null
    @JvmField
    var registerCountry: String? = null

    fun getAge(): Int? {
        return age
    }

    fun setAge(age: Int) {
        this.age = age
    }

    override fun toString(): String {
        return "Ag{" +
                "age=" + age +
                ", avatar='" + avatar + '\'' +
                ", broadcasterId='" + broadcasterId + '\'' +
                ", gender=" + gender +
                ", nickName='" + nickName + '\'' +
                ", registerCountry='" + registerCountry + '\'' +
                '}'
    }

    override fun equals(o: Any?): Bo<PERSON>an {
        if (this === o) return true
        if (o == null || javaClass != o.javaClass) return false
        val ag = o as com.juicy.common.model.bean.SimpleUserInfoBean
        return gender == ag.gender && age == ag.age && avatar == ag.avatar && broadcasterId == ag.broadcasterId && nickName == ag.nickName && registerCountry == ag.registerCountry
    }
}
