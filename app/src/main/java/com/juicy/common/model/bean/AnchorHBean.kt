package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class AnchorHBean {
    val broadcasterId: Long
    private var mValue = "" //af的value
    private var mTime: Long = 0 //af的time
    @JvmField
    val complainCategory: String
    var complainSub: String = ""
        private set

    constructor(broadcasterId: Long, complainCategory: String) {
        this.broadcasterId = broadcasterId
        this.complainCategory = complainCategory
    }

    constructor(broadcasterId: Long, complainCategory: String, complainSub: String) {
        this.broadcasterId = broadcasterId
        this.complainCategory = complainCategory
        this.complainSub = complainSub
    }

    override fun toString(): String {
        return "AnchorHBean(broadcasterId=$broadcasterId, complainCategory='$complainCategory', complainSub='$complainSub', mValue='$mValue', mTime=$mTime)"
    }
}

