package com.juicy.common.model.message_event

import androidx.annotation.Keep

@Keep
class AppOnPickupBean {
    @JvmField
    var channelName: String? = null
    @JvmField
    var fromUserId: String? = null
    @JvmField
    var toUserId: String? = null
    var clientSessionId: String? = null // 客户端会话id
    var rtcToken: String? = null
    var chooseVideoSdk: Int? = null
    var isFree: Boolean? = null //是否免费通话
    var videoPlayMode: String? = null
    var videoFileUrl: String? = null
    var callFreeSeconds: Int? = null

    override fun toString(): String {
        return "AppOnPickupBean{" +
                "channelName='" + channelName + '\'' +
                ", fromUserId='" + fromUserId + '\'' +
                ", toUserId='" + toUserId + '\'' +
                ", clientSessionId='" + clientSessionId + '\'' +
                ", rtcToken='" + rtcToken + '\'' +
                ", chooseVideoSdk=" + chooseVideoSdk +
                ", isFree=" + isFree +
                ", videoPlayMode='" + videoPlayMode + '\'' +
                ", videoFileUrl='" + videoFileUrl + '\'' +
                ", callFreeSeconds=" + callFreeSeconds +
                '}'
    }
}
