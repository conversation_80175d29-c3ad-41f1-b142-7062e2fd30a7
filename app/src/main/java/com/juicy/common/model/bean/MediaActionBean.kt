package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class MediaActionBean {
    var actionType: Int? = null //新增＝1，更新＝2；删除＝3；
    var coins: Int? = null //设置的媒体价格；
    var deleteMediaId: String? = null //要删除的mediaId；
    var mediaType: String? = null
    var mediaPath: String? = null
    var replaceMediaId: Int? = null


    companion object {
        fun getDeleteCr(deleteMediaId: String?): com.juicy.common.model.bean.MediaActionBean {
            val cr = com.juicy.common.model.bean.MediaActionBean()
            cr.actionType = 3
            cr.deleteMediaId = deleteMediaId
            cr.mediaType = "photo"
            return cr
        }

        @JvmStatic
        fun getInsertCr(mediaPath: String?): com.juicy.common.model.bean.MediaActionBean {
            val cr = com.juicy.common.model.bean.MediaActionBean()
            cr.mediaType = "photo"
            cr.actionType = 1
            cr.mediaPath = mediaPath
            return cr
        }
    }
}
