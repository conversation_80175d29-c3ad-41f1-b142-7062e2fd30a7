package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
open class NetResponseBean<T : Any?> {
    @JvmField
    val code: Int? = null
    val key: String? = null
    @JvmField
    val msg: String? = null
    open val data: T? = null

    val isOk: Boolean
        get() = code == 0

    override fun toString(): String {
        return "NetResponseBean{" +
                "code=" + code +
                ", key='" + key + '\'' +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}'
    }
}
