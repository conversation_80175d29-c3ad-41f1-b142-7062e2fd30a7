package com.juicy.common.model.message_event

import androidx.annotation.Keep

@Keep
class MessageEventBean {
    @JvmField
    var fromUserId: String? = null
    private var nickName: String? = null
    var toUserId: String? = null
    @JvmField
    var content: String? = null // 对方发来的消息文本内容
    private var channelName: String? = null
    var timestamp: Long? = null // 消息构建时间
    @JvmField
    var command: String? = null
    var fromUserName: String? = null
    var toUserName: String? = null

    override fun toString(): String {
        return "MessageEventBean{" +
                "fromUserId='" + fromUserId + '\'' +
                ", nickName='" + nickName + '\'' +
                ", toUserId='" + toUserId + '\'' +
                ", content='" + content + '\'' +
                ", channelName='" + channelName + '\'' +
                ", timestamp=" + timestamp +
                ", command='" + command + '\'' +
                ", fromUserName='" + fromUserName + '\'' +
                ", toUserName='" + toUserName + '\'' +
                '}'
    }  

}