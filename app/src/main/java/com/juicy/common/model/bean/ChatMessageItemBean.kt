package com.juicy.common.model.bean

import androidx.annotation.Keep
import com.chad.library.adapter.base.entity.MultiItemEntity

@Keep
class ChatMessageItemBean : MultiItemEntity {
    @JvmField
    var content: String? = null
    var mName: String? = null
    @JvmField
    var translateContent: String? = null
    private var mTimestamp: String? = null
    var timestamp: String? = null
    @JvmField
    var avatar: String? = null
    private var mAvatar: String? = null //af的avatar
    override var itemType: Int = 0
    var toUserName: String? = null
    private var mToUserName: String? = null //af的to_user_name
    @JvmField
    var fromUserId: String? = null

    override fun toString(): String {
        return "ChatMessageItemBean{" +
                "content='" + content + '\'' +
                ", mName='" + mName + '\'' +
                ", translateContent='" + translateContent + '\'' +
                ", mTimestamp='" + mTimestamp + '\'' +
                ", timestamp='" + timestamp + '\'' +
                ", avatar='" + avatar + '\'' +
                ", mAvatar='" + mAvatar + '\'' +
                ", itemType=" + itemType +
                ", mToUserName='" + mToUserName + '\'' +
                '}'
    }

}
