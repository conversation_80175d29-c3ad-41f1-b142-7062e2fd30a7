package com.juicy.common.model.bean

import androidx.annotation.Keep


@Keep
class GiftItemBean {
    @JvmField
    val giftList: List<String>? = null
    private var mGiftList: List<String>? = null //af的gift_list

    @JvmField
    val labelsList: List<String>? = null
    private var mLabelsList: List<String>? = null //af的labels_list

    override fun toString(): String {
        return "GiftItemBean{" +
                "giftList=" + giftList +
                ", mGiftList=" + mGiftList +
                ", labelsList=" + labelsList +
                ", mLabelsList=" + mLabelsList +
                '}'
    }
}
