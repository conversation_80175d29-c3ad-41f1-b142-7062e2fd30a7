package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class AppConfigItemsBean {
    @JvmField
    var name: String = ""
    private var mName: String = "" //af的name
    @JvmField
    var data: Any? = null
    private var mData: Any? = null //af的data

    override fun toString(): String {
        return "AppConfigItemsBean(name='$name', mName='$mName', data=$data, mData=$mData)"
    }
}
