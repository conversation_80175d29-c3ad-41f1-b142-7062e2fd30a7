package com.juicy.common.model.bean

import androidx.annotation.Keep
import com.juicy.app.JuicyApplication
import com.juicy.common.utils.AppUtil

@Keep
class AdjustBean {
    private var pkg: String = JuicyApplication.juicyApplication?.packageName ?:""//包名
    private var ver = "" //版本号
    private var deviceId: String = AppUtil.getAndroidId(JuicyApplication.juicyApplication)?:"" //android_id
    private var mValue = "" //af的value
    private var mTime: Long = 0 //af的time
    private var mIp = "" //af的ip
    private var userId = "" //用户id，没有登录时可以传空
    private var utmSource = "" //af的media_source
    private var adgroupId = "" //af的adgroup_id
    private var adset = "" //af的adset
    private var adsetId = "" //af的adset_id
    private var afStatus = "" //af的af_status
    private var agency = "" //af的agency
    private var afChannel = "" //af的af_channel
    private var campaign = "" //af的campaign
    private var campaignId = "" //af的campaignId
    private var mEvent = "" //af的event
    private var mLabel = "" //af的label
    


    fun setPkg(pkg: String) {
        this.pkg = pkg
    }

    fun setVer(ver: String) {
        this.ver = ver
    }

    fun setDeviceId(deviceId: String) {
        this.deviceId = deviceId
    }

    fun setUserId(userId: String) {
        this.userId = userId
    }

    fun setUtmSource(utmSource: String) {
        this.utmSource = utmSource
    }

    fun setAdgroupId(adgroupId: String) {
        this.adgroupId = adgroupId
    }

    fun setAdset(adset: String) {
        this.adset = adset
    }

    fun setAdsetId(adsetId: String) {
        this.adsetId = adsetId
    }

    fun setAfStatus(afStatus: String) {
        this.afStatus = afStatus
    }

    fun setAgency(agency: String) {
        this.agency = agency
    }

    fun setAfChannel(afChannel: String) {
        this.afChannel = afChannel
    }

    fun setCampaign(campaign: String) {
        this.campaign = campaign
    }

    fun setCampaignId(campaignId: String) {
        this.campaignId = campaignId
    }

    override fun toString(): String {
        return "AdjustBean{" +
                "pkg='" + pkg + '\'' +
                ", ver='" + ver + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", mValue='" + mValue + '\'' +
                ", mTime=" + mTime +
                ", mIp='" + mIp + '\'' +
                ", userId='" + userId + '\'' +
                ", utmSource='" + utmSource + '\'' +
                ", adgroupId='" + adgroupId + '\'' +
                ", adset='" + adset + '\'' +
                ", adsetId='" + adsetId + '\'' +
                ", afStatus='" + afStatus + '\'' +
                ", agency='" + agency + '\'' +
                ", afChannel='" + afChannel + '\'' +
                ", campaign='" + campaign + '\'' +
                ", campaignId='" + campaignId + '\'' +
                ", mEvent='" + mEvent + '\'' +
                ", mLabel='" + mLabel + '\'' +
                '}'
    }
    
}
