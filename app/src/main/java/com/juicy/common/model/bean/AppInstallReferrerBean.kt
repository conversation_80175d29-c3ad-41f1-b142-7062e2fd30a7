package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class AppInstallReferrerBean {
    private var referrerUrl: String? = null
    private var mReferrerUrl: String = "" //af的referrer_url
    private var installVersion: String? = null
    private var mInstallVersion: String = "" //af的install_version
    private var appInstallTime: Long = 0
    private var mAppInstallTime: Long = 0 //af的app_install_time
    private var appInstallServerTime: Long = 0
    private var mAppInstallServerTime: Long = 0 //af的app_install_server_time

    fun setReferrerUrl(referrerUrl: String?) {
        this.referrerUrl = referrerUrl
    }

    fun setInstallVersion(installVersion: String?) {
        this.installVersion = installVersion
    }

    fun setAppInstallTime(appInstallTime: Long) {
        this.appInstallTime = appInstallTime
    }

    fun setAppInstallServerTime(appInstallServerTime: Long) {
        this.appInstallServerTime = appInstallServerTime
    }

    fun setReferrerClickTime(referrerClickTime: Long) {
        this.referrerClickTime = referrerClickTime
    }

    fun setReferrerClickServerTime(referrerClickServerTime: Long) {
        this.referrerClickServerTime = referrerClickServerTime
    }

    private var referrerClickTime: Long = 0
    private var referrerClickServerTime: Long = 0


    override fun toString(): String {
        return "AppInstallReferrerBean(referrerUrl='$referrerUrl', mReferrerUrl='$mReferrerUrl', installVersion='$installVersion', mInstallVersion='$mInstallVersion', appInstallTime=$appInstallTime, mAppInstallTime=$mAppInstallTime, appInstallServerTime=$appInstallServerTime, mAppInstallServerTime=$mAppInstallServerTime, referrerClickTime=$referrerClickTime, referrerClickServerTime=$referrerClickServerTime)"
    }
}
