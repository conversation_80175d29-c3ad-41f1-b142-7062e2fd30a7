package com.juicy.common.model.message_event

import androidx.annotation.Keep

@Keep
class EstHangUpTimeBean {
    var channelName: String? = null
    private var currentTime: Long? = null // 当前时间
    private var isPaused: Boolean = false // 是否暂停
    var payUserId: String? = null // 付费用户id
    @JvmField
    var estimateTime: Long? = null // 预估剩余时间，单位：秒

    private var totalTime: Long? = null // 总时间


    override fun toString(): String {
        return "EstHangUpTimeBean{" +
                "channelName='" + channelName + '\'' +
                ", payUserId='" + payUserId + '\'' +
                ", estimateTime=" + estimateTime +
                ", totalTime=" + totalTime +
                ", currentTime=" + currentTime +
                ", isPaused=" + isPaused +
                '}'
    }
}
