package com.juicy.common.model.bean

import androidx.annotation.Keep
import com.juicy.common.model.bean.AppConfigItemsBean

@Keep
class JuicyConfigBean {
    var rvsta: String? = null
    @JvmField
    var ver: String? = null
    @JvmField
    var items: List<com.juicy.common.model.bean.AppConfigItemsBean>? = null

    @JvmField
    var riskConfigBean: com.juicy.common.model.bean.RiskConfigBean? = null

    override fun toString(): String {
        return "Ac{" +
                "ver='" + ver + '\'' +
                ", items=" + items +
                '}'
    }
}
