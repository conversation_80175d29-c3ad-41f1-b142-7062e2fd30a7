package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class RankInfoBean {
    @JvmField
    var monthName: String? = null
    @JvmField
    var sortNo: String? = null
    @JvmField
    var rankData: List<RankDataBean>? = null

    class RankDataBean {
        @JvmField
        var sort: Int = 0
        @JvmField
        var userId: String? = null
        var totalIncomeCoins: Int = 0
        @JvmField
        var nickname: String? = null
        @JvmField
        var avatar: String? = null
        var avatarMapPath: String? = null
        var isShowLeaderboard: Boolean = false
    }
}
