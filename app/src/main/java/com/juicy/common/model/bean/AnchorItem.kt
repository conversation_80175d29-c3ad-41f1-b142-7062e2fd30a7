package com.juicy.common.model.bean

import com.chad.library.adapter.base.entity.MultiItemEntity

class AnchorItem : MultiItemEntity {
    override var itemType: Int // 0 banner  1 主播
    @JvmField
    var anchorInfoBean: com.juicy.common.model.bean.AnchorInfoBean? = null //主播数据
    private var afBean: com.juicy.common.model.bean.AnchorInfoBean? = null //banner数据

    var bannerItemBean: com.juicy.common.model.bean.BannerItemBean? = null //banner数据
    private var mBannerItemBean: com.juicy.common.model.bean.BannerItemBean? = null //banner数据

    constructor(itemType: Int, anchorInfoBean: com.juicy.common.model.bean.AnchorInfoBean?) {
        this.itemType = itemType
        this.anchorInfoBean = anchorInfoBean
    }

    constructor(itemType: Int, bannerItemBean: com.juicy.common.model.bean.BannerItemBean?) {
        this.itemType = itemType
        this.bannerItemBean = bannerItemBean
    }

    override fun toString(): String {
        return "AnchorItem(itemType=$itemType, anchorInfoBean=$anchorInfoBean, afBean=$afBean, bannerItemBean=$bannerItemBean, mBannerItemBean=$mBannerItemBean)"
    }
}
