package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class BaseUserInfoBean {
    val about: String? = null //个性签名
    @JvmField
    val age: Int? = null //年龄
    private var mAbout: String = "" //af的about
    val avatar: String? = null //头像
    val avatarMiddleThumbUrl: String? = null //头像middle缩略图url
    private var mAvatarMiddleThumbUrl: String = "" //af的avatar_middle_thumb_url
    val avatarThumbUrl: String? = null //头像缩略图url
    @JvmField
    val avatarUrl: String? = null //头像url
    val birthday: String? = null // 生日
    @JvmField
    val country: String? = null // 国家
    private var mCountry: String = "" //af的country
    val favors: String? = null // 偏好
    @JvmField
    val gender: Int? = null // 性别
    val specialFollow: Boolean? = null //是否特别关注
    private var mFavors: String = "" //af的favors
    val vip: Boolean? = null //是否Vip
    val language: String? = null //语言
    private var mLanguage: String = "" //af的language
    val level: Int? = null // 用户等级
    @JvmField
    val nickname: String? = null // 昵称

    @JvmField
    var onlineStatus: String? = null // 在线状态
    val unitPrice: Int? = null //主播单位分钟价格
    @JvmField
    val userId: String? = null // 用户id
    private var mUserId: String = "" //af的user_id
    val userType: Int? = null // 用户类型
    val vipExpiryTime: String? = null // Vip到期时间
    private var mVipExpiryTime: String = "" //af的vip_expiry_time


    override fun toString(): String {
        return "BaseUserInfoBean{" +
                "about='" + about + '\'' +
                ", age=" + age +
                ", mAbout='" + mAbout + '\'' +
                ", avatar='" + avatar + '\'' +
                ", avatarMiddleThumbUrl='" + avatarMiddleThumbUrl + '\'' +
                ", mAvatarMiddleThumbUrl='" + mAvatarMiddleThumbUrl + '\'' +
                ", avatarThumbUrl='" + avatarThumbUrl + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", birthday='" + birthday + '\'' +
                ", country='" + country + '\'' +
                ", mCountry='" + mCountry + '\'' +
                ", favors='" + favors + '\'' +
                ", mFavors='" + mFavors + '\'' +
                ", vip=" + vip +
                ", language='" + language + '\'' +
                ", mLanguage='" + mLanguage + '\'' +
                ", level=" + level +
                ", nickname='" + nickname + '\'' +
                ", onlineStatus='" + onlineStatus + '\'' +
                ", unitPrice=" + unitPrice +
                ", userId='" + userId + '\'' +
                ", mUserId='" + mUserId + '\'' +
                ", userType=" + userType +
                ", vipExpiryTime='" + vipExpiryTime + '\'' +
                ", mVipExpiryTime='" + mVipExpiryTime + '\'' +
                '}'
    }



}
