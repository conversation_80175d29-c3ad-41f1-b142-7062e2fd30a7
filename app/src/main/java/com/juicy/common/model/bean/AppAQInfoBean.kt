package com.juicy.common.model.bean

import androidx.annotation.Keep
import com.juicy.common.model.bean.MessageAnswer

@Keep
class AppAQInfoBean {
    var code: Long = 0
    @JvmField
    var handleType: Long = 0
    private var mHandleType: Long = 0 //af的handle_type
    @JvmField
    var imageUrl: String? = null
    @JvmField
    var isViewExample: Boolean = false
    private var mImageUrl: String = "" //af的image_url
    @JvmField
    var messageAnswer: com.juicy.common.model.bean.MessageAnswer? = null
    @JvmField
    var question: String? = null
    private var mQuestion: String = "" //af的question
    @JvmField
    var toUrl: String? = null
    @JvmField
    var type: Long = 0
    private var mType: Long = 0 //af的type

    override fun toString(): String {
        return "AppAQInfoBean(code=$code, handleType=$handleType, mHandleType=$mHandleType, imageUrl=$imageUrl, mImageUrl='$mImageUrl', isViewExample=$isViewExample, messageAnswer=$messageAnswer, question=$question, mQuestion='$mQuestion', toUrl=$toUrl, type=$type, mType=$mType)"
    }
}
