package com.juicy.common.model.bean

import androidx.annotation.Keep
import com.juicy.common.model.bean.AppAQInfoBean

@Keep
class RobotBean {
    @JvmField
    var content: String? = null
    private var appAQInfoBean: List<com.juicy.common.model.bean.AppAQInfoBean>? = null

    fun getfaqInfoList(): List<com.juicy.common.model.bean.AppAQInfoBean>? {
        return appAQInfoBean
    }

    fun setfaqInfoList(value: List<com.juicy.common.model.bean.AppAQInfoBean>?) {
        this.appAQInfoBean = value
    }
}
