package com.juicy.common.model.bean

import androidx.annotation.Keep


@Keep
class AnchorWallTagBean {
    @JvmField
    var badTagList: List<String>? = null
    var broadcasterId: String? = null
    private var mBroadcasterId: String = "" //af的broadcaster_id

    var broadcasterName: String? = null
    @JvmField
    var channelName: String? = null
    @JvmField
    var duration: Int = 0
    var isClubService: Boolean = false
    private var mChannelName: String = "" //af的channel_name
    private var mTagList: List<String> = listOf() //af的tag_list

    var recommendList: List<com.juicy.common.model.bean.VideoCallRoomBean> = ArrayList()
    @JvmField
    var tagList: List<String>? = null

    override fun toString(): String {
        return "AnchorWallTagBean(badTagList=$badTagList, broadcasterId=$broadcasterId, mBroadcasterId='$mBroadcasterId', broadcasterName=$broadcasterName, channelName=$channelName, duration=$duration, isClubService=$isClubService, mChannelName='$mChannelName', mTagList=$mTagList, recommendList=$recommendList, tagList=$tagList)"
    }
}