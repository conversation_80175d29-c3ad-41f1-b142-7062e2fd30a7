package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class AnchorTagItemBean {
    @JvmField
    var tagName: String? = null
    private var mTagName: String = "" //af的tag_name

    @JvmField
    var subTagList: List<String>? = null
    var selectedPos: Int = 0 // 当前选定的subtab
    private var mSubTagList: List<String> = listOf() //af的sub_tag_list

    var showName: String? = null

    override fun toString(): String {
        return "AnchorTagItemBean(tagName='$tagName', mTagName='$mTagName', subTagList=$subTagList, selectedPos=$selectedPos, mSubTagList=$mSubTagList, showName=$showName)"
    }
}
