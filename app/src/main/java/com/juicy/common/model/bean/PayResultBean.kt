package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class PayResultBean(@JvmField var goodsCode: String, @JvmField var orderNo: String, @JvmField var paidAmount: String) {
    var goodsName: String? = null
    var payAmount: String? = null
    var paidCurrency: String? = null
    @JvmField
    var requestUrl: String? = null
    var tradeNo: String? = null


    override fun toString(): String {
        return "PayResultBean{" +
                "goodsCode='" + goodsCode + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", payAmount='" + payAmount + '\'' +
                ", paidAmount='" + paidAmount + '\'' +
                ", paidCurrency='" + paidCurrency + '\'' +
                ", requestUrl='" + requestUrl + '\'' +
                ", tradeNo='" + tradeNo + '\'' +
                '}'
    }
}
