package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class OssPolicyINfoBean {
    @JvmField
    var accessKeyId: String? = null
    @JvmField
    var policy: String? = null
    @JvmField
    var signature: String? = null
    var expire: String? = null
    @JvmField
    var dir: String? = null
    @JvmField
    var host: String? = null
    @JvmField
    var callback: String? = null

    override fun toString(): String {
        return "UserOssPolicy{" +
                "accessKeyId='" + accessKeyId + '\'' +
                ", policy='" + policy + '\'' +
                ", signature='" + signature + '\'' +
                ", expire='" + expire + '\'' +
                ", dir='" + dir + '\'' +
                ", host='" + host + '\'' +
                ", callback='" + callback + '\'' +
                '}'
    }
}
