package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class CameraCOnfigInfoBean {
    @JvmField
    var isOpenCamera: Boolean = false
    private var mIsOpenCamera: Boolean = false //af的is_open_camera
    @JvmField
    var openCoins: Int = 0
    private var mOpenCoins: Int = 0 //af的open_coins
    @JvmField
    var openDay: Int = 0
    private var mOpenDay: Int = 0 //af的open_day

    override fun toString(): String {
        return "CameraCOnfigInfoBean{" +
                "isOpenCamera=" + isOpenCamera +
                ", openCoins=" + openCoins +
                ", openDay=" + openDay +
                '}'
    }

    fun getIsOpenCamera(): Boolean {
        return mIsOpenCamera
    }

    fun getOpenCoins(): Int {
        return mOpenCoins
    }

    fun getOpenDay(): Int {
        return mOpenDay
    }
}
