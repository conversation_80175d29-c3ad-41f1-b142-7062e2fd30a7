package com.juicy.common.model.message_event

import androidx.annotation.Keep

/**
 * 第三方支付状态
 */
@Keep
class MatchChatFreeTimesBean {
    /**
     * 免费次数
     */
    var freeTimes: Int = 0

    /**
     *
     */
    var command: String? = null

    override fun toString(): String {
        return "FlashChatFreeTimesObject{" +
                "freeTimes=" + freeTimes +
                ", command='" + command + '\'' +
                '}'
    }
}

