package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class VideoCallInfoBean {
    var broadcastMaskConfig: com.juicy.common.model.bean.GiftCountDownBean? = null // 主播面具配置
    var broadcasterUserName: String? = null // 主播名
    @JvmField
    var callFreeSeconds: Int? = null // 免费时长
    @JvmField
    var channelName: String? = null // 通话频道
    var chooseVideoSdk: Int? = null // 选用的视频sdk: 1- > 声网；2 -> bigo
    var discountedPrice: Int? = null // 主播收入单价
    var duration: Int? = null // 视频时长
    var fromUserId: String? = null // 拨打方用户id
    var greenMode: Boolean? = null // 是否绿色模式
    @JvmField
    var rtcToken: String? = null // 声网token
    @JvmField
    var toUserId: String? = null // 被拨打方用户id
    var unitPrice: Int? = null // 用户通话单价
    var videoFileUrl: String? = null // 视频文件Url
    var videoPlayMode: String? = null // 视频播放模式 stream,player

    override fun toString(): String {
        return "Cl{" +
                "broadcastMaskConfig=" + broadcastMaskConfig +
                ", broadcasterUserName='" + broadcasterUserName + '\'' +
                ", callFreeSeconds=" + callFreeSeconds +
                ", channelName='" + channelName + '\'' +
                ", chooseVideoSdk=" + chooseVideoSdk +
                ", discountedPrice=" + discountedPrice +
                ", duration=" + duration +
                ", fromUserId='" + fromUserId + '\'' +
                ", isGreenMode=" + greenMode +
                ", rtcToken='" + rtcToken + '\'' +
                ", toUserId='" + toUserId + '\'' +
                ", unitPrice=" + unitPrice +
                ", videoFileUrl='" + videoFileUrl + '\'' +
                ", videoPlayMode='" + videoPlayMode + '\'' +
                '}'
    }
}
