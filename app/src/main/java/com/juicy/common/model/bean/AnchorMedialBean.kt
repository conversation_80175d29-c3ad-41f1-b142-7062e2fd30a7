package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class AnchorMedialBean {
    val coins: Int? = null
    val mediaId: String? = null
    private var mCoins: Int = 0 //af的coins
    private var mMediaId: String = "" //af的media_id
    val mediaPath: String? = null
    val mediaType: String? = null
    val mediaUrl: String? = null
    private var mStatus: String = "" //af的status
    val middleThumbUrl: String? = null
    val sort: Int? = null
    private var mMiddleThumbUrl: String = "" //af的middle_thumb_url
    @JvmField
    val thumbUrl: String? = null
    val userId: String? = null
    private var mUserId: String = "" //af的user_id

    override fun toString(): String {
        return "AnchorMedialBean(coins=$coins, mediaId=$mediaId, mCoins=$mCoins, mMediaId='$mMediaId', mediaPath=$mediaPath, mediaType=$mediaType, mediaUrl=$mediaUrl, mStatus='$mStatus', middleThumbUrl=$middleThumbUrl, sort=$sort, mMiddleThumbUrl='$mMiddleThumbUrl', thumbUrl=$thumbUrl, userId=$userId, mUserId='$mUserId')"
    }
}
