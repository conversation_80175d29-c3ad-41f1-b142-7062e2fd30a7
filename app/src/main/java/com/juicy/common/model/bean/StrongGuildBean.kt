package com.juicy.common.model.bean

import androidx.annotation.Keep


@Keep
class StrongGuildBean {
    @JvmField
    val content: String? = null //内容
    @JvmField
    val inIcon: String? = null //新应用图标
    @JvmField
    val inPkgName: String? = null //新包图标
    @JvmField
    val inviteCode: String = "" //邀请码
    val iosAppId: String? = null //iosAppId
    val pkgname: String? = null //pkgname
    @JvmField
    val rewardCoins: Int? = null //奖励
    @JvmField
    val source: Int? = null //source
    @JvmField
    val url: String? = null //url

    @JvmField
    var nativeRechargeRedirect: Int = 0 //
}
