package com.juicy.common.model.bean

class DisturbPatchParam {
    @kotlin.jvm.JvmField
    var isSwitchNotDisturbCall: Boolean = false
    var isSwitchNotDisturbIm: Boolean = false

    constructor(isSwitchNotDisturbCall: <PERSON><PERSON><PERSON>, isSwitchNotDisturbIm: <PERSON>olean) {
        this.isSwitchNotDisturbCall = isSwitchNotDisturbCall
        this.isSwitchNotDisturbIm = isSwitchNotDisturbIm
    }

    constructor()

    override fun toString(): String {
        return "DisturbParam{" +
                "isSwitchNotDisturbCall=" + isSwitchNotDisturbCall +
                ", isSwitchNotDisturbIm=" + isSwitchNotDisturbIm +
                '}'
    }
}
