package com.juicy.common.model.bean

import androidx.annotation.Keep
import java.io.Serializable

@Keep
class MediaInfoBean : Serializable {
    @JvmField
    val mediaId: String? = null
    val userId: String? = null
    @JvmField
    val mediaType: String? = null
    val mediaPath: String? = null
    @JvmField
    val thumbUrl: String? = null

    @JvmField
    var mediaUrl: String? = null
    val sort: Int? = null
    val coins: Int? = null
    @JvmField
    val middleThumbUrl: String? = null

    override fun toString(): String {
        return "MediaInfoBean{" +
                "mediaId='" + mediaId + '\'' +
                ", userId='" + userId + '\'' +
                ", mediaType='" + mediaType + '\'' +
                ", mediaPath='" + mediaPath + '\'' +
                ", thumbUrl='" + thumbUrl + '\'' +
                ", mediaUrl='" + mediaUrl + '\'' +
                ", sort=" + sort +
                ", coins=" + coins +
                ", middleThumbUrl='" + middleThumbUrl + '\'' +
                '}'
    }
}
