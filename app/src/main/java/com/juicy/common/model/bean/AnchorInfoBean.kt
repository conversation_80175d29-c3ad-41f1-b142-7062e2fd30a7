package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class AnchorInfoBean {
    @JvmField
    val userId: String? = null // 用户id
    private var mValue = "" //af的value
    private var mTime: Long = 0 //af的time
    private var mIp = "" //af的ip
    @JvmField
    val nickname: String? = null // 昵称
    @JvmField
    val avatar: String? = null // 头像
    val avatarMapPath: String? = null
    @JvmField
    val gender: Int? = null // 性别
    private var mGender: Int = 0 //af的gender
    @JvmField
    val age: Int? = null // 年龄
    @JvmField
    val country: String? = null // 国家
    @JvmField
    var status: String? = null // 状态，在线/离线/忙碌
    @JvmField
    val callCoins: Int? = null // 通话价格
    val unit: String? = null // 价格单位
    private var mStatus: String = "" //af的status
    private var mCallCoins: Int = 0 //af的call_coins
    private var mUnit: String = "" //af的unit
    val videoMapPaths: List<String>? = null // 视频相对路径集合
    val followNum: Int? = null // follow数量
    val friend: Boolean? = null
    val multiple: Boolean? = null
    val applicableTags: List<String>? = null
    @JvmField
    val activityTagUrl: String? = null
    private var mAge: Int = 0 //af的age
    private var mCountry: String = "" //af的country

    override fun toString(): String {
        return "AnchorInfoBean(userId=$userId, nickname=$nickname, avatar=$avatar, avatarMapPath=$avatarMapPath, gender=$gender, age=$age, country=$country, status=$status, callCoins=$callCoins, unit=$unit, videoMapPaths=$videoMapPaths, followNum=$followNum, friend=$friend, multiple=$multiple, applicableTags=$applicableTags, activityTagUrl=$activityTagUrl, mAge=$mAge, mCountry=$mCountry)"
    }
}
