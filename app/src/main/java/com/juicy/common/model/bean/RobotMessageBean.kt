package com.juicy.common.model.bean

import com.juicy.common.model.bean.AppAQInfoBean

class RobotMessageBean {
    @JvmField
    var type: Int = 0 //0 问题 1 回答 2用户
    @JvmField
    var content: String = "" //1 和 2有
    @JvmField
    var robotBean: com.juicy.common.model.bean.RobotBean? = null
    @JvmField
    var appAQInfoBean: com.juicy.common.model.bean.AppAQInfoBean? = null
    @JvmField
    var isHelp: Int = -1
    @JvmField
    var isTraning: Boolean = false
    @JvmField
    var tranString: String? = null
}
