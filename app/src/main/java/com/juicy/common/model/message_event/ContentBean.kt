package com.juicy.common.model.message_event

import androidx.annotation.Keep

@Keep
class ContentBean {
    var code: String? = null
    var coinPrice: Int = 0
    var count: Int = 0
    private var mType: String? = null
    var giftDesc: String? = null
    var iconPath: String? = null
    var iconThumbPath: String? = null
    var name: String? = null
    var sortNo: Int = 0
    var uuid: String? = null

    private var mId: String? = null

    override fun toString(): String {
        return "ContentBean{" +
                "code='" + code + '\'' +
                ", coinPrice=" + coinPrice +
                ", count=" + count +
                ", giftDesc='" + giftDesc + '\'' +
                ", iconPath='" + iconPath + '\'' +
                ", iconThumbPath='" + iconThumbPath + '\'' +
                ", name='" + name + '\'' +
                ", sortNo=" + sortNo +
                ", uuid='" + uuid + '\'' +
                ", type='" + mType + '\'' +
                ", id='" + mId + '\'' +
                '}'
    }
}
