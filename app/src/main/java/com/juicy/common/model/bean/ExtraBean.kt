package com.juicy.common.model.bean

import androidx.annotation.Keep
import com.chad.library.adapter.base.entity.MultiItemEntity

@Keep
class ExtraBean {
    var extra: com.juicy.common.model.bean.ExtraBean.ExtraBean? = null
    private var mExtra: com.juicy.common.model.bean.ExtraBean.ExtraBean? = null //af的extra

    @JvmField
    var channelList: List<com.juicy.common.model.bean.ExtraBean.ChannelInfoBean>? = null
    private var mChannelList: List<com.juicy.common.model.bean.ExtraBean.ChannelInfoBean>? = null //af的channel_list

    inner class ExtraBean {
        var isShowTppPopout: Boolean = false
        var tppPopoutType: Int = 0
        var chooseChannel: String? = null
        private var mIsShowTppPopout: Boolean = false //af的is_show_tpp_popout
        private var mTppPopoutType: Int = 0 //af的tpp_popout_type
        private var mChooseChannel: String? = "" //af的choose_channel

        override fun toString(): String {
            return "ExtraBean{" +
                    "isShowTppPopout=" + isShowTppPopout +
                    ", tppPopoutType=" + tppPopoutType +
                    ", chooseChannel='" + chooseChannel + '\'' +
                    ", mIsShowTppPopout=" + mIsShowTppPopout +
                    ", mTppPopoutType=" + mTppPopoutType +
                    ", mChooseChannel='" + mChooseChannel + '\'' +
                    '}'
        }
    }

    class ChannelInfoBean : MultiItemEntity {
        @JvmField
        var payChannel: String? = null
        private var mPayChannel: String? = null //af的pay_channel
        @JvmField
        var title: String? = null
        private var mTitle: String? = null //af的title
        @JvmField
        var iconUrl: String? = null
        private var mIconUrl: String? = null //af的icon_url
        @JvmField
        var presentCoinRatio: Int = 0
        private var mPresentCoinRatio: Int = 0 //af的present_coin_ratio
        @JvmField
        var promotionPresentCoinRatio: Int = 0
        private var mPromotionPresentCoinRatio: Int = 0 //af的promotion_present_coin_ratio
        @JvmField
        var jumpType: Int = 0
        @JvmField
        var recommendReason: String? = null
        override var itemType: Int = 0
        var isCheckBox: Boolean = false


        override fun toString(): String {
            return "ChannelInfoBean{" +
                    "payChannel='" + payChannel + '\'' +
                    ", title='" + title + '\'' +
                    ", iconUrl='" + iconUrl + '\'' +
                    ", presentCoinRatio=" + presentCoinRatio +
                    ", promotionPresentCoinRatio=" + promotionPresentCoinRatio +
                    ", jumpType=" + jumpType +
                    ", recommendReason='" + recommendReason + '\'' +
                    ", itemType=" + itemType +
                    ", isCheckBox=" + isCheckBox +
                    ", mPayChannel='" + mPayChannel + '\'' +
                    ", mTitle='" + mTitle + '\'' +
                    ", mIconUrl='" + mIconUrl + '\'' +
                    ", mPresentCoinRatio=" + mPresentCoinRatio +
                    ", mPromotionPresentCoinRatio=" + mPromotionPresentCoinRatio +
                    '}'
        }
    }

    override fun toString(): String {
        return "PayChannelBean{" +
                "extra=" + extra +
                ", channelList=" + channelList +
                '}'
    }

}
