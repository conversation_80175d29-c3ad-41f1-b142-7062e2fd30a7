package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class CallResultInfoBean {
    var channelName: String? = null
    var broadcasterId: String? = null
    var broadcasterName: String? = null
    private var mChannelName: String = "" //af的channel_name
    private var mBroadcasterId: String = "" //af的broadcaster_id
    private var mBroadcasterName: String = "" //af的broadcaster_name
    var duration: Int = 0
    var tagList: List<String>? = null
    var badTagList: List<String>? = null
    private var mTagList: List<String> = ArrayList() //af的tag_list
    private var mBadTagList: List<ReListBean> = ArrayList() //af的bad_tag_list

    var recommendList: List<com.juicy.common.model.bean.CallResultInfoBean.RecommendListBean>? = null
    var isClubService: Boolean = false

    class RecommendListBean {
        var broadcasterId: String? = null
        var broadcasterName: String? = null
        var avatarMapPath: String? = null
        var avatarThumbUrl: String? = null
        var gender: Int = 0
        var unitPrice: Double = 0.0
    }
    private class ReListBean {
        var mBroadcasterId: String = "" //af的broadcaster_id
        var mBroadcasterName: String = ""
        var unitPrice: Double = 0.0
    }

    override fun toString(): String {
        return "CallResultInfoBean{" +
                "channelName='" + channelName + '\'' +
                ", broadcasterId='" + broadcasterId + '\'' +
                ", broadcasterName='" + broadcasterName + '\'' +
                ", mChannelName='" + mChannelName + '\'' +
                ", mBroadcasterId='" + mBroadcasterId + '\'' +
                ", mBroadcasterName='" + mBroadcasterName + '\'' +
                ", duration=" + duration +
                ", mTagList=" + mTagList +
                ", mBadTagList=" + mBadTagList +
                ", recommendList=" + recommendList +
                ", isClubService=" + isClubService +
                '}'
    }
    
}
