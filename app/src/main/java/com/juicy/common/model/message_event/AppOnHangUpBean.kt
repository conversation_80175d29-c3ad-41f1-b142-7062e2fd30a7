package com.juicy.common.model.message_event

import androidx.annotation.Keep

@Keep
class AppOnHangUpBean {
    @JvmField
    var channelName: String? = null // 通话频道
    @JvmField
    var fromUserId: String? = null // 拨打方用户id
    private var nickName: String? = null // 拨打方用户昵称
    var toUserId: String? = null // 被拨打方用户id
    @JvmField
    var reason: Int? = null // 退出视频原因
    private var reasonDesc: String? = null // 退出视频原因描述

    override fun toString(): String {
        return "AppOnHangUpBean(channelName=$channelName, fromUserId=$fromUserId, nickName=$nickName, toUserId=$toUserId, reason=$reason, reasonDesc=$reasonDesc)"
    }

}
