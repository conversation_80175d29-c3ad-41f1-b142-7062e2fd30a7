package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class FreeCallConBean {
    val isSwitch: Boolean = false
    private var mIsSwitch: Boolean = false //af的is_switch
    val isFreeCall: Boolean = false
    private var mIsFreeCall: Boolean = false //af的is_free_call
    var residueFreeCallTimes: Int = 0
    private var mResidueFreeCallTimes: Int = 0 //af的residue_free_call_times

    override fun toString(): String {
        return "FreeCallConBean{" +
                "isSwitch=" + isSwitch +
                ", mIsSwitch=" + mIsSwitch +
                ", isFreeCall=" + isFreeCall +
                ", mIsFreeCall=" + mIsFreeCall +
                ", residueFreeCallTimes=" + residueFreeCallTimes +
                ", mResidueFreeCallTimes=" + mResidueFreeCallTimes +
                '}'
    }
}
