package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class ImageInfoBean {
    @JvmField
    val filename: String? = null
    private var mFilename: String? = null //af的filename
    val height: String? = null
    val mHeight: String? = null //af的height
    val mimeType: String? = null
    val size: String? = null
    private var mSize: String? = null //af的size
    val width: String? = null

    override fun toString(): String {
        return "ImageInfoBean{" +
                "filename='" + filename + '\'' +
                ", mFilename='" + mFilename + '\'' +
                ", height='" + height + '\'' +
                ", mHeight='" + mHeight + '\'' +
                ", mimeType='" + mimeType + '\'' +
                ", size='" + size + '\'' +
                ", mSize='" + mSize + '\'' +
                ", width='" + width + '\'' +
                '}'
    }
}
