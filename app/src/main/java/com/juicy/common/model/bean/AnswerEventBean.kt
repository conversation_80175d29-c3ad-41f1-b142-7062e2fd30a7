package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class AnswerEventBean {
    @JvmField
    var handleType: Long = 0
    private var mHandleType: Long = 0 //af的handle_type
    @JvmField
    var matchStr: String? = null
    @JvmField
    var toUrl: String? = null
    private var mMatchStr: String = "" //af的match_str
    private var mToUrl: String = "" //af的to_url

    override fun toString(): String {
        return "AnswerEventBean(handleType=$handleType, mHandleType=$mHandleType, matchStr=$matchStr, toUrl=$toUrl, mMatchStr='$mMatchStr', mToUrl='$mToUrl')"
    }
}
