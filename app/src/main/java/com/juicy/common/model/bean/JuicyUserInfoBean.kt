package com.juicy.common.model.bean

import androidx.annotation.Keep


@Keep
class JuicyUserInfoBean {
    @JvmField
    var userId: String = "" // 用户Id
    var userType: Int = 0 // 用户类型
    @JvmField
    var nickname: String = "" // 昵称
    @JvmField
    var age: Int = 0 // 年龄
    var auditStatus: Int = 0 // 审核状态
    var internal: Boolean = false // 是否内部用户
    var avatar: String = "" // 头像
    @JvmField
    var availableCoins: Int = 0 // 可用金币
    var avatarMiddleThumbUrl: String = "" // 头像中等缩略图
    @JvmField
    var avatarUrl: String = "" // 头像URL
    @JvmField
    var avatarThumbUrl: String = "" // 头像缩略图
    @JvmField
    var birthday: String = "" // 生日
    @JvmField
    var gender: Int = 0 // 性别
    var language: String = "" // 语言
    @JvmField
    var country: String = "" // 国家
    var pkgName: String = "" // 包名
    var club: Boolean = false // 是否俱乐部主播
    var followNum: Int = 0 // folow数量
    var answer: Boolean = false // 是否接听
    var block: Boolean = false // 是否被block

    @JvmField
    var isFriend: Boolean = false
    var havePassword: Boolean = false // 是否设置过密码
    var recharge: Boolean = false // 是否充值
    var switchNotDisturbCall: Boolean = false // 是否打开通话免打扰
    var switchNotDisturbIm: Boolean = false // 是否打开IM免打扰
    var vip: Boolean = false // 是否Vip
    @JvmField
    var level: Int = 0 // 用户登记
    @JvmField
    var rongcloudToken: String = "" // 融云token
    var praiseNum: Int = 0 //
    var tagDetails: List<com.juicy.common.model.bean.TagTipBean>? = null //用户标签详情
    var tagsList: List<String>? = null // 用户标签
    var clubAvatarThumbUrl: String = "" // club头像缩略图
    var clubAvatar: String = "" // club头像
    var clubAvatarUrl: String = "" // club头像url
    var clubAvatarMiddleThumbUrl: String = "" // club中等头像
    @JvmField
    var about: String = "" //个性签名
    @JvmField
    var unitPrice: Int = 0 // 通话价格
    var grade: Int = 0 // 主播等级
    @JvmField
    var vipUnitPrice: Int = 0 // vip通话价格
    var acceptMultipleCall: Boolean = false
    var probationPeriod: Boolean = false
    var horizontalAvatar: String = "" // 横向头像
    var horizontalAvatarUrl: String = ""
    var horizontalAvatarThumbUrl: String = ""
    var horizontalAvatarMiddleThumbUrl: String = ""
    var review: Boolean = false
    var multiple: Boolean = false
    @JvmField
    var mediaList: MutableList<com.juicy.common.model.bean.MediaInfoBean>? = null // 照片、视频
    @JvmField
    var email: String = "" //邮箱
    @JvmField
    var vipExpiryTime: String = "" //Vip到期时间;

    override fun toString(): String {
        return "JuicyUserInfoBean{" +
                "userId='" + userId + '\'' +
                ", userType=" + userType +
                ", nickname='" + nickname + '\'' +
                ", age=" + age +
                ", auditStatus=" + auditStatus +
                ", isInternal=" + internal +
                ", avatar='" + avatar + '\'' +
                ", availableCoins=" + availableCoins +
                ", avatarMiddleThumbUrl='" + avatarMiddleThumbUrl + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", avatarThumbUrl='" + avatarThumbUrl + '\'' +
                ", birthday='" + birthday + '\'' +
                ", gender=" + gender +
                ", language='" + language + '\'' +
                ", country='" + country + '\'' +
                ", pkgName='" + pkgName + '\'' +
                ", isClub=" + club +
                ", followNum=" + followNum +
                ", isAnswer=" + answer +
                ", isBlock=" + block +
                ", isHavePassword=" + havePassword +
                ", isRecharge=" + recharge +
                ", isSwitchNotDisturbCall=" + switchNotDisturbCall +
                ", isSwitchNotDisturbIm=" + switchNotDisturbIm +
                ", isVip=" + vip +
                ", level=" + level +
                ", rongcloudToken='" + rongcloudToken + '\'' +
                ", praiseNum=" + praiseNum +
                ", tagDetails=" + tagDetails +
                ", tagsList=" + tagsList +
                ", clubAvatarThumbUrl='" + clubAvatarThumbUrl + '\'' +
                ", clubAvatar='" + clubAvatar + '\'' +
                ", clubAvatarUrl='" + clubAvatarUrl + '\'' +
                ", clubAvatarMiddleThumbUrl='" + clubAvatarMiddleThumbUrl + '\'' +
                ", about='" + about + '\'' +
                ", unitPrice=" + unitPrice +
                ", grade=" + grade +
                ", vipUnitPrice=" + vipUnitPrice +
                ", acceptMultipleCall=" + acceptMultipleCall +
                ", isProbationPeriod=" + probationPeriod +
                ", horizontalAvatar='" + horizontalAvatar + '\'' +
                ", horizontalAvatarUrl='" + horizontalAvatarUrl + '\'' +
                ", horizontalAvatarThumbUrl='" + horizontalAvatarThumbUrl + '\'' +
                ", horizontalAvatarMiddleThumbUrl='" + horizontalAvatarMiddleThumbUrl + '\'' +
                ", isReview=" + review +
                ", isMultiple=" + multiple +
                ", mediaList=" + mediaList +
                ", email='" + email + '\'' +
                '}'
    }
}
