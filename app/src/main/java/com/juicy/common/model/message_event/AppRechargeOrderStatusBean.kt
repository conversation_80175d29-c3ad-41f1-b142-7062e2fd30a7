package com.juicy.common.model.message_event

import androidx.annotation.Keep

/**
 * 第三方支付状态
 */
@Keep
class AppRechargeOrderStatusBean {
    /**
     * 订单号
     */
    @JvmField
    var orderNo: String? = null

    private var channelName: String? = null

    /**
     * 用户id
     */
    var userId: String? = null

    private var callType: String? = null

    /**
     * 商品
     */
    @JvmField
    var code: String? = null

    private var nickName: String? = null

    /**
     * 状态
     */
    @JvmField
    var status: Int? = null //0 1 2

    override fun toString(): String {
        return "RechargeOrderStatusObject{" +
                "orderNo='" + orderNo + '\'' +
                ", userId='" + userId + '\'' +
                ", code='" + code + '\'' +
                ", status=" + status +
                ", channelName='" + channelName + '\'' +
                ", callType='" + callType + '\'' +
                ", nickName='" + nickName + '\'' +
                '}'
    }
}

