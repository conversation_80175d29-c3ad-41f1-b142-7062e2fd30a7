package com.juicy.common.model.bean

import androidx.annotation.Keep

@Keep
class LogInfoBean {
    var data: List<com.juicy.common.model.bean.LogInfoBean.DataBean>? = null
    @JvmField
    var log_type: String? = null
    @JvmField
    var subtype: String? = null
    @JvmField
    var behavior: String? = null
    @JvmField
    var utm_source: String? = null
    @JvmField
    var android_id: String? = null
    @JvmField
    var user_id: String? = null
    @JvmField
    var pkg: String? = null
    var chn_id: String? = null
    @JvmField
    var ver: String? = null
    @JvmField
    var platform: String? = null
    var platform_ver: Int = 0
    @JvmField
    var model: String? = null
    var p_ver: String? = null
    @JvmField
    var lan_id: String? = null
    @JvmField
    var sec_id: Int = 0
    @JvmField
    var sys_lan: String? = null
    @JvmField
    var country: String? = null
    var isIs_in_bg: Boolean = false
        private set
    var isIs_anchor: Boolean = false
        private set
    @JvmField
    var client_type: String? = null
    @JvmField
    var bizver: String? = null
    @JvmField
    var tm: Long = 0


    fun setIs_in_bg(is_in_bg: Boolean) {
        this.isIs_in_bg = is_in_bg
    }

    fun setIs_anchor(is_anchor: Boolean) {
        this.isIs_anchor = is_anchor
    }

    override fun toString(): String {
        return "LogInfoBean{" +
                "data=" + data +
                ", log_type='" + log_type + '\'' +
                ", subtype='" + subtype + '\'' +
                ", behavior='" + behavior + '\'' +
                ", utm_source='" + utm_source + '\'' +
                ", android_id='" + android_id + '\'' +
                ", user_id='" + user_id + '\'' +
                ", pkg='" + pkg + '\'' +
                ", chn_id='" + chn_id + '\'' +
                ", ver='" + ver + '\'' +
                ", platform='" + platform + '\'' +
                ", platform_ver=" + platform_ver +
                ", model='" + model + '\'' +
                ", p_ver='" + p_ver + '\'' +
                ", lan_id='" + lan_id + '\'' +
                ", sec_id=" + sec_id +
                ", sys_lan='" + sys_lan + '\'' +
                ", country='" + country + '\'' +
                ", is_in_bg=" + isIs_in_bg +
                ", is_anchor=" + isIs_anchor +
                ", client_type='" + client_type + '\'' +
                ", bizver='" + bizver + '\'' +
                ", tm=" + tm +
                '}'
    }

    class DataBean {
        @JvmField
        var event: String? = null
        @JvmField
        var code: String? = null
        @JvmField
        var uuid: String? = null
        @JvmField
        var orderId: String? = null
        var durationTime: Long = 0
        var elapsedTime: Long = 0
        @JvmField
        var result: String? = null
        @JvmField
        var resultCode: Int = 0
        var tm: Long = 0

        override fun toString(): String {
            return "DataBean{" +
                    "event='" + event + '\'' +
                    ", code='" + code + '\'' +
                    ", uuid='" + uuid + '\'' +
                    ", orderId='" + orderId + '\'' +
                    ", durationTime=" + durationTime +
                    ", elapsedTime=" + elapsedTime +
                    ", result='" + result + '\'' +
                    ", resultCode=" + resultCode +
                    ", tm=" + tm +
                    '}'
        }
    }
}
