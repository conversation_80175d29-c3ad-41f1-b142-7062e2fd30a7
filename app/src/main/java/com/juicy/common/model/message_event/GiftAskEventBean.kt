package com.juicy.common.model.message_event

import androidx.annotation.Keep

@Keep
class GiftAskEventBean {
    @JvmField
    var code: String? = null
    @JvmField
    var coinPrice: Int = 0
    var count: Int = 0
    private var giftName: String? = null
    var giftDesc: String? = null
    var iconPath: String? = null
    var iconThumbPath: String? = null
    private var nickName: String? = null
    var name: String? = null
    var sortNo: Int = 0
    private var channelName: String? = null
    var uuid: String? = null

    override fun toString(): String {
        return "GiftAskEventObject{" +
                "code='" + code + '\'' +
                ", coinPrice=" + coinPrice +
                ", count=" + count +
                ", giftDesc='" + giftDesc + '\'' +
                ", iconPath='" + iconPath + '\'' +
                ", iconThumbPath='" + iconThumbPath + '\'' +
                ", name='" + name + '\'' +
                ", sortNo=" + sortNo +
                ", uuid='" + uuid + '\'' +
                ", nickName='" + nickName + '\'' +
                ", channelName='" + channelName + '\'' +
                ", giftName='" + giftName + '\'' +
                '}'
    }
}


