package com.juicy.common.model.message_event

import androidx.annotation.Keep

@Keep
class AppOnCallBean {
    @JvmField
    var channelName: String? = null // 通话频道
    var avatar: String? = null // 拨打方头像
    private var fromUserName: String? = null // 拨打方用户名
    var avatarThumbUrl: String? = null // 拨打方头像缩略图
    @JvmField
    var fromUserId: String? = null // 拨打方用户id
    var toUserId: String? = null // 被拨打方用户id
    private var toUserName: String? = null // 被拨打方用户名
    var videoPlayMode: String? = null // 视频播放模式
    var videoFileUrl: String? = null // 视频文件Url
    var age: Int? = null // 拨打方年龄
    private var fromUserAge: Int? = null // 拨打方年龄
    var country: String? = null // 拨打方国家
    @JvmField
    var isFree: Boolean? = null // 是否免费通话
    var isFriend: Boolean? = null // 是否好友
    var chooseVideoSdk: Int? = null // 选用的视频sdk: 1- > 声网；2 -> bigo；
    var nickname: String? = null // 拨打方昵称
    private var phoneNumber: String? = null // 拨打方昵称
    var gender: Int? = null // 拨打方性别
    var broadcasterUnitPrice: Int? = null // 主播通话单价
    @JvmField
    var rtcToken: String? = null // 声网token
    var uiTips: String? = null // 拨打与被拨打界面显示的分钟话费文案
    var broadcasterUserName: String? = null // 主播名
    private var broadcasterUserAge: Int? = null // 主播年龄
    var callCategory: Int? = null
    var discountedPrice: Int? = null // 主播收入单价
    private var broadcasterUserCountry: String? = null // 主播国家
    var isGreenMode: Boolean? = null // 是否绿色模式
    private var userCountry: String? = null // 用户国家
    var unitPrice: Int? = null // 用户通话单价
    var callFreeSeconds: Int? = null // 免费时长


    override fun toString(): String {
        return "AppOnCallBean{" +
                "channelName='" + channelName + '\'' +
                ", avatar='" + avatar + '\'' +
                ", fromUserName='" + fromUserName + '\'' +
                ", avatarThumbUrl='" + avatarThumbUrl + '\'' +
                ", fromUserId='" + fromUserId + '\'' +
                ", toUserId='" + toUserId + '\'' +
                ", toUserName='" + toUserName + '\'' +
                ", videoPlayMode='" + videoPlayMode + '\'' +
                ", videoFileUrl='" + videoFileUrl + '\'' +
                ", age=" + age +
                ", fromUserAge=" + fromUserAge +
                ", country='" + country + '\'' +
                ", isFree=" + isFree +
                ", isFriend=" + isFriend +
                ", chooseVideoSdk=" + chooseVideoSdk +
                ", nickname='" + nickname + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", gender=" + gender +
                ", broadcasterUnitPrice=" + broadcasterUnitPrice +
                ", rtcToken='" + rtcToken + '\'' +
                ", uiTips='" + uiTips + '\'' +
                ", broadcasterUserName='" + broadcasterUserName + '\'' +
                ", broadcasterUserAge=" + broadcasterUserAge +
                ", callCategory=" + callCategory +
                ", discountedPrice=" + discountedPrice +
                ", broadcasterUserCountry='" + broadcasterUserCountry + '\'' +
                ", isGreenMode=" + isGreenMode +
                ", userCountry='" + userCountry + '\'' +
                ", unitPrice=" + unitPrice +
                ", callFreeSeconds=" + callFreeSeconds +
                '}'

    }


                
    
}
