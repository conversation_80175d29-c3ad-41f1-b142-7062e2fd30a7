package com.juicy.common.model.event

import androidx.annotation.Keep
import com.juicy.common.model.bean.CallLogInfoBean

@Keep
class CallEvent {
    @JvmField
    var channelName: String? = null
    @JvmField
    var nickName: String? = null
    @JvmField
    var userId: String? = null

    @JvmField
    var isMatch: Boolean = false
    @JvmField
    var isMatch20Out: Boolean = false

    @JvmField
    var isAllJoin: Boolean = false

    @JvmField
    var anchorPhoto: String? = null

    @JvmField
    var callType: String? = null

    var logList: MutableList<com.juicy.common.model.bean.CallLogInfoBean.DataBean> = ArrayList()
        set(logList) {
            field.addAll(logList)
        }
}
