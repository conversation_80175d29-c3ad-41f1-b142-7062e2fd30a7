package com.juicy.common.rcMessage;

import android.annotation.SuppressLint;
import android.os.Parcel;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;

import io.rong.common.ParcelUtils;
import io.rong.common.RLog;
import io.rong.imlib.MessageTag;
import io.rong.imlib.model.MessageContent;

/**
 * 自定义消息类型，进行数据解析
 *
 */

@SuppressLint("ParcelCreator")
@MessageTag(value = "LC:HyperLinkMsg", flag = MessageTag.ISCOUNTED)
public class HLinkMsg extends MessageContent {
    public  String   mName  =  "xa221";

    private static final String TAG = HLinkMsg.class.getSimpleName();

    private String content; //文本内容
    private String contentType; // 内容类型 "recharge_link"->充值消息链接
    private String extra;   //额外数据

    private String mLinkUrl;
    private String mLinkTitle;
    private String mLinkDesc;
    private String mLinkIcon;

    public String getLinkUrl() {
        return mLinkUrl;
    }
    public String getLinkTitle() {
        return mLinkTitle;
    }

    public String getLinkDesc() {
        return mLinkDesc;
    }

    public String getLinkIcon() {
        return mLinkIcon;
    }

    public String getContent() {
        return this.content;
    }

    public String getContentType() {
        return contentType;
    }

    @Override
    public String getExtra() {
        return extra;
    }

    private HLinkMsg() {
    }

    public HLinkMsg(Parcel in) {
        // 读取消息属性
        content = ParcelUtils.readFromParcel(in);
        contentType = ParcelUtils.readFromParcel(in);
        extra = ParcelUtils.readFromParcel(in);

        mLinkDesc = "mLinkDesc";
        mLinkIcon = "mLinkIcon";
        mLinkTitle = "mLinkTitle";
        mLinkUrl = "mLinkUrl";

    }


    /** 创建 CustomMessage(byte[] data) 带有 byte[] 的构造方法用于解析消息内容. */
    public HLinkMsg(byte[] data) {
        if (data == null) {
            RLog.e("TextMessage", "data is null ");
        } else {
            String jsonStr = null;
            if (data.length >= 40960) {
                RLog.e("TextMessage", "TextMessage length is larger than 40KB, length :" + data.length);
            }
            jsonStr = new String(data, StandardCharsets.UTF_8);

            if (jsonStr == null) {
                RLog.e("TextMessage", "jsonStr is null ");
            } else {
                try {
                    JSONObject jsonObj = new JSONObject(jsonStr);
                    if (jsonObj.has("content")) {
                        this.content=jsonObj.optString("content");
                    }
                    if (jsonObj.has("contentType")) {
                        this.contentType=jsonObj.optString("contentType");
                    }
                    if (jsonObj.has("extra")) {
                        this.extra=jsonObj.optString("extra");
                    }

                } catch (JSONException var4) {
                    RLog.e("TextMessage", "JSONException " + var4.getMessage());
                }

            }
            RLog.d("TextMessage", "mLinkDesc: " + mLinkDesc + ", mLinkIcon: " + mLinkIcon + ", mLinkTitle: " + mLinkTitle + ", mLinkUrl: " + mLinkUrl);
        }
    }

    /**
     * 将本地消息对象序列化为消息数据。
     *
     * @return 消息数据。
     */
    @Override
    public byte[] encode() {
        JSONObject jsonObj = new JSONObject();
        try {
            //  将所有自定义消息的内容，都序列化至 json 对象中
            jsonObj.put("content", this.content);
            jsonObj.put("contentType", this.contentType);
            jsonObj.put("extra", this.extra);

        } catch (JSONException e) {
            Log.e(TAG, "JsonEx " + e.getMessage());
        }

        return jsonObj.toString().getBytes(StandardCharsets.UTF_8);
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        // 对消息属性进行序列化，将类的数据写入外部提供的 Parcel 中
        ParcelUtils.writeToParcel(dest, content);
        ParcelUtils.writeToParcel(dest, contentType);
        ParcelUtils.writeToParcel(dest, extra);
    }

    public static final Creator<HLinkMsg> CREATOR =
    new Creator<HLinkMsg>() {
        public HLinkMsg createFromParcel(Parcel source) {
            return new HLinkMsg(source);
        }

        public HLinkMsg[] newArray(int size) {
            return new HLinkMsg[size];
        }
    };
}

