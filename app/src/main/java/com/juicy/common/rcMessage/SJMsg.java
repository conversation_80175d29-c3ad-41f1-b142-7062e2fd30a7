package com.juicy.common.rcMessage;

import android.annotation.SuppressLint;
import android.os.Parcel;
import android.text.TextUtils;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.rong.common.ParcelUtils;
import io.rong.common.RLog;
import io.rong.imlib.MessageTag;
import io.rong.imlib.model.MessageContent;

/**
 * 礼物消息
 * 用户点击礼物之后，赠送对应主播礼物并发送一条im消息
 */
@SuppressLint("ParcelCreator")
@MessageTag(value = "LC:SingleJsonMsg", flag = MessageTag.ISCOUNTED)
public class SJMsg extends MessageContent {
    private static final String TAG = SJMsg.class.getSimpleName();

    private String content; //文本内容
    private String contentType; // 类型为"gift" 礼物消息
    private String extra;   //额外数据
    private String senderId;
    private String receiverId;

    private String giftId;
    private String giftName;
    private String giftIcon;
    private int giftCount;

    public String getSenderId() {
        return senderId;
    }

    public String getReceiverId() {
        return receiverId;
    }

    // 快速构建消息对象方法
    public static SJMsg obtain(String content, String contentType, String extra) {
        SJMsg msg = new SJMsg();
        msg.content = content;
        msg.contentType = contentType;
        msg.extra = extra;
        msg.giftId = "1";
        msg.giftName = "AP";
        msg.giftIcon = "iCOn";
        msg.giftCount = 10;
        return msg;
    }


    // 快速构建消息对象方法
    public static SJMsg obtain(String content, String contentType, String senderId, String receiverId, String extra) {
        SJMsg msg = new SJMsg();
        msg.content = content;
        msg.contentType = contentType;
        msg.senderId = senderId;
        msg.receiverId = receiverId;
        msg.extra = extra;
        msg.giftId = "1";
        msg.giftName = "AP";
        msg.giftIcon = "iCOn";
        msg.giftCount = 10;

        return msg;
    }


    public String getContent() {
        return this.content;
    }

    public String getContentType() {
        return contentType;
    }

    @Override
    public String getExtra() {
        return extra;
    }

    private SJMsg() {
    }

    public SJMsg(Parcel in) {
        // 读取消息属性
        content = ParcelUtils.readFromParcel(in);
        contentType = ParcelUtils.readFromParcel(in);
        extra = ParcelUtils.readFromParcel(in);
    }

    ///是否为礼物消息
    public boolean isGift() {
        return "gift".equals(contentType);
    }
    //isTppContent
    public boolean isTppContent() {
        return "tpp".equals(contentType);
    }

    /**
     * 创建 CustomMessage(byte[] data) 带有 byte[] 的构造方法用于解析消息内容.
     */
    public SJMsg(byte[] data) {
        if (data == null) {
            RLog.e("TextMessage", "data is null ");
        } else {
            String jsonStr = null;

            if (data.length >= 40960) {
                RLog.e("TextMessage", "TextMessage length is larger than 40KB, length :" + data.length);
            }

            jsonStr = new String(data, StandardCharsets.UTF_8);

            if (jsonStr == null) {
                RLog.e("TextMessage", "jsonStr is null ");
            } else {
                try {
                    JSONObject jsonObj = new JSONObject(jsonStr);
                    if (jsonObj.has("content")) {
                        this.content = jsonObj.optString("content");
                    }
                    if (jsonObj.has("contentType")) {
                        this.contentType = jsonObj.optString("contentType");
                    }
                    if (jsonObj.has("extra")) {
                        this.extra = jsonObj.optString("extra");
                    }

                } catch (JSONException var4) {
                    RLog.e("TextMessage", "JSONException " + var4.getMessage());
                }

            }
        }
    }

    /**
     * 将本地消息对象序列化为消息数据。
     *
     * @return 消息数据。
     */
    @Override
    public byte[] encode() {
        JSONObject jsonObj = new JSONObject();
        try {
            //  将所有自定义消息的内容，都序列化至 json 对象中
            if (!TextUtils.isEmpty(this.getContent()))
                jsonObj.put("content", this.content);
            if (!TextUtils.isEmpty(this.getContentType()))
                jsonObj.put("contentType", this.contentType);
            if (!TextUtils.isEmpty(this.getExtra()))
                jsonObj.put("extra", this.extra);

        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }

        return jsonObj.toString().getBytes(StandardCharsets.UTF_8);

    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        // 对消息属性进行序列化，将类的数据写入外部提供的 Parcel 中
        ParcelUtils.writeToParcel(dest, content);
        ParcelUtils.writeToParcel(dest, contentType);
        ParcelUtils.writeToParcel(dest, extra);
    }

    private String getEmotion(String content){
        Pattern pattern = Pattern.compile("\\[/u([0-9A-Fa-f]+)\\]");
        Matcher matcher = pattern.matcher(content);

        StringBuffer sb;
        int inthex;
        for (sb = new StringBuffer();matcher.find();matcher.appendReplacement(sb,String.valueOf(Character.toChars(inthex)))){
        String matchStr = matcher.group(1);
        inthex = 0;
        if(matchStr!=null){
            inthex = Integer.parseInt(matchStr,16);
        }
    }

        matcher.appendTail(sb);
        return sb.toString();
    }

    public List<String> getSearchableWord(){
        List<String> words = new ArrayList<>();
        words.add(this.content);
        return words;
    }

    public static final Creator<SJMsg> CREATOR = new Creator<SJMsg>() {
        public SJMsg createFromParcel(Parcel source) {
            return new SJMsg(source);
        }

        public SJMsg[] newArray(int size) {
            return new SJMsg[size];
        }
    };

    @Override
    public String toString() {
        return "SingleJsonMsg{" +
                "content='" + content + '\'' +
                ", contentType='" + contentType + '\'' +
                ", extra='" + extra + '\'' +
                ", senderId='" + senderId + '\'' +
                ", receiverId='" + receiverId + '\'' +
                '}';
    }
}

