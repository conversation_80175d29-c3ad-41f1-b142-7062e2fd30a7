package com.juicy.common.rcMessage;

import android.annotation.SuppressLint;
import android.os.Parcel;
import android.util.Log;

import com.juicy.common.config.Cache;

import org.json.JSONException;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;

import io.rong.common.ParcelUtils;
import io.rong.common.RLog;
import io.rong.imlib.MessageTag;
import io.rong.imlib.model.MessageContent;

/**
 * 空消息，作用于优惠商品，收到此类型消息且contentType = special-offer
 * 只需要注册，融云监听特定消息处理即可
 */
@SuppressLint("ParcelCreator")
@MessageTag(value = "LC:NoneFlagMsg", flag = MessageTag.NONE)
public class NFMsg extends MessageContent {

    private static final String TAG = NFMsg.class.getSimpleName();

    private String content; //文本内容
    private String contentType; // 内容类型
    private String extra;   //额外数据

    private  String mName  =  "xa221";
    private  int mAge = 0;
    private  int mHeight = 0;

    public String getContent() {
        return this.content;
    }

    public String getContentType() {
        return contentType;
    }

    @Override
    public String getExtra() {
        return extra;
    }

    private NFMsg() {
    }

    public NFMsg(Parcel in) {
        // 读取消息属性
        content = ParcelUtils.readFromParcel(in);
        contentType = ParcelUtils.readFromParcel(in);
        extra = ParcelUtils.readFromParcel(in);

        mName = Cache.getInstance().appName;
        mAge = 10;
        mHeight = 20;
    }


    /** 创建 CustomMessage(byte[] data) 带有 byte[] 的构造方法用于解析消息内容. */
    public NFMsg(byte[] data) {
        if (data == null) {
            RLog.e("TextMessage", "data is null ");
        } else {
            String jsonStr = null;

            if (data.length >= 40960) {
                RLog.e("TextMessage", "TextMessage length is larger than 40KB, length :" + data.length);
            }

            jsonStr = new String(data, StandardCharsets.UTF_8);

            if (jsonStr == null) {
                RLog.e("TextMessage", "jsonStr is null ");
            } else {
                try {
                    JSONObject jsonObj = new JSONObject(jsonStr);
                    if (jsonObj.has("content")) {
                        this.content=jsonObj.optString("content");
                    }
                    if (jsonObj.has("contentType")) {
                        this.contentType=jsonObj.optString("contentType");
                    }
                    if (jsonObj.has("extra")) {
                        this.extra=jsonObj.optString("extra");
                    }

                } catch (JSONException var4) {
                    RLog.e("TextMessage", "JSONException " + var4.getMessage());
                }

            }
        }
    }

    /**
     * 将本地消息对象序列化为消息数据。
     *
     * @return 消息数据。
     */
    @Override
    public byte[] encode() {
        JSONObject jsonObj = new JSONObject();
        try {
            //  将所有自定义消息的内容，都序列化至 json 对象中
            jsonObj.put("content", this.content);
            jsonObj.put("contentType", this.contentType);
            jsonObj.put("extra", this.extra);

        } catch (JSONException e) {
            Log.e(TAG, "JSONException " + e.getMessage());
        }

        return jsonObj.toString().getBytes(StandardCharsets.UTF_8);
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        // 对消息属性进行序列化，将类的数据写入外部提供的 Parcel 中
        ParcelUtils.writeToParcel(dest, content);
        ParcelUtils.writeToParcel(dest, contentType);
        ParcelUtils.writeToParcel(dest, extra);
    }

    public static final Creator<NFMsg> CREATOR =
    new Creator<NFMsg>() {
        public NFMsg createFromParcel(Parcel source) {
            return new NFMsg(source);
        }

        public NFMsg[] newArray(int size) {
            return new NFMsg[size];
        }
    };
}

