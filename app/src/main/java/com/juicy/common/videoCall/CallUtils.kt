package com.juicy.common.videoCall

import android.os.CountDownTimer
import com.blankj.utilcode.util.ToastUtils
import com.juicy.common.model.bean.VideoCallInfoBean
import com.juicy.common.networks.delegate.FalshCancelInterface.cancel
import com.juicy.common.networks.delegate.FalshChatInterface.falshChat
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.utils.LanguageManager
import java.util.UUID

class CallUtils private constructor() {
    var videoCallViewModel: VideoCallViewModel? = null

    var countDownTimer: CountDownTimer? = null

    var tipCountDownTimer: CountDownTimer? = null

    var callInfoModel: CallInfoModel? = null

    var macthClientSessionId: String = ""

    interface StartFlashEventBack {
        fun onSuccess(data: VideoCallInfoBean?)
        fun onError()
    }

    fun stopFalsh(clientSessionId: String?) {
        cancel(clientSessionId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<String>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<String>) {
                super.onNext(t)
            }
        })
    }

    fun startFalsh(back: StartFlashEventBack?) {
        val uuid = UUID.randomUUID().toString()
        instance?.macthClientSessionId = uuid
        falshChat(uuid, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<VideoCallInfoBean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<VideoCallInfoBean>) {
                super.onNext(t)
                if (t.data != null) {
                    if (uuid != instance!!.macthClientSessionId) {
                        instance?.stopFalsh(uuid)
                    }
                    back?.onSuccess(t.data)
                } else {
                    //报错
                    if (!Cache.instance.reviewPkg) {
                        //审核模式不Toast
                        ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Theres_no_users_available_nowplease_try_again_later"))
                    }

                    //                    back();
                    back?.onError()
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                //报错
                if (!Cache.instance.reviewPkg) {
                    //非审核模式才Toast
                    ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("The_network_request_failed"))
                }

                back?.onError()
            }
        })
    }

    //挂断处理
    fun hangUp(callType: String, hanson: Int) {
        if (videoCallViewModel == null) return
        if (callInfoModel == null) return
        instance?.callInfoModel?.callType = (callType)
        videoCallViewModel?.hangUp(
            callInfoModel?.channelName,
            hanson,
            if (callInfoModel?.anchorId != null) callInfoModel?.anchorId else "",
            isCall = true
        )
    }

    fun pickUp() {
        if (videoCallViewModel == null) return
        if (callInfoModel == null) return
        videoCallViewModel?.pickUp(callInfoModel?.channelName)
    }

    fun createChannel() {
        if (videoCallViewModel == null) return
        if (callInfoModel == null) return
        videoCallViewModel!!.createChannel(
            instance!!.callInfoModel!!.anchorId,
            callInfoModel!!.callSource.toInt()
        )
    }

    companion object {
        @Volatile
        var instance: CallUtils? = null
            get() {
                if (field == null) {
                    synchronized(CallUtils::class.java) {
                        if (field == null) {
                            field = CallUtils()
                        }
                    }
                }
                return field
            }
            private set

        fun getCallViewModel(): VideoCallViewModel? {
            return instance!!.videoCallViewModel
        }

        fun getCallInfoModel(): CallInfoModel? {
            return instance!!.callInfoModel
        }
    }
}
