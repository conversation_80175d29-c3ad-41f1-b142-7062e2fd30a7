package com.juicy.common.videoCall

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.ToastUtils
import com.juicy.common.model.event.FollowParamsEvent
import com.juicy.common.model.message_event.AppOnCallBean
import com.juicy.common.networks.delegate.AddFriendInterface.addToFriendList
import com.juicy.common.networks.delegate.CancelFriendInterface.cancelFriend
import com.juicy.common.networks.delegate.GetRearCameraConfig.cameraConfig
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.GiveUserGiftInterface.giveUserGift
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.networks.delegate.OpenRearCamera.rearCameraOpen
import com.juicy.common.networks.delegate.VideoChannelCreateInterface.create
import com.juicy.common.networks.delegate.VideoHangUpInterface.hangUp
import com.juicy.common.networks.delegate.VideoJoinAndAgoraUidUpdateInterface.joinAndUpdate
import com.juicy.common.networks.delegate.VideoPickUpInterface.pickUp
import com.juicy.common.config.Cache
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.LanguageManager
import org.greenrobot.eventbus.EventBus

class VideoCallViewModel {
    var loadFollowData: MutableLiveData<Boolean> = MutableLiveData<Boolean>(false)
    var hangUpData: MutableLiveData<Boolean?> = MutableLiveData<Boolean?>(false)
    var createCallError: MutableLiveData<String?> = MutableLiveData<String?>("")
    var callResulData: MutableLiveData<com.juicy.common.model.bean.AnchorWallTagBean> = MutableLiveData<com.juicy.common.model.bean.AnchorWallTagBean>()
    var pickUpData: MutableLiveData<Boolean> = MutableLiveData<Boolean>()
    var sendGiftData: MutableLiveData<com.juicy.common.model.bean.CoinsBean?> = MutableLiveData<com.juicy.common.model.bean.CoinsBean?>()
    var openCameraData: MutableLiveData<Boolean> = MutableLiveData<Boolean>()
    var userData: MutableLiveData<com.juicy.common.model.bean.JuicyUserInfoBean?> = MutableLiveData<com.juicy.common.model.bean.JuicyUserInfoBean?>()
    var addFollowData: MutableLiveData<Boolean> = MutableLiveData<Boolean>(false)
    var deleteFollowData: MutableLiveData<Boolean> = MutableLiveData<Boolean>(false)



    val camera: Unit
        get() {
            cameraConfig(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.CameraCOnfigInfoBean>>() {
                override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.CameraCOnfigInfoBean>) {
                    super.onNext(t)
                    if (t.data != null) {
                        readCameraData.postValue(t.data)
                    } else {
                        readCameraData.postValue(null)
                    }
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                    readCameraData.postValue(null)
                }
            })
        }
    var readCameraData: MutableLiveData<com.juicy.common.model.bean.CameraCOnfigInfoBean?> = MutableLiveData<com.juicy.common.model.bean.CameraCOnfigInfoBean?>()
    var createCallData: MutableLiveData<AppOnCallBean> =  MutableLiveData<AppOnCallBean>()
    var loadNum = 0

    fun loadUserData(id: String?) {
        if (id == null) return
        loadNum++
        getUserInfo(id, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                super.onNext(t)
                if (t.data != null) {
                    userData.postValue(t.data)
                    loadFollowData.postValue(t.data?.isFriend)
                } else {
                    if (loadNum < 3) {
                        loadUserData(id)
                    }
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                if (loadNum < 3) {
                    loadUserData(id)
                }
            }
        })
    }
    fun Join(agoraUid: String?, channelName: String?) {
        if (agoraUid == null || channelName == null) return
        joinAndUpdate(agoraUid, channelName, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(t)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }

    //随机主播
    fun openCamera() {
        rearCameraOpen(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(t)
                if (t.data != null && t.data == true) {
                    ToastUtils.showShort("Successfully Open")
                    openCameraData.postValue(true)
                } else {
                    openCameraData.postValue(false)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                openCameraData.postValue(false)
            }
        })
    }
    fun createChannel(id: String?, callSource: Int) {
        if (id == null) return
        create(id, callSource, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<AppOnCallBean>>() {

            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<AppOnCallBean>) {
                super.onNext(t)
                if (t.data == null) {
                    createCallError.postValue(t.msg)
                    return
                }
                createCallData.postValue(t.data)
                Cache.instance.callingChannelName = t.data?.channelName?:""
                //如果当前不是在呼叫页和视频页，挂断
                var isPickUp = true
                val currentActivity = currentActivity as AppCompatActivity? ?: return
                if (currentActivity.javaClass.simpleName == "VideoCallActivity") {
                    isPickUp = false
                }

                if (isPickUp) {
                    //快速挂断
                    hangUp(t.data?.channelName, 1, id)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                e.printStackTrace()
                createCallError.postValue("The network request failed")
            }
        })
    }

    fun pickUp(channelName: String?) {
        if (channelName == null) return
        pickUp(channelName, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(t)
                if (t.data != null) {
                    pickUpData.postValue(t.data)
                } else {
                    if (t.code == 20010024) {
                        ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("The_user_is_on_a_call")?:"")
                    }
                    pickUpData.postValue(false)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("The_network_request_failed")?:"")
                pickUpData.postValue(false)
            }
        })
    }




    //加载主播数据
    fun loadCacheUserData(id: String?) {
        if (id == null) return
        val azBean = getCacheUserInfo(id)
        if (azBean != null) {
            userData.postValue(azBean)
            return
        }
        loadUserData(id)
    }


    fun deleteFollow(id: String) {
        cancelFriend(com.juicy.common.model.bean.FollowUserIdParamsBean(id), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(t)
                if (t.data != null && t.data == true) {
                    deleteFollowData.postValue(true)
                    EventBus.getDefault().post(FollowParamsEvent(id, false))
                    return
                }
                deleteFollowData.postValue(false)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                deleteFollowData.postValue(false)
            }
        })
    }
    fun addFollow(id: String) {
        addToFriendList(com.juicy.common.model.bean.AnchorIDBean(id), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(t)
                if (t.data != null && t.data == true) {
                    addFollowData.postValue(true)
                    EventBus.getDefault().post(FollowParamsEvent(id, true))
                    return
                }
                addFollowData.postValue(false)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                addFollowData.postValue(false)
            }
        })
    }
    fun sendGift(askGiftEventBean: com.juicy.common.model.bean.AskGiftEventBean) {
        if (Cache.instance.userInfo!!.availableCoins < askGiftEventBean.coinPrice) {
            sendGiftData.postValue(null)
            return
        }
        giveUserGift(askGiftEventBean, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.CoinsBean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.CoinsBean>) {
                super.onNext(t)
                if (t.data != null) {
                    if (t.data?.coins != null) {
                        sendGiftData.postValue(t.data)
                    }
                } else {
                    sendGiftData.postValue(null)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                sendGiftData.postValue(null)
            }
        })
    }

    fun hangUp(channelName: String?, hanson: Int, anchorId: String?,isCall:Boolean = false) {
        if (channelName == null || anchorId == null) return
        hangUp(channelName, hanson, anchorId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(t)
                //                if (booleanBaseBean.getData() != null) {
//                    hangUpData.setValue(booleanBaseBean.getData());
//                    return;
//                }
//                hangUpData.setValue(null);
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                //                hangUpData.setValue(null);
            }
        })
        if(isCall){

                hangUpData.postValue(null)

        }else{

                hangUpData.postValue(true)

        }
    }


}
