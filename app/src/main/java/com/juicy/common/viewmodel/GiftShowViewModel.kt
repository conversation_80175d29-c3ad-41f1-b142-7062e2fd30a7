package com.juicy.common.viewmodel

import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.blankj.utilcode.util.ToastUtils
import com.juicy.common.model.event.CoinsNumChangeEvent
import com.juicy.common.networks.delegate.GetCoinCountInterface.getCoin
import com.juicy.common.networks.delegate.GiftListInterface.giftList
import com.juicy.common.networks.delegate.GiveUserGiftInterface.giveUserGift
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.utils.AppActivityUtil
import com.juicy.common.utils.AppUtil
import com.juicy.common.utils.LanguageManager
import org.greenrobot.eventbus.EventBus

class GiftShowViewModel : ViewModel() {
    var coinsData: MutableLiveData<String> = MutableLiveData<String>()
    var giftData: MutableLiveData<List<com.juicy.common.model.bean.OrderInfoBean>> = MutableLiveData<List<com.juicy.common.model.bean.OrderInfoBean>>()
    var sendGiftData: MutableLiveData<com.juicy.common.model.bean.CoinsBean> = MutableLiveData<com.juicy.common.model.bean.CoinsBean>()




    fun updateCoinsData() {
        getCoin(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>() {
            override fun onNext(aeBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>) {
                super.onNext(aeBaseBean)
                if (aeBaseBean.data != null) {
                    coinsData.postValue(aeBaseBean.data?.availableCoins?.toInt().toString())
                    return
                }
                coinsData.postValue(null)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }
    fun updateGiftData() {
        giftList(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<ArrayList<com.juicy.common.model.bean.OrderInfoBean>>>() {
            override fun onNext(arrayListBaseBean: com.juicy.common.model.bean.NetResponseBean<ArrayList<com.juicy.common.model.bean.OrderInfoBean>>) {
                super.onNext(arrayListBaseBean)
                if (arrayListBaseBean.data != null) {
                    Cache.instance.giftList = arrayListBaseBean.data
                    giftData.postValue(arrayListBaseBean.data)
                    return
                }
                giftData.postValue(null)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                giftData.postValue(null)
            }
        })
    }
    private var lastGiftFinish = true
    fun sendGift(askGiftEventBean: com.juicy.common.model.bean.AskGiftEventBean) {
        if (AppUtil.checkNetworkToast()) return
        if (Cache.instance?.userInfo?.availableCoins?:0 < askGiftEventBean.coinPrice) {
            sendGiftData!!.value = null
            return
        }

        if (!lastGiftFinish) {
            ToastUtils.showShort(
                LanguageManager.instance?.getLocalTranslate("Gift_giving_in_progress_please_wait_a_moment") ?: ""
            )
            return
        }
        lastGiftFinish = false

        giveUserGift(askGiftEventBean, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.CoinsBean>>() {
            override fun onNext(giveResult: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.CoinsBean>) {
                super.onNext(giveResult)
                lastGiftFinish = true
                Cache.instance.userInfo?.availableCoins =
                    Cache.instance?.userInfo?.availableCoins?:0 - askGiftEventBean.coinPrice
                updateCoinsData()
                EventBus.getDefault().post(CoinsNumChangeEvent())
                if (giveResult.data != null) {
                    if (giveResult.data!!.coins != null) {
                        sendGiftData!!.value = giveResult.data
                    }
                } else {
                    sendGiftData!!.setValue(null)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                lastGiftFinish = true
                //                sendGiftData.setValue(null);

                //送礼失败，刷新金币。稍后再试，是否在聊天界面和通话界面
                //判断页面路由
                var isCanshow = false
                updateCoinsData()
                val currentActivity = AppActivityUtil.currentActivity as AppCompatActivity
                if (currentActivity == null || currentActivity.javaClass.simpleName == null) {
                    return
                }

                //当前是否在在私聊页
                if (currentActivity.javaClass.simpleName == "VideoCallActivity") {
                    isCanshow = true
                }else if (currentActivity.javaClass.simpleName == "MessageActivity") {
                    isCanshow = true
                }

                if (isCanshow) {
                    ToastUtils.showShort(
                        LanguageManager.instance?.getLocalTranslate("Gift_giving_failed_please_try_again_later")?:""
                    )
                }
            }
        })
    }
}
