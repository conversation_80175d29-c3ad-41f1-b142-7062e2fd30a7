package com.juicy.common.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.juicy.common.model.event.BlockParamsEvent
import com.juicy.common.model.event.FollowParamsEvent
import com.juicy.common.networks.delegate.AddFriendInterface.addToFriendList
import com.juicy.common.networks.delegate.CancelBlockInterface.cancelBlock
import com.juicy.common.networks.delegate.CancelFriendInterface.cancelFriend
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.InsertRecordInterface.insertRecord
import com.juicy.common.networks.delegate.ObservableCallBack
import io.reactivex.rxjava3.disposables.Disposable
import io.rong.imlib.RongIMClient
import org.greenrobot.eventbus.EventBus

class BlockViewModel : ViewModel() {
    private var addBlockData: MutableLiveData<Boolean>? = null
    private var deleteBlockData: MutableLiveData<Boolean>? = null
    private var addFollowData: MutableLiveData<Boolean>? = null

    private var followChangeData: MutableLiveData<Boolean>? = null
    private var deleteFollowData: MutableLiveData<Boolean>? = null
    private var blockListData: MutableLiveData<Boolean>? = null


    fun getBlockListData(): MutableLiveData<Boolean> {
        if (blockListData == null) {
            blockListData = MutableLiveData()
        }
        return blockListData!!
    }

    fun getFollowChangeData(): MutableLiveData<Boolean> {
        if (followChangeData == null) {
            followChangeData = MutableLiveData()
        }
        return followChangeData!!
    }

    fun getAddBlockData(): MutableLiveData<Boolean> {
        if (addBlockData == null) {
            addBlockData = MutableLiveData()
        }
        return addBlockData!!
    }

    fun getAddFollowData(): MutableLiveData<Boolean> {
        if (addFollowData == null) {
            addFollowData = MutableLiveData()
        }
        return addFollowData!!
    }

    fun getDeleteBlockData(): MutableLiveData<Boolean> {
        if (deleteBlockData == null) {
            deleteBlockData = MutableLiveData()
        }

        return deleteBlockData!!
    }

    fun getDeleteFollowData(): MutableLiveData<Boolean> {
        if (deleteFollowData == null) {
            deleteFollowData = MutableLiveData()
        }
        return deleteFollowData!!
    }

    fun getBlockList(anchorId: String) {
        val user = getCacheUserInfo(anchorId)
        if (user != null) {
            blockListData!!.value = user.block
            return
        }

        loadUserInfo(anchorId)
    }

    fun loadUserInfo(anchorId: String) {
        getUserInfo(anchorId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(userBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                super.onNext(userBean)
                if (userBean.data != null) {
                    blockListData!!.value = userBean.data!!.block
                    followChangeData!!.value = userBean.data!!.isFriend
                    EventBus.getDefault().post(FollowParamsEvent(anchorId, userBean.data!!.isFriend))
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }

            override fun onComplete() {
                super.onComplete()
            }
        })
    }

    fun addFollow(id: String) {
        addToFriendList(com.juicy.common.model.bean.AnchorIDBean(id), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(isFollowData: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(isFollowData)
                if (isFollowData.isOk) {
                    addFollowData!!.value = true
                    EventBus.getDefault().post(FollowParamsEvent(id, true))
                    return
                }
                addFollowData!!.value = false
                loadUserInfo(id)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                addFollowData!!.value = false
            }
        })
    }

    fun deleteFollow(id: String) {
        cancelFriend(com.juicy.common.model.bean.FollowUserIdParamsBean(id), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(isnFollowData: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(isnFollowData)
                if (isnFollowData.isOk) {
                    deleteFollowData!!.value = true
                    EventBus.getDefault().post(FollowParamsEvent(id, false))
                    return
                }
                deleteFollowData!!.value = false
                loadUserInfo(id)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                deleteFollowData!!.value = false
            }
        })
    }

    fun deleteBlockData(blockInfo: com.juicy.common.model.bean.AnchorBlockBean, userId: String) {
        cancelBlock(blockInfo, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(booleanBaseBean: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(booleanBaseBean)
                if (booleanBaseBean.isOk) {
                    deleteBlockData!!.setValue(true)
                    //取消拉黑
                    RongIMClient.getInstance().removeFromBlacklist(userId, null)
                    EventBus.getDefault().post(BlockParamsEvent(userId, false))
                } else {
                    deleteBlockData!!.setValue(false)
                    loadUserInfo(userId)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                deleteBlockData!!.value = false
            }
        })
    }


    fun addBlock(blockInfo: com.juicy.common.model.bean.AnchorHBean, userId: String) {
        insertRecord(blockInfo, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(booleanBaseBean: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(booleanBaseBean)
                if (booleanBaseBean.isOk) {
                    addBlockData!!.setValue(true)
                    if (blockInfo.complainCategory == "Block") {
                        RongIMClient.getInstance().addToBlacklist(userId, null)
                        EventBus.getDefault().post(BlockParamsEvent(userId, true))
                    }
                } else {
                    addBlockData!!.setValue(false)
                    loadUserInfo(userId)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                addBlockData!!.value = false
            }
        })
    }
}
