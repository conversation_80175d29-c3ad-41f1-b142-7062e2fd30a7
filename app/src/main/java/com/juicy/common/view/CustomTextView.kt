package com.juicy.common.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView


class CustomTextView : AppCompatTextView {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )
    private var strokeWidth = 0.6f // 默认描边宽度
    private var strokeColor = Color.parseColor("#FF222222") // 默认描边颜色




    fun setStrokeColor(color: Int) {
        strokeColor = color
        invalidate() // 重新绘制
    }


    override fun onDraw(canvas: Canvas) {
        val text = text.toString()
        // 1. 绘制描边
        val paint = paint
        paint.strokeWidth = strokeWidth // 设置描边宽度
        paint.color = strokeColor // 设置描边颜色
        paint.style = Paint.Style.STROKE // 设置描边模式


        canvas.drawText(text, paddingLeft.toFloat(), (height - paddingBottom).toFloat(), paint)

        // 2. 绘制文字
        paint.style = Paint.Style.FILL // 恢复填充模式
        paint.color = currentTextColor // 恢复文字颜色
        super.onDraw(canvas)
    }

    fun setStrokeWidth(width: Float) {
        strokeWidth = width
        invalidate()
    }
}