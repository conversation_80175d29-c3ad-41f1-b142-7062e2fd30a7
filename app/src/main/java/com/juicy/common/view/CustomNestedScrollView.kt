package com.juicy.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.core.view.ViewCompat
import androidx.core.widget.NestedScrollView


class CustomNestedScrollView : NestedScrollView {
    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    override fun onStartNestedScroll(child: View, target: View, axes: Int): Bo<PERSON>an {
        return (axes and ViewCompat.SCROLL_AXIS_VERTICAL) != 0
    }
}
