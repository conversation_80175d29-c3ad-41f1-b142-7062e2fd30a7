package com.juicy.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.modules.base.adapter.BannerAdapter
import com.juicy.app.databinding.FragmentChartBinding
import com.juicy.app.modules.base.dialog.DerivativeDialog
import com.juicy.app.modules.base.activity.WebViewActivity
import com.juicy.common.networks.delegate.GetBannerInterface.GetBannerInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.AppUtil.checkNetworkToast
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.LanguageManager.Companion.instance
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.youth.banner.config.IndicatorConfig
import com.youth.banner.indicator.RectangleIndicator
import java.util.UUID

class MdBannerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    LinearLayout(context, attrs, defStyleAttr) {
    private lateinit var binding: FragmentChartBinding
    private var bannerList: MutableList<com.juicy.common.model.bean.BannerItemBean>? = mutableListOf()

    private var bannerAdapter: BannerAdapter? = null
    var isShowBanner: Boolean = false

    init {
        init(context)
    }

    private fun init(context: Context) {
        binding = FragmentChartBinding.inflate(
            LayoutInflater.from(context),
            this, true
        )
        val indicator = RectangleIndicator(getContext())
        binding.headerAbove.setIndicator(indicator)
        binding.headerAbove.setIntercept(true)

        binding.headerAbove.setLoopTime(5000)
        //        if (Cache.instance?.reviewPkg) {
//            setVisibility(View.GONE);
//        }
        if (instance?.isLanguageForce == true) {
            binding.headerAbove.layoutDirection = LAYOUT_DIRECTION_RTL
            indicator.scaleX = -1f
        }
        binding.headerAbove.setIndicatorGravity(IndicatorConfig.Direction.CENTER)
        bannerAdapter = BannerAdapter(bannerList, object : BannerAdapter.CallBack {
            override fun onCallBack(bannerData: com.juicy.common.model.bean.BannerItemBean, position: Int) {
                if (checkNetworkToast()) return
                if (isFastClick) return
                val currentActivity =
                    currentActivity as AppCompatActivity?
                var intent: Intent
                when (bannerData.type) {
                    "1" -> //                        String joinUrl;
                        //                        Intent intent = new Intent(currentActivity, MyWebActivity.class);
                        //
                        //                        if (SpSaveUtil.getStringValue(SpKeyPool.TPP_OPEN_TYPE, "").equals("0")) {
                        //                            joinUrl = bannerData.getJumpUrl() + "&source=" + Constant.GlobalTppSource.BANNER + "&uuid=" + UUID.randomUUID().toString();
                        //                            Log.v(Constant.GlobalTppEvent.CLICK, "source：" + Constant.GlobalTppSource.BANNER + "open：webview");
                        //                            intent.putExtra("urlPath", joinUrl);
                        //                            intent.putExtra("orderNo", "");
                        //                            intent.putExtra("mResultPage", "CoreActivity");
                        //                            currentActivity.startActivity(intent);
                        //
                        //                        } else if (SpSaveUtil.getStringValue(SpKeyPool.TPP_OPEN_TYPE, "").equals("1")) {
                        //                            //跳转到外部浏览器
                        //                            joinUrl = bannerData.getJumpUrl() + "&source=" + Constant.GlobalTppSource.BANNER + "&uuid=" + UUID.randomUUID().toString();
                        //                            Uri uri = Uri.parse(joinUrl);
                        //                            Log.v(Constant.GlobalTppEvent.CLICK, "source：" + Constant.GlobalTppSource.BANNER + "open：browser");
                        //                            intent = new Intent(Intent.ACTION_VIEW, uri);
                        //                            currentActivity.startActivity(intent);
                        //                        }
                        if (bannerData.bizType == "1") {
                            val joinUrl: String
                            intent =
                                Intent(currentActivity, WebViewActivity::class.java)

                            if (getStringValue(SpKeyPool.TPP_OPEN_TYPE, "") == "0") {
                                joinUrl =
                                    bannerData.jumpUrl + "&source=" + Constant.GlobalTppSource.BANNER + "&uuid=" + UUID.randomUUID()
                                        .toString()
                                Log.v(
                                    Constant.GlobalTppEvent.CLICK,
                                    "source：" + Constant.GlobalTppSource.BANNER + "open：webview"
                                )
                                intent.putExtra("urlPath", joinUrl)
                                intent.putExtra("orderNo", "")
                                intent.putExtra("mResultPage", "CoreActivity")
                                currentActivity?.startActivity(intent)
                            } else if (getStringValue(
                                    SpKeyPool.TPP_OPEN_TYPE,
                                    ""
                                ) == "1"
                            ) {
                                //跳转到外部浏览器
                                joinUrl =
                                    bannerData.jumpUrl + "&source=" + Constant.GlobalTppSource.BANNER + "&uuid=" + UUID.randomUUID()
                                        .toString()
                                val uri = Uri.parse(joinUrl)
                                Log.v(
                                    Constant.GlobalTppEvent.CLICK,
                                    "source：" + Constant.GlobalTppSource.BANNER + "open：browser"
                                )
                                intent =
                                    Intent(Intent.ACTION_VIEW, uri)
                                currentActivity?.startActivity(intent)
                            }
                        } else {
                            //应用内打开
                            intent =
                                Intent(currentActivity, WebViewActivity::class.java)
                            intent.putExtra("urlPath", bannerData.jumpUrl)

                            //      intent.putExtra("urlPath", "https://new-test-h5.blinku.me/H5-app/index.html?entry=banner&v=2&&pkgName=com.vv.testvivas&ver=1.0.5&token=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxNjIzOTU2MTY2NDAzMDMxMDQwIiwidXNlcl90eXBlIjoxLCJleHAiOjQ4MzkwNDI5NTcsImNyZWF0ZWQiOjE2ODMzNjkzNTczNTl9.cg-gI7EuAumQGbptPLR7onsAkNY5ONnu_2uXlJ-G4rguhFN9xICoPHYKRrtTTVTrmOwh90nWhgORAmkxG0iCMQ");
                            intent.putExtra("orderNo", "")
                            intent.putExtra("mResultPage", "CoreActivity")
                            currentActivity?.startActivity(intent)
                        }

                    "3" -> if (null != Cache.instance.guide) {
                        val conductivityDialog = DerivativeDialog()
                        if (isDestroy(currentActivity)) return
                        currentActivity?.let {
                            conductivityDialog.show(it.supportFragmentManager, "")
                        }

                    }

                    else -> if (bannerData.type == "4") {
                        if (bannerData.bizType == "1") {
                            val joinUrl: String
                            intent =
                                Intent(currentActivity, WebViewActivity::class.java)

                            if (getStringValue(SpKeyPool.TPP_OPEN_TYPE, "") == "0") {
                                joinUrl =
                                    bannerData.jumpUrl + "&source=" + Constant.GlobalTppSource.BANNER + "&uuid=" + UUID.randomUUID()
                                        .toString()
                                Log.v(
                                    Constant.GlobalTppEvent.CLICK,
                                    "source：" + Constant.GlobalTppSource.BANNER + "open：webview"
                                )
                                intent.putExtra("urlPath", joinUrl)
                                intent.putExtra("orderNo", "")
                                intent.putExtra("mResultPage", "CoreActivity")
                                currentActivity?.startActivity(intent)
                            } else if (getStringValue(
                                    SpKeyPool.TPP_OPEN_TYPE,
                                    ""
                                ) == "1"
                            ) {
                                //跳转到外部浏览器
                                joinUrl =
                                    bannerData.jumpUrl + "&source=" + Constant.GlobalTppSource.BANNER + "&uuid=" + UUID.randomUUID()
                                        .toString()
                                val uri = Uri.parse(joinUrl)
                                Log.v(
                                    Constant.GlobalTppEvent.CLICK,
                                    "source：" + Constant.GlobalTppSource.BANNER + "open：browser"
                                )
                                intent =
                                    Intent(Intent.ACTION_VIEW, uri)
                                currentActivity?.startActivity(intent)
                            }
                        } else {
                            try {
                                val uri = Uri.parse(bannerData.jumpUrl)
                                intent =
                                    Intent(Intent.ACTION_VIEW, uri)
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                currentActivity?.startActivity(intent)
                            } catch (ex: Exception) {
                                // 如果还是失败，可以提示用户
                                ToastUtils.showShort(
                                    instance?.getLocalTranslate(
                                        "Unable to open link"
                                    )
                                )
                            }
                        }
                    }
                }
            }

        })
        binding.headerAbove.setAdapter(bannerAdapter)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun loadBanner() {
        isShowBanner = true

        if (Cache.instance.reviewPkg) {
            Cache.instance.bannerList = mutableListOf()
            val bannerItemBean = com.juicy.common.model.bean.BannerItemBean()
            bannerItemBean.bizType = "-10024"
            bannerItemBean.jumpUrl = "https://leaf.juicyhubl.com/Community-Guidelines.html"
            bannerItemBean.type = "1"
            Cache.instance.bannerList?.add(bannerItemBean)
            bannerList = Cache.instance.bannerList
            bannerAdapter?.setDatas(bannerList?.toList()?: listOf<com.juicy.common.model.bean.BannerItemBean>())
            bannerAdapter?.notifyDataSetChanged()
            visibility = VISIBLE
            return
        } else {
            if (Cache.instance?.bannerList != null && !Cache.instance?.bannerList.isNullOrEmpty()) {
                if (Cache.instance?.bannerList?.get(0)?.bizType == "-10024") {
                    Cache.instance?.bannerList?.clear()
                }
            }
        }

        if (Cache.instance?.bannerList != null && !Cache.instance?.bannerList.isNullOrEmpty()) {
            bannerList = Cache.instance?.bannerList
            bannerAdapter?.setDatas(bannerList?.toList()?: listOf<com.juicy.common.model.bean.BannerItemBean>())
            bannerAdapter?.notifyDataSetChanged()
            if (Cache.instance?.reviewPkg == false) {
                visibility = VISIBLE
            }
        }
        GetBannerInfo(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.BannerItemBean>>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.BannerItemBean>>) {
                super.onNext(t)
                if (t != null) {
                    if (!t.data.isNullOrEmpty()) {
                        bannerList = t.data!!.toMutableList()
                        Cache.instance?.bannerList = bannerList
                        bannerAdapter?.setDatas(bannerList?.toList())
                        bannerAdapter?.notifyDataSetChanged()
                        binding.headerAbove.setStartPosition(0)
                        if (Cache.instance?.reviewPkg == false) {
                            visibility = VISIBLE
                        }
                    } else {
                        visibility = GONE
                    }
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }
}