package com.juicy.common.config

object SpKeyPool {
    const val RESULT: String = "install_result"
    const val REFERRER_URL: String = "install_referrer_url"
    const val INSTALL_VERSION: String = "install_ver"
    const val REFERRER_CLICK_TIME: String = "install_referrer_click_time"
    const val REFERRER_CLICK_SERVER_TIME: String = "install_referrer_click_server_time"
    const val APP_INSTALL_TIME: String = "install_app_install_time"
    const val APP_INSTALL_SERVER_TIME: String = "install_app_install_server_time"
    const val INSTANT_EXPERIENCE_LAUNCHED: String = "instant_exp_launched"
    const val LANGUAGE: String = "my_language"
    const val CHANGE_LANGUAGE: String = "select_language"
    const val LANGUAGE_COUNTRY: String = "language_country"
    const val LANGUAGE_NAME: String = "language_name"
    const val LANGUAGE_LAST: String = "language_last"

    //保存token的key（登录接口请求返回的token，后端下发）
    const val APP_TOKEN_KEY: String = "APP_TOKEN_KEY"
    const val RIGISTER_LOGIN: String = "RIGISTER_LOGIN"

    //保存用户ID的key
    const val USER_ID_DEVICE: String = "USER_ID_DEVICE"

    //保存语言code
    const val LANGUAGE_CODE: String = "LANGUAGE_CODE"

    //配置信息的ver字段
    const val APP_CONFIG_VER: String = "APP_CONFIG_VER"

    //记录条款弹窗是否需要弹出
    const val PRIVACY_IS_SHOW: String = "PRIVACY_IS_SHOW"

    const val MY_RTC_KEY: String = "my_rtc_key"

    const val MY_RECHARGE_LOG: String = "my_recharge_log"

    const val MY_SA_NUM: String = "my_sa_num"

    /**
     * 翻译类型
     */
    //谷歌
    const val TRANSLATE_TYPE_GOOGLE: String = "google_translation_key"

    //微软
    const val TRANSLATE_TYPE_MICROSOFT: String = "microsoft_translation_key"

    //翻译类型的key（存translate_type-->int型）
    const val TRANSLATE_TYPE_KEY: String = "translate_type_key"


    //融云Key
    const val RCK_KEY: String = "rck_key"
    const val LAST_RONG_KEY: String = "lastRongCloudAppKey"

    const val RC_TYPE: String = "rc_area_code"

    const val RC_APP_KEY: String = "rc_app_key"

    const val ENCRYPT_KEY: String = "encrypt_key"

    //Im充值卡片打开方式
    const val TPP_OPEN_TYPE: String = "tpp_open_type"
    const val INVITATION_ID: String = "InvitationId"

    // 声网Key
    const val RTCK_KEY: String = "RTCK_KEY"

    const val CURRECTCOUNTRY: String = "CURRECTCOUNTRY"

    const val SUBSCRIPTIONDATE: String = "SUBSCRIPTIONDATE"

    const val OPEN_CAMERA: String = "open_camera"

    const val HACK_REVENUE_FACTOR_FB: String = "hack_revenue_factor_FB"
    const val HACK_REVENUE_FACTOR_AF: String = "hack_revenue_factor_AF"

    const val IS_AUTO_TRANSLATE: String = "is_auto_translate"

    const val HAS_EVALUATE: String = "has_evaluate"

    const val DIRECTION: String = "direction"

    const val IS_OPENED: String = "is_opened"
}
