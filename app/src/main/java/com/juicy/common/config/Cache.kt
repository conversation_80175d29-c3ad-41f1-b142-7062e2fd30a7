package com.juicy.common.config

import android.annotation.SuppressLint
import android.util.Log
import com.juicy.app.JuicyApplication
import com.juicy.app.modules.base.dialog.FullShoppingDialog
import com.juicy.app.modules.base.activity.WebViewActivity
import com.juicy.common.networks.Service
import com.juicy.common.networks.cache.NetworkCacheManager
import com.juicy.common.model.bean.ExtraBean.ChannelInfoBean
import com.juicy.common.model.bean.StrongGuildBean
import com.juicy.common.model.bean.OssPolicyINfoBean
import com.juicy.common.model.event.StrategyChangeEvent
import com.juicy.common.model.result.LoginUserInfoResult
import com.juicy.common.model.result.UserStratResult
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.networks.delegate.ConfigStrategyInterface.getStrategy
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.utils.DateMetaUtil.getDate
import com.juicy.common.utils.LogBeanFactory
import com.juicy.common.utils.SpSaveUtil.getStringValue
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import io.rong.imlib.model.InitOption
import org.greenrobot.eventbus.EventBus
import java.io.UnsupportedEncodingException
import java.net.URLEncoder
import java.text.SimpleDateFormat
import java.util.Date
import java.util.regex.Pattern

class Cache private constructor() {
    var thrPayLogMap: HashMap<String, com.juicy.common.model.bean.ActivityInfoBean> = HashMap() // 三方支付商品跳转回调FB打点
    @JvmField
    var coinGoods: List<com.juicy.common.model.bean.ActivityInfoBean>? = null
    @JvmField
    var subscribeGood: com.juicy.common.model.bean.ActivityInfoBean? = null //订阅商品
    @JvmField
    var buildVersionName: String? = null
    @JvmField
    var token: String? = null
    @JvmField
    var appName: String? = null
    @JvmField
    var loginUserInfoResult: LoginUserInfoResult? = null

    @JvmField
    var isFirstRegister: Boolean = false
    @JvmField
    var AppsFlyerData: Map<String, String?> = HashMap() //归因数据本地保存
    @JvmField
    var ossPolicy: OssPolicyINfoBean? = null //oss信息
    @JvmField
    var userInfo: com.juicy.common.model.bean.JuicyUserInfoBean? = null //用户信息
    @JvmField
    var isLogin: Boolean = false //是否登录

    @JvmField
    var fullShoppingDialog: FullShoppingDialog? = null
    @JvmField
    var userStratResult: UserStratResult? = null //策略
    @JvmField
    var webViewActivity: WebViewActivity? = null
    @JvmField
    var presented: com.juicy.common.model.bean.ExpCoinsBean? = null //新用户注册商品信息
    @JvmField
    var guide: StrongGuildBean? = null
    @JvmField
    var bannerList: MutableList<com.juicy.common.model.bean.BannerItemBean>? = null

    //是否刚进来
    @JvmField
    var isFirstOpen: Boolean = true
    var unConsumerCheckedTime: Long = 0L
    var isEventPromotionWindow: Boolean = false //判断是不是促销商品活动促销商品
    @JvmField
    var reviewPkg: Boolean = false //是否审核模式
    @JvmField
    var unreadCount: Int = 0 //未读消息数量

    var subscribeGoods: List<com.juicy.common.model.bean.ActivityInfoBean>? = null //订阅商品列表

    //配置信息
    var cacheAppConfig: com.juicy.common.model.bean.JuicyConfigBean? = null

    //翻译有关
    @JvmField
    var translateType: String? = null // 翻译类型 1：微软 2：谷歌
    @JvmField
    var googleTranslationKey: String? = null // 微软翻译： glt
    @JvmField
    var microsoftTranslationKey: String? = null // 微软翻译： microsoft_translation_key

    @JvmField
    var hackRevenueFactorFB: String? = null // FB购买事件折扣字段
    @JvmField
    var hackRevenueFactorAF: String? = null // AF购买事件折扣字段

    @JvmField
    var appconfig: com.juicy.common.model.bean.JuicyConfigBean? = null

    //融云的KEY
    @JvmField
    var rongRck: String? = null
    @JvmField
    var rtck: String? = null

    @JvmField
    var rctype: String? = null

    @JvmField
    var rcAppKey: String? = null //新的融云key
    @JvmField
    var areaCode: InitOption.AreaCode? = null
    @JvmField
    var tppOpenType: String? = null //IM充值卡片

    //秘钥
    @JvmField
    var encryptKey: String? = null

    @JvmField
    var currentFirstTab: Int = 0
    @JvmField
    var currentSecondTab: Int = 0

    var registerGood: com.juicy.common.model.bean.ExpCoinsBean? = null //注册奖励数据
    @JvmField
    var userPromotionGood: com.juicy.common.model.bean.ActivityInfoBean? = null //新用户促销商品
    @JvmField
    var userPromotionCreateTime: Long = 0

    @JvmField
    var salePromotionGood: com.juicy.common.model.bean.ActivityInfoBean? = null //活动促销商品
    @JvmField
    var salePromotionCreateTime: Long = 0

    @JvmField
    var allHomeRequest: Boolean = false //注册奖励数据
    @JvmField
    var presentedHasShow: Boolean = false //用于弹框顺序
    @JvmField
    var guideHasShow: Boolean = false //用于弹框顺序
    @JvmField
    var userPromotionHasShow: Boolean = false //用于弹框顺序
    @JvmField
    var saleActivityHasShow: Boolean = false //用于弹框顺序

    @JvmField
    var userPromotionResetShow: Boolean = true //用于自动弹框
    @JvmField
    var saleActivityResetShow: Boolean = true //用于自动弹框

    @JvmField
    var callingChannelName: String = "" //用于挂断等防抖
    var isShowSubcribe: Boolean? = null //是否显示订阅弹窗
    @JvmField
    var isCurrentHomePage: Boolean = false
    @JvmField
    var giftList: List<com.juicy.common.model.bean.OrderInfoBean>? = null
    var OssPolicy: OssPolicyINfoBean? = null
    @JvmField
    var thirdPayGRLogMap: HashMap<String, com.juicy.common.model.bean.ActivityInfoBean> = HashMap()

    @JvmField
    var isMoveLeft: Boolean = false

    //记录当前CoreActivity选中的下标
    @JvmField
    var currentIndex: Int = 0

    //记录商品市场价格
    var storePriceMap: MutableMap<String, String> = mutableMapOf()

    //缓存支付渠道，每次弹框调用
    var lastPayChannelModel: com.juicy.common.model.bean.ExtraBean? = null
    var payChannelList: List<ChannelInfoBean>? = null

    //当前是否已经登录 Get 方法
    fun isLogin(): Boolean {
        return !getStringValue(SpKeyPool.APP_TOKEN_KEY, "")!!.isEmpty()
    }

    //获取Application 实例
    @JvmField
    var networkCacheManager: NetworkCacheManager =
        NetworkCacheManager(JuicyApplication.juicyApplication!!.applicationContext, "networkCache")


    //数据为空则返回空字符串
    fun IsNull(key: String): String? {
        if (null != AppsFlyerData && null != AppsFlyerData[key]) {
            if (checkcountname(AppsFlyerData[key]!!)) {
                try {
                    return URLEncoder.encode(AppsFlyerData[key], "utf-8")
                } catch (e: UnsupportedEncodingException) {
                    e.printStackTrace()
                    return ""
                }
            } else {
                return AppsFlyerData[key]
            }
        } else {
            return ""
        }
    }

    fun checkcountname(countname: String): Boolean {
        val p = Pattern.compile("[\u4e00-\u9fa5]")
        val m = p.matcher(countname)
        return m.find()
    }

    fun getShowSubcribe(): Boolean {
        var isShowSubcr = false
        val currentDate = Date()
        // 定义日期格式
        val dateFormat = SimpleDateFormat("yyyy-MM-dd")
        // 将当前时间转换为指定格式的字符串
        val dateString = dateFormat.format(currentDate)

        if (instance.isFirstRegister) {
            isShowSubcr = false
        } else {
            if (instance.userInfo != null && instance.userInfo!!.vip) {
                val vipdateString =
                    getDate("yyyy-MM-dd", instance.userInfo!!.vipExpiryTime.toLong())
                isShowSubcr = vipdateString == dateString
            } else {
                isShowSubcr = true
            }
        }

        val subcriOldDate = getStringValue(SpKeyPool.SUBSCRIPTIONDATE, "")

        isShowSubcr = isShowSubcr && dateString != subcriOldDate

        return isShowSubcr
    }

    val netStrategy: Unit
        get() {
            getStrategy(object :
                ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<UserStratResult>>() {
                override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<UserStratResult>) {
                    if (azBaseBean.isOk && azBaseBean.data != null) {
                        instance.userStratResult =
                            azBaseBean.data

                        //判读是否审核
                        if(instance.userStratResult!!.isReviewPkg ?: true){
                            //修改主播墙
                            val broadcasterWallTagList =
                                (instance.userStratResult?.broadcasterWallTagList ?: listOf()).toMutableList()
                            if(broadcasterWallTagList.isNotEmpty()){
                                //如果
                                broadcasterWallTagList[0].showName = "New"
                            }else{
                                //创建Item
                                val anchorTagItemBean =
                                    com.juicy.common.model.bean.AnchorTagItemBean()
                                anchorTagItemBean.showName = "New"
                                anchorTagItemBean.tagName = "Popular"
                                anchorTagItemBean.subTagList = null
                                broadcasterWallTagList +=  anchorTagItemBean
                            }
                            if(broadcasterWallTagList.size > 1){
                                broadcasterWallTagList[1].showName = "Hot"
                            }else{
                                val anchorTagItemBean =
                                    com.juicy.common.model.bean.AnchorTagItemBean()
                                anchorTagItemBean.showName = "Hot"
                                anchorTagItemBean.tagName = "Popular"
                                anchorTagItemBean.subTagList = null
                                broadcasterWallTagList +=  anchorTagItemBean
                            }
                            if(broadcasterWallTagList.size > 2){
                                broadcasterWallTagList[2].showName = "Followed"
                            }else{
                                val anchorTagItemBean =
                                    com.juicy.common.model.bean.AnchorTagItemBean()
                                anchorTagItemBean.showName = "Followed"
                                anchorTagItemBean.tagName = "Followed"
                                anchorTagItemBean.subTagList = null
                                broadcasterWallTagList +=  anchorTagItemBean
                            }
                            instance.userStratResult?.broadcasterWallTagList = broadcasterWallTagList
                        }else{
                            //遍历 broadcasterWallTagList 修改 showName = tagName
                            instance.userStratResult?.broadcasterWallTagList?.forEach {
                                it.showName = it.tagName
                            }
                        }

                        EventBus.getDefault().post(StrategyChangeEvent())
                    } else if (Constant.TOKEN_EXPIRE_CODES.contains(
                            azBaseBean.code
                        )
                    ) {
                    }
                }

                override fun onError(e: Throwable) {
                    Log.e(LogBeanFactory.TAG, "user level error" + e.message)
                }
            })
        }

    val payChannle: Unit
        @SuppressLint("CheckResult")
        get() {
            RetrofitManage.instance
                .create(Service::class.java)
                .payChannel()
                .subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe({ bpBaseBean ->
                    if (null != bpBaseBean) {
                        val listData =
                            bpBaseBean.data!!.channelList
                        payChannelList = listData
                        lastPayChannelModel = bpBaseBean.data
                    } else {
                        payChannelList = ArrayList()
                    }
                }, { })
        }


    companion object {
        private var cache: Cache? = null
        @JvmStatic
        val instance: Cache
            get() {
                if (cache == null) {
                    cache = Cache()
                }
                return cache!!
            }
    }
}
