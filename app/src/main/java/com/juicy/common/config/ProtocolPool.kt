package com.juicy.common.config

import android.os.Build
import com.juicy.app.JuicyApplication
import com.juicy.common.utils.AppUtil.gid
import com.juicy.common.utils.AppUtil.pkgName
import com.juicy.common.utils.ApplicationUtil
import com.juicy.common.utils.LanguageTool.defaultLanguageCodeFunc

object ProtocolPool {
    val commonHeaderWithToken: HashMap<String, String>
        get() {
            val map =
                HashMap<String, String>()
            map["ver"] = Cache.Companion.instance.buildVersionName?:""
            map["device-id"] = ApplicationUtil.getAndroidId(JuicyApplication.juicyApplication)?:""
            map["utm-source"] = ""
            map["model"] = Build.MODEL
            map["lang"] = defaultLanguageCodeFunc
            map["sys_lan"] = defaultLanguageCodeFunc
            map["is_anchor"] = "false"
            map["pkg"] = pkgName
            map["platform"] = "Android"
            map["Authorization"] = "Bearer" + Cache.Companion.instance.token
            map["google_ad_id"] = gid?:""
            return map
        }
}
