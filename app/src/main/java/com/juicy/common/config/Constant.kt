package com.juicy.common.config

import com.juicy.app.JuicyApplication
import com.juicy.app.R
import com.juicy.common.utils.AppMessageUtil
import com.juicy.common.utils.AppMessageUtil.appVersionCode
import com.juicy.common.utils.AppMessageUtil.appVersionName
import java.util.Collections

object Constant {
    const val CALLING_USER_IS_BUSY_CODE: Int = 20010024
    const val VIDEO_POSITION: String = "video_position"
    const val STATUS: String = "status"
    const val USER: String = "user"
    const val USER_CONTENT: String = "user_content"
    const val LATER_TIME: Long = (3 * 1000).toLong()
    const val OAUTH_TYPE: Int = 4

    @JvmField
    val TOKEN_EXPIRE_CODES: Set<Int>

    init {
        val fruits: MutableSet<Int> = HashSet()
        // {10010003,10010304,100103,10010303,10010302,10010301};
        fruits.add(10010003)
        fruits.add(10010304)
        fruits.add(100103)
        fruits.add(10010303)
        fruits.add(10010302)
        fruits.add(10010301)
        TOKEN_EXPIRE_CODES = Collections.unmodifiableSet(fruits)
    }

    const val SEX_MAN: Int = 1 //性别男
    const val SEX_WOMAN: Int = 2 //性别女

    const val HACK_REVENUE_FACTOR_FB_KEY: String = "hack_revenue_factor_FB"
    const val HACK_REVENUE_FACTOR_AF_KEY: String = "hack_revenue_factor_AF"
    const val SPECIAL_OFFER: String = "special_offer"

    //EventLog
    const val PURCHASE_SUCCESS: String = "purchase_success"
    const val USD: String = "USD"
    const val CALL_STATUS: String = "call_status"
    const val IS_FREE: String = "isFree"

    const val IS_MATCH: String = "isMatch"

    /**
     * AF归因数据
     */
    const val UTM_SOURCE: String = "utm-source"
    const val AF_AGENCY: String = "agency"
    const val AF_CHANNEL: String = "af_channel"
    const val MEDIA_SOURCE: String = "media_source"
    const val AF_STATUS: String = "af_status"
    const val REVIEW_ORDER: String = "review_order" //请求库存列表
    const val LAUNCH_PAY: String = "launch_pay" //调起支付 调起相应支付前打点
    const val CAMPAIGN: String = "campaign"
    const val ADSET: String = "adset"
    const val CONSUME_ORDER_RESPONSE: String = "consume_order_resp" //消费订单回调
    const val VERIFY_ORDER_RESPONSE: String = "verify_order_resp" //校验订单回调
    const val ADSET_ID: String = "adset_id"
    const val CAMPAIGN_ID: String = "campaign_id"
    const val CREATE_ORDER: String = "create_order"
    const val ADGROUP_ID: String = "adgroup_id"



    /**
     * 短连接上传参数
     */
    const val AF_ADGROUP_ID: String = "af_adgroup_id"

    const val AF_AGENCY_L: String = "af_agency"
    const val AF_CHANNEL_L: String = "af_channel"
    const val AF_ADSET: String = "af_adset"
    const val AF_ADSET_ID: String = "af_adset_id"
    const val PURCHASE_DETAIL: String = "purchase_detail"
    const val PLATFORM_SDK_VER: String = "32"
    const val ANDROID: String = "Android"
    const val EVENT: String = "event"
    const val LIVE_CALL: String = "livecall"
    const val COMMON: String = "common"
    const val AF_STATUS_L: String = "af_status"
    const val FALSE: String = "false"


    val VERSION_NAME: String = appVersionName
    val VERSION_CODE: Long = appVersionCode.toLong()
    val PACKAGE_NAME: String = JuicyApplication.juicyApplication!!.packageName
    const val USER_STATUS: String = "status"
    const val ANCHOR_INFO: String = "anchorInfo"
    val ANDROID_ID: String? = AppMessageUtil.getAndroidId(JuicyApplication.juicyApplication!!)
    const val IS_FOLLOW: String = "is_follow"
    const val ANCHOR_ID: String = "anchorId"

    const val BUSY: String = "Busy" // 橘黄
    const val LIVE: String = "Live" // 紫色
    const val OFFLINE: String = "Offline" // 灰色
    const val AVAILABLE: String = "Available" //
    const val IN_CALL: String = "InCall" // 红色
    const val ONLINE: String = "Online" // 绿色
    const val ORDER_NO: String = "orderNo"
    const val SALE_NUMBER: String = "sale_number"
    const val URL_PATH: String = "urlPath"

    //礼物列表
    @JvmStatic
    val giftResMap: HashMap<String, Int> = hashMapOf(
        "qinggua" to R.drawable.ic_gift_qinggua_black,
        "meigui" to R.drawable.ic_gift_meigui_cyan,
        "xiong" to  R.drawable.ic_gift_xiong_ash,
        "chungao" to R.drawable.ic_gift_chungao_magenta,
        "xiangshui" to R.drawable.ic_gift_xaingshui_white,
        "rose" to  R.drawable.ic_gift_rose_azure,
        "biao" to R.drawable.ic_gift_biao_jade,
        "shuijingxie" to  R.drawable.ic_gift_shuijingxie_pearl,
        "xiangjiao" to R.drawable.ic_gift_xiangjiao_mist,
        "qiaokeli" to  R.drawable.ic_gift_qiaokeli_white,
        "xiangbing" to R.drawable.ic_gift_xaingbing_ivory,
        "erhuan" to R.drawable.ic_gift_erhuan_green,
        "yongyi" to R.drawable.ic_gift_yongyi_sage,
        "hunsha" to R.drawable.ic_gift_hunsha_cherry,
        "aimashibao" to R.drawable.ic_gift_aimashibao_salmon,
        "jiezi" to R.drawable.ic_gift_jiezhi_slate,
        "kiss" to  R.drawable.ic_gift_kiss_white,
        "shouzhuo" to R.drawable.ic_gift_shouzhuo_crimson,
        "chuang" to  R.drawable.ic_gift_chuang_frost,
        "xianglian" to  R.drawable.ic_gift_xianglian_gray,
        "huangguan" to R.drawable.ic_gift_huangguan_lemon,
        "paoche" to R.drawable.ic_gift_paoche_sapphire,
        "bieshu" to R.drawable.ic_gift_bieshu_platinum,
        "feiji" to R.drawable.ic_gift_feiji_pink,
        "youlun" to  R.drawable.ic_gift_youting_crimson,
        "chengbao" to R.drawable.ic_gift_chengbao_cherry,
    )

    // 线下临散官方账号
    @JvmStatic
    var nativeOfficeUserList: MutableList<String>? = null
        get() {
            if (field == null) {
                field = mutableListOf()
                
            }
            return field
        }
        private set

    /**
     * 这里是配置信息请求回来的数据item（ConfigItemContentBase）中的name字段
     */
    const val RCK: String = "rck" //融云
    const val TRANSLATE_TYPE_GOOGLE: String = "glt" //谷歌翻译字段
    const val GOOGLE_PAY: String = "GP" //谷歌支付方式
    const val TPP_OPEN_TYPE: String = "tpp_open_type" //IM充值佳卡片
    const val RTCK: String = "rtck" // 声网Key
    const val TRANSLATE_TYPE: String = "translate_type" //翻译类型字段
    const val TRANSLATE_TYPE_MICROSOFT: String = "microsoft_translation_key" //微软翻译字段


    const val USER_ID: String = "userId"
    const val CALL_TYPE: String = "callType"
    const val CHANNEL_NAME: String = "channelName"

    const val CALL_CREATE: String = "call_create"
    const val TO_USER_ID: String = "toUserId"
    const val FROM_USER_ID: String = "fromUserId"
    const val BROADCASTER_PRICE: String = "broadcasterPrice"
    const val CALLING_USER_ID: String = "callingUserId"
    const val RTC_TOKEN: String = "rtcToken"
    const val BE_CALL: String = "beCall"
    const val BLOCK: String = "Block"
    const val REPORT: String = "Report"
    const val CALL_SOURCE: String = "callSource"

    //路由路径
    const val MAIN_ACTIVITY_ROUTE: String = "/app/CoreActivity" //主页面
    const val SPLASH_ACTIVITY_ROUTE: String = "/app/LaunchActivity" //启动页面
    const val LOGIN_ACTIVITY_ROUTE: String = "/app/AuthActivity" //登录页面

    const val VIDEO_CALL_ACTIVITY_ROUTE: String = "/video/CallActivity"
    const val ANCHOR_INFO_ACTIVITY_ROUTE: String = "/info/ProfileActivity"
    const val MESSAGE_FRAGMENT_ROUTE: String = "/msg/MessFragment"
    const val MINE_FRAGMENT_ROUTE: String = "/user/MineFragment"
    const val CHAT_ROUTE: String = "/msg/MessageActivity"



    const val MISSED_CALL: String = "Missed call" //未接通/无人接通通话
    const val CANCELLED_CALL: String = "Cancelled call" //拨出并自己取消的通话
    const val OUTGOING_CALL: String = "Outgoing call" //拨出且接通
    const val INCOMING_CALL: String = "Incoming call" //收到且接通
    const val REJECTED_CALL: String = "Rejected call" //挂断/被挂断通话
    const val UNANSWERED_CALL: String = "UnAnswered call" //未接通/无人接通通话

    const val RECHARGE: String = "recharge" //主播推荐商品
    const val GOODS: String = "goods" //普通的商品
    const val GIFT: String = "gift" //普通的商品
    const val TC_PREFIX: String = "tc_"

    //充值事件类型
    object GlobalTppEvent {
        const val SHOW: String = "show"
        const val CLICK: String = "click"
    }


    /**
     * 创建来源
     */
    const val CONVERSATION: Int = 1 //会话界面
    const val JSAPI: Int = 2 //页端调用
    const val MATCH_RESULT: Int = 3 //匹配结果页
    const val POPULAR_WALL: Int = 4 //主播/用户墙
    const val ANOTHER_ANCHOR: Int = 5 //呼叫失败时推荐另一批
    const val DETAIL_PAGE: Int = 6 //主播主页调用
    const val CALL_DIALOG_PAGE: Int = 9 //被呼叫弹窗;
    const val DETAIL_VIDEO_PAGE: Int = 7 //主播视频详情页
    const val MISS_CALL_PAGE: Int = 8 //miss call 弹框回拨
    const val CONNECTION_ERROR_CALL_BACK: Int = 9 //用户网络不好弹框回拨
    const val MEDIA_WALL: Int = 10 //视频流
    const val FAST_MATCH: Int = 11 //快速匹配
    const val VIDEOCHAT: Int = 12 //视频页面
    object GlobalTppSource {
        //事件来源source
        const val BANNER: String = "banner"
        const val IM: String = "im" //充值卡片
        const val Dialog: String = "dialog" //渠道弹窗
        const val FAQ: String = "faq"
        const val EXCLUSIVE_OFFER: String = "exclusiveOffer" //三方拉新弹窗
    }

    object ImTppSource {
        const val BANNER: String = "banner"
        const val IM: String = "im" //充值卡片
        const val Dialog: String = "dialog" //渠道弹窗
        const val FAQ: String = "faq"
        const val EXCLUSIVE_OFFER: String = "exclusiveOffer" //三方拉新弹窗
    }
    /**
     *
     * 挂断原因
     *
     */
    const val NORMAL: Int = 1
    const val NO_COINS: Int = 2
    const val CL_EXCEPTION: Int = 3
    const val CL_CALL_TIMEOUT: Int = 4
    const val CL_REMOTE_USER_LEFT: Int = 5
    const val SER_EXCEPTION: Int = 6
    const val NET_EXCEPTION: Int = 7






}
