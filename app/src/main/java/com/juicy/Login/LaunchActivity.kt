package com.juicy.Login

import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.R
import com.juicy.Login.AuthActivity.ConfigCallBack
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.SpSaveUtil.getStringValue

@Route(path = Constant.SPLASH_ACTIVITY_ROUTE)
class LaunchActivity : BaseActivity() {
    private val TAG: String = javaClass.simpleName
    private var service: Service? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        initRetrofit()
        setContentView(R.layout.activity_container)
        super.onCreate(savedInstanceState)
    }

    private fun initRetrofit() {
        service = RetrofitManage.instance.create(Service::class.java)
    }

    public override fun initData() {
        //校验token
        val str = getStringValue(SpKeyPool.APP_TOKEN_KEY, "")
        if (!str.isNullOrEmpty()) {
            AppConfigAndSomeDataRequest.instance?.getAppConfig(object : ConfigCallBack {
                    override val successful: Unit
                        get() {
                            AppConfigAndSomeDataRequest.instance?.getStrategy(object : ConfigCallBack {
                                    override val successful: Unit
                                        get() {
                                            if (Cache.instance.userStratResult == null) {
                                                toLoginVc()
                                            } else {
                                                ARouter.getInstance()
                                                    .build(Constant.MAIN_ACTIVITY_ROUTE)
                                                    .navigation(this@LaunchActivity)
                                                finish()
                                            }
                                        }

                                    override val unSuccessful: Unit
                                        get() {
                                            //退出到登录界面
                                            toLoginVc()
                                        }
                                })
                        }

                    override val unSuccessful: Unit
                        get() {
                            //配置信息未获取到，跳转登录页面

                            toLoginVc()
                        }
                })
        } else {
            //token为空，跳转登录页面
            ProcessingTokenExpiration()
        }
    }


    private fun toLoginVc() {
        //配置信息未获取到，跳转登录页面
        if (ActivityUtils.getTopActivity() != null && ActivityUtils.getTopActivity() !is AuthActivity) {
            startActivity(
                Intent(
                    this@LaunchActivity,
                    AuthActivity::class.java
                )
            )
            if (!isDestroy(this)) {
                finish()
            }

            ToastUtils.showShort(R.string.msg_warning)
        }
        Log.e(TAG, "config get error")
    }

    override fun initView() {
    }

    private fun ProcessingTokenExpiration() {
        if (ActivityUtils.getTopActivity() !is AuthActivity) {
            startActivity(
                Intent(
                    this@LaunchActivity,
                    AuthActivity::class.java
                )
            )
            finish()
        }
    }

    override fun transLocalText() {
    }
}