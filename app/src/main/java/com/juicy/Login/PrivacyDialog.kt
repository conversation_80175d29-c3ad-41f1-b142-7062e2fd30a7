package com.juicy.Login

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.SpanUtils
import com.juicy.app.databinding.DialogBinding
import com.juicy.common.config.ApiPool
import com.juicy.common.utils.LanguageManager

class PrivacyDialog(private val context: Context) : Dialog(context) {
    private lateinit var dialogPrivacyBinding: DialogBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        dialogPrivacyBinding = DialogBinding.inflate(layoutInflater)
        setContentView(dialogPrivacyBinding.root)

        setView()
        initView()
        if (LanguageManager.instance?.isLanguageForce == true) {
            dialogPrivacyBinding.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        }
        dialogPrivacyBinding.dynamicAround.text = LanguageManager.instance?.getLocalTranslate("Later")
        dialogPrivacyBinding.underlayFixed.text = LanguageManager.instance?.getLocalTranslate("Agree")
        dialogPrivacyBinding.elasticSurface.text = LanguageManager.instance?.getLocalTranslate("Notice")
        //设置ClickableSpan跳转链接的前提需要
        dialogPrivacyBinding.chooseBrief.movementMethod =
            LinkMovementMethod.getInstance()
        //设置隐私和协议
        SpanUtils.with(dialogPrivacyBinding.chooseBrief)
            .append(LanguageManager.instance?.getLocalTranslate("By_using_our_App_you_agree_with_our")?:"")
            .append(initTerms()).append(
                LanguageManager.instance?.getLocalTranslate("and")?:""
            ).append(initPolicy()).create()
    }

    private fun setView() {
        val dialogWindow = window
        val layoutParams = dialogWindow?.attributes?:return
        layoutParams.gravity = Gravity.CENTER
        layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
        dialogWindow.attributes = layoutParams
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initView() {
        dialogPrivacyBinding.dynamicAround.setOnClickListener { view: View? -> dismiss() }
    }

    val dialogPrivacy: DialogBinding
        get() {
            if (dialogPrivacyBinding == null) {
                dialogPrivacyBinding =
                    DialogBinding.inflate(
                        layoutInflater
                    )
            }
            return dialogPrivacyBinding
        }

    private fun initTerms(): SpannableString {
        val titleTerms = LanguageManager.instance?.getLocalTranslate("Terms_Conditions")?:""
        val terms = SpannableString(titleTerms)
        terms.setSpan(UnderlineSpan(), 0, terms.length, 0)
        val termsClickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                intentRongActivity(titleTerms, ApiPool.TERM_CONDITIONS)
            }
        }
        terms.setSpan(termsClickableSpan, 0, terms.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        terms.setSpan(
            ForegroundColorSpan(-0x149a),
            0,
            terms.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return terms
    }

    private fun initPolicy(): SpannableString {
        val titlePolicy = LanguageManager.instance?.getLocalTranslate("Privacy_Policy") + "."
        val policy = SpannableString(titlePolicy)
        policy.setSpan(UnderlineSpan(), 0, policy.length, 0)
        val policyClickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                intentRongActivity(titlePolicy, ApiPool.PRIVACY)
            }
        }
        policy.setSpan(policyClickableSpan, 0, policy.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        policy.setSpan(
            ForegroundColorSpan(-0x149a),
            0,
            policy.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return policy
    }

    private fun intentRongActivity(title: String, url: String) {
        val intent = Intent(getContext(), com.juicy.app.modules.base.activity.BasicWebActivity::class.java)
        intent.putExtra("title", title)
        intent.putExtra("url", url)
        ActivityUtils.startActivity(intent)
    }
}
