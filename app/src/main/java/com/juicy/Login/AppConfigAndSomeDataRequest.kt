package com.juicy.Login

import android.annotation.SuppressLint
import android.content.ContentValues
import android.util.Log
import com.juicy.Login.AuthActivity.ConfigCallBack
import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.SpSaveUtil.putIntValue
import com.juicy.common.utils.SpSaveUtil.putStringValue
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import io.rong.imlib.model.InitOption

/**
 * 配置信息、策略信息以及其他需提前获取到的信息的些许数据请求
 */
class AppConfigAndSomeDataRequest {
    @SuppressLint("CheckResult")
    fun getAppConfig(callBack: ConfigCallBack) {
        RetrofitManage.instance.create(Service::class.java).appConfigCall()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ appconfigResultBaseBean ->
                if (appconfigResultBaseBean.data != null && appconfigResultBaseBean.isOk) {
                    //保存信息
                    val data = appconfigResultBaseBean.data
                    Cache.instance.appconfig = data
                    val items = data!!.items

                    if (data.ver != null) {
                        putIntValue(SpKeyPool.APP_CONFIG_VER, data.ver!!.toInt())
                    }
                    for (jsonObject in items!!) {
                        val name = jsonObject.name
                        val content = jsonObject.data
                        if (content == "") {
                            continue
                        }
                        //保存请求回来的翻译类型   当ConfigItemContentBase里name字段的值为“translate_type”时保存
                        if (name == Constant.TRANSLATE_TYPE) {
                            //转成int类型保存（1->微软，2->谷歌）
                            val translateType = Math.round(content as Double).toInt()
                            Log.d(
                                ContentValues.TAG,
                                "translateType: ------------->$translateType"
                            )
                            putIntValue(SpKeyPool.TRANSLATE_TYPE_KEY, translateType)
                            Cache.instance.translateType =
                                translateType.toString()
                        }

                        if (name == Constant.RTCK) {
                            // 将声网的Key保存起来
                            putStringValue(SpKeyPool.RTCK_KEY, content.toString())
                            Cache.instance.rtck = content.toString()
                        }

                        //同翻译类型一样保存微软翻译
                        if (name == Constant.TRANSLATE_TYPE_MICROSOFT) {
                            //                        Log.d(TAG, "onResponse: 微软翻译的key--------------》" + name);
                            //转成String类型保存
                            putStringValue(
                                SpKeyPool.TRANSLATE_TYPE_MICROSOFT,
                                content.toString()
                            )
                            Cache.instance.microsoftTranslationKey =
                                content.toString()
                        }

                        //同理保存谷歌翻译
                        if (name == Constant.TRANSLATE_TYPE_GOOGLE) {
                            //转成String类型保存
                            putStringValue(
                                SpKeyPool.TRANSLATE_TYPE_GOOGLE,
                                content.toString()
                            )
                            Cache.instance.googleTranslationKey =
                                content.toString()
                        }
                        //获取融云key
                        if (name == Constant.RCK) {
                            //                        Log.d(TAG, "onResponse: 融云的key--------------》" + name);
                            //转成String类型保存
                            putStringValue(SpKeyPool.RCK_KEY, content.toString())
                            Cache.instance.rongRck = content.toString()
                            putStringValue(SpKeyPool.RC_APP_KEY, "")
                        }
                        if (name == Constant.TPP_OPEN_TYPE) {
                            putStringValue(SpKeyPool.TPP_OPEN_TYPE, content.toString())
                            Cache.instance.tppOpenType = content.toString()
                        }
                        if (name == SpKeyPool.HACK_REVENUE_FACTOR_FB) {
                            putStringValue(
                                SpKeyPool.HACK_REVENUE_FACTOR_FB,
                                content.toString()
                            )
                            Cache.instance.hackRevenueFactorFB =
                                content.toString()
                        }
                        if (name == SpKeyPool.HACK_REVENUE_FACTOR_AF) {
                            putStringValue(
                                SpKeyPool.HACK_REVENUE_FACTOR_AF,
                                content.toString()
                            )
                            Cache.instance.hackRevenueFactorAF =
                                content.toString()
                        }
                        if (name == SpKeyPool.RC_APP_KEY) {
                            putStringValue(SpKeyPool.RC_APP_KEY, content.toString())
                            Cache.instance.rcAppKey = content.toString()
                        }
                        if (name == SpKeyPool.RC_TYPE) {
                            putStringValue(SpKeyPool.RC_TYPE, content.toString())
                            Cache.instance.rctype = content.toString()
                            if (Cache.instance.rctype == "NA") {
                                Cache.instance.areaCode = InitOption.AreaCode.NA
                            } else if (Cache.instance.rctype == "SA") {
                                Cache.instance.areaCode = InitOption.AreaCode.SA
                            } else if (Cache.instance.rctype == "SG") {
                                Cache.instance.areaCode = InitOption.AreaCode.SG
                            } else if (Cache.instance.rctype == "SG_B") {
                                Cache.instance.areaCode =
                                    InitOption.AreaCode.SG_B
                            } else {
                                Cache.instance.areaCode = InitOption.AreaCode.BJ
                            }
                        }
                        if (name == SpKeyPool.ENCRYPT_KEY) {
                            putStringValue(SpKeyPool.ENCRYPT_KEY, content.toString())
                            Cache.instance.encryptKey = content.toString()
                        }
                    }
                    callBack.successful
                } else {
                    callBack.unSuccessful
                }
            }, { throwable ->
                Log.e(ContentValues.TAG, "accept: " + throwable.message)
                callBack.unSuccessful
            })
    }


    fun getStrategy(callBack: ConfigCallBack) {
       var dis = RetrofitManage.instance.create(Service::class.java).strategyCall
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ strategyResultBaseBean ->
                if (strategyResultBaseBean.data != null && strategyResultBaseBean.isOk) {
                    //保存信息
                    val data = strategyResultBaseBean.data
                    Cache.instance.userStratResult = data
                    if (data!!.isReviewPkg != null) {
                        Cache.instance.reviewPkg = data.isReviewPkg!!
                    }
                    //判读是否审核
                    if(Cache.instance.userStratResult!!.isReviewPkg ?: true){
                        //修改主播墙
                        val broadcasterWallTagList =
                            (Cache.instance.userStratResult?.broadcasterWallTagList ?: listOf()).toMutableList()
                        if(broadcasterWallTagList.isNotEmpty()){
                            //如果
                            broadcasterWallTagList[0].showName = "New"
                        }else{
                            //创建Item
                            val anchorTagItemBean = com.juicy.common.model.bean.AnchorTagItemBean()
                            anchorTagItemBean.showName = "New"
                            anchorTagItemBean.tagName = "Popular"
                            anchorTagItemBean.subTagList = List<String>(1){"All"};
                            broadcasterWallTagList +=  anchorTagItemBean
                        }
                        if(broadcasterWallTagList.size > 1){
                            broadcasterWallTagList[1].showName = "Hot"
                        }else{
                            val anchorTagItemBean = com.juicy.common.model.bean.AnchorTagItemBean()
                            anchorTagItemBean.showName = "Hot"
                            anchorTagItemBean.tagName = "Popular"
                            anchorTagItemBean.subTagList = List<String>(1){"All"};
                            broadcasterWallTagList +=  anchorTagItemBean
                        }
                        if(broadcasterWallTagList.size > 2){
                            broadcasterWallTagList[2].showName = "Followed"
                        }else{
                            val anchorTagItemBean = com.juicy.common.model.bean.AnchorTagItemBean()
                            anchorTagItemBean.showName = "Followed"
                            anchorTagItemBean.tagName = "Followed"
                            anchorTagItemBean.subTagList = List<String>(1){"All"};
                            broadcasterWallTagList +=  anchorTagItemBean
                        }
                        Cache.instance.userStratResult?.broadcasterWallTagList = broadcasterWallTagList
                    }else{
                        //遍历 broadcasterWallTagList 修改 showName = tagName
                        Cache.instance.userStratResult?.broadcasterWallTagList?.forEach {
                            it.showName = it.tagName
                        }
                    }


                    callBack.successful
                } else {
                    callBack.unSuccessful
                }
            }, { throwable ->
                Log.e(ContentValues.TAG, "accept: " + throwable.message)
                callBack.unSuccessful
            })
    }

    companion object {
        private var service: Service? = null
        private var someDataRequest: AppConfigAndSomeDataRequest? = null

        val instance: AppConfigAndSomeDataRequest?
            get() {
                if (someDataRequest == null) {
                    someDataRequest =
                        AppConfigAndSomeDataRequest()
                }
                service =
                    RetrofitManage.instance.create(
                        Service::class.java
                    )
                return someDataRequest
            }
    }
}
