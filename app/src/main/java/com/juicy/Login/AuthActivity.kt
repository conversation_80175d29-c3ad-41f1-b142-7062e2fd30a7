package com.juicy.Login

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.CompoundButton
import androidx.databinding.DataBindingUtil
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.SpanUtils
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.JuicyApplication
import com.juicy.app.R
import com.juicy.app.databinding.ActivityProgressBinding
import com.juicy.app.modules.base.dialog.JuicyLoadDialog
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.networks.Service
import com.juicy.common.model.result.LoginUserInfoResult
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.config.ApiPool
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppUtil.safeInfoString
import com.juicy.common.utils.ApplicationUtil.getAndroidId
import com.juicy.common.utils.DialogShowUtils.showDialogOutsideUnClose
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import com.juicy.common.utils.SpSaveUtil.putStringValue
import com.juicy.common.utils.throttle.Throttle.throttle
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers

@Route(path = Constant.LOGIN_ACTIVITY_ROUTE)
class AuthActivity : BaseActivity() {
    private val TAG: String = javaClass.simpleName
    private lateinit var activityLoginBinding: ActivityProgressBinding
    private val isAgree = false
    private var privacyDialog: PrivacyDialog? = null
    private var juicyLoadDialog: JuicyLoadDialog? = null

    var loginPreResultBaseBean: com.juicy.common.model.bean.NetResponseBean<LoginUserInfoResult>? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        activityLoginBinding =
            DataBindingUtil.setContentView(this, R.layout.activity_progress)
        setContentView(activityLoginBinding.root)
        //        StatusBarUtil.setTranslucentStatus(this);
        initOtherView()
        initData()
        initView()
        toPreLogin()
        super.onCreate(savedInstanceState)
    }

    private fun initOtherView() {
        privacyDialog = PrivacyDialog(this)
        juicyLoadDialog = JuicyLoadDialog(this)
    }

    public override fun initData() {
        activityLoginBinding.topWrapper.isChecked = (getBooleanVal(SpKeyPool.PRIVACY_IS_SHOW, false)?:false)
        activityLoginBinding.topWrapper.setOnCheckedChangeListener { v: CompoundButton?, isChecked: Boolean ->
            putBooleanValue(
                SpKeyPool.PRIVACY_IS_SHOW,
                isChecked
            )
        }
        activityLoginBinding.stretchSelect.setOnClickListener { view: View? ->
            //第一次登录，弹出条款协议弹窗
            if (getBooleanVal(SpKeyPool.PRIVACY_IS_SHOW, false) != true) {
                //弹出条款协议弹窗
                showDialogOutsideUnClose(privacyDialog)
                privacyDialog?.dialogPrivacy?.underlayFixed?.setOnClickListener { view1: View? ->
                    //同意条款，记录一下
                    putBooleanValue(SpKeyPool.PRIVACY_IS_SHOW, true)
                    activityLoginBinding.topWrapper.isChecked = true
                    privacyDialog?.dismiss()

                    //条款给他设置一下同意

                    //走登录流程
                    initLoginAboutRequest()
                }
            } else {
                //已经弹过,提示同意一下条款，再走登录流程
                initLoginAboutRequest()
            }
        }
    }

    public override fun initView() {
        //条款协议文字
        //设置ClickableSpan跳转链接的前提需要
        activityLoginBinding.unfocusedMask.movementMethod =
            LinkMovementMethod.getInstance()
        //设置隐私和协议
        SpanUtils.with(activityLoginBinding.unfocusedMask)
            .append(LanguageManager.instance?.getLocalTranslate("By_using_our_App_you_agree_with_our")?:"")
            .append(initTerms()).append(
                LanguageManager.instance?.getLocalTranslate("and")?:""
            ).append(initPolicy()).create()
    }

    private fun initPolicy(): SpannableString {
        val titlePolicy = LanguageManager.instance?.getLocalTranslate("Privacy_Policy") + "."
        val policy = SpannableString(titlePolicy)
        policy.setSpan(UnderlineSpan(), 0, policy.length, 0)
        val policyClickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                intentRongActivity(titlePolicy, ApiPool.PRIVACY)
            }
        }
        policy.setSpan(policyClickableSpan, 0, policy.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        policy.setSpan(
            ForegroundColorSpan(-0x149a),
            0,
            policy.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return policy
    }

    private fun initTerms(): SpannableString {
        val titleTerms = LanguageManager.instance?.getLocalTranslate("Terms_Conditions")?:""
        val terms = SpannableString(titleTerms)
        terms.setSpan(UnderlineSpan(), 0, terms.length, 0)
        val termsClickableSpan: ClickableSpan = object : ClickableSpan() {
            override fun onClick(view: View) {
                intentRongActivity(titleTerms, ApiPool.TERM_CONDITIONS)
            }
        }
        terms.setSpan(termsClickableSpan, 0, terms.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        terms.setSpan(
            ForegroundColorSpan(-0x149a),
            0,
            terms.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return terms
    }

    private fun intentRongActivity(title: String, url: String) {
        val intent = Intent(
            this@AuthActivity,
            com.juicy.app.modules.base.activity.BasicWebActivity::class.java
        )
        intent.putExtra("title", title)
        intent.putExtra("url", url)
        startActivity(intent)
    }

    private var isLogining = false

    private var isPreLogining = false

    private fun toPreLogin() {
        if (isPreLogining) {
            return
        }
        isPreLogining = true

        AppConfigAndSomeDataRequest.instance?.getAppConfig(object : ConfigCallBack {
            override val successful: Unit
                get() {
                    //登录
                    toPreNetLogin()
                }

            override val unSuccessful: Unit
                get() {
                    if (Cache.instance.appconfig != null) {
                        //登录
                        toPreNetLogin()
                    } else {
                        isPreLogining = false
                        if (isLogining) {
                            toLoginFinished()
                        }
                    }
                }
        })
    }

    @SuppressLint("CheckResult")
    private fun toPreNetLogin() {
        //登录请求
        RetrofitManage.instance.create<Service>(Service::class.java).app_login(
            com.juicy.common.model.bean.LoginParamBean(
                "4",
                getAndroidId(
                    JuicyApplication.juicyApplication!!
                ) ?: "", safeInfoString
            )
        )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ loginResultBaseBean ->
                loginPreResultBaseBean = loginResultBaseBean
                if (loginResultBaseBean.data != null && loginResultBaseBean.isOk) {
                    //保存信息

                    if (loginResultBaseBean.data?.firstRegister != null && loginResultBaseBean.data?.firstRegister == true) {
                        putStringValue(SpKeyPool.RIGISTER_LOGIN, "1")
                    }

                    Cache.instance.token = loginResultBaseBean.data?.token
                    putStringValue(
                        SpKeyPool.APP_TOKEN_KEY,
                        loginResultBaseBean.data?.token
                    )
                    AppConfigAndSomeDataRequest.instance?.getStrategy(object : ConfigCallBack {
                            override val successful: Unit
                                get() {
                                    isPreLogining = false
                                    putStringValue(SpKeyPool.APP_TOKEN_KEY, "")
                                    if (isLogining) {
                                        toLoginFinished()
                                    }
                                }

                            override val unSuccessful: Unit
                                get() {
                                    isPreLogining = false
                                    Cache.instance.token = null
                                    putStringValue(SpKeyPool.APP_TOKEN_KEY, "")

                                    if (isLogining) {
                                        toLoginFinished()
                                    }
                                }
                        })
                } else {
                    isPreLogining = false
                    if (isLogining) {
                        toLoginFinished()
                    }
                }
            }, {
                isPreLogining = false
                if (isLogining) {
                    toLoginFinished()
                }
            })
    }

    private fun initLoginAboutRequest() {
        throttle("login_to") {
            if (isLogining) {
                return@throttle
            }
            isLogining = true
            //弹出加载弹窗
            showDialogOutsideUnClose(juicyLoadDialog)
            //设置用户禁止触摸
            window.setFlags(
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
            )

            if (!isPreLogining && loginPreResultBaseBean != null && Cache.instance.userStratResult != null) {
                toLoginFinished()
                return@throttle
            }
            if (Cache.instance.appconfig != null) {
                //登录
                toNetLogin()
            } else {
                AppConfigAndSomeDataRequest.instance?.getAppConfig(object : ConfigCallBack {
                        override val successful: Unit
                            get() {
                                //登录
                                toNetLogin()
                            }

                        override val unSuccessful: Unit
                            get() {
                                if (Cache.instance.appconfig != null) {
                                    //登录
                                    toNetLogin()
                                } else {
                                    //登录失败
                                    toLoginFinished()
                                }
                            }
                    })
            }
        }
    }


    @SuppressLint("CheckResult")
    private fun toNetLogin() {
        var androidId = ""
        JuicyApplication.juicyApplication?.let {
            androidId = getAndroidId(it
            )?:""
        }
        //登录请求
        RetrofitManage.instance.create<Service>(Service::class.java).app_login(

            com.juicy.common.model.bean.LoginParamBean(
                "4",
                androidId, safeInfoString
            )
        )
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ loginResultBaseBean ->
                loginPreResultBaseBean = loginResultBaseBean
                if (loginResultBaseBean.data != null && loginResultBaseBean.isOk) {
                    //保存信息
                    if (loginResultBaseBean.data?.firstRegister != null && loginResultBaseBean.data?.firstRegister == true) {
                        putStringValue(SpKeyPool.RIGISTER_LOGIN, "1")
                    }

                    //                            //保存Token
                    Cache.instance.token = loginResultBaseBean.data?.token
                    putStringValue(
                        SpKeyPool.APP_TOKEN_KEY,
                        loginResultBaseBean.data?.token
                    )

                    //请求相关配置
                    AppConfigAndSomeDataRequest.instance?.getStrategy(object : ConfigCallBack {
                            override val successful: Unit
                                get() {
                                    if (Cache.instance.userStratResult == null) {
                                        //                                        isLogining = false;
                                        //                                        ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate("Login_failed_possibly_due_to_network_problems_or_server_errors_Please_try_again_later"));
                                        //                                        loadCloseAndCanTouch();
                                        toLoginFinished()
                                    } else {
                                        toLoginFinished()
                                    }
                                }

                            override val unSuccessful: Unit
                                get() {
                                    //                                    isLogining = false;
                                    //                                    ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate("Login_failed_possibly_due_to_network_problems_or_server_errors_Please_try_again_later"));
                                    //                                    loadCloseAndCanTouch();
                                    Cache.instance.token = null
                                    putStringValue(SpKeyPool.APP_TOKEN_KEY, "")
                                    toLoginFinished()
                                }
                        })
                } else {
                    toLoginFinished()
                    //                            if (loginResultBaseBean.getCode() == 10010003) {
                    //                                if (loginResultBaseBean.getMsg() != null) {
                    //                                    ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate(loginResultBaseBean.getMsg()));
                    //                                }
                    //                            }
                    //                            isLogining = false;
                    //                            loadCloseAndCanTouch();
                    //                            Log.e(TAG, "登录接口返回信息: " + loginResultBaseBean.getMsg());
                }
            }, { throwable ->
                Log.e(TAG, "accept: " + throwable.message)
                //                        isLogining = false;
                //                        ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate("Login_failed_possibly_due_to_network_problems_or_server_errors_Please_try_again_later"));
                //                        loadCloseAndCanTouch();
                toLoginFinished()
            })
    }

    fun toLoginFinished() {
        if (isLogining) {
            isLogining = false
            loadCloseAndCanTouch()
            if (loginPreResultBaseBean != null && loginPreResultBaseBean?.data != null && loginPreResultBaseBean?.isOk == true) {
                val loginResult = loginPreResultBaseBean?.data
                Cache.instance.loginUserInfoResult = loginResult
                //保存用户ID
                Cache.instance.userInfo = loginResult?.userInfo
                /*   if (Cache.getInstance().userInfo.getRongcloudToken().equals("")){
                                loadCloseAndCanTouch();
                                return;
                            }*/
                putStringValue(SpKeyPool.USER_ID_DEVICE, Cache.instance.userInfo?.userId)
                //                            SpSaveUtil.putStringValue(SpKeyPool.RTC_COULD, Cache.getInstance().userInfo.getRongcloudToken());
                //保存Token
                Cache.instance.token = loginResult?.token
                putStringValue(SpKeyPool.APP_TOKEN_KEY, loginResult?.token)

                if (Cache.instance.userStratResult == null) {
                    Cache.instance.token = null
                    putStringValue(SpKeyPool.APP_TOKEN_KEY, "")
                    ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Login_failed_possibly_due_to_network_problems_or_server_errors_Please_try_again_later"))
                } else {
                    ARouter.getInstance().build(Constant.MAIN_ACTIVITY_ROUTE).navigation(
                        this@AuthActivity
                    )
                    <EMAIL>()
                }
            } else if (loginPreResultBaseBean != null) {
                if (loginPreResultBaseBean?.code == 10010003) {
                    if (loginPreResultBaseBean?.msg != null) {
                        ToastUtils.showShort(
                            LanguageManager.instance?.getLocalTranslate(
                                loginPreResultBaseBean?.msg?:""
                            )
                        )
                    }
                }
            } else {
                ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Login_failed_possibly_due_to_network_problems_or_server_errors_Please_try_again_later"))
            }
        }
    }

    private fun loadCloseAndCanTouch() {
        juicyLoadDialog?.dismiss()
        //设置用户可以获取触摸事件
        window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
    }

    interface ConfigCallBack {
        val successful: Unit

        val unSuccessful: Unit
    }

    override fun transLocalText() {
        activityLoginBinding.stretchSelect.text =
            LanguageManager.instance?.getLocalTranslate("Fast_Login")
    }
}