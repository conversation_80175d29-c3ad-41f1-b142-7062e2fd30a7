package com.juicy.app

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ProcessLifecycleOwner
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ActivityUtils
import com.facebook.core.Core
import com.juicy.app.modules.wall.WallFirstFragment
import com.juicy.app.modules.call.fragment.FlashFragment
import com.juicy.app.modules.call.VideoCallActivity
import com.juicy.message.CustomConversationProvider
import com.juicy.message.CustomMessageItemProvider
import com.juicy.message.View.MessageActivity
import com.juicy.message.View.MessageFragment
import com.juicy.message.View.RongMessage.CustomDestructHQVoiceMessage
import com.juicy.message.View.RongMessage.HyperLinkMessage
import com.juicy.message.View.RongMessage.MyExtensionConfig
import com.juicy.message.View.RongMessage.MyFileMessage
import com.juicy.message.View.RongMessage.MyImageMessage
import com.juicy.message.View.RongMessage.MyRecallNotificationMessage
import com.juicy.message.View.RongMessage.MyTextMessage
import com.juicy.message.View.RongMessage.MyVoiceMessage
import com.juicy.message.View.RongMessage.SingleJsonMessage
import com.juicy.message.View.RongMessage.UserInfoProvider
import com.juicy.app.databinding.ActivityLabelBinding
import com.juicy.app.main_impl.MainImpl
import com.juicy.app.main_impl.MainImpl.MergeCallBack
import com.juicy.my.MineFragment
import com.juicy.app.modules.base.dialog.AnchorShoppingDialog
import com.juicy.app.modules.base.dialog.AssessDialog
import com.juicy.app.modules.base.dialog.DerivativeDialog
import com.juicy.app.modules.base.dialog.EvaluateDialog
import com.juicy.app.modules.base.dialog.FullShoppingDialog
import com.juicy.app.modules.base.dialog.NewGoodDialogView
import com.juicy.app.modules.base.dialog.NewUserDialogView
import com.juicy.app.modules.base.dialog.VipDialogFragment
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.UserIdParamsBean
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.model.event.CallEvent
import com.juicy.common.model.event.TabSelectStatusChangeEvent
import com.juicy.common.model.event.PayResultEvent
import com.juicy.common.model.event.DialogShowStatusEvent
import com.juicy.common.model.event.CoinsNumChangeEvent
import com.juicy.common.model.event.RegEditUserEvent
import com.juicy.common.model.event.NewUserRewardsShowStatusChangeEvent
import com.juicy.common.model.message_event.AppOnCallBean
import com.juicy.common.model.message_event.AppRechargeOrderStatusBean
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.networks.request.SocketUtil.SocketUtils
import com.juicy.common.networks.delegate.GetCoinCountInterface.getCoin
import com.juicy.common.networks.delegate.GetCoinGoodsSearchInterface.getCoinGoodsSearch
import com.juicy.common.networks.delegate.GetGoodsPromotionInterface.getGoodsPromotion
import com.juicy.common.networks.delegate.GetLastSpecialOfferInterface2.getLastSpecialOffer
import com.juicy.common.networks.delegate.GetPresentedInterface.getPresented
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.GiveAppsFlyerArgsInterface.GiveAppsFlyerArgs
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.networks.delegate.PostSwitchInterface.postSwitch
import com.juicy.common.networks.delegate.SubmitInstallRefererInterface.submitInstallReferer
import com.juicy.common.rcMessage.HLinkMsg
import com.juicy.common.rcMessage.NFMsg
import com.juicy.common.rcMessage.SJMsg
import com.juicy.common.db.CallDatabase
import com.juicy.common.db.User
import com.juicy.common.status.UserOnlineStatusService
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.AppUtil.safeInfoString
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.DateMetaUtil.ms2HMS
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PayUtils.PayUtils
import com.juicy.common.utils.ProcessLifecycleObserver
import com.juicy.common.utils.RongCloud.RongUtil.ConnectCallBack
import com.juicy.common.utils.RongCloud.RongUtil.RongConnect
import com.juicy.common.utils.RongCloud.RongUtil.RongTokenCallBack
import com.juicy.common.utils.RongCloud.RongUtil.UnReadCount
import com.juicy.common.utils.RongCloud.RongUtil.getRongcloudToken
import com.juicy.common.utils.RongCloud.RongUtil.init
import com.juicy.common.utils.SmartRefreshUtil.initSmartRefresh
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.getLongValue
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import com.juicy.common.utils.SpSaveUtil.putIntValue
import com.juicy.common.utils.SpSaveUtil.putStringValue
import com.juicy.common.utils.ThreadUtil.execute
import com.juicy.common.utils.ThreadUtil.runDelayOnUIThread
import com.juicy.common.utils.TranslateUtils.initCache
import com.juicy.common.utils.throttle.Throttle.throttle
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.functions.Consumer
import io.reactivex.rxjava3.schedulers.Schedulers
import io.rong.imkit.RongIM
import io.rong.imkit.config.BaseDataProcessor
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversation.extension.RongExtensionManager
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.FileMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.HQVoiceMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.ImageMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.RecallNotificationMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.TextMessageItemProvider
import io.rong.imkit.conversation.messgelist.provider.VoiceMessageItemProvider
import io.rong.imkit.conversationlist.provider.PrivateConversationProvider
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.MessageContent
import io.rong.imlib.model.UserInfo
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.stream.Collectors
import kotlin.math.abs

@Route(path = Constant.MAIN_ACTIVITY_ROUTE)
class CoreActivity : BaseActivity(), View.OnClickListener, ProcessLifecycleObserver.Callback {
    private var binding: ActivityLabelBinding? = null
    private var coins = 0
    private var proTimeThread: CountDownTimer? = null
    private var activityTimeThread: CountDownTimer? = null
    private var callEvent: CallEvent? = null
    private val fragments: MutableList<BaseFragment> = ArrayList()
    private var isFirst = false
    private var startNum = 0
    private var socketEventBusBack: SocketEventBusBack? = null

    private var mContext: Context? = null

    private var wallFirstFragment: WallFirstFragment? = null

    private var isRequestUserPromotionGood = false
    private var isRequestActivityGood = false

    //    private NewSalesGoodDialog salesGoodDialog;
    override fun onStart() {
        super.onStart()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        val isFirstRegist = getStringValue(SpKeyPool.RIGISTER_LOGIN, "")
        if (isFirstRegist == "1") {
            putStringValue(SpKeyPool.RIGISTER_LOGIN, "")
            Cache.instance.isFirstRegister = true
        } else {
            Cache.instance.isFirstRegister = false
        }


        Cache.instance.isLogin = true
        val processLifecycleObserver = ProcessLifecycleObserver(this)
        ProcessLifecycleOwner.get().lifecycle.addObserver(processLifecycleObserver)
        val view = View.inflate(this, R.layout.activity_label, null)
        binding = ActivityLabelBinding.bind(view)
        mContext = this
        //        StatusBarUtil.setTranslucentStatus(this);
//        RelativeLayout.LayoutParams mainPageLayoutParams = (RelativeLayout.LayoutParams) binding.contentSelect.getLayoutParams();
//        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0);
        setContentView(binding!!.root)

        // SocketIO初始化
        initSocketIO()
        // 融云初始化
        initRongIM()
        uploadRisk()
        val payUtils = PayUtils(this@CoreActivity)
        payUtils.startPurchaseListen()
        UserOnlineStatusService.instance
        initSmartRefresh()
        super.onCreate(savedInstanceState)
    }

    private fun initClick() {
        binding!!.home.setOnClickListener(this)
        binding!!.message.setOnClickListener(this)
        binding!!.narrowAvatar.setOnClickListener(this)
        binding!!.miniLocked.setOnClickListener(this)
        binding!!.xsPick.setOnClickListener(this)
        binding!!.checkedSmall.setOnClickListener(this)
        binding!!.filterCollapse.setOnClickListener(this)
    }

    private fun changeButton(i: Int) {
        if (i == 0) {
            binding!!.focusedStop.setImageResource(com.juicy.app.R.drawable.ic_home_select_emerald)
            binding!!.footerAdaptive.setTextColor(Color.parseColor("#8966FF"))
            binding!!.pasteNormal.setImageResource(com.juicy.app.R.drawable.ic_calls_unselect_yellow)
            binding!!.wrapTitle.setTextColor(Color.parseColor("#939099"))
            binding!!.xlPager.setImageResource(com.juicy.app.R.drawable.ic_message_gray_mint)
            binding!!.mediumPicker.setTextColor(Color.parseColor("#939099"))
            binding!!.connectedMinimal.setImageResource(com.juicy.app.R.drawable.ic_mine_gray_olive)
            binding!!.panelStop.setTextColor(Color.parseColor("#939099"))
        } else if (i == 1) {
            binding!!.xlPager.setImageResource(com.juicy.app.R.drawable.ic_message_gray_mint)
            binding!!.mediumPicker.setTextColor(Color.parseColor("#939099"))
            binding!!.pasteNormal.setImageResource(com.juicy.app.R.drawable.ic_calls_select_tan)
            binding!!.wrapTitle.setTextColor(Color.parseColor("#8966FF"))
            binding!!.focusedStop.setImageResource(com.juicy.app.R.drawable.ic_home_gray_white)
            binding!!.footerAdaptive.setTextColor(Color.parseColor("#939099"))
            binding!!.connectedMinimal.setImageResource(com.juicy.app.R.drawable.ic_mine_gray_olive)
            binding!!.panelStop.setTextColor(Color.parseColor("#939099"))
        } else if (i == 2) {
            binding!!.xlPager.setImageResource(com.juicy.app.R.drawable.ic_message_select_mint)
            binding!!.mediumPicker.setTextColor(Color.parseColor("#8966FF"))
            binding!!.pasteNormal.setImageResource(com.juicy.app.R.drawable.ic_calls_unselect_yellow)
            binding!!.wrapTitle.setTextColor(Color.parseColor("#939099"))
            binding!!.focusedStop.setImageResource(com.juicy.app.R.drawable.ic_home_gray_white)
            binding!!.footerAdaptive.setTextColor(Color.parseColor("#939099"))
            binding!!.connectedMinimal.setImageResource(com.juicy.app.R.drawable.ic_mine_gray_olive)
            binding!!.panelStop.setTextColor(Color.parseColor("#939099"))
        } else {
            binding!!.xlPager.setImageResource(com.juicy.app.R.drawable.ic_message_gray_mint)
            binding!!.mediumPicker.setTextColor(Color.parseColor("#939099"))
            binding!!.pasteNormal.setImageResource(com.juicy.app.R.drawable.ic_calls_unselect_yellow)
            binding!!.wrapTitle.setTextColor(Color.parseColor("#939099"))
            binding!!.focusedStop.setImageResource(com.juicy.app.R.drawable.ic_home_gray_white)
            binding!!.footerAdaptive.setTextColor(Color.parseColor("#939099"))
            binding!!.connectedMinimal.setImageResource(com.juicy.app.R.drawable.ic_mine_select_magenta)
            binding!!.panelStop.setTextColor(Color.parseColor("#8966FF"))
        }
    }

    private fun initFragment() {
        binding!!.root.post(Runnable {
            if (isDestroy(mContext as Activity?) || binding == null) return@Runnable
            if (supportFragmentManager.isStateSaved) return@Runnable
            wallFirstFragment = WallFirstFragment()
            fragments.add(wallFirstFragment!!)
            fragments.add(FlashFragment())
            fragments.add(MessageFragment())
            fragments.add(MineFragment())
            if (Cache.instance.userStratResult != null && Cache.instance.userStratResult?.initTab != null) {
                Log.d(
                    "TAG",
                    "initFragment: " + Cache.instance.userStratResult?.initTab
                )

                changePage(Cache.instance.userStratResult?.initTab?:0)
            } else {
                changePage(0)
            }


            binding!!.footerAdaptive.text = LanguageManager.instance?.getLocalTranslate("Anchor_wall")
            binding!!.wrapTitle.text = LanguageManager.instance?.getLocalTranslate("Flash_Chat")
            binding!!.mediumPicker.text = LanguageManager.instance!!.getLocalTranslate("Message")
            binding!!.panelStop.text = LanguageManager.instance!!.getLocalTranslate("Mine")
        })
    }

    private var currentPageNum = 0
    private fun changePage(page: Int) {
        binding!!.root.post(object : Runnable {
            override fun run() {
                if (isDestroy(mContext as Activity?) || binding == null) return
                if (supportFragmentManager.isStateSaved) return
                if (page == 0 && Cache.instance.userInfo != null &&
                    !Cache.instance.userInfo!!.recharge && Cache.instance.userStratResult != null
                    && Cache.instance.userStratResult?.isReviewPkg != null &&
                    Cache.instance.userStratResult?.isReviewPkg == false
                ) {
                    presented
                }

                changeFragment(page)
                changeButton(page)
                currentPageNum = page
                //记录用于消息提醒功能过滤使用
                Cache.instance.currentIndex = currentPageNum

                initButton()
            }
        })
    }

    private val presented: Unit
        get() {
            if (Cache.instance.userStratResult!!.isReviewPkg!!) return
            getPresented(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ExpCoinsBean>>() {
                override fun onSubscribe(d: Disposable) {
                    super.onSubscribe(d)
                }

                override fun onNext(bsBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ExpCoinsBean>) {
                    super.onNext(bsBaseBean)
                    if (bsBaseBean != null && bsBaseBean.code == 0) {
                        Cache.instance.presented = bsBaseBean.data
                    }
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                }
            })
        }

    @SuppressLint("CommitTransaction")
    private fun changeFragment(currentPageNum: Int) {
        if (isDestroy(this)) return
        if (currentPageNum < 0 || fragments.isEmpty()) return
        if (supportFragmentManager.isStateSaved) return

        //发送通知更改tab
        try {
            val historyFragments = supportFragmentManager.fragments

            if (!historyFragments
                    .contains(fragments[currentPageNum])
            ) {
                if (currentPageNum != this.currentPageNum && !fragments[this.currentPageNum].isStateSaved) {
                    supportFragmentManager.beginTransaction().hide(fragments[this.currentPageNum])
                        .commit()
                }
                if (!fragments[currentPageNum].isAdded && !fragments[currentPageNum].isStateSaved) {
                    supportFragmentManager.beginTransaction()
                        .add(R.id.contentSelect, fragments[currentPageNum])
                        .commit()
                    supportFragmentManager.beginTransaction().show(fragments[currentPageNum])
                        .commit()
                }
            } else {
                if (!fragments[currentPageNum].isStateSaved) {
                    if (currentPageNum != this.currentPageNum && !fragments[this.currentPageNum].isStateSaved) {
                        supportFragmentManager.beginTransaction()
                            .hide(fragments[this.currentPageNum]).commit()
                    }
                    supportFragmentManager.beginTransaction().show(fragments[currentPageNum])
                        .commit()
                }
            }

            if (currentPageNum == 3) {
                val myMineFragment = fragments[currentPageNum] as MineFragment
                if (myMineFragment.view != null) {
                    myMineFragment.requestUserInfo(getStringValue(SpKeyPool.USER_ID_DEVICE, "")!!)
                }
            }
            if (currentPageNum == 1) {
                val myMineFragment = fragments[currentPageNum] as FlashFragment
                if (myMineFragment.view != null) {
                    myMineFragment.updateUiData()
                }
            }
            EventBus.getDefault().post(TabSelectStatusChangeEvent(currentPageNum))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    private fun executeData() {
        MainImpl.coinsShop
       // MainImpl.getCoinsShop()

        MainImpl.oss

        MainImpl.giftList
        MainImpl.userInfo
        if (!Cache.instance.reviewPkg) {
//            LoadDialog loadDialog = new LoadDialog(this);
//            loadDialog.show();
            MainImpl.merge(object : MergeCallBack {
                override fun onSuccess() {
                    orderShowDialog()
                    initButton()
                    Cache.instance.allHomeRequest = true
                }

            })
        }
        execute {
            initCache()
            //上传归因数据
            putAf()
        }
    }

//    override fun onBackPressed() {
//        //禁止右滑退出
//    }

    private fun putAf() {
        if (!Cache.instance.AppsFlyerData.isNullOrEmpty()) {
            val appsFlyerArgs = com.juicy.common.model.bean.AdjustBean()
            appsFlyerArgs.setVer(MyBuildConfig.VERSION_NAME)
            if (null != Cache.instance.userInfo) {
                appsFlyerArgs.setUserId(Cache.instance.userInfo!!.userId)
            }
            //            appsFlyerArgs.setUtmSource("MEDIA_SOURCE");
//            appsFlyerArgs.setAdgroupId("ADGROUP_ID");
//            appsFlyerArgs.setAdset("ADSET");
//            appsFlyerArgs.setAdsetId("ADSET_ID");
//            appsFlyerArgs.setAfStatus("AF_STATUS");
//            appsFlyerArgs.setAgency("AF_AGENCY");
//            appsFlyerArgs.setAfChannel("AF_CHANNEL");
//            appsFlyerArgs.setCampaign("CAMPAIGN");
//            appsFlyerArgs.setCampaignId("CAMPAIGN_ID");
            appsFlyerArgs.setUtmSource(Cache.instance.IsNull(Constant.MEDIA_SOURCE)!!)
            appsFlyerArgs.setAdgroupId(Cache.instance.IsNull(Constant.ADGROUP_ID)!!)
            appsFlyerArgs.setAdset(Cache.instance.IsNull(Constant.ADSET)!!)
            appsFlyerArgs.setAdsetId(Cache.instance.IsNull(Constant.ADSET_ID)!!)
            appsFlyerArgs.setAfStatus(Cache.instance.IsNull(Constant.AF_STATUS)!!)
            appsFlyerArgs.setAgency(Cache.instance.IsNull(Constant.AF_AGENCY)!!)
            appsFlyerArgs.setAfChannel(Cache.instance.IsNull(Constant.AF_CHANNEL)!!)
            appsFlyerArgs.setCampaign(Cache.instance.IsNull(Constant.CAMPAIGN)!!)
            appsFlyerArgs.setCampaignId(Cache.instance.IsNull(Constant.CAMPAIGN_ID)!!)
            GiveAppsFlyerArgs(appsFlyerArgs, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
                override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                    super.onNext(t)
                    var result = ""
                    if (t?.data != null) {
                        result = t.data.toString()
                    }
                    Log.v("LOG_TAG", "submit af result$result")
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                }
            })
        }
        //归因上报
        if (!getBooleanVal(SpKeyPool.RESULT, false)!!) {
            val installRefererArgs = com.juicy.common.model.bean.AppInstallReferrerBean()
            installRefererArgs.setReferrerUrl(getStringValue(SpKeyPool.REFERRER_URL, ""))
            installRefererArgs.setReferrerClickTime(getLongValue(SpKeyPool.REFERRER_CLICK_TIME, 0L))
            installRefererArgs.setReferrerClickServerTime(
                getLongValue(
                    SpKeyPool.REFERRER_CLICK_SERVER_TIME,
                    0L
                )
            )
            installRefererArgs.setAppInstallTime(getLongValue(SpKeyPool.APP_INSTALL_TIME, 0L))
            installRefererArgs.setInstallVersion(getStringValue(SpKeyPool.INSTALL_VERSION, ""))
            installRefererArgs.setAppInstallServerTime(
                getLongValue(
                    SpKeyPool.APP_INSTALL_SERVER_TIME,
                    0L
                )
            )
            submitInstallReferer(
                installRefererArgs,
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
                    override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                        super.onNext(t)
                        if (t.data != null && t.data == true) {
                            putBooleanValue(SpKeyPool.RESULT, true)
                            Log.v("gy", "提交归因成功")
                        } else {
                            Log.v("gy", "提交归因失败")
                        }
                    }

                    override fun onError(e: Throwable) {
                        super.onError(e)
                    }
                })
        }
    }

    private var promotionCommodityShow = false
    private fun initButton() {
        if (currentPageNum == 1) {
            binding!!.filterCollapse.visibility = View.GONE
            binding!!.checkedSmall.visibility = View.GONE
            binding!!.xsPick.visibility = View.GONE
            promotionCommodityShow = false
        } else {
            if (null != Cache.instance.userPromotionGood
                && Cache.instance.userPromotionGood!!.surplusMillisecond > 0
            ) {
                if (Cache.instance.userInfo == null || Cache.instance.userInfo!!.availableCoins <= 150) {
                    val remainMilliseconds =
                        Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
                    if (remainMilliseconds > 0) {
                        binding!!.filterCollapse.visibility = View.VISIBLE
                        promotionCommodityShow = true
                        if (proTimeThread == null) {
                            setProTimeThread()
                        }
                    } else {
                        binding!!.filterCollapse.visibility = View.GONE
                        promotionCommodityShow = false
                    }
                } else {
                    binding!!.filterCollapse.visibility = View.GONE
                    promotionCommodityShow = false
                }
            } else {
                binding!!.filterCollapse.visibility = View.GONE
                promotionCommodityShow = false
            }

            if (null != Cache.instance.salePromotionGood && Cache.instance.salePromotionGood!!.remainMilliseconds > 0) {
                val remainMilliseconds =
                    Cache.instance.salePromotionGood!!.remainMilliseconds - (System.currentTimeMillis() - Cache.instance.salePromotionCreateTime)
                if (remainMilliseconds > 0) {
                    binding!!.checkedSmall.visibility = View.VISIBLE
                    if (activityTimeThread == null) {
                        setActivityTimeThread()
                    }
                } else {
                    binding!!.checkedSmall.visibility = View.GONE
                }
            } else {
                binding!!.checkedSmall.visibility = View.GONE
            }
            binding!!.xsPick.visibility = View.VISIBLE
        }


        if (null != Cache.instance.userPromotionGood && (Cache.instance.userPromotionGood?.surplusMillisecond
                ?: 0) > 0 && (Cache.instance.userInfo == null || (Cache.instance.userInfo?.availableCoins
                ?: 0) <= 150)
        ) {
        } else {
            binding!!.loadedFront.visibility = View.GONE
        }

        if (null != Cache.instance.salePromotionGood && (Cache.instance.salePromotionGood?.remainMilliseconds
                ?: 0) > 0
        ) {
        } else {
            binding!!.clockXl.visibility = View.GONE
        }
    }

    private fun orderShowDialog() {
        if (Cache.instance.reviewPkg) return
        try {
            if (null != Cache.instance.guide &&
                !Cache.instance.guideHasShow && Cache.instance.userStratResult != null &&
                Cache.instance.userStratResult!!.isSwitchStrongGuide!!
            ) {
                showOrderGuide()
            } else {
                Cache.instance.guideHasShow = true
            }

            if (Cache.instance.guideHasShow
                && null != Cache.instance.presented && null != Cache.instance.presented?.coins && (Cache.instance.presented?.coins?:0) > 0
                && !Cache.instance.presentedHasShow
            ) {
                //TODO: 预留新用户奖励处理逻辑
            } else {
                Cache.instance.presentedHasShow = true
            }


            if (Cache.instance.guideHasShow
                && Cache.instance.presentedHasShow
                && Cache.instance.userPromotionGood != null && Cache.instance.userPromotionGood!!.surplusMillisecond > 0 && (!Cache.instance.userPromotionHasShow || Cache.instance.allHomeRequest)
            ) {
                //弹出新用户促销弹框
                if (Cache.instance.userPromotionResetShow) {
                    Cache.instance.userPromotionResetShow = false
                    showOrderPro()
                }
            }
            if (Cache.instance.userPromotionGood == null) {
                Cache.instance.userPromotionHasShow = true
            } else {
                setPromotion(true)
            }

            if (Cache.instance.guideHasShow
                && Cache.instance.presentedHasShow
                && Cache.instance.userPromotionHasShow
                && Cache.instance.salePromotionGood != null && Cache.instance.salePromotionGood!!.remainMilliseconds > 0 && (!Cache.instance.saleActivityHasShow || Cache.instance.allHomeRequest)
            ) {
                //弹出活动促销弹框
                if (Cache.instance.saleActivityResetShow) {
                    Cache.instance.saleActivityResetShow = false
                    showOrderSales()
                }
            }
            if (Cache.instance.salePromotionGood == null) {
                Cache.instance.saleActivityHasShow = true
            } else {
                setActivityDialog()
            }

            if (Cache.instance.guideHasShow
                && Cache.instance.presentedHasShow
                && Cache.instance.userPromotionHasShow
                && Cache.instance.saleActivityHasShow
            ) {
                //弹出订阅弹框
                showSubscribe()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    private fun setActivityDialog() {
        if (this == null) return
        //        if (Cache.getInstance().salePromotionGood.getActivitySmallPic() != null) {
//            Glide.with(this)
//                    .load(Cache.getInstance()
//                            .salePromotionGood
//                            .getActivitySmallPic())
//                    .into(binding.enabledSmall);
//        }
        if (Cache.instance.salePromotionGood!!.remainMilliseconds != null) {
            setActivityTimeThread()
        }
    }

    fun setActivityTimeThread() {
        if (Cache.instance.salePromotionGood != null && Cache.instance.salePromotionGood!!.remainMilliseconds != null) {
            if (activityTimeThread != null) {
                activityTimeThread!!.cancel()
            }
            val remainMilliseconds =
                Cache.instance.salePromotionGood!!.remainMilliseconds - (System.currentTimeMillis() - Cache.instance.salePromotionCreateTime)
            if (remainMilliseconds <= 0) {
                Cache.instance.salePromotionGood = null
                initButton()
                return
            }
            activityTimeThread = object : CountDownTimer(remainMilliseconds, 1000) {
                override fun onTick(l: Long) {
                    if (Cache.instance.salePromotionGood == null) return
                    val remainMilliseconds =
                        Cache.instance.salePromotionGood!!.remainMilliseconds - (System.currentTimeMillis() - Cache.instance.salePromotionCreateTime)
                    binding!!.nearThick.text = ms2HMS(remainMilliseconds)
                    if (abs((l - remainMilliseconds).toDouble()) > 1000) {
                        setActivityTimeThread()
                    }
                }

                override fun onFinish() {
                    Cache.instance.salePromotionGood = null
                    initButton()
                }
            }.start()
        }
    }

    private fun showOrderPro() {
        binding!!.loadedFront.visibility = View.VISIBLE
        binding!!.loadedFront.initData()

        //        NewUserSaleDialog newUserSaleDialog = new NewUserSaleDialog();
//        newUserSaleDialog.show(getSupportFragmentManager(), "");

//        newUserSaleDialog.setDismiss(new NewUserSaleDialog.DismissCallBack() {
//            @Override
//            public void onDismiss() {
//                if (Cache.getInstance().salePromotionGood != null) {
//                    showOrderSales();
//                } else {
//                    showSubscribe();
//                }
//            }
//        });
    }

    private fun showOrderSales() {
        binding!!.clockXl.visibility = View.VISIBLE
        binding!!.clockXl.initData()

        //        salesGoodDialog = new NewSalesGoodDialog();
//        salesGoodDialog.show(getSupportFragmentManager(), "");
    }

   private fun showSubscribe() {
        if (Cache.instance.userInfo != null) {
            if (Cache.instance.getShowSubcribe()) {
                if (Cache.instance.subscribeGood != null) {
                    val evaluateDialog = VipDialogFragment()
                    val currentActivity = currentActivity as CoreActivity?
                    if (currentActivity != null && !isDestroy(currentActivity) && !evaluateDialog.isStateSaved) {
                        evaluateDialog.show(currentActivity.supportFragmentManager, "")
                    }
                } else {
                    coinsShop
                }
            }
        } else {
            subscribeGetUserInfo()
        }
    }

    private fun subscribeGetUserInfo() {
        getUserInfo(
            getStringValue(SpKeyPool.USER_ID_DEVICE, "")!!,
            object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                    super.onNext(t)
                    if (t.data != null) {
                        Cache.instance.userInfo = t.data
                        showSubscribe()
                    }
                }

                override fun onError(e: Throwable) {
                    if (Cache.instance.userInfo != null) {
                        showSubscribe()
                    }
                    super.onError(e)
                }
            })
    }

    val coinsShop: Unit
        get() {
            getCoinGoodsSearch(
                ActivityUtils.getTopActivity(),
                PlayParamsBean(true),
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>() {
                    override fun onSubscribe(d: Disposable) {
                        super.onSubscribe(d)
                    }

                    override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>) {
                        super.onNext(t)

                        if (t.isOk) {
                            if (!t.data.isNullOrEmpty()) {
                                val coinGoods: MutableList<com.juicy.common.model.bean.ActivityInfoBean> =
                                    ArrayList()
                                for (i in t.data!!.indices) {
                                    val bhBean = t.data!![i]
                                    if (bhBean.type == "1") { //订阅
                                        Cache.instance.subscribeGood =
                                            bhBean
                                    } else {
                                        coinGoods.add(bhBean)
                                    }
                                }
                                Cache.instance.coinGoods = coinGoods
                                PayUtils(ActivityUtils.getTopActivity()).batchQueryPurchases()
                                if (Cache.instance.subscribeGood != null) {
                                    showSubscribe()
                                }
                            }
                        }
                    }

                    override fun onError(e: Throwable) {
                        if (Cache.instance.subscribeGood != null) {
                            showSubscribe()
                        }
                    }

                    override fun onComplete() {
                        super.onComplete()
                    }
                })
        }

    private fun showOrderGuide() {
        val dialog = DerivativeDialog()
        dialog.setDismiss(object :DerivativeDialog.DismissCallBack {
            override fun onDismiss() {
                Cache.instance.guideHasShow = true
                orderShowDialog()
            }

        })
        if (!dialog.isStateSaved) {
            dialog.show(supportFragmentManager, "")
        }
    }


    override fun initData() {
        Cache.instance.payChannle
        socketEventBusBack = object : SocketEventBusBack {
            override fun call(appOnCallBean: AppOnCallBean?) {
                super<SocketEventBusBack>.call(appOnCallBean)
                throttle("onCall_alter", 100,
                    {
                        if (currentActivity !is VideoCallActivity) {
                            val intent = Intent(
                                this@CoreActivity,
                                VideoCallActivity::class.java
                            )
                            intent.putExtra(Constant.ANCHOR_ID, appOnCallBean!!.fromUserId)
                            intent.putExtra(Constant.CHANNEL_NAME, appOnCallBean.channelName)
                            intent.putExtra(Constant.RTC_TOKEN, appOnCallBean.rtcToken)
                            intent.putExtra(Constant.CALL_STATUS, Constant.BE_CALL)
                            intent.putExtra(Constant.IS_FREE, appOnCallBean.isFree)
                            startActivity(intent)
                        }
                    })
            }

            override fun rechargeSuccessBack(appRechargeOrderStatusBean: AppRechargeOrderStatusBean?) {
                if (appRechargeOrderStatusBean!!.status == 2) {
                    Cache.instance.payChannle
                    super<SocketEventBusBack>.rechargeSuccessBack(appRechargeOrderStatusBean)
                    if (Cache.instance.webViewActivity != null) {
                        Cache.instance.webViewActivity!!.refreshAgentWeb()
                    }
                    if (null != appRechargeOrderStatusBean.orderNo && null != appRechargeOrderStatusBean.code && !Cache.instance.thirdPayGRLogMap.isEmpty()) {
                        if (Cache.instance.thirdPayGRLogMap.containsKey(
                                appRechargeOrderStatusBean.orderNo
                            )
                        ) {
                            val good =
                                Cache.instance.thirdPayGRLogMap[appRechargeOrderStatusBean.orderNo]
                            PayUtils(this@CoreActivity).purchaseFinishedEvent(
                                good!!.price.toString(),
                                appRechargeOrderStatusBean.orderNo
                            )
                        }
                    }
                    binding!!.clockXl.visibility = View.GONE
                    checkCoins()
                    netOfferInterface2()
                    coinsShop
                    val fragment = getFragment(3) as MineFragment?
                    fragment!!.loadBanner()
                    initButton()
                }
            }
        }

        addMessageEvent(socketEventBusBack!!)
        Cache.instance.isCurrentHomePage = true
        executeData()
        if (Cache.instance.userInfo != null) {
            if (Cache.instance.userInfo!!.availableCoins != null) {
                coins = Cache.instance.userInfo!!.availableCoins
            } else {
                checkCoins()
            }
        }
    }

    private val isShowMatch: Boolean
        //是否显示匹配入口
        get() {
            return if (Cache.instance.reviewPkg) {
                true
            } else {
                if ((Cache.instance.userStratResult != null && Cache.instance.userStratResult?.flashChatConfig
                    != null && Cache.instance.userStratResult?.flashChatConfig?.isSwitch == true)) {
                    true
                } else {
                    false
                }
            }
        }


    override fun initView() {
//        if (Cache.getInstance().reviewPkg) {
//            binding.xsPick.setVisibility(View.GONE);
//        }

        binding!!.root.post(object : Runnable {
            override fun run() {
                if (isDestroy(mContext as Activity?) || binding == null) return
                if (supportFragmentManager.isStateSaved) return
                initFragment()
                initClick()
                binding!!.narrowAvatar.visibility = if (isShowMatch) View.VISIBLE else View.GONE

                binding!!.loadedFront.setDismiss(object :NewUserDialogView.DismissCallBack {
                    override fun onDismiss() {
                        binding!!.loadedFront.visibility = View.GONE
                        Cache.instance.userPromotionHasShow = true
                        orderShowDialog()
                    }

                })

                binding!!.clockXl.setDismiss(object :NewGoodDialogView.DismissCallBack {
                    override fun onDismiss() {
                        binding!!.clockXl.visibility = View.GONE
                        Cache.instance.saleActivityHasShow = true
                        orderShowDialog()
                    }

                })
            }
        })


        binding!!.warningUp.setText(LanguageManager.instance?.getLocalTranslate("Store"));
    }

    override fun onClick(view: View) {



        if (view === binding!!.home) {
            Cache.instance.isCurrentHomePage = true
            changePage(0)
        } else if (view === binding!!.narrowAvatar) {
            Cache.instance.isCurrentHomePage = false
            changePage(1)
        } else if (view === binding!!.message) {
            Cache.instance.isCurrentHomePage = false
            changePage(2)
        } else if (view === binding!!.miniLocked) {
            Cache.instance.isCurrentHomePage = false
            changePage(3)
        } else if (view === binding!!.checkedSmall) {
            if (isFastClick) return  // 防抖处理

            showOrderSales()
        } else if (view === binding!!.filterCollapse) {
            if (isFastClick) return  // 防抖处理

            showOrderPro()
            //            NewUserSaleDialog userSaleDialog = new NewUserSaleDialog();
//            userSaleDialog.show(getSupportFragmentManager(), "");
        } else if (view === binding!!.xsPick) {
            if (isFastClick) return  // 防抖处理

            val dialog = FullShoppingDialog("match_float")
            if (!dialog.isStateSaved) {
                dialog.show(supportFragmentManager, "")
            }
        }
    }

    override fun onResume() {
        super.onResume()

        resetRequest()
    }

    private fun resetRequest() {
        if (Cache.instance.userInfo != null && Cache.instance.userInfo!!.availableCoins <= 150) {
            requestPromotion()
        }

        netOfferInterface2()

        initButton()

        checkCoins()
    }

    private fun setPromotion(isShow: Boolean) {
        if (isShow) {
            try {
                initButton()
                if (Cache.instance.userPromotionGood!!.surplusMillisecond != null) {
                    setProTimeThread()
                }
            } catch (e: Exception) {
            }
        } else {
            try {
                if (proTimeThread != null) {
                    proTimeThread!!.cancel()
                    proTimeThread = null
                }
                Cache.instance.userPromotionGood = null
                initButton()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun setProTimeThread() {
        if (proTimeThread != null) {
            proTimeThread!!.cancel()
            proTimeThread = null
        }
        if (Cache.instance.userPromotionGood == null) {
            return
        }
        val remainMilliseconds =
            Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
        if (remainMilliseconds <= 0) {
            Cache.instance.userPromotionGood = null
            initButton()
            return
        }
        proTimeThread = object : CountDownTimer(remainMilliseconds, 1000) {
            override fun onTick(l: Long) {
                try {
                    if (null != Cache.instance.userPromotionGood) {
                        val remainMilliseconds =
                            Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
                        binding!!.avatarTitle.text = ms2HMS(remainMilliseconds)
                        if (Cache.instance.userInfo != null) {
                            if (!promotionCommodityShow && Cache.instance.userInfo!!.availableCoins <= 150) {
                                initButton()
                            } else if (promotionCommodityShow && Cache.instance.userInfo!!.availableCoins > 150) {
                                initButton()
                            }
                        }
                        if (abs((l - remainMilliseconds).toDouble()) > 1000) {
                            setProTimeThread()
                        }
                    } else {
                        if (proTimeThread != null) {
                            proTimeThread?.cancel()
                            proTimeThread = null
                        }

                        initButton()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    if (proTimeThread != null) {
                        proTimeThread?.cancel()
                        proTimeThread = null
                    }
                    Cache.instance.userPromotionGood = null
                    initButton()
                }
            }

            override fun onFinish() {
                Cache.instance.userPromotionGood = null
                initButton()
            }
        }.start()
    }

    private fun setActivityCommodity(isShow: Boolean) {
        if (activityTimeThread != null) {
            activityTimeThread!!.cancel()
            activityTimeThread = null
        }
        if (isShow) {
            initButton()
            setActivityDialog()
        } else {
            Cache.instance.salePromotionGood = null
            initButton()
            if (activityTimeThread != null) {
                activityTimeThread?.cancel()
            }
        }
    }

    private fun requestPromotion() {
        if (!Cache.instance.allHomeRequest) {
            return
        }
        if (isRequestUserPromotionGood) return
        isRequestUserPromotionGood = true
        getGoodsPromotion(PlayParamsBean(false), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ActivityInfoBean>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(bhBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ActivityInfoBean>) {
                super.onNext(bhBaseBean)
                isRequestUserPromotionGood = false
                if (bhBaseBean.isOk) {
                    if (bhBaseBean.data != null) {
                        if (Cache.instance.userPromotionGood == null || Cache.instance.userPromotionGood!!.code != bhBaseBean.data!!.code) {
                            Cache.instance.userPromotionResetShow = true
                        }
                        Cache.instance.userPromotionGood = bhBaseBean.data
                        Cache.instance.userPromotionCreateTime = System.currentTimeMillis()
                        setPromotion(true)
                        orderShowDialog()
                    }
                }
            }

            override fun onError(e: Throwable) {
                isRequestUserPromotionGood = false
            }

            override fun onComplete() {
                super.onComplete()
            }
        })
    }

    private fun finishTimer() {
        if (proTimeThread != null) {
            proTimeThread!!.cancel()
            proTimeThread = null
        }
        if (activityTimeThread != null) {
            activityTimeThread!!.cancel()
            activityTimeThread = null
        }
    }

    override fun onDestroy() {
       try {
           finishTimer()
           socketEventBusBack?.let {
               removeMessageEvent(it)
           }
           super.onDestroy()
           Log.i("ygt", "onDestroy")
       }catch (e:Exception){}
    }

    private fun checkCoins() {
        getCoin(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>) {
                super.onNext(t)
                if (t?.data?.availableCoins?.toInt() != 0) {
                    if (coins != (t.data?.availableCoins?:-1).toInt()) {
                        coins = t.data?.availableCoins?.toInt()?:-1
                        if (coins < 150) {
                            requestPromotion()
                        }
                    }
                }
                initButton()
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }


    private fun initSocketIO() {
        runDelayOnUIThread({
            SocketUtils.initAndConnect()
        }, 2000)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPayEvent(userDialogEvent: PayResultEvent) {
        if (userDialogEvent.isSuccess) {
            Cache.instance.payChannle
        }
        resetRequest()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLanguageEvent(languageItemBean: com.juicy.common.model.bean.LanguageItemBean?) {
        Cache.instance.currentFirstTab = 0
        Cache.instance.currentSecondTab = 0

        val intent = Intent(
            applicationContext,
            CoreActivity::class.java
        )
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        startActivity(intent)
        finishAffinity()
    }

    @SuppressLint("CheckResult")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(myCoinsNumMessage: com.juicy.common.model.bean.MyCoinsNumMessage?) {
        //        if (Cache.getInstance().fullShoppingDialog != null) {
//            if (!Cache.getInstance().fullShoppingDialog.isPerseronShow) {
//                Cache.getInstance().fullShoppingDialog.dismissAllowingStateLoss();
//            }
//        }

        RetrofitManage
            .instance
            .create(Service::class.java)
            .getUserInfo(UserIdParamsBean(getStringValue(SpKeyPool.USER_ID_DEVICE, "")!!))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ azBeanNetBean ->
                if (azBeanNetBean.data != null) {
                    Cache.instance.userInfo = azBeanNetBean.data
                    if (fragments.size >= 3) {
                        val myMineFragment = fragments[3] as MineFragment
                        myMineFragment.initUserInfo(Cache.instance.userInfo!!)
                    }
                }
            }, { })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(newUserRewardsShowStatusChangeEvent: NewUserRewardsShowStatusChangeEvent?) {
        setPromotion(false)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(callEvent: CallEvent) {
        CallEventDealWith(callEvent)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onUpdateCoins(coinsEvent: CoinsNumChangeEvent?) {
        if (Cache.instance.userInfo != null && Cache.instance.userInfo!!.availableCoins <= 150) {
            requestPromotion()
        }
        checkCoins()
    }

    private fun CallEventDealWith(callEvent: CallEvent) {
        this.callEvent = callEvent
        requestPromotion()
        if (!Cache.instance.reviewPkg && !callEvent.isMatch20Out) {
            if (Cache.instance.userInfo!!.recharge != null && Cache.instance.userInfo!!.recharge) {
            } else if (callEvent.isAllJoin) { // 延迟100毫秒执行
                val handler = Handler(Looper.getMainLooper())
                handler.postDelayed(Runnable {
                    val activity: FragmentActivity =
                        ActivityUtils.getTopActivity() as FragmentActivity
                            ?: return@Runnable
                    val anchorShoppingDialog =
                        AnchorShoppingDialog(callEvent.nickName, callEvent.anchorPhoto)
                    if (!anchorShoppingDialog.isStateSaved) {
                        anchorShoppingDialog.show(activity.supportFragmentManager, "")
                    }
                }, 100)
            }
        }
        MainImpl.getResult(callEvent.channelName, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorWallTagBean>>() {
            @SuppressLint("CheckResult")
            override fun onNext(anchorWallTagBeanNetResponseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorWallTagBean>) {
                super.onNext(anchorWallTagBeanNetResponseBean)
                try {
                    if (anchorWallTagBeanNetResponseBean.data != null) {
                        val anchorInfo = anchorWallTagBeanNetResponseBean.data
                        if (!Cache.instance.reviewPkg) {
                            if (callEvent.callType == Constant.OUTGOING_CALL
                                || callEvent.callType == Constant.INCOMING_CALL
                            ) {
                                val activity: FragmentActivity =
                                    ActivityUtils.getTopActivity() as FragmentActivity
                                        ?: return

                                if (!getBooleanVal(
                                        SpKeyPool.HAS_EVALUATE,
                                        false
                                    )!! && !callEvent.isMatch20Out
                                ) {
                                    putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
                                    val assessDialog = AssessDialog()
                                    if (!assessDialog.isStateSaved) {
                                        assessDialog.show(activity.supportFragmentManager, "")
                                    }
                                }
                                //充值用户
                                if (Cache.instance.userInfo?.recharge != null && Cache.instance.userInfo?.recharge == true
                                    && callEvent.isAllJoin) {
                                    val evaluateDialog = EvaluateDialog(
                                        anchorInfo,
                                        callEvent.nickName,
                                        callEvent.anchorPhoto
                                    )
                                    if (!evaluateDialog.isStateSaved) {
                                        evaluateDialog.show(activity.supportFragmentManager, "")
                                    }
                                }
                            }
                        }
                        if (callEvent.anchorPhoto != null && callEvent.channelName != null && !callEvent.anchorPhoto!!.isEmpty() && !callEvent.channelName!!.isEmpty()) {
                            val user = User(
                                callEvent.callType!!,
                                anchorInfo!!.duration.toString(),
                                System.currentTimeMillis().toString(),
                                callEvent.anchorPhoto!!,
                                callEvent.nickName!!, callEvent.userId!!
                            )
                            execute {
                                CallDatabase.instance
                                    ?.userDao()
                                    ?.insertUser(user)
                            }
                        } else {
                            val cacheUser = getCacheUserInfo(callEvent.userId!!)
                            if (cacheUser != null) {
                                val user = User(
                                    callEvent.callType!!,
                                    anchorInfo!!.duration.toString(),
                                    System.currentTimeMillis().toString(),
                                    cacheUser.avatarUrl,
                                    cacheUser.nickname, callEvent.userId!!
                                )
                                execute {
                                    CallDatabase.instance
                                        ?.userDao()
                                        ?.insertUser(user)
                                }
                            } else {
                                RetrofitManage
                                    .instance
                                    .create(Service::class.java)
                                    .getUserInfo(UserIdParamsBean(callEvent.userId!!))
                                    .subscribeOn(Schedulers.io())
                                    .observeOn(AndroidSchedulers.mainThread())
                                    .subscribe({ userDate ->
                                        if (userDate.data != null) {
                                            val user = User(
                                                callEvent.callType!!,
                                                anchorInfo!!.duration.toString(),
                                                System.currentTimeMillis().toString(),
                                                userDate.data!!.avatarUrl,
                                                userDate.data!!.nickname, callEvent.userId!!
                                            )
                                            execute {
                                                CallDatabase.instance
                                                    ?.userDao()
                                                    ?.insertUser(user)
                                            }
                                        }
                                    }, { })
                            }
                        }
                    } else {
                        callInsertError(callEvent)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                callInsertError(callEvent)
            }
        })
        MainImpl.submitLog(callEvent.logList)
    }

    fun callInsertError(callEvent: CallEvent) {
        if (callEvent.anchorPhoto != null && callEvent.channelName != null && !callEvent.anchorPhoto!!.isEmpty() && !callEvent.channelName!!.isEmpty()) {
            val user = User(
                callEvent.callType!!,
                0.toString(),
                System.currentTimeMillis().toString(),
                callEvent.anchorPhoto!!,
                callEvent.nickName!!, callEvent.userId!!
            )
            execute {
                CallDatabase.instance
                    ?.userDao()
                    ?.insertUser(user)
            }
        } else {
            val cacheUser = getCacheUserInfo(callEvent.userId!!)
            if (cacheUser != null) {
                val user = User(
                    callEvent.callType!!,
                    0.toString(),
                    System.currentTimeMillis().toString(),
                    cacheUser.avatarUrl,
                    cacheUser.nickname, callEvent.userId!!
                )
                execute {
                    CallDatabase.instance
                        ?.userDao()
                        ?.insertUser(user)
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(userDialogEvent: DialogShowStatusEvent) {
        if (userDialogEvent.isShow) {
//            if (Cache.getInstance().reviewPkg) return;
            Cache.instance.saleActivityResetShow = true
            netOfferInterface2()
        } else {
            setActivityCommodity(false)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onUpdateUser(userInfoEvent: RegEditUserEvent) {
        RongUserInfoManager.getInstance().refreshUserInfoCache(
            UserInfo(
                userInfoEvent.userId,
                userInfoEvent.nickname,
                Uri.parse(userInfoEvent.avatarUrl)
            )
        )
        RongUserInfoManager.getInstance().setUserInfoProvider(UserInfoProvider(), true)
    }

    fun netOfferInterface2() {
        if (!Cache.instance.allHomeRequest) {
            return
        }
        //        if (Cache.getInstance().reviewPkg) return;
        if (isRequestActivityGood) return
        isRequestActivityGood = true
        getLastSpecialOffer(com.juicy.common.model.bean.GoodsParamsBean(false), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>() {
            override fun onNext(listBaseBean: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>) {
                super.onNext(listBaseBean)
                isRequestActivityGood = false
                if (listBaseBean.isOk && listBaseBean.data!!.isEmpty()) {
                    setActivityCommodity(false)
                }

                if (listBaseBean.data != null && !listBaseBean.data!!.isEmpty()) {
                    if (listBaseBean.data!![0] != null) {
                        Cache.instance.salePromotionGood = listBaseBean.data!![0]
                        Cache.instance.salePromotionCreateTime = System.currentTimeMillis()
                        putIntValue(
                            SpKeyPool.MY_SA_NUM,
                            Cache.instance.salePromotionGood!!.capableRechargeNum!!
                        )

                        setActivityCommodity(true)
                        orderShowDialog()
                    }
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                isRequestActivityGood = false
            }
        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(userDialogEvent: UnReadCount?) {
        totalCount
    }

    @SuppressLint("CheckResult")
    private fun initRongIM() {
        init(application, object :ConnectCallBack {
            override fun connectSuccessful() {
                val myMessages = ArrayList<Class<out MessageContent?>>()
                myMessages.add(HLinkMsg::class.java)
                myMessages.add(NFMsg::class.java)
                myMessages.add(SJMsg::class.java)
                RongIMClient.registerMessageType(myMessages)
                RongIM.registerMessageTemplate(HyperLinkMessage())
                RongIM.registerMessageTemplate(SingleJsonMessage())
                val providerManager =
                    RongConfigCenter.conversationListConfig().providerManager //获取会话模板管理器
                providerManager.replaceProvider(
                    PrivateConversationProvider::class.java,
                    CustomConversationProvider()
                ) //用自定义模板替换 SDK 原有模板

                RouteUtils.registerActivity(
                    RouteUtils.RongActivityType.ConversationActivity,
                    MessageActivity::class.java
                )
                RongUserInfoManager.getInstance().setUserInfoProvider(UserInfoProvider(), false)

                RongConfigCenter.conversationConfig().replaceMessageProvider(
                    BaseMessageItemProvider::class.java,
                    CustomMessageItemProvider()
                )
                RongConfigCenter.conversationConfig()
                    .replaceMessageProvider(TextMessageItemProvider::class.java, MyTextMessage())
                //            RongConfigCenter.conversationConfig().replaceMessageProvider(VoiceMessageItemProvider.class, new MyDestructVoiceMessage());
//            RongConfigCenter.conversationConfig().replaceMessageProvider(HQVoiceMessageItemProvider.class, new MyDestructHQVoiceMessage());
                RongConfigCenter.conversationConfig().replaceMessageProvider(
                    HQVoiceMessageItemProvider::class.java,
                    CustomDestructHQVoiceMessage()
                )
                RongConfigCenter.conversationConfig()
                    .replaceMessageProvider(ImageMessageItemProvider::class.java, MyImageMessage())
                RongConfigCenter.conversationConfig()
                    .replaceMessageProvider(VoiceMessageItemProvider::class.java, MyVoiceMessage())
                RongConfigCenter.conversationConfig()
                    .replaceMessageProvider(FileMessageItemProvider::class.java, MyFileMessage())
                RongConfigCenter.conversationConfig().replaceMessageProvider(
                    RecallNotificationMessageItemProvider::class.java, MyRecallNotificationMessage()
                )
                RongExtensionManager.getInstance().extensionConfig = MyExtensionConfig()


                // 过滤掉不需要展示在会话列表的官方账号
                val enableCheck = true // 关闭消息排重
                RongCoreClient.getInstance().setCheckDuplicateMessage(enableCheck)
                RongConfigCenter.conversationListConfig()
                    .setDataProcessor(object : BaseDataProcessor<Conversation>() {
                        override fun filtered(list: List<Conversation>): List<Conversation> {
                            return list.stream().filter { it: Conversation ->
                                if (Cache.instance.reviewPkg) {
                                    RongCoreClient.getInstance().setCheckDuplicateMessage(enableCheck)
                                    //审核模式过滤掉审核黑名单账号、客服账号
                                    if (Cache.instance.userStratResult != null && it.targetId != null && Cache.instance.userStratResult!!.reviewOfficialBlacklistUserIds != null &&
                                        Cache.instance.userStratResult!!.reviewOfficialBlacklistUserIds!!.contains(
                                            it.targetId
                                        )
                                        || (Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.userServiceAccountId != null &&
                                                Cache.instance.userStratResult!!.userServiceAccountId == it.targetId)
                                    ) {
                                        return@filter false
                                    }
                                }
                                if (Cache.instance.userStratResult != null && it.targetId != null && Cache.instance.userStratResult!!.officialBlacklistUserIds != null &&
                                    !Cache.instance.userStratResult!!.officialBlacklistUserIds!!.contains(
                                        it.targetId
                                    )
                                ) {
                                    return@filter true
                                } else {
                                    return@filter false
                                }
                            }.collect(Collectors.toList<Conversation>())
                        }
                    })
            }

        })

        getRongcloudToken(object : RongTokenCallBack {
            override val successful: Unit
                get() {
                    RongConnect()
                }

            override val unSuccessful: Unit
                get() {
                    RongConnect()
                }
        })

        if (!getStringValue(
                SpKeyPool.RCK_KEY,
                ""
            )!!.isEmpty() && !getStringValue(SpKeyPool.MY_RTC_KEY, "")!!
                .isEmpty()
        ) {
        } else {
            RetrofitManage
                .instance
                .create(Service::class.java)
                .getUserInfo(UserIdParamsBean(getStringValue(SpKeyPool.USER_ID_DEVICE, "")!!))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(Consumer<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>> { userDate ->
                    if (userDate.data != null) {
                        Cache.instance.userInfo = userDate.data
                        putStringValue(
                            SpKeyPool.MY_RTC_KEY,
                            Cache.instance.userInfo!!.rongcloudToken
                        )
                        //                                if (!SpSaveUtil.getStringValue(SpKeyPool.RCK_KEY, "").isEmpty() && !SpSaveUtil.getStringValue(SpKeyPool.RTC_COULD, "").isEmpty()) {
                        //                                    RongUtil.init(getApplication(), () -> {
                        //                                        ArrayList<Class<? extends MessageContent>> myMessages = new ArrayList<>();
                        //                                        myMessages.add(HyperLinkMsg.class);
                        //                                        myMessages.add(NoneFlagMsg.class);
                        //                                        myMessages.add(SingleJsonMsg.class);
                        //                                        RongIMClient.registerMessageType(myMessages);
                        //                                        RongIM.registerMessageTemplate(new HyperLinkMessage());
                        //                                        RongIM.registerMessageTemplate(new SingleJsonMessage());
                        //                                        ProviderManager<BaseUiConversation> providerManager = RongConfigCenter.conversationListConfig().getProviderManager(); //获取会话模板管理器
                        //                                        providerManager.replaceProvider(PrivateConversationProvider.class, new CustomConversationProvider()); //用自定义模板替换 SDK 原有模板
                        //                                        RouteUtils.registerActivity(RouteUtils.RongActivityType.ConversationActivity, MessageActivity.class);
                        //                                        RongUserInfoManager.getInstance().setUserInfoProvider(new UserInfoProvider(), true);
                        //                                        RongConfigCenter.conversationConfig().replaceMessageProvider(TextMessageItemProvider.class, new MyTextMessage());
                        //                                    });
                        //                                }
                    }
                }, Consumer<Throwable> { })
        }
    }

    @SuppressLint("CheckResult")
    private fun uploadRisk() {
        var afterTime = 5000
        if (Cache.instance.appconfig == null || Cache.instance.appconfig!!.riskConfigBean == null) {
            return
        }
        if (Cache.instance.appconfig!!.riskConfigBean != null && Cache.instance.appconfig!!.riskConfigBean!!.k_interval > 0) {
            afterTime = Cache.instance.appconfig!!.riskConfigBean!!.k_interval * 1000
        }
        runDelayOnUIThread({
            val uploadRiskString = safeInfoString
            if (!TextUtils.isEmpty(uploadRiskString)) {
                val bean = com.juicy.common.model.bean.InfoItemBean()
                bean.info = uploadRiskString

                RetrofitManage.instance.create(Service::class.java)
                    .uploadRisk(bean)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe({ upReslut ->
                        if (upReslut.data != null && upReslut.isOk) {
                            val isSuccess = upReslut.data
                            if (isSuccess!!) {
                            }
                        } else {
                        }
                    }, {error ->
                        Log.e("MainAc","error$error")
                    })
            }
        }, afterTime.toLong())
    }

    private fun getFragment(position: Int): BaseFragment? {
        if (position >= fragments.size) {
            return null
        }

        if (fragments[position] is WallFirstFragment) {
            return fragments[position]
        }


        if (fragments[position] is MessageFragment) {
            return fragments[position]
        }


        if (fragments[position] is MineFragment) {
            return fragments[position]
        }

        return null
    }

    private val totalCount: Unit
        get() {
            RongIMClient.getInstance()
                .getTotalUnreadCount(object : RongIMClient.ResultCallback<Int>() {
                    override fun onSuccess(integer: Int) {
                        if (isDestroy(mContext as Activity?)) return
                        val fragment = getFragment(2) as MessageFragment?
                        //设置未读消息数
                        if (Cache.instance.reviewPkg) {
                            if (isFirst) {
                                startNum = integer
                                isFirst = false
                            }
                            Cache.instance.unreadCount = integer
                            Cache.instance.unreadCount -= startNum

                            //减去指定账号的未读数unrea
                            //审核模式过滤掉审核黑名单账号、客服账号
                            RongIMClient.getInstance().getConversationList(object :
                                RongIMClient.ResultCallback<List<Conversation>>() {
                                override fun onSuccess(conversations: List<Conversation>) {
                                    if (isDestroy(mContext as Activity?)) return

                                    for (conversation in conversations) {
                                        if (Cache.instance.userStratResult != null &&
                                            conversation.targetId == Cache.instance.userStratResult!!.userServiceAccountId
                                        ) {
                                            Cache.instance.unreadCount -= conversation.unreadMessageCount
                                            continue
                                        }
                                        if (Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.reviewOfficialBlacklistUserIds != null && conversation.targetId != null &&
                                            Cache.instance.userStratResult!!.reviewOfficialBlacklistUserIds!!.contains(
                                                conversation.targetId
                                            )
                                        ) {
                                            Cache.instance.unreadCount -= conversation.unreadMessageCount
                                            continue
                                        }
                                    }

                                    if (Cache.instance.unreadCount > 0) {
                                        fragment?.setRedPoint(true)
                                        binding!!.timerFixed.visibility = View.VISIBLE
                                        if (Cache.instance.unreadCount > 99) {
                                            binding!!.timerFixed.text = "99+"
                                        } else {
//                        binding.timerFixed.setText("99+");
                                            binding!!.timerFixed.text =
                                                Cache.instance.unreadCount.toString()
                                        }
                                    } else {
                                        fragment?.setRedPoint(false)
                                        binding!!.timerFixed.visibility = View.GONE
                                    }
                                }

                                override fun onError(e: RongIMClient.ErrorCode) {
                                    if (isDestroy(mContext as Activity?)) return
                                    if (Cache.instance.unreadCount > 0) {
                                        fragment?.setRedPoint(true)
                                        binding!!.timerFixed.visibility = View.VISIBLE
                                        if (Cache.instance.unreadCount > 99) {
                                            binding!!.timerFixed.text = "99+"
                                        } else {
//                        binding.timerFixed.setText("99+");
                                            binding!!.timerFixed.text =
                                                Cache.instance.unreadCount.toString()
                                        }
                                    } else {
                                        fragment?.setRedPoint(false)
                                        binding!!.timerFixed.visibility = View.GONE
                                    }
                                }
                            })
                        } else {
                            Cache.instance.unreadCount = integer
                        }
                        if (!Cache.instance.reviewPkg) {
                            if (Cache.instance.unreadCount > 0) {
                                fragment?.setRedPoint(true)
                                binding!!.timerFixed.visibility = View.VISIBLE
                                if (Cache.instance.unreadCount > 99) {
                                    binding!!.timerFixed.text = "99+"
                                } else {
//                        binding.timerFixed.setText("99+");
                                    binding!!.timerFixed.text =
                                        Cache.instance.unreadCount.toString()
                                }
                            } else {
                                fragment?.setRedPoint(false)
                                binding!!.timerFixed.visibility = View.GONE
                            }
                        }
                    }

                    override fun onError(errorCode: RongIMClient.ErrorCode) {
                        Log.v("unreadCountMainUtil", errorCode.msg)
                    }
                })
        }


    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

    }

    //1后台，0前台
    override fun onAppBackground() {
        handleAppLifecycle(false)
        UserOnlineStatusService.instance!!.onEnterBackground()
    }

    override fun onAppForeground() {
        handleAppLifecycle(true)
        UserOnlineStatusService.instance!!.onEnterForeground()
    }

    //当前是前台还是后台
    var currentIsForeground: Boolean = true

    //处理前后台切换
    private fun handleAppLifecycle(isForeground: Boolean) {
        currentIsForeground = isForeground
        throttle("handleAppLifecycle", 500,
            {},
            {
                //判断是否登录
                if (!Cache.instance.isLogin()) {
                    return@throttle
                }
                if (currentIsForeground) {
                    try {
                        SocketUtils.appLifecycleCheck()
                    } catch (e: Exception) {
                        throw RuntimeException(e)
                    }
                    postSwitch(0)
                } else {
                    postSwitch(1)
                }
            })
    }

    private fun postSwitch(mode: Int) {
        postSwitch(mode, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(booleanBaseBean: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(booleanBaseBean)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                postSwitch(mode)
            }
        })
    }

    override fun onPause() {
        super.onPause()
        Log.i("ygt", "onPause")
    }

    override fun onStop() {
        super.onStop()
        Log.i("ygt", "onStop")
    }

    override fun transLocalText() {
    }
}