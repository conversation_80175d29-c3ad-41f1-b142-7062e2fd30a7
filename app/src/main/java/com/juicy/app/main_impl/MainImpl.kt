package com.juicy.app.main_impl

import com.blankj.utilcode.util.ActivityUtils
import com.juicy.app.modules.base.dialog.LoadDialog
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.model.bean.StrongGuildBean
import com.juicy.common.model.bean.OssPolicyINfoBean
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.networks.delegate.GetCoinGoodsSearchInterface.getCoinGoodsSearch
import com.juicy.common.networks.delegate.GetGoodsPromotionInterface.getGoodsPromotion
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.GetUserOssPolicyInterface.getUserOssPolicy
import com.juicy.common.networks.delegate.GiftListInterface.giftList
import com.juicy.common.networks.delegate.LogNetworkUploadInterface.LogResult
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.networks.delegate.VideoCallsResultInterface.videoCallsResult
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.PayUtils.PayUtils
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.ThreadUtil.execute
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.core.Observer
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers

object MainImpl {
    var errorNum: Int = 1

    val giftList: Unit
        get() {
            giftList(object :
                ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<ArrayList<com.juicy.common.model.bean.OrderInfoBean>>>() {
                override fun onSubscribe(d: Disposable) {
                    super.onSubscribe(d)
                }

                override fun onNext(arrayListBaseBean: com.juicy.common.model.bean.NetResponseBean<ArrayList<com.juicy.common.model.bean.OrderInfoBean>>) {
                    super.onNext(arrayListBaseBean)
                    if (errorNum == 4) {
                        errorNum = 0
                        return
                    }
                    if (arrayListBaseBean.isOk) {
                        if (arrayListBaseBean.data != null && !arrayListBaseBean.data!!.isEmpty()) {
                            Cache.instance.giftList = arrayListBaseBean.data
                            return
                        }
                    }
                    if (errorNum <= 3) {
                        giftList
                      //  MainImpl.getGiftList()
                        errorNum++
                    }
                }

                override fun onError(e: Throwable) {
                }

                override fun onComplete() {
                    super.onComplete()
                }
            })
        }

    val oss: Unit
        get() {
            getUserOssPolicy(object :
                ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<OssPolicyINfoBean>>() {
                override fun onSubscribe(d: Disposable) {
                    super.onSubscribe(d)
                }

                override fun onNext(ckBaseBean: com.juicy.common.model.bean.NetResponseBean<OssPolicyINfoBean>) {
                    super.onNext(ckBaseBean)

                    if (ckBaseBean.isOk && ckBaseBean.data != null) {
                        Cache.instance.ossPolicy = ckBaseBean.data
                    }
                    synchronized(this) {
                        if (errorNum == 4) {
                            errorNum = 0
                            return
                        }
                        if (errorNum <= 3) {
                            oss
                            //MainImpl.getOss()
                            errorNum++
                        }
                    }
                }

                override fun onError(e: Throwable) {
                }

                override fun onComplete() {
                    super.onComplete()
                }
            })
        }

    val coinsShop: Unit
        get() {
            getCoinGoodsSearch(
                ActivityUtils.getTopActivity(),
                PlayParamsBean(true),
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>() {
                    override fun onSubscribe(d: Disposable) {
                        super.onSubscribe(d)
                    }

                    override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>) {
                        super.onNext(t)


                        if (t.isOk) {
                            if (t.data != null && !t.data.isNullOrEmpty()) {
                                val coinGoods: MutableList<com.juicy.common.model.bean.ActivityInfoBean> =
                                    ArrayList()
                                for (i in t.data!!.indices) {
                                    val activityInfoBean: com.juicy.common.model.bean.ActivityInfoBean = t.data?.get(i)?: com.juicy.common.model.bean.ActivityInfoBean()
                                    if (activityInfoBean.type == "1") { //订阅
                                        Cache.instance.subscribeGood =
                                            activityInfoBean
                                    } else {
                                        coinGoods.add(activityInfoBean)
                                    }
                                }
                                Cache.instance.coinGoods = coinGoods
                                PayUtils(ActivityUtils.getTopActivity()).batchQueryPurchases()
                                return
                            }
                        }
                        synchronized(this) {
                            if (errorNum == 4) {
                                errorNum = 0
                                return
                            }
                            if (errorNum <= 3) {
                                coinsShop
                                //MainImpl.getCoinsShop()
                                errorNum++
                            }
                        }
                    }

                    override fun onError(e: Throwable) {
                    }

                    override fun onComplete() {
                        super.onComplete()
                    }
                })
        }

    fun merge(mergeCallBack: MergeCallBack) {
        if (Cache.instance.userStratResult != null && Cache.instance.userStratResult?.isSwitchStrongGuide == true) {
            Observable.mergeDelayError<com.juicy.common.model.bean.NetResponseBean<out Any?>>(
                guide,
                new,
                salasGood
            ).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : Observer<com.juicy.common.model.bean.NetResponseBean<out Any?>> {
                    override fun onSubscribe(d: Disposable) {
                    }

                    override fun onNext(baseBean: com.juicy.common.model.bean.NetResponseBean<*>) {
                        if (baseBean.data != null) {
                            if (baseBean.data is List<*>) {
                                if (!(baseBean.data as List<*>).isEmpty()) {
                                    if ((baseBean.data as List<*>).get(0) is com.juicy.common.model.bean.ActivityInfoBean) {
                                        Cache.instance.salePromotionGood =
                                            (baseBean.data as List<*>).get(0) as com.juicy.common.model.bean.ActivityInfoBean?
                                        Cache.instance.salePromotionCreateTime =
                                            System.currentTimeMillis()
                                    }
                                }
                            }
                        }
                        if (baseBean.data is com.juicy.common.model.bean.ExpCoinsBean) {
                            Cache.instance.presented = baseBean.data as com.juicy.common.model.bean.ExpCoinsBean?
                        }
                        if (baseBean.data is StrongGuildBean) {
                            Cache.instance.guide = baseBean.data as StrongGuildBean?
                        }
                    }

                    override fun onError(e: Throwable) {
                    }

                    override fun onComplete() {
                        getProGood(null, mergeCallBack)
                    }
                })
        } else {
            Observable.mergeDelayError<com.juicy.common.model.bean.NetResponseBean<out Any?>>(
                new,
                salasGood
            ).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : Observer<com.juicy.common.model.bean.NetResponseBean<out Any?>> {
                    override fun onSubscribe(d: Disposable) {
                    }

                    override fun onNext(baseBean: com.juicy.common.model.bean.NetResponseBean<*>) {
                        if (baseBean.data != null) {
                            if (baseBean.data is List<*>) {
                                if (!(baseBean.data as List<*>).isEmpty()) {
                                    if ((baseBean.data as List<*>).get(0) is com.juicy.common.model.bean.ActivityInfoBean) {
                                        Cache.instance.salePromotionGood =
                                            (baseBean.data as List<*>).get(0) as com.juicy.common.model.bean.ActivityInfoBean?
                                        Cache.instance.salePromotionCreateTime =
                                            System.currentTimeMillis()
                                    }
                                }
                            }
                        }
                        if (baseBean.data is com.juicy.common.model.bean.ExpCoinsBean) {
                            Cache.instance.presented = baseBean.data as com.juicy.common.model.bean.ExpCoinsBean?
                        }
                        if (baseBean.data is StrongGuildBean) {
                            Cache.instance.guide = baseBean.data as StrongGuildBean?
                        }
                    }

                    override fun onError(e: Throwable) {
                    }

                    override fun onComplete() {
                        getProGood(null, mergeCallBack)
                    }
                })
        }
    }

    fun getProGood(loadDialog: LoadDialog?, mergeCallBack: MergeCallBack) {
        getGoodsPromotion(PlayParamsBean(false), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ActivityInfoBean>>() {
            override fun onNext(proData: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ActivityInfoBean>) {
                super.onNext(proData)
                Cache.instance.userPromotionGood = proData.data
                Cache.instance.userPromotionCreateTime = System.currentTimeMillis()
                if (loadDialog != null) {
                    loadDialog.dismiss()
                }

                mergeCallBack.onSuccess()
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                if (loadDialog != null) {
                    loadDialog.dismiss()
                }
            }
        })
    }

    val new: Observable<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ExpCoinsBean>>
        get() = RetrofitManage.instance.create(Service::class.java).presented

    val salasGood: Observable<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>
        get() = RetrofitManage.instance.create(Service::class.java)
            .GetLastSpecialOffer2(com.juicy.common.model.bean.GoodsParamsBean(false))

    val guide: Observable<com.juicy.common.model.bean.NetResponseBean<StrongGuildBean>>
        get() {
            return RetrofitManage.instance.create(Service::class.java)
                .GetStrongGuide()
        }

    fun getResult(channelName: String?, callBack: ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorWallTagBean>>) {
        videoCallsResult(channelName, callBack)
    }

    fun submitLog(logBean: List<com.juicy.common.model.bean.CallLogInfoBean.DataBean>) {
        execute(object : Runnable {
            override fun run() {
                LogResult(logBean)
            }
        })
    }

    val userInfo: Unit
        get() {
            getUserInfo(
                getStringValue(SpKeyPool.USER_ID_DEVICE, "")!!,
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                    override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                        super.onNext(azBaseBean)
                        if (azBaseBean.data != null) {
                            Cache.instance.userInfo = azBaseBean.data
                        }
                    }

                    override fun onError(e: Throwable) {
                        super.onError(e)
                    }
                })
        }

    interface MergeCallBack {
        fun onSuccess()
    }
}
