package com.juicy.app

import android.app.ActivityManager
import android.app.Application
import android.util.Log
import androidx.appcompat.app.AppCompatDelegate
import com.alibaba.android.arouter.launcher.ARouter
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.appsflyer.AppsFlyerConversionListener
import com.appsflyer.AppsFlyerLib
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.networks.request.RetrofitManage.resetRetrofitClient
import com.juicy.common.networks.request.SocketUtil.SocketUtils
import com.juicy.common.networks.delegate.GiveAppsFlyerArgsInterface.GiveAppsFlyerArgs
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.LanguageTool.localeLanguageCodeCountryFunc
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import com.juicy.common.utils.SpSaveUtil.putLongValue
import com.juicy.common.utils.SpSaveUtil.putStringValue
import com.juicy.common.utils.ThreadUtil.execute
import com.juicy.common.utils.ThreadUtil.runOnUIThread
import io.reactivex.rxjava3.exceptions.UndeliverableException
import io.reactivex.rxjava3.functions.Consumer
import io.reactivex.rxjava3.plugins.RxJavaPlugins
import java.io.IOException

class JuicyApplication : Application() {

    //初始化Theme
    private fun initTheme(){
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
    }
    //初始化状态
    private fun initStatus(){
        putStringValue(SpKeyPool.LANGUAGE_LAST, localeLanguageCodeCountryFunc)
        putStringValue(SpKeyPool.CURRECTCOUNTRY, "")
        Cache.instance.buildVersionName = MyBuildConfig.VERSION_NAME
        Cache.instance.appName = getString(R.string.app_name)
    }

    //初始化三方库
    private fun initThirdParty(){
        if (MyBuildConfig.DEBUG) {
            ARouter.openLog() // 打印日志
            ARouter.openDebug()
        }
        // ARouter初始化
        ARouter.init(this)
        intAppsFlyerSDK()
        initGoogleInstallReferer()
    }
    override fun onCreate() {
        super.onCreate()
        juicyApplication = this
        initTheme()
        initStatus()
        initThirdParty()
        LanguageManager.instance!!.initLanguage()

        setRxJavaErrorHandler()
    }

    private fun intAppsFlyerSDK() {
        val conversionListener: AppsFlyerConversionListener = object : AppsFlyerConversionListener {
            override fun onConversionDataSuccess(conversionData: Map<String, Any>) {
                //在此获取归因数据，保存一份在本地，header需要传。另外需要单独上传AF归因数据
                val map = HashMap<String, String?>()
                for (attrName in conversionData.keys) {
                    Log.d("LOG_TAG", "attribute: " + attrName + " = " + conversionData[attrName])
                    if (conversionData[attrName] != null) {
                        map[attrName] = conversionData[attrName].toString()
                    }
                }
                //将数据存储到本地
                //要设置一个默认数据
                Cache.instance.AppsFlyerData = map

                runOnUIThread {
                    if (null != RetrofitManage.instance) { // 更新短链  header AF归因
                        resetRetrofitClient()
                    }
                    if (null != SocketUtils) {  // 更新长链 header AF归因
                        SocketUtils.disconnect()
                        SocketUtils.initAndConnect()
                    }
                }

                execute {
                    //上传归因数据
                    if (null != Cache.instance.AppsFlyerData) {
                        val appsFlyerArgs = com.juicy.common.model.bean.AdjustBean()
                        appsFlyerArgs.setVer(com.android.billingclient.BuildConfig.VERSION_NAME)
                        if (null != Cache.instance.userInfo) {
                            appsFlyerArgs.setUserId(Cache.instance.userInfo!!.userId)
                        }
                        appsFlyerArgs.setAdgroupId(Cache.instance.IsNull(Constant.ADGROUP_ID)!!)
                        appsFlyerArgs.setAdset(Cache.instance.IsNull(Constant.ADSET)!!)
                        appsFlyerArgs.setAdsetId(Cache.instance.IsNull(Constant.ADSET_ID)!!)
                        appsFlyerArgs.setAfStatus(Cache.instance.IsNull(Constant.AF_STATUS)!!)
                        appsFlyerArgs.setAgency(Cache.instance.IsNull(Constant.AF_AGENCY)!!)
                        appsFlyerArgs.setUtmSource(Cache.instance.IsNull(Constant.MEDIA_SOURCE)!!)

                        appsFlyerArgs.setAfChannel(Cache.instance.IsNull(Constant.AF_CHANNEL)!!)
                        appsFlyerArgs.setCampaign(Cache.instance.IsNull(Constant.CAMPAIGN)!!)
                        appsFlyerArgs.setCampaignId(
                            Cache.instance.IsNull(
                                Constant.CAMPAIGN_ID
                            )!!
                        )
                        GiveAppsFlyerArgs(
                            appsFlyerArgs,
                            object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
                                override fun onNext(aBoolean: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                                    super.onNext(aBoolean)
                                    if (aBoolean.data != null && aBoolean.data!!) {
                                        Log.v("LOG_TAG", "提交归因数据成功")
                                    } else {
                                        Log.v("LOG_TAG", "提交归因数据失败")
                                    }
                                }

                                override fun onError(e: Throwable) {
                                    super.onError(e)
                                }
                            })
                    }
                }

                resetRetrofitClient()
            }

            override fun onConversionDataFail(errorMessage: String) {
                Log.d("LOG_TAG", "error getting conversion data: $errorMessage")
            }

            override fun onAppOpenAttribution(conversionData: Map<String, String>) {
                for (attrName in conversionData.keys) {
                    Log.d("LOG_TAG", "attribute: " + attrName + " = " + conversionData[attrName])
                }
            }

            override fun onAttributionFailure(errorMessage: String) {
                Log.d("LOG_TAG", "error onAttributionFailure : $errorMessage")
            }
        }
        AppsFlyerLib.getInstance().init(
            AF_DEV_KEY, conversionListener,
            applicationContext
        )
        AppsFlyerLib.getInstance().start(this)
    }
    private fun setRxJavaErrorHandler() {
        if (RxJavaPlugins.getErrorHandler() != null || RxJavaPlugins.isLockdown()) {
//            LogUtils.w("michael", "setRxJavaErrorHandler getErrorHandler()!=null||isLockdown()");
            return
        }
        RxJavaPlugins.setErrorHandler(Consumer { e ->
            var e = e
            if (e is UndeliverableException) {
                e = e.cause!!
                //                    LogUtils.w("michael", "setRxJavaErrorHandler UndeliverableException=" + e);
                return@Consumer
            } else if ((e is IOException)) {
                // fine, irrelevant network problem or API that throws on cancellation
                return@Consumer
            } else if (e is InterruptedException) {
                // fine, some blocking code was interrupted by a dispose call
                return@Consumer
            } else if ((e is NullPointerException) || (e is IllegalArgumentException)) {
                // that's likely a bug in the application
                val uncaughtExceptionHandler =
                    Thread.currentThread().uncaughtExceptionHandler
                uncaughtExceptionHandler?.uncaughtException(Thread.currentThread(), e)
                return@Consumer
            } else if (e is IllegalStateException) {
                // that's a bug in RxJava or in a custom operator
                val uncaughtExceptionHandler =
                    Thread.currentThread().uncaughtExceptionHandler
                uncaughtExceptionHandler?.uncaughtException(Thread.currentThread(), e)
                return@Consumer
            }
            //                LogUtils.w("michael", "setRxJavaErrorHandler unknown exception=" + e);
        })
    }
    private fun initGoogleInstallReferer() {
        val referrerClient =
            InstallReferrerClient.newBuilder(this).build()
        referrerClient.startConnection(object : InstallReferrerStateListener {
            override fun onInstallReferrerSetupFinished(responseCode: Int) {
                when (responseCode) {
                    InstallReferrerClient.InstallReferrerResponse.OK -> try {
                        reportInstallRefererData(referrerClient)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }

                    InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {}
                    InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {}
                }
            }

            override fun onInstallReferrerServiceDisconnected() {
            }
        })
    }

    @Throws(Exception::class)
    private fun reportInstallRefererData(referrerClient: InstallReferrerClient) {
        val referrerDetails = referrerClient.installReferrer
        val referrerUrl = referrerDetails.installReferrer
        val installVersion = referrerDetails.installVersion
        val referrerClickTime = referrerDetails.referrerClickTimestampSeconds
        val referrerClickServerTime = referrerDetails.referrerClickTimestampServerSeconds
        val appInstallTime = referrerDetails.installBeginTimestampSeconds
        val appInstallServerTime = referrerDetails.installBeginTimestampServerSeconds
        val instantExperienceLaunched = referrerDetails.googlePlayInstantParam
        putStringValue(SpKeyPool.REFERRER_URL, referrerUrl)
        putStringValue(SpKeyPool.INSTALL_VERSION, installVersion)
        putLongValue(SpKeyPool.REFERRER_CLICK_TIME, referrerClickTime)
        putLongValue(SpKeyPool.REFERRER_CLICK_SERVER_TIME, referrerClickServerTime)
        putLongValue(SpKeyPool.APP_INSTALL_TIME, appInstallTime)
        putLongValue(SpKeyPool.APP_INSTALL_SERVER_TIME, appInstallServerTime)
        putBooleanValue(SpKeyPool.INSTANT_EXPERIENCE_LAUNCHED, instantExperienceLaunched)


        //此处对数据进行本地保存，后续等用户注册完成进入主界面再进行上传；
    }

    companion object {
        var juicyApplication: Application? = null
            private set

        val activityManager: ActivityManager
            get() = juicyApplication!!.getSystemService(ACTIVITY_SERVICE) as ActivityManager
        //af归因key值
        private const val AF_DEV_KEY = ""
    }
}
