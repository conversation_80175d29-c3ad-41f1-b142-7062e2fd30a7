package com.juicy.app.modules.rank.adapter

import android.view.Gravity
import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.app.R
import com.juicy.common.model.bean.RankInfoBean
import com.juicy.common.config.Cache
import com.juicy.common.utils.GlideImageUtil.load
import com.juicy.common.utils.LanguageManager

class ItemAdapter(layoutResId: Int) :
    BaseQuickAdapter<RankInfoBean.RankDataBean, ItemVH>(layoutResId) {

    override fun convert(holder: ItemVH, item: RankInfoBean.RankDataBean) {
        if (LanguageManager.instance?.isLanguageForce == true && !isCharm) {
            holder.anchorName.gravity = Gravity.END
        }
        holder.sort.text = item.sort.toString()
        if (item.nickname != null) holder.anchorName.text = item.nickname
        load(context, item.avatar, holder.anchorPhoto)
        if (!isCharm && item.userId != null && Cache.instance.userInfo != null
            && Cache.instance.userInfo?.userId != null &&
            item.userId == Cache.instance.userInfo?.userId
        ) {
            holder.itemView.setBackgroundResource(R.drawable.ic_layer_pay_select_color_ruby)
        } else {
            holder.itemView.background = null
        }
    }
    var isCharm: Boolean = false
}