package com.juicy.app.modules.rank

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.widget.RelativeLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.BarUtils
import com.juicy.app.R
import com.juicy.app.modules.rank.adapter.FragmentAdapter
import com.juicy.app.modules.rank.adapter.TabAdapter
import com.juicy.app.databinding.LayoutSpinnerBinding
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.utils.LanguageManager

class UserRankActivity : BaseActivity() {
    private var fragmentAdapter: FragmentAdapter? = null
    private var names: MutableList<String> = ArrayList()
    private lateinit var binding: LayoutSpinnerBinding
    private var fragmentList: MutableList<BaseFragment> = ArrayList()

    private var tabAdapter: TabAdapter? = null


    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        names.add(LanguageManager.instance?.getLocalTranslate("Charm")?:"")
        names.add(LanguageManager.instance?.getLocalTranslate("Rich")?:"")
        tabAdapter?.setList(names)
        tabAdapter?.notifyDataSetChanged()
        fragmentList.add(RankFm(RankFm.Companion.CHARM))
        fragmentList.add(RankFm(RankFm.Companion.RICK))
        fragmentAdapter?.fragmentList = fragmentList
        fragmentAdapter?.notifyDataSetChanged()
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        binding = LayoutSpinnerBinding.inflate(layoutInflater)
        setContentView(binding.root)
        tabAdapter = TabAdapter(R.layout.item_spacer, names)
        fragmentAdapter = FragmentAdapter(supportFragmentManager, lifecycle)
        super.onCreate(savedInstanceState)
    }
    override fun transLocalText() {
        if (LanguageManager.instance?.isLanguageForce == true) {
            binding.emptyBadge.scaleX = -1f
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initView() {
        val mainPageLayoutParams = binding.maximalCounter.layoutParams as RelativeLayout.LayoutParams
        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        binding.maximalCounter.layoutParams = mainPageLayoutParams

        binding.emptyBadge.setOnClickListener { v: View? -> finish() }
        val lm = LinearLayoutManager(this)
        lm.orientation = RecyclerView.HORIZONTAL
        binding.withinSlider.layoutManager = lm
        binding.withinSlider.adapter = tabAdapter
        binding.aboveTab.adapter = fragmentAdapter
//        binding.activeSm.setTextColor(resources.getColor(R.color.white))
//        binding.activeSm.background =
//            create().bgColor(Color.parseColor("#26FFFFFF"))
//                .corner(dip2px(20f).toFloat()).build()
        tabAdapter?.addChildClickViewIds(R.id.topBottom)
        tabAdapter?.setOnItemChildClickListener { adapter, view, position ->
            binding.aboveTab.currentItem = position
            tabAdapter?.selectPosition = position
            tabAdapter?.notifyDataSetChanged()
        }
        binding.aboveTab.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                tabAdapter?.selectPosition = position
                tabAdapter?.notifyDataSetChanged()
            }
        })
    }
    fun setRankTime(text: String?) {
        //if (text != null) binding.activeSm.text = text
        text?.let {
            binding.activeSm.text = it
            binding.belowSecondary.text = it
        }
    }


}
