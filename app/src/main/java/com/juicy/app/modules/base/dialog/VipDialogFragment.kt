package com.juicy.app.modules.base.dialog

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.AbsoluteSizeSpan
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.R
import com.juicy.app.databinding.FragmentNavbarBinding
import com.juicy.common.config.ApiPool
import com.juicy.common.model.event.SubscribeStatusChangeEvent
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.DateMetaUtil.getDate
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PayUtils.PayUtils
import com.juicy.common.utils.PayUtils.PayUtils.FailOnListen
import com.juicy.common.utils.PayUtils.PayUtils.SuccessOnListen
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.SpSaveUtil.putStringValue
import org.greenrobot.eventbus.EventBus
import java.text.SimpleDateFormat
import java.util.Date
import java.util.regex.Pattern

class VipDialogFragment : DialogFragment {
    private var dialogBinding: FragmentNavbarBinding? = null
    private var dotDidView: View? = null
    var dotsView: MutableList<View>? = null

    @JvmField
    var entry: String = "subscribe_dialog"

    var isAnchorInfo: Boolean = false

    var bannerTexts: MutableList<String>? = null

    @SuppressLint("SimpleDateFormat")
    constructor() {
        // Required empty public constructor
        val currentDate = Date()
        // 定义日期格式
        val dateFormat = SimpleDateFormat("yyyy-MM-dd")
        // 将当前时间转换为指定格式的字符串
        val dateString = dateFormat.format(currentDate)
        putStringValue(SpKeyPool.SUBSCRIPTIONDATE, dateString)
    }

    constructor(isAnchorInfo: Boolean) {
        // Required empty public constructor
        this.isAnchorInfo = isAnchorInfo
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialogBinding = FragmentNavbarBinding.inflate(inflater)
        return dialogBinding!!.root
    }

    override fun onResume() {
        super.onResume()
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setDimAmount(0f)
            dialog!!.window!!.setGravity(Gravity.CENTER)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    fun initData() {

        if (Cache.instance.subscribeGood != null) {
            val p = Cache.instance.subscribeGood?.exchangeCoin ?: 0
            dialogBinding!!.xxlCoordinator.text = StringBuilder().append("+$p")
            dialogBinding!!.scrollDisconnected.text = Cache.instance.subscribeGood?.localPrice?:""
            //            dialogBinding.pauseOuter.setText("$" + Cache.getInstance().subscribeGood.getPrice() + "/" + Cache.getInstance().subscribeGood.getValidityUnit());
            dialogBinding!!.pauseOuter.text = LanguageManager.instance?.getLocalTranslate("Subscribe")?:""
        }

        if(Cache.instance.subscribeGood != null){
            val str = LanguageManager.instance?.getLocalTranslate("1200 coins per month.")?.replace("1200","${Cache.instance.subscribeGood?.exchangeCoin}")?:""
            val ssb =  SpannableStringBuilder(str)
            val start = str.indexOf("${Cache.instance.subscribeGood?.exchangeCoin}")
            val end = start + "${Cache.instance.subscribeGood?.exchangeCoin}".length
            if (start >=0 && end >=0){
                ssb.setSpan( ForegroundColorSpan(Color.parseColor("#FFFF7F37")), start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
                dialogBinding!!.bodyCard.text = ssb
            }
        }
        dialogBinding!!.hiddenOpen.text = LanguageManager.instance?.getLocalTranslate("Chat_anytime_with_anyone")?:""
        val str2 = LanguageManager.instance?.getLocalTranslate("10% off on calls") ?: "10% off on calls"
        val ssb =  SpannableStringBuilder(str2)
        val start = str2.indexOf("10%")
        val end = start + "10%".length
        if(start >=0 && end >=0){
            ssb.setSpan( ForegroundColorSpan(Color.parseColor("#FFFF7F37")), start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
            dialogBinding!!.outsideLeft.text = ssb
        }
        dialogBinding!!.secondaryDelete.text = LanguageManager.instance?.getLocalTranslate("premium_vip_badge")?:""
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initData()
        dialogBinding!!.responsiveDistant.setOnClickListener { v: View? ->
            PayUtils(
                activity,
                entry,
                true,
                Cache.instance.subscribeGood,
                object : SuccessOnListen {
                    override fun onListen() {
                        ToastUtils.showShort(
                            LanguageManager.instance!!.getLocalTranslate(
                                "Subscribe successfully"
                            )
                        )
                        if (Cache.instance.webViewActivity != null) {
                            Cache.instance.webViewActivity!!.refreshAgentWeb()
                        }

                        EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
                        EventBus.getDefault().post(SubscribeStatusChangeEvent())
                        //刷新个人信息
                        val userId = getStringValue(SpKeyPool.USER_ID_DEVICE, "")
                        requestUserInfo(userId!!)
                    }
                }
            ) { }.openPayDialog()
        }
        dialogBinding!!.dimmedClose.setOnClickListener { v: View? ->
            PayUtils(
                activity,
                "vipDialog",
                true,
                Cache.instance.subscribeGood,
                object : SuccessOnListen {
                    override fun onListen() {
                        ToastUtils.showShort(
                            LanguageManager.instance!!.getLocalTranslate(
                                "Subscribe successfully"
                            )
                        )
                        if (Cache.instance.webViewActivity != null) {
                            Cache.instance.webViewActivity!!.refreshAgentWeb()
                        }

                        EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
                        EventBus.getDefault().post(SubscribeStatusChangeEvent())
                        //刷新个人信息
                        val userId = getStringValue(SpKeyPool.USER_ID_DEVICE, "")
                        requestUserInfo(userId!!)
                    }
                }
            ) { }.openPayDialog()
        }
        dialogBinding!!.selectAdaptive.setOnClickListener { v: View? -> dismiss() }

        if (Cache.instance.userInfo != null && Cache.instance.userInfo?.vip != null &&
            Cache.instance.userInfo?.vip == true
        ) {
            dialogBinding!!.dimmedClose.visibility = View.GONE
            dialogBinding!!.responsiveDistant.visibility = View.GONE
            dialogBinding!!.removePlay.visibility = View.VISIBLE
            val dateString = getDate("yyyy-MM-dd", Cache.instance.userInfo!!.vipExpiryTime.toLong())
            dialogBinding!!.briefAvatar.text = (LanguageManager.instance?.getLocalTranslate("Valid_until")?:"") + dateString
        }
        if (Cache.instance.reviewPkg) {
            initDescText()
        }

        dialogBinding!!.elasticBorder.text = LanguageManager.instance?.getLocalTranslate("1_Month")?:""

        //        dialogBinding.surfaceDynamic.setOnClickListener(v -> {});
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            dialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            dialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
    }

    fun initDescText() {
        dialogBinding!!.root.setBackgroundColor(Color.parseColor("#B3000000"))
        dialogBinding!!.connectedWithin.visibility = View.VISIBLE
        val textView = dialogBinding!!.connectedWithin


           var originalText = LanguageManager.instance?.getLocalTranslate("For our auto-renewing subscription, you can cancel anytime through your Google Play settings. It will automatically renew 24 hours before your current period ends. By clicking \"Subscribe,\" you agree to our Terms of Service and Privacy Policy.")?:("For our auto-renewing subscription, you can cancel anytime through your Google Play settings. It will automatically renew 24 hours before your current period ends. By clicking \"Subscribe,\" you agree to our Terms of Service and Privacy Policy.")
//        val originalText = LanguageManager.instance!!.getLocalTranslate(
//            LanguageManager.instance!!.getLocalTranslate("Payment_will_be_charged_to_your_Google_Play_StoreAccount_at_confirmation_of_purchase_You_can_turn_off_autorenewal_at_least_24_hours_before_the_end_of_your_current_subscription_period_We_do_not_offer_a_free_trial_for_subscription_content_The_subscription_period_is_one_month_and_your_subscription_will_automatically_renew_within_the_same_package_period_at_the_same_price_Subscriptions_can_be_cancelled_at_any_time_Cancel_your_subscription_by_clicking_here") + LanguageManager.instance!!.getLocalTranslate(
//                "Cancel subscription"
//            ) + LanguageManager.instance!!.getLocalTranslate("For_more_information_visit_our") + LanguageManager.instance!!.getLocalTranslate(
//                "Terms of Service"
//            ) + LanguageManager.instance!!.getLocalTranslate("and") + LanguageManager.instance!!.getLocalTranslate(
//                "Privacy Policy"
//            ) + "."
//        )


        // 创建富文本字符串
        val spannableStringBuilder = SpannableStringBuilder(originalText)

        // 设置字体颜色和大小
        val textColorSpan = ForegroundColorSpan(Color.parseColor("#DADADA"))
        spannableStringBuilder.setSpan(
            textColorSpan,
            0,
            spannableStringBuilder.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        val textSizeSpan = AbsoluteSizeSpan(12, true)
        spannableStringBuilder.setSpan(
            textSizeSpan,
            0,
            spannableStringBuilder.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )



        val keyword2 = LanguageManager.instance!!.getLocalTranslate("Terms_of_Service")
        val pattern2 = Pattern.compile(Pattern.quote(keyword2), Pattern.CASE_INSENSITIVE)
        val matcher2 = pattern2.matcher(originalText)
        while (matcher2.find()) {
            val start = matcher2.start()
            val end = matcher2.end()

            val clickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    if (isFastClick) {
                        return
                    }
                    intentRongActivity(
                        LanguageManager.instance!!.getLocalTranslate("Terms_amp_Conditions"),
                        ApiPool.TERM_CONDITIONS
                    )
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.color = Color.parseColor("#3891FE") // 菊花色
                    ds.isUnderlineText = true // 添加下划线
                }
            }
            if (start >=0 && end >=0){
                spannableStringBuilder.setSpan(
                    clickableSpan,
                    start,
                    end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }


        val keyword3 = LanguageManager.instance!!.getLocalTranslate("Privacy_Policy")
        val pattern3 = Pattern.compile(Pattern.quote(keyword3), Pattern.CASE_INSENSITIVE)
        val matcher3 = pattern3.matcher(originalText)
        while (matcher3.find()) {
            val start = matcher3.start()
            val end = matcher3.end()

            val clickableSpan: ClickableSpan = object : ClickableSpan() {
                override fun onClick(widget: View) {
                    // 处理点击事件，跳转到相关页面
                    if (isFastClick) {
                        return
                    }
                    intentRongActivity(
                        LanguageManager.instance!!.getLocalTranslate("Privacy_Policy"),
                        ApiPool.PRIVACY
                    )
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.color = Color.parseColor("#3891FE") // 菊花色
                    ds.isUnderlineText = true // 添加下划线
                }
            }
            if (start >=0 && end >=0){
                spannableStringBuilder.setSpan(
                    clickableSpan,
                    start,
                    end,
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }


//        val keyword4 = LanguageManager.instance!!.getLocalTranslate("Cancel_subscription")
//        val pattern4 = Pattern.compile(Pattern.quote(keyword4), Pattern.CASE_INSENSITIVE)
//        val matcher4 = pattern4.matcher(originalText)
//        while (matcher4.find()) {
//            val start = matcher4.start()
//            val end = matcher4.end()
//
//            val clickableSpan: ClickableSpan = object : ClickableSpan() {
//                override fun onClick(widget: View) {
//                    // 处理点击事件，跳转到相关页面
//                    if (isFastClick) {
//                        return
//                    }
//                    val url = "https://play.google.com/store/account/subscriptions" // 替换为您要跳转的网页链接
//                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
//                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//                    startActivity(intent)
//                }
//
//                override fun updateDrawState(ds: TextPaint) {
//                    super.updateDrawState(ds)
//                    ds.color = Color.parseColor("#FF9292") // 菊花色
//                    ds.isUnderlineText = true // 添加下划线
//                }
//            }
//            spannableStringBuilder.setSpan(
//                clickableSpan,
//                start,
//                end,
//                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
//            )
//        }

        textView.text = spannableStringBuilder
        textView.movementMethod = LinkMovementMethod.getInstance()
    }

    private fun intentRongActivity(title: String, url: String) {
        val intent = Intent(context, com.juicy.app.modules.base.activity.BasicWebActivity::class.java)
        intent.putExtra("title", title)
        intent.putExtra("url", url)

        startActivity(intent)
    }

    fun requestUserInfo(userId: String) {
        //获取用户数据
        getUserInfo(userId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
            override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                if (azBaseBean != null) {
                    if (azBaseBean.isOk && azBaseBean.data != null) {
                        Cache.instance.userInfo = azBaseBean.data
                        initData()
                    } else if (Constant.TOKEN_EXPIRE_CODES.contains(azBaseBean.code)) {
                    }
                }
            }

            override fun onError(e: Throwable) {
                Log.e("VipDialogFragment", "mine user info error" + e.message)
            }
        })
    }
}