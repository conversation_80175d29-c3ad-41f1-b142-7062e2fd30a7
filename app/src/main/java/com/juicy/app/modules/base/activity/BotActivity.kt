package com.juicy.app.modules.base.activity

import android.os.Bundle
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.BarUtils
import com.bumptech.glide.Glide
import com.juicy.app.modules.base.adapter.RobotAdapter
import com.juicy.app.databinding.ActivityBodyBinding
import com.juicy.common.networks.delegate.GetRobotFAQInterface.getRobotFAQ
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.utils.LanguageManager
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation

class BotActivity : BaseActivity() {
    private var binding: ActivityBodyBinding? = null

    var robotMessageBeanList: MutableList<com.juicy.common.model.bean.RobotMessageBean> = ArrayList()

    private var robotAdapter: RobotAdapter? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        binding = ActivityBodyBinding.inflate(layoutInflater)
        setContentView(binding!!.root)
        super.onCreate(savedInstanceState)
    }

    fun scrollToBottom() {
        binding!!.chipWrapper.scrollToPosition(robotAdapter!!.itemCount - 1)
    }

    override fun initData() {
        binding!!.chipWrapper.layoutManager = LinearLayoutManager(
            this
        )
        robotAdapter = RobotAdapter(robotMessageBeanList, this)
        binding!!.chipWrapper.adapter = robotAdapter
        robotAdapter!!.setOnItemClickListener(object : RobotAdapter.OnImgBigClickListener {
            override fun onItemClick(imgUrl: String?) {
                binding!!.bodySurface.visibility = View.VISIBLE
                Glide.with(this@BotActivity).load(imgUrl).into(
                    binding!!.bodySurface
                )
            }
        })
        robotAdapter!!.scrollBack = object : RobotAdapter.RobotListLongDialogCallBack {
            override fun newMessage() {
                scrollToBottom()
            }
        }
        binding!!.bodySurface.setOnClickListener { view: View? ->
            binding!!.bodySurface.visibility = View.GONE
        }

        getRobotFAQ(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.RobotBean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.RobotBean>) {
                super.onNext(t)
                if (t.data != null) {
                    val robotMessageBean = com.juicy.common.model.bean.RobotMessageBean()
                    robotMessageBean.type = 0
                    robotMessageBean.robotBean = t.data
                    robotMessageBeanList.add(robotMessageBean)
                    robotAdapter!!.items = robotMessageBeanList
                    robotAdapter!!.notifyDataSetChanged()
                }
            }
        })
    }

    override fun initView() {
        val mainPageLayoutParams =
            binding!!.primarySync.layoutParams as ConstraintLayout.LayoutParams
        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        binding!!.primarySync.layoutParams = mainPageLayoutParams

        if (LanguageManager.instance!!.isLanguageForce) {
            binding!!.flexibleFixed.scaleX = -1f
        }
        binding!!.choosePlay.text =
            LanguageManager.instance!!.getLocalTranslate("Customer_Service")
        binding!!.disconnectedContent.setOnClickListener { view: View? ->
            if (Cache.instance.userStratResult != null &&
                Cache.instance.userStratResult!!.userServiceAccountId != null
            ) {
                RouteUtils.routeToConversationActivity(
                    this,
                    Conversation.ConversationType.PRIVATE,
                    Cache.instance.userStratResult!!.userServiceAccountId
                )
            }
        }
        binding!!.flexibleFixed.setOnClickListener { view: View? ->
            finish()
        }
    }

    override fun transLocalText() {
    }
}