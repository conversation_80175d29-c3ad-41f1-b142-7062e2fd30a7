package com.juicy.app.modules.banner

import android.content.Context
import android.widget.ImageView
import androidx.appcompat.content.res.AppCompatResources
import com.bumptech.glide.Glide
import com.juicy.app.R
import com.youth.banner.adapter.BannerImageAdapter
import com.youth.banner.holder.BannerImageHolder

class ItemAdapter(mData: List<com.juicy.common.model.bean.MediaInfoBean>, private val context: Context) :
    BannerImageAdapter<com.juicy.common.model.bean.MediaInfoBean>(mData) {
    override fun onBindView(holder: BannerImageHolder, data: com.juicy.common.model.bean.MediaInfoBean, position: Int, size: Int) {
        if (data.mediaUrl != null) {
            Glide.with(context)
                .load(data.mediaUrl)
                .placeholder(AppCompatResources.getDrawable(context, R.drawable.img_empty_big_full_pearl))
                .into(holder.imageView)
        }
        //holder.imageView 设置#E7E7E7背景
        holder.imageView.setBackgroundColor(-0x181819)
        holder.imageView.scaleType = ImageView.ScaleType.CENTER

    }
}
