package com.juicy.app.modules.base.adapter

import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.juicy.app.R

class PayOneViewHolder(view: View) : RecyclerView.ViewHolder(view) {
    var payIcon: AppCompatImageView =
        view.findViewById(R.id.aroundCancel)
    var bg: View = view.findViewById(R.id.fillGrid)
    var itemCl: ConstraintLayout =
        view.findViewById(R.id.loadedProgress)
    var recentlyUsed: AppCompatTextView =
        view.findViewById(R.id.swipeDynamic)
    var payName: AppCompatTextView =
        view.findViewById(R.id.headerCompact)
    var payDiscount: AppCompatTextView =
        view.findViewById(R.id.openBack)
    var discountLl: LinearLayout =
        view.findViewById(R.id.dimmedMedium)
    var checkBtn: Button =
        view.findViewById(R.id.mediumXxl)
}
