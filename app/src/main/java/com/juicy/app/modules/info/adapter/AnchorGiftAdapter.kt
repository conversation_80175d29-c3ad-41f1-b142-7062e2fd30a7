package com.juicy.app.modules.info.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.common.config.Constant.giftResMap

class AnchorGiftAdapter(layoutResId: Int) : BaseQuickAdapter<String, AnchorGiftViewHolder>(layoutResId) {
    override fun convert(holder: AnchorGiftViewHolder, item: String) {
        if (item.isNotEmpty()) {
            val split = item.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            val giftNum = giftResMap[split[0]]
            if (giftNum != null) {
                holder.icon.setImageResource(giftNum)
            }
            holder.num.text = StringBuilder().append("x").append(split[1]).append(" ")
        }
    }
}


