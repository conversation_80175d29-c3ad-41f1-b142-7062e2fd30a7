package com.juicy.app.modules.base.dialog

import android.content.Context
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.PopupWindow
import androidx.fragment.app.Fragment
import com.juicy.app.R
import com.juicy.app.databinding.DialogTitleBinding
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.rcMessage.HLinkMsg
import com.juicy.common.rcMessage.SJMsg
import com.juicy.common.config.Cache
import com.juicy.common.utils.AppActivityUtil
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.throttle.Throttle.throttle
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import io.rong.message.FileMessage
import io.rong.message.ImageMessage
import io.rong.message.TextMessage
import io.rong.message.VoiceMessage

class MessageNoticationDialog // Required empty public constructor
    (private val messageContent: Message) : Fragment() {
    private var binding: DialogTitleBinding? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = DialogTitleBinding.inflate(inflater)
        return binding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initView()
        initData()
    }

    override fun onStart() {
        super.onStart()
    }


    /**= ======================初始化数据======================== */
    private fun setDirection() {
    }

    private fun initView() {
    }

    private fun initData() {
    }

    companion object {
        /**等待处理的消息 */
        private var waitHandleMessage: Message? = null

        //判断当前是否可以显示
        private fun canShow(): Boolean {
            //判断当前是否登录

            if (!Cache.instance.isLogin()) {
                return false
            }
            //判断是否审核模式
            if (Cache.instance.reviewPkg) {
                return false
            }
            //判断页面路由
            val currentActivity = currentActivity
            if (currentActivity == null || isDestroy(currentActivity) || currentActivity.javaClass.simpleName == null) {
                return false
            }
            //当前是否在登录页面
            if (currentActivity.javaClass.simpleName == "AuthActivity") {
                return false
            }

            //当前是否在在私聊页
            if (currentActivity.javaClass.simpleName == "MessageActivity") {
                return false
            }
            //如果当前在首页并且选择了聊天列表
            if (currentActivity.javaClass.simpleName == "CoreActivity") {
                if (Cache.instance.currentIndex == 2) {
                    return false
                }
            }
            if (currentActivity.javaClass.simpleName == "CallActivity") {
                return false
            }

            return true
        }

        //    static Toast messageToast;
        var popupWindow: PopupWindow? = null

        /**===============  静态方法  ============= */
        fun show(message: Message?) {
            if (!canShow()) {
                return
            }

            waitHandleMessage = message
            throttle("messageNotication", 100,
                { }, Runnable {
                    if (!canShow()) {
                        return@Runnable
                    }
                    val message = waitHandleMessage
                    val userInfo = getCacheUserInfo(message!!.targetId)

                    if (userInfo != null) {
                        _show(message, userInfo)

                        return@Runnable
                    }
                    //请求个人信息接口
                    getUserInfo(
                        message.targetId,
                        object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                            override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                                super.onNext(azBaseBean)
                                _show(
                                    waitHandleMessage!!,
                                    azBaseBean.data
                                )
                            }
                        })
                })
        }

        private fun _show(message: Message, userInfo: com.juicy.common.model.bean.JuicyUserInfoBean?) {
            if (userInfo != null) {
                val currentActivity = currentActivity
                if (!canShow()) {
                    return
                }
                close()

                val inflater =
                    currentActivity!!.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
                val binding = DialogTitleBinding.inflate(inflater)

                val windowManager =
                    currentActivity.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                if (message.content != null) {
                    binding.compactNarrow.text =
                        getMessageExText(message.content)
                }
                if (userInfo.nickname != null) {
                    binding.bottomTop.text = userInfo.nickname
                }
                //设置圆形头像
                loadCircleImage(currentActivity, userInfo.avatarThumbUrl, binding.acrossLocked)

                binding.fillGrid.isClickable = true
                binding.fillGrid.isFocusable = true
                binding.fillGrid.setOnClickListener(View.OnClickListener {
                    close()
                    //转跳消息详情页
                    val currentActivity = AppActivityUtil.currentActivity
                    if (isDestroy(currentActivity)) return@OnClickListener
                    RouteUtils.routeToConversationActivity(
                        currentActivity,
                        Conversation.ConversationType.PRIVATE,
                        message.targetId
                    )
                })
                var width = 0.0f
                //WindowMetrics
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    val windowMetrics = windowManager.currentWindowMetrics
                    width = windowMetrics.bounds.width().toFloat()
                } else {
                    width = windowManager.defaultDisplay.width.toFloat()
                }
                val layoutParams = LinearLayout.LayoutParams(width.toInt(), dip2px(77.5f))
                //设置TextView的宽度为 屏幕宽度
                binding.root.layoutParams = layoutParams

                // Initialize a new instance of popup window
                popupWindow = PopupWindow(
                    binding.root,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                popupWindow!!.animationStyle =
                    R.style.animation_message_notication_anim_style
                popupWindow!!.isFocusable = false
                popupWindow!!.setBackgroundDrawable(
                    currentActivity.getDrawable(android.R.color.transparent)
                )

                // Set an elevation value for popup window
                // Call requires api level 21
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    popupWindow!!.elevation = 10f
                }
                //点击popupWindow空白背景不消失，不捕抓事件
                binding.root.setOnTouchListener { v, event -> true }
                val currentPopupWindow = popupWindow
                //3秒后自动消失
                Handler().postDelayed({
                    if (currentPopupWindow!!.isShowing) {
                        currentPopupWindow.dismiss()
                    }
                }, 3000)
                try {
                    popupWindow!!.showAtLocation(
                        currentActivity.window.decorView,
                        Gravity.TOP,
                        0,
                        0
                    )
                } catch (e: Exception) {
                }
            }
        }

        /**关闭 */
        @JvmStatic
        fun close() {
            if (popupWindow != null) {
                //界面向上移动消失

                popupWindow!!.dismiss()
                popupWindow = null
            }
        }

        fun getMessageExText(message: MessageContent?): String {
            if (message is TextMessage) {
                val textMessage = message
                return if (textMessage.content != null) textMessage.content else ""
            }
            if (message is FileMessage) {
                return "[File]"
            }
            if (message is ImageMessage) {
                return "[Image]"
            }
            if (message is VoiceMessage) {
                return "[Voice]"
            }
            if (message is HLinkMsg || message is SJMsg) {
                if (message is SJMsg) {
                    val singleJsonMessage = message
                    if (singleJsonMessage.isGift) {
                        return "[Gift]"
                    }
                    if (singleJsonMessage.isTppContent) {
                        return "[Recharge Card]"
                    }
                }
                return "[Link]"
            }
            return ""
        }
    }
}

// 防抖
//支持设置延迟时间，设置tag 相同tag 防抖
// 之前 延迟前后执行，避免重复
internal class DebounceTimer 