package com.juicy.app.modules.base.dialog.GiftDialog

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.ToastUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.app.R
import com.juicy.app.modules.base.adapter.GiftAdapter
import com.juicy.app.databinding.LayoutVideoBinding
import com.juicy.app.modules.base.dialog.HalfShoppingDialog
import com.juicy.common.model.message_event.MyHaseCoinsObject
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.config.Cache
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.RecycleViewUtils.PagerGridLayoutManager
import com.juicy.common.viewmodel.GiftShowViewModel


class GiftDialog : DialogFragment {
    private var giftDialogBinding: LayoutVideoBinding? = null
    private var giftShowViewModel: GiftShowViewModel? = null
    private var sendGiftCallBack: SendGiftCallBack? = null
    private var giftName: String? = ""
    private var toUserId = ""
    private var channelName = ""

    private var isShowRobot = true

    constructor()
    constructor(
        isShowRobot: Boolean,
        sendGiftCallBack: SendGiftCallBack?,
        toUserId: String,
        channelName: String
    ) {
        this.isShowRobot = isShowRobot
        this.sendGiftCallBack = sendGiftCallBack
        this.toUserId = toUserId
        this.channelName = channelName
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        giftDialogBinding = LayoutVideoBinding.inflate(inflater)
        return giftDialogBinding!!.root
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.BOTTOM)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initData()
        initVM()
        initClick()
        initEvent()
    }

    fun setDirection() {
        if (LanguageManager.instance?.isLanguageForce == true) {
            giftDialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            giftDialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        giftDialogBinding!!.normalWide.text = LanguageManager.instance?.getLocalTranslate("Get_Coins")
    }

    private fun initClick() {
        giftDialogBinding!!.normalWide.setOnClickListener { v: View? ->
            val shoppingDialog = HalfShoppingDialog(
                isShowRobot,
                object : HalfShoppingDialog.CallBack {
                    override fun onCallBack() {
                        giftShowViewModel!!.updateCoinsData()
                    }
                })
            shoppingDialog.entry = if (isShowRobot) "im" else "chatting"
            activity?.let {
                shoppingDialog.show(it.supportFragmentManager, "")
            }

        }
    }

    private var socketEventBusBack: SocketEventBusBack? = null
    private fun initEvent() {
        giftShowViewModel?.updateCoinsData()
        if (Cache.instance.giftList.isNullOrEmpty()) {
            giftShowViewModel?.updateGiftData()
        } else {
            initRc()
        }
        socketEventBusBack = object : SocketEventBusBack {
            override fun AvailableCoins(myHaseCoinsObject: MyHaseCoinsObject) {
                super.AvailableCoins(myHaseCoinsObject)
                giftShowViewModel!!.updateCoinsData()
            }
        }
        addMessageEvent(socketEventBusBack!!)
    }

    private fun initVM() {
        giftShowViewModel?.giftData?.observe(this) { giftList: List<com.juicy.common.model.bean.OrderInfoBean> ->
            if (giftList == null) {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Error......"
                    )
                )
            } else {
                initRc()
            }
        }
        giftShowViewModel?.coinsData?.observe(
            this,
            Observer { s: String? ->
                if (s == null) {
                    ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Error"))
                } else {
                    giftDialogBinding!!.unselectedNext.text = "$s "
                }
            })
        giftShowViewModel?.sendGiftData?.observe(this, Observer { sendGiftData: com.juicy.common.model.bean.CoinsBean? ->
                if (sendGiftData != null) {
                    if (Cache.instance.userInfo != null &&
                        Cache.instance.userInfo?.availableCoins != null) {
                        giftDialogBinding!!.unselectedNext.text =
                            Cache.instance.userInfo?.availableCoins.toString() + " "
                    }
                    if (giftName != null) {
                        sendGiftCallBack!!.onSuccess(giftName)
                    }
                } else {
                    val shoppingDialog =
                        HalfShoppingDialog(isShowRobot, object : HalfShoppingDialog.CallBack {
                            override fun onCallBack() {
                                giftShowViewModel!!.updateCoinsData()
                            }
                        })
                    shoppingDialog.entry = if (isShowRobot) "im_gift" else "chatting_gift"
                    activity?.let {
                        shoppingDialog.show(it.supportFragmentManager, "")
                    }


                    // 创建一个Handler实例
                    val handler = Handler(Looper.getMainLooper())

                    // 使用postDelayed方法延迟0.5秒执行代码
                    handler.postDelayed({
                        ToastUtils.showLong(
                            LanguageManager.instance!!.getLocalTranslate(
                                "Coin Not Enough"
                            )
                        )
                    }, 200)
                }
            })
    }

    private fun initData() {
        if (Cache.instance.userInfo != null && Cache.instance.userInfo?.availableCoins != null) {
            giftDialogBinding!!.unselectedNext.text = Cache.instance.userInfo?.availableCoins.toString() + " "
        }
        giftShowViewModel = ViewModelProvider(this).get(
            GiftShowViewModel::class.java
        )
    }


    var dotsView: MutableList<View>? = null
    private var dotDidView: View? = null
    private fun initRc() {
        val columns = 2 // 2 列
        val rows = 3 // 3 行
        val pagerGridLayoutManager = PagerGridLayoutManager(columns, rows)
        giftDialogBinding!!.miniText.layoutManager = pagerGridLayoutManager
        pagerGridLayoutManager.setPagerChangedListener(object :
            PagerGridLayoutManager.PagerChangedListener {
            override fun onPagerCountChanged(pagerCount: Int) {
                Log.d("TAG", "onPagerCountChanged: ")

                val linearLayout = giftDialogBinding!!.rigidCancel // 获取 LinearLayout 的引用
                linearLayout.removeAllViews()
                dotsView = ArrayList()
                // 动态添加子 View 的布局
                for (i in 0 until pagerCount) {
                    // 创建子 View
                    val view = View(context)
                    if (i == 0) {
                        val drawableResId = R.drawable.bg_gift_dot_sel_platinum
                        view.setBackgroundResource(drawableResId)
                        dotDidView = view
                    } else {
                        val drawableResId = R.drawable.bg_gift_dot_nor_ebony
                        view.setBackgroundResource(drawableResId)
                    }
                    // 创建子 View 的布局参数
                    var layoutParams = if (i == 0) {
                        LinearLayout.LayoutParams(
                            dip2px(15f),
                            dip2px(4f)
                        )
                    } else {
                        LinearLayout.LayoutParams(
                            dip2px(4f),
                            dip2px(4f)
                        )
                    }
                    layoutParams.leftMargin = dip2px(2.5f)
                    // 设置子 View 的布局参数
                    view.layoutParams = layoutParams
                    dotsView?.add(view)
                    // 将子 View 添加到 LinearLayout 中
                    linearLayout.addView(view)
                }
            }

            override fun onPagerIndexSelected(
                prePagerIndex: Int,
                currentPagerIndex: Int,
                pagerCount: Int
            ) {
                Log.d("TAG", "onPagerIndexSelected: ")
                val selectView = dotsView!![currentPagerIndex]

                if (selectView !== dotDidView) {
                    val layoutParams2 = LinearLayout.LayoutParams(
                        dip2px(4f),
                        dip2px(4f)
                    )
                    layoutParams2.leftMargin = dip2px(2.5f)
                    dotDidView!!.layoutParams = layoutParams2
                    val drawableResId2 = R.drawable.bg_gift_dot_nor_ebony
                    dotDidView!!.setBackgroundResource(drawableResId2)

                    val layoutParams = LinearLayout.LayoutParams(
                        dip2px(15f),
                        dip2px(4f)
                    )
                    layoutParams.leftMargin = dip2px(2.5f)
                    selectView.layoutParams = layoutParams
                    val drawableResId = R.drawable.bg_gift_dot_sel_platinum
                    selectView.setBackgroundResource(drawableResId)

                    dotDidView = selectView
                }
            }
        })


        //        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(),2,RecyclerView.HORIZONTAL,false);
//        giftDialogBinding.miniText.setLayoutManager(gridLayoutManager);
        val giftAdapter = GiftAdapter(R.layout.item_progress)



        giftDialogBinding!!.miniText.adapter = giftAdapter


        giftAdapter.setList(Cache.instance.giftList?.toMutableList()?: mutableListOf<com.juicy.common.model.bean.OrderInfoBean>())
        giftAdapter.setOnItemClickListener { adapter: BaseQuickAdapter<*, *>?, view: View?, position: Int ->
            if(!Cache.instance.giftList.isNullOrEmpty()){
                if (position <= Cache.instance.giftList!!.size - 1) {
                    giftName = Cache.instance.giftList!![position].code
                    val askGiftEventBean = com.juicy.common.model.bean.AskGiftEventBean(
                        channelName, giftName!!, 1, toUserId,
                        Cache.instance.giftList!![position].coinPrice
                    )
                    giftShowViewModel!!.sendGift(askGiftEventBean)
                }
            }
        }
    }

    interface SendGiftCallBack {
        fun onSuccess(giftName: String?)
    }

    override fun onDestroy() {
        removeMessageEvent(socketEventBusBack!!)
        super.onDestroy()
    }
}
