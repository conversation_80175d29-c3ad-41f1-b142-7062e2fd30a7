package com.juicy.app.modules.base.dialog.GiftDialog

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.blankj.utilcode.util.SpanUtils
import com.juicy.app.R
import com.juicy.app.databinding.DialogCardBinding
import com.juicy.common.utils.LanguageManager

class SwtichCameraDialog(
    context: Context?,
    private val settingText: String,
    private val confirmBack: ConfirmBack
) :
    DialogFragment() {
    private var switchBinding: DialogCardBinding? = null
    private var coins: String? = null
    private var day: String? = null

    override fun onStart() {
        super.onStart()
        initSetting()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.app_dialogtheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // 设置点击外部不关闭
        dialog!!.setCanceledOnTouchOutside(false)

        // 设置返回键不关闭
//        getDialog().setCancelable(false);
        switchBinding = DialogCardBinding.inflate(inflater)
        return switchBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        setData()
        initEvent()
        if (coins != null && day != null) {
            switchBinding!!.selectedCollapsed.visibility = View.VISIBLE
            SpanUtils.with(switchBinding!!.selectedCollapsed)
                .append(coins!!)
                .append(" ")
                .append(getString(R.string.common_ok_16))
                .append(" ")
                .append("/")
                .append(" ")
                .append(day!!)
                .append(" ")
                .append(getString(R.string.time_now))
                .create()
        }
        switchBinding!!.editLower.text = LanguageManager.instance?.getLocalTranslate("No")?:"No"
        switchBinding!!.holderMain.text = LanguageManager.instance?.getLocalTranslate("Yes")?:"Yes"
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            switchBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            switchBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        switchBinding!!.holderMain.text = LanguageManager.instance?.getLocalTranslate("Yes")?:"Yes"
        switchBinding!!.editLower.text = LanguageManager.instance?.getLocalTranslate("No")?:"No"
    }

    private fun initEvent() {
        switchBinding!!.editLower.setOnClickListener {
            confirmBack.onCancelBack()
            dismiss()
        }
        switchBinding!!.holderMain.setOnClickListener {
            confirmBack.onConfirmBack()
            dismiss()
        }
    }

    private fun setData() {
        switchBinding!!.stopDistant.text = settingText
    }

    private fun initSetting() {
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.CENTER)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    fun setCameraText(coins: String?, day: String?) {
        this.coins = coins
        this.day = day
    }

    interface ConfirmBack {
        fun onConfirmBack()
        fun onCancelBack() {}
    }
}
