package com.juicy.app.modules.call.fragment

import android.media.MediaPlayer
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.Observer
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.juicy.app.R
import com.juicy.app.databinding.FragmentSidebarBinding
import com.juicy.app.modules.call.VideoCallActivity
import com.juicy.common.videoCall.CallUtils
import com.juicy.app.modules.base.dialog.HalfShoppingDialog
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.model.message_event.AppOnHangUpBean
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PermissionUtil.Companion.checkVideoPermission
import com.juicy.common.utils.PermissionUtil.PermissionCallback
import com.juicy.common.utils.VibratorUtils
import jp.wasabeef.glide.transformations.BlurTransformation

class BeCallFragment : BaseFragment() {
    private var socketEventBusBack: SocketEventBusBack? = null
    private var beCallBinding: FragmentSidebarBinding? = null
    private var user: com.juicy.common.model.bean.JuicyUserInfoBean? = null
    //铃声播放器
    private var mediaPlayer: MediaPlayer? = null
    private var activity: VideoCallActivity? = null


    //当前是否正在挂断
    private var isHangUping = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun initData() {
        activity = getActivity() as VideoCallActivity?
        mediaPlayer = MediaPlayer.create(context, R.raw.ic_oldcall_cyan)
        mediaPlayer?.setLooping(true)
        if (mediaPlayer != null) {
            mediaPlayer!!.start()
        }
        VibratorUtils.startVibration()
        initEvent()
        CallUtils.instance?.videoCallViewModel?.loadCacheUserData(CallUtils.instance?.callInfoModel?.anchorId)
    }

    private fun initUser(user: com.juicy.common.model.bean.JuicyUserInfoBean) {
        if (user.age != null && (user.age is Int) && user.age.toString() != null) {
            beCallBinding!!.reloadDisabled.text = user.age.toString() + " | "
        }
        if (user.nickname != null) {
            beCallBinding!!.hiddenForeground.text = user.nickname
        }

        if (user.avatarUrl != null) {
            Glide.with(this)
                .load(user.avatarUrl)
                .apply(RequestOptions.bitmapTransform(BlurTransformation(20)))
                .into(beCallBinding!!.xlAdd)
            loadCircleImage(activity, user.avatarUrl, beCallBinding!!.wrapClosed)
        }
        var price = "--"
        if (Cache.instance.userInfo != null && Cache.instance.userInfo!!.vip != null && Cache.instance.userInfo!!.vip) {
            if (user.vipUnitPrice != null && user.vipUnitPrice.toString() != null) {
                price = user.vipUnitPrice.toString()
            }
        } else {
            if (user.unitPrice != null && user.unitPrice.toString() != null) {
                price = user.unitPrice.toString()
            }
        }
        if (CallUtils.instance?.callInfoModel != null && CallUtils.instance?.callInfoModel?.isFree == true) {
            beCallBinding!!.lastFlexible.visibility = View.VISIBLE
            beCallBinding!!.gridLarge.visibility = View.INVISIBLE
        } else {
            beCallBinding!!.gridLarge.visibility = View.VISIBLE
            beCallBinding!!.lastFlexible.visibility = View.GONE
        }
        beCallBinding!!.copyPanel.setText(
            price + LanguageManager.instance!!.getLocalTranslate("minute"),
            TextView.BufferType.SPANNABLE
        )
        if (user.country != null) {
            beCallBinding!!.fieldUpdate.text = user.country
        }
    }

    //初始化事件监听
    private fun initEvent() {
        //监听是否被挂断
        socketEventBusBack = object : SocketEventBusBack {
            override fun hangup(appOnHangUpBean: AppOnHangUpBean?) {
                super.hangup(appOnHangUpBean)
                //                beCallBinding.compactBorder.setEnabled(false);
                if (isDestroy(activity)) return
                if (isHangUping) return
                isHangUping = true
                CallUtils.instance?.hangUp(Constant.MISSED_CALL, Constant.CL_REMOTE_USER_LEFT)
                //监听到马上挂断，不需要等接口完成
                activity!!.finish()
            }
        }
        addMessageEvent(socketEventBusBack!!)

        //监听用户信息变化
        CallUtils.getCallViewModel()?.userData?.observe(
            this,
            Observer<com.juicy.common.model.bean.JuicyUserInfoBean?> { user: com.juicy.common.model.bean.JuicyUserInfoBean? ->
                if (isDestroy(activity) || user == null) return@Observer
                this.user = user
                try {

                    if (user.avatarUrl != null) {
                        CallUtils.getCallInfoModel()?.anchorPhoto = (user.avatarUrl)
                    }
                    if (user.nickname != null) {
                        CallUtils.getCallInfoModel()?.anchorName = (user.nickname)
                    }
                    if (user.userId != null) {
                        initUser(user)
                    }
                } catch (e: Exception) {
                }
            })

        //监听是否被挂断
        CallUtils.getCallViewModel()?.hangUpData?.observe(
            this,
            Observer<Boolean?> { aBoolean: Boolean? ->
                if (isDestroy(activity)) return@Observer
                aBoolean?.let {
                    if (it){
                        activity?.finish()
                    }
                }
            })


        //监听接听数据
        CallUtils.getCallViewModel()?.pickUpData?.observe(this, object : Observer<Boolean?> {
            override fun onChanged(value: Boolean?) {
                if (isDestroy(activity)) return
                if (value != null && value == true) {
                    if (user != null) {
                        CallUtils.instance?.callInfoModel?.callType = (Constant.INCOMING_CALL)
                        activity!!.jumpCallingPage(user)
                    }
                }
            }
        })
    }

    override fun initView() {
        if (Cache.instance.reviewPkg) beCallBinding!!.copyPanel.visibility =
            View.INVISIBLE

        //监听挂断按钮
        beCallBinding!!.compactBorder.setOnClickListener { view: View? ->
            beCallBinding!!.compactBorder.isEnabled = false
            if (isHangUping) return@setOnClickListener
            isHangUping = true
            CallUtils.instance?.hangUp(
                Constant.REJECTED_CALL,
                Constant.NORMAL
            )

            //主动挂的马上结束就行
            activity?.finish()
        }


        beCallBinding!!.successFocused.setOnClickListener {
            if (user == null) return@setOnClickListener
            //获取视频权限
            activity?.let {
                checkVideoPermission(it,
                    object : PermissionCallback {
                        override fun complete() {
                            doCall()
                        }
                    })
            }
        }
        if (LanguageManager.instance!!.isLanguageForce) {
            beCallBinding!!.mdForward.scaleX = -1f
        }
        beCallBinding!!.stopNear.text = LanguageManager.instance!!.getLocalTranslate("free")
    }

    /**执行呼叫 */
    private fun doCall() {
        var canCall = false
        if (Cache.instance.reviewPkg) {
            CallUtils.instance?.pickUp()
        } else {
            if (CallUtils.instance?.callInfoModel == null) return
            if (CallUtils.instance?.callInfoModel?.isFree == true) {
                CallUtils.instance?.pickUp()
                return
            }
            if (Cache.instance.userInfo != null && Cache.instance.userInfo!!.availableCoins != null) {
                if (Cache.instance.userInfo!!.vip != null && Cache.instance.userInfo!!.vip) {
                    if (user!!.vipUnitPrice != null && Cache.instance.userInfo!!.availableCoins >= user!!.vipUnitPrice) {
                        canCall = true
                    }
                } else {
                    if (user!!.unitPrice != null && Cache.instance.userInfo!!.availableCoins >= user!!.unitPrice) {
                        canCall = true
                    }
                }
                if (canCall) {
                    CallUtils.instance?.pickUp()
                } else {
                    activity?.let {
                        val halfShoppingDialog = HalfShoppingDialog()
                        halfShoppingDialog.entry = "oncall"
                        halfShoppingDialog.show(it.supportFragmentManager, "")
                    }
                    val handler = Handler()
                    handler.postDelayed({
                        ToastUtils.showShort(
                            LanguageManager.instance!!.getLocalTranslate("Coins_Not_Enough")
                        )
                    }, 100) // 延迟时间
                }
            }
        }
    }
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        beCallBinding = FragmentSidebarBinding.inflate(inflater)
        return beCallBinding!!.root
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mediaPlayer != null) {
            mediaPlayer!!.release()
            mediaPlayer = null
        }
        activity?.closeTime()
        VibratorUtils.stopVibration()
        removeMessageEvent(socketEventBusBack!!)
    }


}