package com.juicy.app.modules.base.dialog

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.juicy.app.databinding.DialogCounterBinding
import com.juicy.common.utils.LanguageManager

class DeleteDialog : DialogFragment {
    private var dialogDeleteBinding: DialogCounterBinding? = null
    private var confirmBack: SettingDialog.ConfirmBack? = null


    constructor()

    constructor(confirmBack: SettingDialog.ConfirmBack?) {
        this.confirmBack = confirmBack
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dialogDeleteBinding = DialogCounterBinding.inflate(inflater)
        return dialogDeleteBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initClick()
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            dialogDeleteBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            dialogDeleteBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        dialogDeleteBinding!!.backFirst.text =
            LanguageManager.instance?.getLocalTranslate("Confirm account deletion? ")?:"Confirm account deletion? "
        dialogDeleteBinding!!.distantSwipe.text =
            LanguageManager.instance?.getLocalTranslate("This_will_erase_all_your_data_including_any_products_youve_purchased_Please_make_sure_this_is_what_you_want")?:"This will erase all your data, including any products you’ve purchased. Please make sure this is what you want."
        dialogDeleteBinding!!.pressedBrief.text = LanguageManager.instance?.getLocalTranslate("No")?:"No"
        dialogDeleteBinding!!.pagerBar.text = LanguageManager.instance?.getLocalTranslate("Yes")?:"Yes"
    }

    private fun initClick() {
        dialogDeleteBinding!!.pressedBrief.setOnClickListener { view: View? -> dismiss() }
        dialogDeleteBinding!!.throughPlay.setOnClickListener { view: View? -> dismiss() }
        dialogDeleteBinding!!.pagerBar.setOnClickListener { v: View? ->
            confirmBack!!.onConfirmBack()
        }
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.CENTER)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    interface ConfirmBack {
        fun onConfirmBack()
    }
}
