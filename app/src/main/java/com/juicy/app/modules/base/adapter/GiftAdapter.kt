package com.juicy.app.modules.base.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.common.config.Constant.giftResMap

class GiftAdapter(layoutResId: Int) : BaseQuickAdapter<com.juicy.common.model.bean.OrderInfoBean, GiftViewHolder>(layoutResId) {

    override fun convert(holder: GiftViewHolder, item: com.juicy.common.model.bean.OrderInfoBean) {
        if (giftResMap[item.code] != null) {
            holder.giftIcon.setImageResource(giftResMap[item.code]?:0)
        }
        holder.giftPrice.text = item.coinPrice.toString()?:""
    }
}


