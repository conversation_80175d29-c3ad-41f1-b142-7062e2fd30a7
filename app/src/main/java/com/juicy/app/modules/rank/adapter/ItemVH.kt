package com.juicy.app.modules.rank.adapter

import android.view.View
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatTextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.common.utils.view.CircleImageView

class ItemVH(view: View) : BaseViewHolder(view) {
    var anchorName: AppCompatTextView
    var anchorPhoto: CircleImageView
    var itemCVView: RelativeLayout
    var sort: AppCompatTextView

    init {


        sort = view.findViewById(com.juicy.app.R.id.extendedChecked)
        anchorPhoto = view.findViewById(com.juicy.app.R.id.fluidForeground)
        anchorName = view.findViewById(com.juicy.app.R.id.avatarLarge)

        itemCVView = view.findViewById<RelativeLayout>(com.juicy.app.R.id.thickToggle)
    }
}