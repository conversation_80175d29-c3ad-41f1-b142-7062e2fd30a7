package com.juicy.app.modules.info.adapter

import androidx.appcompat.content.res.AppCompatResources
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.app.R
import com.juicy.common.model.bean.MediaInfoBean

class AnchorVideoAdapter(layoutResId: Int) : BaseQuickAdapter<MediaInfoBean, AnchorVideoVH>(layoutResId) {
    override fun convert(holder: AnchorVideoVH, item: MediaInfoBean) {
        Glide.with(context)
            .load(item.middleThumbUrl ?: (item.thumbUrl ?: ""))
            .placeholder(AppCompatResources.getDrawable(context, R.drawable.img_empty_big_full_pearl))
            .into(holder.videoPhoto)
    }
}


