package com.juicy.app.modules.wall

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.BarUtils
import com.juicy.app.R
import com.juicy.app.modules.rank.UserRankActivity
import com.juicy.app.modules.wall.adapter.TabAdapter
import com.juicy.app.databinding.LayoutSwitchBinding
import com.juicy.app.modules.base.dialog.CountrySreenDialogFragment
import com.juicy.app.modules.base.dialog.CountrySreenDialogFragment.CountryCallBack
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.model.event.AnchorisFirstParamEvent
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.SpSaveUtil.putStringValue
import com.yesterselga.countrypicker.Country
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class WallFirstFragment : BaseFragment() {
    private lateinit var binding: LayoutSwitchBinding
    private val tabNames: MutableList<String> = ArrayList()
    private var isSubAll = true
    private var currentPager = 0
    private var fragmentManager: FragmentManager? = null
    private var adapter: TabAdapter? = null
    private val secondFragments: MutableList<AnchorWallSecondFragment> = ArrayList()


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = LayoutSwitchBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }
    private fun initFragments() {
        for (fragment in secondFragments) {
            fragmentManager
                ?.beginTransaction()
                ?.setCustomAnimations(
                    R.anim.anim_slide_in_right_yellow,  // 进入动画
                    R.anim.anim_slide_out_left_brown // 退出动画
                )
                ?.add(binding.nextDown.id, fragment)
                ?.hide(fragment) // 先隐藏所有 Fragment
                ?.commit()
        }

        // 显示第一个 Fragment
        if (!secondFragments.isEmpty()) {
            fragmentManager?.beginTransaction()
                ?.setCustomAnimations(
                    R.anim.anim_slide_in_right_yellow,
                    R.anim.anim_slide_out_left_brown
                )
                ?.show(secondFragments[0])
                ?.commit()
        }
    }


    override fun initData() {
        binding.root.post(Runnable {
            if (isDestroy(activity)) return@Runnable
            if (!isAdded || view == null) return@Runnable
            fragmentManager = childFragmentManager
            if (Cache.instance.userStratResult != null && Cache.instance.userStratResult?.broadcasterWallTagList != null
                && !Cache.instance.userStratResult?.broadcasterWallTagList.isNullOrEmpty()) {
                for (name in Cache.instance.userStratResult?.broadcasterWallTagList!!) {
                    tabNames.add(name.showName?:"")
                }
                for (i in tabNames.indices) {
                    secondFragments.add(AnchorWallSecondFragment(i, object : PagerCallBack {
                        @SuppressLint("NotifyDataSetChanged")
                        override fun onCallBack(pager: Int) {
                            secondFragments[Cache.instance.currentFirstTab].stopFreshTime()
                            Cache.instance.currentFirstTab = pager
                            adapter?.notifyDataSetChanged()
                            changeFragment(pager)
                            binding.withinSlider.scrollToPosition(Cache.instance.currentFirstTab)
                            Cache.instance.currentSecondTab =
                                secondFragments[Cache.instance.currentFirstTab].currentSubPosition
                        }
                    }))
                }
                adapter = TabAdapter(
                    R.layout.item_wrapper,
                    tabNames.toList()?: listOf<String>()
                )
                initFragments()
                changeFragment(0)
            }
        })
    }
    fun showCountryBtn(isShow: Boolean) {
        if (Cache.instance.userStratResult != null && Cache.instance.userStratResult?.isReviewPkg != null &&
            Cache.instance.userStratResult?.isReviewPkg == true
        ) {
            binding.clockEnabled.visibility = View.INVISIBLE
            putStringValue(SpKeyPool.CURRECTCOUNTRY, "")
        } else {
            binding.clockEnabled.visibility =
                if (isShow && isSubAll) View.VISIBLE else View.INVISIBLE
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onAnchorSecondEvent(event: AnchorisFirstParamEvent) {
        //
        isSubAll = event.isFirstAll
        showCountryBtn(event.isFirstAll)
    }



    fun checkDidSelectCountry() {
        val allCountry = Country.getAllCountries()
        var currentCountryModel: Country? = null
        val currentCountry = getStringValue(SpKeyPool.CURRECTCOUNTRY, "")
        for (country in allCountry) {
            if (country.code == currentCountry) {
                //
                currentCountryModel = country
                break
            }
        }


        if (currentCountryModel != null) {
            binding.clockEnabled.setImageResource(currentCountryModel.flag)
        } else {
            binding.clockEnabled.setImageResource(R.drawable.ic_main_country_olive)
        }
    }


    @SuppressLint("NotifyDataSetChanged")
    override fun initView() {
        binding.root.post(Runnable {
            if (isDestroy(activity)) return@Runnable
            if (!isAdded || view == null) return@Runnable
            if (Cache.instance.reviewPkg) {
                binding.wideFlexible.visibility = View.GONE
            }
            val manager = LinearLayoutManager(
                activity
            )
            manager.orientation = LinearLayoutManager.HORIZONTAL
            binding.withinSlider.layoutManager = manager
            if (LanguageManager.instance?.isLanguageForce == true) {
                binding.withinSlider.layoutDirection = View.LAYOUT_DIRECTION_RTL
            }
            val mainPageLayoutParams =
                binding.contentSelect.layoutParams as FrameLayout.LayoutParams
            mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
            binding.contentSelect.layoutParams = mainPageLayoutParams
            binding.wideFlexible.setOnClickListener {
                val intent = Intent(
                    context,
                    UserRankActivity::class.java
                )
                startActivity(intent)
            }

            binding.clockEnabled.setOnClickListener {
                val evaluateDialog = CountrySreenDialogFragment(object : CountryCallBack {
                    override fun didSelectBlock() {
                        checkDidSelectCountry()
                    }
                })
                val currentActivity =
                    currentActivity as? AppCompatActivity?
                currentActivity?.let {
                    evaluateDialog.show(it.supportFragmentManager, "")
                }

            }

            if (adapter != null) {
                binding.withinSlider.adapter = adapter
                adapter?.addChildClickViewIds(R.id.upCard)
                adapter?.setOnItemChildClickListener { adapter, view, position ->
                    if (view.id == R.id.upCard) {
                        if (Cache.instance.currentFirstTab != position) {
                            secondFragments[Cache.instance.currentFirstTab].stopFreshTime()
                            Cache.instance.currentFirstTab = position
                            adapter.notifyDataSetChanged()
                            changeFragment(position)
                            binding.withinSlider.scrollToPosition(Cache.instance.currentFirstTab)
                            Cache.instance.currentSecondTab =
                                secondFragments[Cache.instance.currentFirstTab].currentSubPosition
                        }
                    }
                    showCountryBtn(position == 0)
                }
            }
            checkDidSelectCountry()
        })
    }





    interface PagerCallBack {
        fun onCallBack(pager: Int)
    }
    private fun changeFragment(position: Int) {
        try {
            val isLeft = position < currentPager

            val transaction = fragmentManager?.beginTransaction()
            if (LanguageManager.instance?.isLanguageForce == true) {
                if (isLeft) {
                    transaction?.setCustomAnimations(
                        R.anim.anim_slide_in_right_yellow,
                        R.anim.anim_slide_out_left_brown
                    )
                } else {
                    transaction?.setCustomAnimations(
                        R.anim.anim_slide_in_left_navy,
                        R.anim.anim_slide_out_right_ebony
                    )
                }
            } else {
                if (isLeft) {
                    transaction?.setCustomAnimations(
                        R.anim.anim_slide_in_left_navy,
                        R.anim.anim_slide_out_right_ebony
                    )
                } else {
                    transaction?.setCustomAnimations(
                        R.anim.anim_slide_in_right_yellow,
                        R.anim.anim_slide_out_left_brown
                    )
                }
            }



            for (i in secondFragments.indices) {
                if (i == position) {
                    transaction?.show(secondFragments[i])
                } else {
                    transaction?.hide(secondFragments[i])
                }
            }
            transaction?.commit()

            showCountryBtn(position == 0)

            currentPager = position
        } catch (e: Exception) {
        }
    }
}
