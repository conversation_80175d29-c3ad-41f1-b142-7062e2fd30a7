package com.juicy.app.modules.base.dialog

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.juicy.app.R
import com.juicy.app.databinding.DialogVideoBinding
import com.juicy.common.utils.LanguageManager

class HandUpDialog(
    context: Context?,
    private val settingText: String,
    private val confirmBack: ConfirmBack
) :
    DialogFragment() {
    private var handUpBinding: DialogVideoBinding? = null
    private var coins: String? = null
    private var day: String? = null

    override fun onStart() {
        super.onStart()
        initSetting()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.app_dialogtheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // 设置点击外部不关闭
        dialog!!.setCanceledOnTouchOutside(false)

        // 设置返回键不关闭
//        getDialog().setCancelable(false);
        handUpBinding = DialogVideoBinding.inflate(inflater)
        return handUpBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        setData()
        initEvent()
        handUpBinding!!.editLower.text = LanguageManager.instance!!.getLocalTranslate("Cancel")
        handUpBinding!!.holderMain.text =
            LanguageManager.instance!!.getLocalTranslate("Confirm")
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            handUpBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            handUpBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        handUpBinding!!.holderMain.text =
            LanguageManager.instance!!.getLocalTranslate("Confirm")
        handUpBinding!!.editLower.text = LanguageManager.instance!!.getLocalTranslate("Cancel")
    }

    private fun initEvent() {
        handUpBinding!!.editLower.setOnClickListener {
            confirmBack.onCancelBack()
            dismiss()
        }
        handUpBinding!!.holderMain.setOnClickListener {
            confirmBack.onConfirmBack()
            dismiss()
        }
    }

    private fun setData() {
        handUpBinding!!.stopDistant.text = settingText
    }

    private fun initSetting() {
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.CENTER)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    fun setCameraText(coins: String?, day: String?) {
        this.coins = coins
        this.day = day
    }

    interface ConfirmBack {
        fun onConfirmBack()
        fun onCancelBack() {}
    }
}
