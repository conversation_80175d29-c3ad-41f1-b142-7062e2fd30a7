package com.juicy.app.modules.base.dialog

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.juicy.app.R
import com.juicy.app.modules.base.adapter.ReportItemAdapter
import com.juicy.app.databinding.ItemLineBinding
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.LanguageManager
import com.juicy.common.viewmodel.BlockViewModel

class ReportDialogFragment : DialogFragment {
    private var reportLayoutBinding: ItemLineBinding? = null
    private var anchorName: String? = null
    private var anchorPhoto: String? = null
    private var anchorId: String? = null
    private val reportNames: MutableList<String> = ArrayList()
    private var blockViewModel: BlockViewModel? = null

    constructor()

    constructor(anchorName: String?, anchorPhoto: String?, anchorId: String?) {
        this.anchorName = anchorName
        this.anchorPhoto = anchorPhoto
        this.anchorId = anchorId
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.BOTTOM)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        reportLayoutBinding = ItemLineBinding.inflate(inflater)
        return reportLayoutBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initData()
        initView()
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            reportLayoutBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            reportLayoutBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        reportLayoutBinding!!.pressedBrief.text = LanguageManager.instance!!.getLocalTranslate("Cancel")
    }

    private fun initView() {
        reportLayoutBinding!!.avatarLarge.text = anchorName
        Glide.with(this).load(anchorPhoto).into(reportLayoutBinding!!.fluidForeground)
        reportLayoutBinding!!.fluidForeground.borderColor = context!!.getColor(R.color.white)
        reportLayoutBinding!!.fluidForeground.borderWidth = dip2px(2f)
        reportLayoutBinding!!.pressedBrief.setOnClickListener { view: View? -> dismiss() }
        initRc()
    }

    private fun initRc() {
        val adapter = ReportItemAdapter(R.layout.item_avatar)
        val manager = LinearLayoutManager(
            context
        )
        manager.orientation = RecyclerView.VERTICAL
        reportLayoutBinding!!.fillSurface.layoutManager = manager
        adapter.setNewInstance(reportNames)
        reportLayoutBinding!!.fillSurface.adapter = adapter
        adapter.setOnItemClickListener(OnItemClickListener { adapter, view, position ->
            if (anchorId!!.isEmpty()) return@OnItemClickListener
            blockViewModel!!.addBlock(
                com.juicy.common.model.bean.AnchorHBean(
                    anchorId!!.toLong(), Constant.REPORT,
                    reportNames[position]
                ),
                anchorId!!
            )
        })
    }

    private fun initData() {
        blockViewModel = ViewModelProvider(this).get(BlockViewModel::class.java)
        reportNames.add(LanguageManager.instance!!.getLocalTranslate("Fraud"))
        reportNames.add(LanguageManager.instance!!.getLocalTranslate("Pornographic"))
        reportNames.add(LanguageManager.instance!!.getLocalTranslate("False_gender"))
        reportNames.add(LanguageManager.instance!!.getLocalTranslate("Political_sensitive"))
        reportNames.add(LanguageManager.instance!!.getLocalTranslate("Other"))
        blockViewModel!!.getAddBlockData().observe(
            this
        ) { aBoolean ->
            if (aBoolean) {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        getString(R.string.msg_available)
                    )
                )
            } else {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        getString(R.string.msg_unavailable)
                    )
                )
            }
            dismiss()
        }
    }
}
