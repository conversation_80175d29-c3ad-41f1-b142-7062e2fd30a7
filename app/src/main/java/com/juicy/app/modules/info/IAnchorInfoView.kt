package com.juicy.app.modules.info

import com.juicy.common.model.bean.JuicyUserInfoBean

interface IAnchorInfoView {
    fun openBlockDialog(userId: String?)
    fun closeLoading(isClose: Boolean)
    fun updateUserIm(imList: List<String?>?)
    fun updateUserStatus(status: String)
    fun updateUserGift(giftList: List<String>?)
    fun changeFollowState(isFollow: Boolean)
    fun updateInfo(userInfo: com.juicy.common.model.bean.JuicyUserInfoBean?)

}
