package com.juicy.app.modules.base.dialog

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.Animation
import android.view.animation.RotateAnimation
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.juicy.app.modules.base.adapter.PayOneAdapter
import com.juicy.app.modules.base.adapter.HidePayAdapter
import com.juicy.app.databinding.LayoutPaddingBinding
import com.juicy.common.model.bean.ExtraBean.ChannelInfoBean
import com.juicy.common.model.event.PayResultEvent
import com.juicy.common.model.event.PayResultStatusChangeEvent
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.AppDeviceUtils.getScreenSize
import com.juicy.common.utils.LanguageManager
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.max

class PayDialog : DialogFragment {
    private var good: com.juicy.common.model.bean.ActivityInfoBean? = null
    private var threePartyDialogBinding: LayoutPaddingBinding? = null
    private var listData: List<ChannelInfoBean> = ArrayList()
    private val headListData: MutableList<ChannelInfoBean> = ArrayList()
    private val hideListData: MutableList<ChannelInfoBean> = ArrayList()
    private var callBack: SummitCallBack? = null
    private var currentChannelBean: ChannelInfoBean? = null
    private var hidePayAdapter: HidePayAdapter? = null
    private var payOneAdapter: PayOneAdapter? = null
    private var animation: Animation? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        threePartyDialogBinding = LayoutPaddingBinding.inflate(inflater)
        return threePartyDialogBinding!!.root
    }

    constructor(listData: List<ChannelInfoBean>, good: com.juicy.common.model.bean.ActivityInfoBean?, callBack: SummitCallBack?) {
        this.listData = listData
        this.good = good
        this.callBack = callBack
    }

    constructor()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        threePartyDialogBinding!!.emptyBelow.setOnClickListener { v: View? -> dismiss() }
        initData()
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            threePartyDialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            threePartyDialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        val scaleX = if (LanguageManager.instance!!.isLanguageForce) -1f else 1f
        threePartyDialogBinding!!.firstShadow.scaleX = scaleX
        threePartyDialogBinding!!.innerEmpty.scaleX = scaleX

        threePartyDialogBinding!!.maximalCounter.text =
            LanguageManager.instance!!.getLocalTranslate("Payment_method")
        threePartyDialogBinding!!.pickForward.text =
            LanguageManager.instance!!.getLocalTranslate("Item")
        threePartyDialogBinding!!.closedMenu.text =
            LanguageManager.instance!!.getLocalTranslate("Price")
        threePartyDialogBinding!!.wrapperUpper.text =
            LanguageManager.instance!!.getLocalTranslate("Give_up_Award_Coins")
        threePartyDialogBinding!!.outsideUndo.text =
            LanguageManager.instance!!.getLocalTranslate("Continue")
    }

    private fun initData() {
        if (good != null && good!!.price != null) {
            threePartyDialogBinding!!.frameAround.text = "$" + good!!.price!!.toDouble()
        }
        if (good != null && good!!.exchangeCoin != null) {
            threePartyDialogBinding!!.primaryUp.text = good!!.exchangeCoin.toString()
        }

        classification()
        if (headListData.isNotEmpty()) {
            initExa(initExaCoins(0))
            currentChannelBean = headListData[0]
        }
        initRc()
    }

    private fun initExaCoins(selectPresent: Int): Int {
        var present = 0
        if (good != null && !headListData.isNullOrEmpty() && selectPresent < headListData.size) {
            present =
                if (good!!.thirdpartyCoinPercent != null && "GP" !=  headListData[selectPresent].payChannel ) {
                    if (good?.promotion == true) {
                        max(
                            headListData[selectPresent].promotionPresentCoinRatio.toDouble(),
                            good!!.thirdpartyCoinPercent?.toDouble()?:0.0
                        ).toInt()
                    } else {
                        max(
                            headListData[selectPresent].presentCoinRatio.toDouble(),
                            good!!.thirdpartyCoinPercent?.toDouble()?:0.0
                        ).toInt()
                    }
                } else if (good!!.extraCoinPercent != null) {
                    if (good?.promotion == true) {
                        max(
                            headListData[selectPresent].promotionPresentCoinRatio.toDouble(),
                            good!!.extraCoinPercent?.toDouble()?:0.0
                        ).toInt()
                    } else {
                        max(
                            headListData[selectPresent].presentCoinRatio.toDouble(),
                            good!!.extraCoinPercent!!.toDouble()
                        ).toInt()
                    }
                } else {
                    if (good?.promotion == true) {
                        headListData[selectPresent].promotionPresentCoinRatio
                    } else {
                        headListData[selectPresent].presentCoinRatio
                    }
                }
        }
        return present
    }

    private fun initRc() {
        val headManager = LinearLayoutManager(
            activity
        )
        headManager.orientation = RecyclerView.VERTICAL
        val hideManager = LinearLayoutManager(
            activity
        )
        hideManager.orientation = RecyclerView.VERTICAL
        threePartyDialogBinding!!.contentTiny.layoutManager = headManager
        threePartyDialogBinding!!.pagerDefault.layoutManager = hideManager
        payOneAdapter = PayOneAdapter(
            good, requireActivity(), headListData, object : PayOneAdapter.CallBack {
                override fun onCallBack(selectPresent: Int) {
                    initExa(initExaCoins(selectPresent))
                    hidePayAdapter!!.selectPosition = -1
                    hidePayAdapter!!.notifyDataSetChanged()
                    currentChannelBean = headListData[selectPresent]
                }

            }
        )

        hidePayAdapter = HidePayAdapter(
            hideListData,object :PayOneAdapter.CallBack{
                override fun onCallBack(selectPresent: Int) {
                    payOneAdapter!!.selectPosition = -1
                    payOneAdapter!!.notifyDataSetChanged()
                    initExa(0)
                    currentChannelBean = hideListData[selectPresent]
                }

            }
        )
        threePartyDialogBinding!!.contentTiny.adapter = payOneAdapter
        threePartyDialogBinding!!.pagerDefault.adapter = hidePayAdapter
        if (hideListData.isEmpty()) {
            threePartyDialogBinding!!.cancelBtn.visibility = View.GONE
            threePartyDialogBinding!!.pagerDefault.visibility = View.GONE
        } else {
            threePartyDialogBinding!!.pagerDefault.visibility = View.VISIBLE
            threePartyDialogBinding!!.cancelBtn.visibility = View.VISIBLE
        }
        threePartyDialogBinding!!.cancelBtn.setOnClickListener { v: View? ->
            if (threePartyDialogBinding!!.pagerDefault.visibility == View.VISIBLE) {
                threePartyDialogBinding!!.pagerDefault.visibility = View.GONE
            } else {
                threePartyDialogBinding!!.pagerDefault.visibility = View.VISIBLE
            }
        }

        threePartyDialogBinding!!.forwardShadow.setOnClickListener(View.OnClickListener {
            if (isFastClick) {
                return@OnClickListener
            }
            threePartyDialogBinding!!.insideFluid.visibility = View.VISIBLE
            animation = RotateAnimation(
                0f,
                360f,
                Animation.RELATIVE_TO_SELF,
                0.5f,
                Animation.RELATIVE_TO_SELF,
                0.5f
            )
            animation?.setDuration(3000)
            animation?.setRepeatCount(50)
            animation?.setFillAfter(true)

            threePartyDialogBinding!!.insideFluid.startAnimation(animation) //show loading image
            callBack!!.onCallBack(currentChannelBean)
            threePartyDialogBinding!!.outsideUndo.isEnabled = false
        })
    }

    private fun initExa(selectPresent: Int) {
        if (good != null && selectPresent > 0) {
            val builder = StringBuilder()
            builder.append("+").append(selectPresent * good!!.exchangeCoin!! / 100)
            threePartyDialogBinding!!.innerEmpty.text = builder
            threePartyDialogBinding!!.firstShadow.visibility = View.VISIBLE
        } else {
            threePartyDialogBinding!!.firstShadow.visibility = View.GONE
        }
    }

    private fun classification() {
        for (channelBean in listData) {
            if (channelBean.itemType == 1) {
                if (channelBean.payChannel == "GP") {
                    headListData.add(channelBean)
                } else {
                    headListData.add(0, channelBean)
                }
            } else if (channelBean.itemType == 2) {
                headListData.add(channelBean)
            } else if (channelBean.itemType == 3) {
                hideListData.add(channelBean)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)

            val layoutParams = dialog!!.window!!.attributes

            dialog!!.window!!.setGravity(Gravity.BOTTOM)
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = getScreenSize(this.context)[1] - ((getScreenSize(
                this.context
            )[1]) * 0.2).toInt()
            dialog!!.window!!.attributes = layoutParams
        }
    }

    interface SummitCallBack {
        fun onCallBack(channelInfoBean: ChannelInfoBean?)
    }

    override fun dismiss() {
        EventBus.getDefault().unregister(this)
        super.dismiss()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(userDialogEvent: PayResultEvent?) {
        dismissAllowingStateLoss()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPayStatus(userDialogEvent: PayResultStatusChangeEvent?) {
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            if (animation != null) {
                animation!!.cancel()
            }
            threePartyDialogBinding!!.insideFluid.clearAnimation()
            threePartyDialogBinding!!.insideFluid.visibility = View.GONE
            threePartyDialogBinding!!.outsideUndo.isEnabled = true
        }, 100)
    }
}
