package com.juicy.app.modules.base.dialog

import android.app.Dialog
import android.content.Context
import android.text.TextUtils
import android.view.animation.Animation
import android.view.animation.RotateAnimation
import android.widget.ImageView
import android.widget.TextView
import com.juicy.app.R
import com.juicy.common.utils.LanguageManager

class LoadDialog {
    private val dialog: Dialog
    private var tvMessage: TextView? = null
    private var ivImage: ImageView? = null

    constructor(context: Context) {
        dialog = Dialog(context, R.style.dialog)
        dialog.setContentView(R.layout.layout_body)
        dialog.setCancelable(true)
        dialog.setCanceledOnTouchOutside(false)
        initView()
    }

    constructor(context: Context, msg: String?) {
        dialog = Dialog(context, R.style.dialog)
        dialog.setContentView(R.layout.layout_body)
        dialog.setCancelable(true)
        dialog.setCanceledOnTouchOutside(false)
        initView()
    }

    fun setMsg(msg: String?) {
        if (!TextUtils.isEmpty(msg)) {
            tvMessage!!.text = msg
        }
    }

    fun show() {
        dialog.show()
    }

    fun dismiss() {
        dialog.dismiss()
        ivImage!!.clearAnimation()
    }

    val isShowing: Boolean
        get() = dialog.isShowing

    private fun initView() {
        val animation: Animation = RotateAnimation(
            0f,
            360f,
            Animation.RELATIVE_TO_SELF,
            0.5f,
            Animation.RELATIVE_TO_SELF,
            0.5f
        )
        animation.duration = 3000
        animation.repeatCount = 50
        animation.fillAfter = true

        tvMessage = dialog.findViewById(R.id.counterOutside)
        ivImage = dialog.findViewById(R.id.fieldTitle)
        ivImage?.startAnimation(animation)
        tvMessage?.setText(LanguageManager.instance!!.getLocalTranslate("Loading"))
    }

    companion object {
        fun newLoadingDialog(context: Context): LoadDialog {
            return LoadDialog(context)
        }

        fun newLoadingDialog(context: Context, msg: String?): LoadDialog {
            return LoadDialog(context, msg)
        }
    }
}
