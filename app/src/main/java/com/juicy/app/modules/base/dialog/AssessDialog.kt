package com.juicy.app.modules.base.dialog

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.juicy.app.databinding.LayoutStatusBinding
import com.juicy.common.utils.GoogleAppRatingUtilis.start
import com.juicy.common.utils.LanguageManager

class AssessDialog : DialogFragment() {
    private var assessBinding: LayoutStatusBinding? = null

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.setCancelable(false)
            dialog!!.window!!.setGravity(Gravity.CENTER)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        assessBinding = LayoutStatusBinding.inflate(inflater)
        return assessBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initClick()
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            assessBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            assessBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        assessBinding!!.maximalCounter.text = LanguageManager.instance?.getLocalTranslate("Five_Star")?:"Five Star"
        assessBinding!!.backFirst.text = LanguageManager.instance?.getLocalTranslate("Loved_the_call_Rate_5_and_keep_the_connections_shining")?:"Loved the call? Rate 5 ⭐ and keep the connections shining!"
        assessBinding!!.fitHolder.text = "⭐ ⭐ ⭐ ⭐ ⭐"
        assessBinding!!.acrossExpand.text = LanguageManager.instance?.getLocalTranslate("Not_great")?:"Not great"
        assessBinding!!.pressedBrief.text = LanguageManager.instance!!.getLocalTranslate("Cancel")
    }

    private fun initClick() {
        assessBinding!!.fitHolder.setOnClickListener { v: View? ->
            activity?.let {
                start(it)
            }
            dismiss()
        }
        assessBinding!!.acrossExpand.setOnClickListener { v: View? -> dismiss() }
        assessBinding!!.pressedBrief.setOnClickListener { v: View? -> dismiss() }
    }
}
