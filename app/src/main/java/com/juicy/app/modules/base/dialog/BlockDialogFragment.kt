package com.juicy.app.modules.base.dialog

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.content.res.AppCompatResources
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.juicy.app.R
import com.juicy.app.databinding.ItemPaddingBinding
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.LanguageManager
import com.juicy.common.viewmodel.BlockViewModel

class BlockDialogFragment : DialogFragment {
    private var anchorPhoto: String? = null
    private var anchorName: String? = null
    private var anchorId: String? = null
    private var isFollow = false
    private var blockDialogLayoutBinding: ItemPaddingBinding? = null
    private var blockCallBack: BlockCallBack? = null
    private var blockViewModel: BlockViewModel? = null
    private var isBlock = false
    private var loadingDialog: LoadDialog? = null
    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.BOTTOM)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    constructor()

    constructor(
        blockCallBack: BlockCallBack?,
        isFollow: Boolean,
        anchorId: String?,
        anchorName: String?,
        anchorPhoto: String?
    ) {
        this.blockCallBack = blockCallBack
        this.isFollow = isFollow
        this.anchorId = anchorId
        this.anchorName = anchorName
        this.anchorPhoto = anchorPhoto
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        blockDialogLayoutBinding = ItemPaddingBinding.inflate(inflater)
        return blockDialogLayoutBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initView()
        initVM()
        initEvent()
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            blockDialogLayoutBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            blockDialogLayoutBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }

        blockDialogLayoutBinding!!.previewChecked.text =
            LanguageManager.instance!!.getLocalTranslate("Block")
        blockDialogLayoutBinding!!.sidebarBadge.text =
            LanguageManager.instance!!.getLocalTranslate("Report")
        blockDialogLayoutBinding!!.pressedBrief.text =
            LanguageManager.instance!!.getLocalTranslate("Cancel")
    }

    private fun initEvent() {
        blockViewModel!!.getBlockList(anchorId!!)
        blockDialogLayoutBinding!!.previewChecked.setOnClickListener { v: View? ->
            loadingDialog = LoadDialog(context!!)
            loadingDialog!!.show()
            if (!isBlock) {
                val anchor =
                    com.juicy.common.model.bean.AnchorHBean(anchorId!!.toLong(), Constant.BLOCK)
                blockViewModel!!.addBlock(anchor, anchorId!!)
            } else {
                val anchor = com.juicy.common.model.bean.AnchorBlockBean(anchorId!!.toLong())
                blockViewModel!!.deleteBlockData(anchor, anchorId!!)
            }
        }
        blockDialogLayoutBinding!!.expandChip.setOnClickListener { v: View? ->
            loadingDialog = LoadDialog(context!!)
            loadingDialog!!.show()
            if (!isFollow) {
                blockViewModel!!.addFollow(anchorId!!)
            } else {
                blockViewModel!!.deleteFollow(anchorId!!)
            }
        }
        blockDialogLayoutBinding!!.pressedBrief.setOnClickListener { v: View? ->
            dismiss()
        }
        blockDialogLayoutBinding!!.sidebarBadge.setOnClickListener { v: View? ->
            if (anchorId!!.isEmpty() || anchorId == "") return@setOnClickListener
            val dialogFragment = ReportDialogFragment(
                anchorName,
                anchorPhoto,
                anchorId
            )
            dialogFragment.show(activity!!.supportFragmentManager, "")
            dismiss()
        }
    }

    private fun initVM() {
        blockViewModel = ViewModelProvider(this).get(BlockViewModel::class.java)
        blockViewModel!!.getBlockListData().observe(
            this
        ) { aBoolean -> refreshBlock(aBoolean) }
        blockViewModel!!.getAddBlockData().observe(
            this
        ) { aBoolean ->
            if (loadingDialog != null) {
                loadingDialog!!.dismiss()
            }
            if (aBoolean) {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Block Success"
                    )
                )
                blockCallBack!!.addBlock()
                refreshBlock(true)
                dismiss()
            } else {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Block Failed"
                    )
                )
            }
        }
        blockViewModel!!.getDeleteBlockData().observe(
            this
        ) { aBoolean ->
            if (loadingDialog != null) {
                loadingDialog!!.dismiss()
            }
            if (aBoolean) {
                refreshBlock(false)
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Cancel Block Success"
                    )
                )
                blockCallBack!!.deleteBlock()
                dismiss()
            } else {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Cancel Block Failed"
                    )
                )
            }
        }
        blockViewModel!!.getAddFollowData().observe(
            this
        ) { aBoolean ->
            if (loadingDialog != null) {
                loadingDialog!!.dismiss()
            }
            if (aBoolean) {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Follow Success"
                    )
                )
                refreshFollow(true)
                blockCallBack!!.addFollow()
                dismiss()
            } else {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Follow Failed"
                    )
                )
            }
        }
        blockViewModel!!.getFollowChangeData().observe(
            this
        ) { aBoolean ->
            if (loadingDialog != null) {
                loadingDialog!!.dismiss()
            }
            refreshFollow(aBoolean)
        }
        blockViewModel!!.getDeleteFollowData().observe(
            this
        ) { aBoolean ->
            if (loadingDialog != null) {
                loadingDialog!!.dismiss()
            }
            if (aBoolean) {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Cancel Follow Success"
                    )
                )
                refreshFollow(false)
                blockCallBack!!.deleteFollow()
                dismiss()
            } else {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Cancel Follow Failed"
                    )
                )
            }
        }
    }

    private fun refreshBlock(aBoolean: Boolean) {
        this.isBlock = aBoolean
        if (aBoolean) {
            blockDialogLayoutBinding!!.previewChecked.text =
                LanguageManager.instance!!.getLocalTranslate("UnBlock")
        } else {
            blockDialogLayoutBinding!!.previewChecked.text =
                LanguageManager.instance!!.getLocalTranslate("Block")
        }
    }

    private fun refreshFollow(isFollow: Boolean) {
        this.isFollow = isFollow
        if (isFollow) {
            blockDialogLayoutBinding!!.expandChip.background =
                AppCompatResources.getDrawable(
                    context!!, R.drawable.bg_followed_charcoal
                )
            blockDialogLayoutBinding!!.unfocusedTop.setImageDrawable(
                AppCompatResources.getDrawable(
                    context!!, R.drawable.ic_like_color_jade
                )
            )
            blockDialogLayoutBinding!!.frontSwitch.text =
                LanguageManager.instance!!.getLocalTranslate("Followed")
        } else {
            blockDialogLayoutBinding!!.frontSwitch.text =
                LanguageManager.instance!!.getLocalTranslate("Follow")
            blockDialogLayoutBinding!!.unfocusedTop.setImageDrawable(
                AppCompatResources.getDrawable(
                    context!!, R.drawable.ic_like_gray_mint
                )
            )
            blockDialogLayoutBinding!!.expandChip.background =
                AppCompatResources.getDrawable(
                    context!!, R.drawable.bg_login_cream
                )
        }
    }

    private fun initView() {
        blockDialogLayoutBinding!!.avatarLarge.text = anchorName
        Glide.with(this).load(anchorPhoto).into(
            blockDialogLayoutBinding!!.fluidForeground
        )
        blockDialogLayoutBinding!!.fluidForeground.borderColor = context!!.getColor(R.color.white)
        blockDialogLayoutBinding!!.fluidForeground.borderWidth = dip2px(2f)
        refreshFollow(isFollow)
    }


    interface BlockCallBack {
        fun deleteBlock()

        fun addBlock()

        fun addFollow()

        fun deleteFollow()
    }
}
