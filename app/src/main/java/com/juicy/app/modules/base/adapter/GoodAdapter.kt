package com.juicy.app.modules.base.adapter

import android.view.View
import androidx.appcompat.content.res.AppCompatResources
import com.blankj.utilcode.util.SpanUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.app.R
import com.juicy.common.config.Cache

class GoodAdapter(layoutResId: Int) : BaseQuickAdapter<com.juicy.common.model.bean.ActivityInfoBean?, GoodViewHolder>(layoutResId) {
    private var newSaleVH: GoodViewHolder? = null
    override fun convert(goodViewHolder: GoodViewHolder, good: com.juicy.common.model.bean.ActivityInfoBean?) {
        newSaleVH = goodViewHolder
        if (good != null) {
            if (good.isVipItem) {
                goodViewHolder.goodTag.visibility = View.GONE
                goodViewHolder.vipAddText.visibility = View.VISIBLE
                goodViewHolder.vipTextIcon.visibility = View.VISIBLE
                goodViewHolder.icon.visibility = View.GONE
                goodViewHolder.clVipKing.visibility = View.VISIBLE
                goodViewHolder.tvOff.visibility = View.GONE
                goodViewHolder.goodItem.background =
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.bg_good_vip_item_mist
                    )
                goodViewHolder.buy.background =
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.bg_buy_bg_rose
                    )
                val builder = StringBuilder()
                builder.append(good.localPrice)
                goodViewHolder.buy.text = builder
                if (good.exchangeCoin != null) {
                    goodViewHolder.tvCoin.text = good.exchangeCoin.toString()
                }
            } else {
                goodViewHolder.good = good
                goodViewHolder.vipAddText.visibility = View.GONE
                goodViewHolder.vipTextIcon.visibility = View.GONE
                goodViewHolder.icon.visibility = View.VISIBLE
                goodViewHolder.clVipKing.visibility = View.GONE
                goodViewHolder.tvOff.visibility = View.VISIBLE
                goodViewHolder.buy.background =
                    AppCompatResources.getDrawable(
                        context,
                        R.drawable.bg_buy_bg_rose
                    )
                if (good.tags != null && !good.tags.isNullOrEmpty()) {
                    goodViewHolder.goodTag.visibility = View.VISIBLE
                    goodViewHolder.goodTag.text = good.tags
                    goodViewHolder.goodTag.background =
                        AppCompatResources.getDrawable(
                            context,
                            R.drawable.ic_my_coins_icon_snow
                        )
                    if (good == Cache.instance.userPromotionGood && Cache.instance.userInfo!!.availableCoins >= 150) {
                        goodViewHolder.goodTag.visibility = View.GONE
                    }
                } else {
                    goodViewHolder.goodTag.visibility = View.GONE
                }
                if (good.promotion != null &&
                    good.promotion!! && Cache.instance.userInfo != null && Cache.instance.userInfo!!.availableCoins < 150 && Cache.instance.userPromotionGood != null
                ) {
                    if (good.price != null && good.originalPrice != null) {
                        SpanUtils.with(goodViewHolder.buy)
                            .append("$" + good.price)
                            .setFontSize(12, true)
                            .setBoldItalic().append(" ")
                            .append("$" + good.originalPrice.toString())
                            .setFontSize(9, true)
                            .setStrikethrough()
                            .setItalic()
                            .append(" ")
                            .create()
                    }
//                    goodVH.goodTag.background =
//                        AppCompatResources.getDrawable(
//                            context,
//                            R.drawable.bg_good_tag_color_gold
//                        )
                    goodViewHolder.goodItem.background =
                        AppCompatResources.getDrawable(
                            context,
                            R.drawable.bg_good_bg_yellow
                        )
                } else {
                    if (good.price != null && good.localPrice != null) {
                        val builder = StringBuilder()
                        builder.append(good.localPrice)
                        goodViewHolder.buy.text = builder
                    }
//                    goodVH.goodTag.background =
//                        AppCompatResources.getDrawable(
//                            context,
//                            R.drawable.bg_good_tag_brown
//                        )
                    goodViewHolder.goodItem.background =
                        AppCompatResources.getDrawable(
                            context,
                            R.drawable.bg_good_bg_yellow
                        )
                }

                if (good.exchangeCoin != null) {
                    goodViewHolder.tvCoin.text = good.exchangeCoin.toString()
                }
                if (good.discount != null && good.discount!!.toDouble().toString() != "0.0") {
                    val builder = StringBuilder()
                    builder.append((good.discount!!.toDouble() * 100).toInt()).append("%")
                        .append(" off").append(" ")
                    goodViewHolder.tvOff.text = builder
                    goodViewHolder.tvOff.visibility = View.VISIBLE
                } else {
                    goodViewHolder.tvOff.visibility = View.INVISIBLE
                }

                val extra =
                    ((if (good.extraCoinPercent != null) good.extraCoinPercent else 0)!! / 100.0 *
                            ((if (good.exchangeCoin != null) good.exchangeCoin else 0)!!)).toInt()

                if (extra > 0) {
                    goodViewHolder.extra.text = "+$extra"
                    goodViewHolder.extra.visibility = View.VISIBLE // 显示标签
                } else {
                    goodViewHolder.extra.visibility = View.GONE // 隐藏标签
                }
            }
        }
    }

    fun setNewSaleTag(good: com.juicy.common.model.bean.ActivityInfoBean) {
        if (newSaleVH == null) {
//            setData(0, Cache.getInstance().userPromotionGood);
//            newSaleVH.goodTag.setText(good.getTags());
        } else {
            newSaleVH?.goodTag?.text = good.tags
        }
    }
}



