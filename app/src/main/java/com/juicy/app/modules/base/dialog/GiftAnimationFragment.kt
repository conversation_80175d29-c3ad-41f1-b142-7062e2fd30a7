package com.juicy.app.modules.base.dialog

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import com.blankj.utilcode.util.ActivityUtils
import com.juicy.app.databinding.FragmentCalendarBinding
import com.juicy.common.config.Constant.giftResMap
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.JuicyAnimalUtil.startGiftAnimal
import com.juicy.common.utils.LanguageManager

class GiftAnimationFragment : DialogFragment {
    private var dialogBinding: FragmentCalendarBinding? = null

    private var giftName: String? = null

    constructor()

    constructor(giftName: String?) {
        this.giftName = giftName
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dialogBinding = FragmentCalendarBinding.inflate(inflater)
        return dialogBinding!!.root
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.CENTER)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        if (giftResMap!![giftName!!] != null) {
            dialogBinding?.largeWithin?.setImageResource(giftResMap!![giftName!!]!!)
            startGiftAnimal(dialogBinding!!.microUpdate, true)
            startGiftAnimal(dialogBinding!!.largeWithin, false)
        }


        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            if (!isDestroy(activity) && !isStateSaved) {
                dismiss()
            }
        }, 2000)
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            dialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            dialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        dialogBinding!!.microUpdate.clearAnimation()
        dialogBinding!!.largeWithin.clearAnimation()
    }

    companion object {
        @JvmStatic
        fun GiftAnimationFragmentShow(giftName: String?) {
            val currentActivity = ActivityUtils.getTopActivity() as FragmentActivity
            val fragmentManager = currentActivity.supportFragmentManager

            val ExtremelyCallDialog =
                fragmentManager.findFragmentByTag("GiftAnimationFragment") as GiftAnimationFragment?
            if (ExtremelyCallDialog != null) {
                ExtremelyCallDialog.dismiss()
                val handler = Handler(Looper.getMainLooper())
                handler.postDelayed({
                    val evaluateDialog = GiftAnimationFragment(giftName)
                    evaluateDialog.show(fragmentManager, "GiftAnimationFragment")
                }, 100)
            } else {
                val evaluateDialog = GiftAnimationFragment(giftName)
                evaluateDialog.show(fragmentManager, "GiftAnimationFragment")
            }
        }
    }
}