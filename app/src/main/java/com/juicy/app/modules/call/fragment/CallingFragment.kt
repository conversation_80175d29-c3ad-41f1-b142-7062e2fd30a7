package com.juicy.app.modules.call.fragment

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.TimeUtils
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.google.gson.Gson
import com.juicy.app.R
import com.juicy.app.modules.call.adapter.VideoCallAdapter
import com.juicy.app.databinding.FragmentBinding
import com.juicy.app.modules.call.VideoCallActivity
import com.juicy.app.JuicyApplication.Companion.juicyApplication
import com.juicy.app.modules.base.adapter.EmojiPaperAdapter
import com.juicy.common.videoCall.CallUtils
import com.juicy.app.modules.base.dialog.BlockDialogFragment
import com.juicy.app.modules.base.dialog.BlockDialogFragment.BlockCallBack
import com.juicy.app.modules.base.dialog.GiftAnimationFragment.Companion.GiftAnimationFragmentShow
import com.juicy.app.modules.base.dialog.GiftDialog.GiftDialog
import com.juicy.app.modules.base.dialog.GiftDialog.GiftDialog.SendGiftCallBack
import com.juicy.app.modules.base.dialog.GiftDialog.SwtichCameraDialog
import com.juicy.app.modules.base.dialog.HalfShoppingDialog
import com.juicy.app.modules.base.dialog.HandUpDialog
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.model.event.BlockParamsEvent
import com.juicy.common.model.event.PayResultEvent
import com.juicy.common.model.message_event.MyHaseCoinsObject
import com.juicy.common.model.message_event.EstHangUpTimeBean
import com.juicy.common.model.message_event.MatchChatFreeTimesBean
import com.juicy.common.model.message_event.GiftAskEventBean
import com.juicy.common.model.message_event.MessageEventBean
import com.juicy.common.model.message_event.AppOnHangUpBean
import com.juicy.common.model.message_event.AppRechargeOrderStatusBean
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.networks.request.SocketUtil.SocketUtils
import com.juicy.common.networks.delegate.GetCoinCountInterface.getCoin
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.Constant.giftResMap
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.LogBeanFactory.getCreateCallLog
import com.juicy.common.utils.PermissionUtil.Companion.checkCallingAudioPermission
import com.juicy.common.utils.PermissionUtil.Companion.checkCallingCamaraPermission
import com.juicy.common.utils.PermissionUtil.PermissionCallingback
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.getIntValue
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import com.juicy.common.utils.SpSaveUtil.putIntValue
import com.juicy.common.utils.TextUtils.getStrLen
import com.juicy.common.utils.TextUtils.subStrByLen
import com.juicy.common.utils.throttle.Throttle.throttle
import com.juicy.common.utils.view.EmojiView.EmojiCallBack
import com.juicy.common.utils.view.TextureVideoViewOutlineProvider
import io.agora.rtc.IRtcEngineEventHandler
import io.agora.rtc.RtcEngine
import io.agora.rtc.video.CameraCapturerConfiguration
import io.agora.rtc.video.CameraCapturerConfiguration.CAMERA_DIRECTION
import io.agora.rtc.video.CameraCapturerConfiguration.CAPTURER_OUTPUT_PREFERENCE
import io.agora.rtc.video.VideoCanvas
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.functions.Consumer
import io.reactivex.rxjava3.schedulers.Schedulers
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONException
import org.json.JSONObject
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.util.Timer
import java.util.TimerTask
import java.util.concurrent.TimeUnit
import kotlin.math.abs

class CallingFragment : BaseFragment {
    private var callingBinding: FragmentBinding? = null

    private var VideoCallAdapter: VideoCallAdapter? = null
    private val chatList: List<com.juicy.common.model.bean.ChatMessageItemBean> = ArrayList()

    private var myUid = 0
    private var anchorUid = 0

    //等待主播加入20秒倒计时
    private var waitJoinTimer: Timer? = null

    private var giftTimer: Disposable? = null
    private var sendGiftName: String? = ""
    private var isLike = false
    private var isOpenCamera = false

    private var not_coin_timer: Timer? = null
    private var giftButtonTimer: Disposable? = null
    private var chatTimer: Disposable? = null
    private var giftName: String? = ""
    private var not_coin_times = 60L

    private var isJuestCamera = -1
    private var isJuestAudio = -1
    private val firstRusume = true
    private var giftCount = 1
    private var socketEventBusBack: SocketEventBusBack? = null
    private var user: com.juicy.common.model.bean.JuicyUserInfoBean? = null
    private var activity: VideoCallActivity? = null
    private var rtcEngine: RtcEngine? = null



    constructor()

    constructor(user: com.juicy.common.model.bean.JuicyUserInfoBean?) {
        this.user = user
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }
    /**取消等待主播加入20秒倒计时 */
    private fun cancelWaitJoinTimer() {
        if (waitJoinTimer != null) {
            waitJoinTimer!!.cancel()
            waitJoinTimer = null
        }
    }

    /**开启20秒等待主播加入倒计时 */
    private fun startWaitJoinTimer() {
        cancelWaitJoinTimer()
        waitJoinTimer = Timer()
        callingBinding!!.dropWithin.visibility = View.VISIBLE
        waitJoinTimer!!.schedule(object : TimerTask() {
            override fun run() {
                activity!!.runOnUiThread {
                    //转跳主线程
                    requireActivity().runOnUiThread {
                        cancelWaitJoinTimer()
                        //Toast 加入频道失败
                        ToastUtils.showShort(
                            LanguageManager.instance!!.getLocalTranslate(
                                "Joining the channel failed"
                            )
                        )
                        offRoom(Constant.CL_EXCEPTION)
                    }
                }
            }
        }, 20000)
    }


    override fun onResume() {
        super.onResume()
        if (isDestroy(getActivity())) return
        getActivity()?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        //       if (!firstRusume) {
        checkCallingCamaraPermission(
            getActivity() as BaseActivity?,
            object : PermissionCallingback {
                override fun onGranted() {
                    if (isJuestCamera != -1 && isJuestCamera != 1 && rtcEngine != null) {
                        // 重新拉取自己视频流
                        rtcEngine!!.enableLocalVideo(true)
                        rtcEngine!!.muteLocalVideoStream(false)
                        rtcEngine!!.enableLocalAudio(true)
                        rtcEngine!!.muteLocalAudioStream(false)

                    }
                    isJuestCamera = 1
                }

                override fun onDenied() {
                    isJuestCamera = 0
                }
            })

        checkCallingAudioPermission(getActivity() as BaseActivity?, object : PermissionCallingback {
            override fun onGranted() {
                if (isJuestAudio != -1 && isJuestAudio != 1 && rtcEngine != null) {
                    // 重新拉取自己视频流
                    rtcEngine!!.enableLocalVideo(true)
                    rtcEngine!!.muteLocalVideoStream(false)
                    rtcEngine!!.enableLocalAudio(true)
                    rtcEngine!!.muteLocalAudioStream(false)

                }
                isJuestAudio = 1
            }

            override fun onDenied() {
                isJuestAudio = 0
            }
        })
        //       }
//
//        if (firstRusume) {
//            firstRusume = false;
//        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBlockEvent(event: BlockParamsEvent) {
        //
        if (user != null && event.userId == user!!.userId) {
            user!!.block = event.isBlock
            Cache.instance.networkCacheManager.putObject("userInfo_" + event.userId + "", user)
        }
        //监听状态
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPayEvent(userDialogEvent: PayResultEvent) {
        if (userDialogEvent.isSuccess) {
            callingBinding!!.updateSelect.visibility = View.GONE
        }
    }

    override fun onStop() {
        super.onStop()
        getActivity()?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (not_coin_timer != null) {
            not_coin_timer!!.cancel()
            not_coin_timer = null
        }
        cancelWaitJoinTimer()
    }

    override fun initData() {
        if (CallUtils.instance?.callInfoModel?.isMatch == true) {
            Cache.instance.netStrategy
            val layoutParams = callingBinding!!.customTitle.layoutParams as ConstraintLayout.LayoutParams
            layoutParams.topToBottom = R.id.largeSecondary
            callingBinding!!.customTitle.layoutParams = layoutParams
        }
        startWaitJoinTimer()

        activity = getActivity() as VideoCallActivity?
        VideoCallAdapter = VideoCallAdapter(R.layout.item_divider)
        socketEventBusBack = object : SocketEventBusBack {
            override fun hangup(appOnHangUpBean: AppOnHangUpBean?) {
                super.hangup(appOnHangUpBean)
                if (CallUtils.instance?.callInfoModel != null && CallUtils.instance?.callInfoModel?.channelName != null
                    && appOnHangUpBean?.channelName != null &&
                    CallUtils.instance?.callInfoModel?.channelName
                        .equals(appOnHangUpBean.channelName)
                ) {
                    if (appOnHangUpBean.reason == Constant.NO_COINS) {
                        //余额不足
                        Toast.makeText(
                            juicyApplication,
                            LanguageManager.instance!!.getLocalTranslate("Coins_Not_Enough"),
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        //对方已挂断
                        Toast.makeText(
                            juicyApplication,
                            LanguageManager.instance!!.getLocalTranslate("The_other_party_has_hang_up"),
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    offRoom(appOnHangUpBean.reason!!)
                }
            }

            override fun EstimatedHangUp(estHangUpTimeBean: EstHangUpTimeBean?) {
                super.EstimatedHangUp(estHangUpTimeBean)

                requireActivity().runOnUiThread(Runnable {
                    // 在主线程中执行的操作
                    if (estHangUpTimeBean?.estimateTime != null && estHangUpTimeBean.estimateTime!! <= 60) {
                        if (CallUtils.instance?.callInfoModel != null) {
                            if (CallUtils.instance?.callInfoModel?.isMatch == true) {
                                if (CallUtils.instance?.callInfoModel?.matchFreeFinish == true) {
                                    callingBinding!!.updateSelect.visibility =
                                        View.VISIBLE
                                }
                            } else {
                                callingBinding!!.updateSelect.visibility =
                                    View.VISIBLE
                            }
                        }



                        if (estHangUpTimeBean.estimateTime!! < not_coin_times) {
                            not_coin_times = estHangUpTimeBean.estimateTime!!
                        }
                        if (not_coin_timer != null) {
                            return@Runnable
                        }
                        not_coin_times = estHangUpTimeBean.estimateTime!!

                        updateNotCoinTime(not_coin_times)

                        not_coin_timer = Timer()
                        val task: TimerTask = object : TimerTask() {
                            override fun run() {
                                // 定时器执行的操作
                                requireActivity().runOnUiThread { // 在主线程中执行的操作
                                    not_coin_times = not_coin_times - 1L
                                    if (CallUtils.instance?.callInfoModel != null) {
                                        if (CallUtils.instance?.callInfoModel?.isMatch == true) {
                                            if (CallUtils.instance?.callInfoModel?.matchFreeFinish == true) {
                                                callingBinding!!.updateSelect.visibility =
                                                    View.VISIBLE
                                                updateNotCoinTime(not_coin_times)
                                            }
                                        } else {
                                            callingBinding!!.updateSelect.visibility =
                                                View.VISIBLE
                                            updateNotCoinTime(not_coin_times)
                                        }
                                    }
                                    if (not_coin_times < 0L) {
                                        callingBinding!!.updateSelect.visibility =
                                            View.GONE
                                        if (not_coin_timer != null) {
                                            not_coin_timer!!.cancel()
                                            not_coin_timer = null
                                        }
                                    }
                                }
                            }
                        }
                        not_coin_timer!!.schedule(task, 1000, 1000)
                    } else {
                        if (not_coin_timer != null) {
                            not_coin_timer!!.cancel()
                            not_coin_timer = null
                            return@Runnable
                        }
                        callingBinding!!.updateSelect.visibility = View.GONE
                    }
                })
            }

            fun updateNotCoinTime(not_coin_times: Long) {
                // 创建富文本
                val text =
                    LanguageManager.instance!!.getLocalTranslate("Video_duration_remaining") + not_coin_times + "s"
                val ssb = SpannableStringBuilder(text)
                val start = text.indexOf(not_coin_times.toString())
                val end = start + not_coin_times.toString().length
                ssb.setSpan(
                    ForegroundColorSpan(Color.parseColor("#FF5656")),
                    start,
                    end,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
                callingBinding!!.xsUnselected.setText(ssb, TextView.BufferType.SPANNABLE)
            }

            override fun AvailableCoins(myHaseCoinsObject: MyHaseCoinsObject) {
                super.AvailableCoins(myHaseCoinsObject)
                //                initMyCoins();
//                if (availableCoinsObject != null && availableCoinsObject.coins != null && availableCoinsObject.coins >= 0) {
//                    Cache.getInstance().userInfo.setAvailableCoins(availableCoinsObject.coins);
//                    if (Cache.getInstance().userInfo.getVip() != null && Cache.getInstance().userInfo.getVip()) {
//                        if (availableCoinsObject.coins < user.getVipUnitPrice())
//                            onpenCoinDialog();
//                    } else {
//                        if (availableCoinsObject.coins < user.getUnitPrice())
//                            onpenCoinDialog();
//
//                    }
//                }
            }

            override fun rechargeSuccessBack(appRechargeOrderStatusBean: AppRechargeOrderStatusBean?) {
                super.rechargeSuccessBack(appRechargeOrderStatusBean)
                callingBinding!!.updateSelect.visibility = View.GONE
            }

            fun initMyCoins() {
                getCoin(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>() {
                    override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>) {
                        super.onNext(t)
                    }

                    override fun onError(e: Throwable) {
                        super.onError(e)
                    }
                })
            }

            override fun messageEvent(eventObject: MessageEventBean?) {
                super.messageEvent(eventObject)
                if (eventObject != null && (eventObject.command == null || (eventObject.command != null && eventObject.command != Constant.GIFT))) {
                    val chatBean = com.juicy.common.model.bean.ChatMessageItemBean()
                    if (!user?.avatarUrl.isNullOrEmpty()) {
                        chatBean.avatar = user?.avatarUrl
                    }
                    if (eventObject.content != null) chatBean.content = eventObject.content
                    if (eventObject.fromUserId != null) chatBean.fromUserId = eventObject.fromUserId
                    chatBean.itemType = 1
                    addChatList(chatBean)
                }
            }

            override fun giftAsk(giftAskEventBean: GiftAskEventBean?) {
                super.giftAsk(giftAskEventBean)
                if (giftAskEventBean != null && user != null) {
                    if (!user!!.block) {
                        showGiveGiftButton(giftAskEventBean)
                    }
                }
            }

            override fun flashChatFreeTimeBack(matchChatFreeTimesBean: MatchChatFreeTimesBean?) {
                super.flashChatFreeTimeBack(matchChatFreeTimesBean)
            }
        }
        socketEventBusBack?.let {
            addMessageEvent(it)
        }

        initRc()
        initUserInfo()
        initRtcConfig()
        initVM()
        initEmojiView()
        initCamera()
        initClick()
    }

    private fun initEmojiView() {
        var startPosition = 0
        val emojis = emojis
        val emojiList: MutableList<List<String?>> = ArrayList()
        while (startPosition <= emojis.size - 1) {
            val tempEmojis: MutableList<String?> = ArrayList()
            for (j in 1..28) {
                if (startPosition == emojis.size) {
                    break
                }
                tempEmojis.add(emojis[startPosition])
                startPosition++
            }
            emojiList.add(tempEmojis)
        }
        val emojiPaperAdapter = EmojiPaperAdapter()
        callingBinding!!.pasteContent.adapter = emojiPaperAdapter
        emojiPaperAdapter.setEmojiCallBack(object : EmojiCallBack {
            override fun onCallBack(text: String?) {
                callingBinding!!.pasteEdit.append(text)
            }
        })
        initIndicator(emojiList, callingBinding!!.pasteContent.currentItem)
        callingBinding!!.pasteContent.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                initIndicator(emojiList, position)
            }
        })
        emojiPaperAdapter.setData(emojiList)
    }

    private fun initIndicator(emojiList: List<List<String?>>, currentItem: Int) {
        if (callingBinding!!.mdDimmed.childCount == 0 && activity != null) {
            for (i in emojiList.indices) {
                val view = View(activity)
                activity?.let {
                    view.background = AppCompatResources.getDrawable(
                       it, R.drawable.bg_point_gray_copper
                    )
                }
                callingBinding!!.mdDimmed.addView(view)
                val layoutParams = view.layoutParams as LinearLayout.LayoutParams
                layoutParams.width = dip2px(8f)
                layoutParams.height = dip2px(8f)
                layoutParams.setMargins(0, 0, dip2px(16f), 0)
                view.layoutParams = layoutParams
            }
        }
        for (i in 0 until callingBinding!!.mdDimmed.childCount) {
            if (i == currentItem) {
                activity?.let {
                    callingBinding!!.mdDimmed.getChildAt(i).background =
                        AppCompatResources.getDrawable(
                            it, R.drawable.bg_point_white_slate
                        )
                }
            } else {
                activity?.let {
                    callingBinding!!.mdDimmed.getChildAt(i).background =
                        AppCompatResources.getDrawable(
                            it, R.drawable.bg_point_gray_copper
                        )
                }
            }
        }
    }

    val emojis: List<String>
        get() {
            var br: BufferedReader? = null
            var emojis: List<String> = ArrayList()
            try {
                val `is` = activity?.assets?.open("app_emoji.json")
                val emojiJson = StringBuilder()
                br = BufferedReader(InputStreamReader(`is`))
                var line: String?
                while (null != (br.readLine().also { line = it })) {
                    emojiJson.append(line).append("\r\n")
                }

                val jsonList: List<String> = Gson().fromJson<List<String>>(
                    emojiJson.toString(),
                    MutableList::class.java
                )
                if (jsonList != null) {
                    emojis = jsonList
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                if (null != br) {
                    try {
                        br.close()
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
            }
            return emojis
        }

    private fun initCamera() {
        if (getBooleanVal(SpKeyPool.IS_OPENED, false) == true) {
            if (getIntValue(SpKeyPool.DIRECTION, 0) == 0) {
                //后置摄像头
                rtcEngine!!.setCameraCapturerConfiguration(
                    CameraCapturerConfiguration(
                        CAPTURER_OUTPUT_PREFERENCE.CAPTURER_OUTPUT_PREFERENCE_AUTO,
                        CAMERA_DIRECTION.CAMERA_REAR
                    )
                )
            } else {
                //前置摄像头
                rtcEngine!!.setCameraCapturerConfiguration(
                    CameraCapturerConfiguration(
                        CAPTURER_OUTPUT_PREFERENCE.CAPTURER_OUTPUT_PREFERENCE_AUTO,
                        CAMERA_DIRECTION.CAMERA_FRONT
                    )
                )
            }
        } else {
        }

        if (Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.isCallRearCamera != null &&
            Cache.instance.userStratResult!!.isCallRearCamera!!
        ) {
            callingBinding!!.customTitle.camera!!.visibility = View.VISIBLE
        } else {
            callingBinding!!.customTitle.camera!!.visibility = View.GONE
        }
    }

    private fun changeCamera() {
        if (getBooleanVal(SpKeyPool.IS_OPENED, false)!!) {
            if (getIntValue(SpKeyPool.DIRECTION, 0) == 1) {
                //后置摄像头
                rtcEngine!!.setCameraCapturerConfiguration(
                    CameraCapturerConfiguration(
                        CAPTURER_OUTPUT_PREFERENCE.CAPTURER_OUTPUT_PREFERENCE_AUTO,
                        CAMERA_DIRECTION.CAMERA_REAR
                    )
                )
                putIntValue(SpKeyPool.DIRECTION, 0)
            } else {
                //前置摄像头
                rtcEngine!!.setCameraCapturerConfiguration(
                    CameraCapturerConfiguration(
                        CAPTURER_OUTPUT_PREFERENCE.CAPTURER_OUTPUT_PREFERENCE_AUTO,
                        CAMERA_DIRECTION.CAMERA_FRONT
                    )
                )
                putIntValue(SpKeyPool.DIRECTION, 1)
            }
        } else {
            putBooleanValue(SpKeyPool.IS_OPENED, true)
            rtcEngine!!.setCameraCapturerConfiguration(
                CameraCapturerConfiguration(
                    CAPTURER_OUTPUT_PREFERENCE.CAPTURER_OUTPUT_PREFERENCE_AUTO,
                    CAMERA_DIRECTION.CAMERA_REAR
                )
            )
        }
    }


    @SuppressLint("ClickableViewAccessibility")
    private fun initRc() {
        val manager = LinearLayoutManager(activity)
        manager.stackFromEnd = true
        callingBinding!!.maskDynamic.layoutManager = manager
        //设置高度
        //获取屏幕宽度
        val dm = getActivity()?.resources?.displayMetrics
        var lp = callingBinding!!.maskDynamic.layoutParams
        if (lp != null && dm != null) {
            lp.height = (dm.heightPixels * 0.5).toInt() - dip2px(50f)
            lp.width = dm.widthPixels - dip2px(120f)
        } else {
            if (dm != null){
                lp = LinearLayout.LayoutParams(
                    dm.widthPixels - dip2px(120f),
                    (dm.heightPixels * 0.5).toInt() - dip2px(50f)
                )
            }
        }

        callingBinding!!.maskDynamic.layoutParams = lp
        callingBinding!!.maskDynamic.adapter = VideoCallAdapter
        VideoCallAdapter!!.setNewInstance(chatList.toMutableList())
        callingBinding!!.maskDynamic.setItemViewCacheSize(500)

        callingBinding!!.maskDynamic.setOnTouchListener(object : View.OnTouchListener {
            private var startY = 0f
            private val CLICK_THRESHOLD = 5f // 点击阈值，小于这个值认为是点击而不是滑动

            override fun onTouch(v: View, event: MotionEvent): Boolean {
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        startY = event.y
                        // 允许父视图拦截触摸事件
                        v.parent.requestDisallowInterceptTouchEvent(false)
                        return false
                    }

                    MotionEvent.ACTION_MOVE -> {
                        val deltaY = event.y - startY
                        // 如果是垂直滑动，则不拦截触摸事件，允许RecyclerView处理滑动
                        if (abs(deltaY.toDouble()) > CLICK_THRESHOLD) {
                            v.parent.requestDisallowInterceptTouchEvent(true)
                            return false
                        }
                    }

                    MotionEvent.ACTION_UP ->                         // 如果移动距离小于阈值，认为是点击事件
                        if (abs((event.y - startY).toDouble()) < CLICK_THRESHOLD) {
                            throttle(
                                "touch.callingBinding.maskDynamic"
                            ) {
                                if (callingBinding!!.pasteEdit.isFocused || callingBinding!!.swipeDelete.visibility == View.VISIBLE) {
                                    callingBinding!!.swipeDelete.visibility = View.GONE
                                    if (callingBinding!!.pasteEdit.isFocused) {
                                        hideKeyboard()
                                    } else {
                                        showChatRc()
                                    }
                                } else {
                                    // 判断当前消息是否显示
                                    if (callingBinding!!.maskDynamic.visibility == View.VISIBLE) {
                                        if (chatTimer != null && !chatTimer!!.isDisposed) {
                                            chatTimer!!.dispose()
                                        }
                                        // 隐藏
                                        callingBinding!!.maskDynamic.visibility = View.GONE
                                    } else {
                                        showChatRc()
                                    }
                                }
                            }
                            return true
                        }
                }


                // 返回false允许RecyclerView处理其他触摸事件
                return false
            }
        })
    }

    private fun showGiveGiftButton(giftAskEventBean: GiftAskEventBean?) {
        if (giftButtonTimer != null && !giftButtonTimer!!.isDisposed) {
            giftButtonTimer!!.dispose()
        }
        val askText = subStrByLen(
            user!!.nickname,
            3
        ) + " " + LanguageManager.instance!!.getLocalTranslate("ask_you_sent_a")
        //        String askText = String.format(getString(com.juicy.utils.R.string.common_ok_68), TextUtils.subStrByLen(user.getNickname(), 3));
        callingBinding!!.defaultField.text = askText
        if (user != null && !user?.avatarUrl.isNullOrEmpty()) {
            Glide.with(this).load(user!!.avatarUrl).into(
                callingBinding!!.activeAuto
            )
        }
        if (giftResMap[giftAskEventBean?.code] != null) {
            val id =  giftResMap[giftAskEventBean?.code]
            id?.let {
                callingBinding!!.xsPaste.setImageResource(id)
            }
        }
        callingBinding!!.connectedAvatar.isEnabled = true
        callingBinding!!.extendedFit.visibility = View.VISIBLE
        giftButtonTimer = Observable.timer(10, TimeUnit.SECONDS)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { callingBinding!!.extendedFit.visibility = View.INVISIBLE }
        callingBinding!!.connectedAvatar.setOnClickListener {
            if (Cache.instance.userInfo?.availableCoins != null && Cache.instance.userInfo!!.availableCoins >= (giftAskEventBean?.coinPrice?:0)) {
                val askGiftEventBean = com.juicy.common.model.bean.AskGiftEventBean(
                    CallUtils.instance?.callInfoModel?.channelName ?: "",
                    giftAskEventBean?.code?:"", 1, user!!.userId, (giftAskEventBean?.coinPrice?:0)
                )
                sendGiftName = giftAskEventBean?.code
                CallUtils.instance?.videoCallViewModel?.sendGift(askGiftEventBean)
                callingBinding!!.connectedAvatar.isEnabled = false
            } else {
                onpenCoinDialog("video_ask_for_gift")
                val handler = Handler()
                handler.postDelayed({
                    ToastUtils.showShort(
                        LanguageManager.instance!!.getLocalTranslate("Coins_Not_Enough")
                    )
                }, 100) // 延迟时间
            }
        }
    }

    private fun sendGift(giftName: String?) {
        GiftAnimationFragmentShow(giftName)
        calculateNumGift(giftName)
    }

    private fun calculateNumGift(giftName: String?) {
        if (giftTimer != null && !giftTimer!!.isDisposed) {
            giftTimer!!.dispose()
        }
        callingBinding!!.updateSelected.visibility = View.VISIBLE
        if (this.giftName != null && giftName != null &&
            giftName == this.giftName
        ) {
            giftCount++
        } else {
            giftCount = 1
        }
        this.giftName = giftName
        val builder = StringBuilder()
        builder.append("x").append(giftCount).append(" ")
        callingBinding!!.shortInput.text = builder
        if (giftResMap!![giftName!!] != null) {
            callingBinding!!.responsiveLower.setImageResource(giftResMap!![giftName]!!)
        }
        giftTimer = Observable.timer(5, TimeUnit.SECONDS)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                giftCount = 0
                callingBinding!!.updateSelected.visibility = View.INVISIBLE
            }
    }

    private fun onpenCoinDialog(entry: String) {
        activity?.let {
            val halfShoppingDialog = HalfShoppingDialog(false)
            halfShoppingDialog.entry = entry
            halfShoppingDialog.show(it.supportFragmentManager, "")
        }
    }

    private fun initVM() {
        if (CallUtils.instance?.videoCallViewModel == null) {
            return
        }
        CallUtils.instance?.videoCallViewModel?.readCameraData?.observe(this
        ) { bu ->
            if (bu != null) {
                if (!bu.isOpenCamera) {
                    putBooleanValue(SpKeyPool.IS_OPENED, false)
                    val switchDialog = SwtichCameraDialog(
                        activity,
                        LanguageManager.instance?.getLocalTranslate("Are_you_sure_to_turn_on_the_rear_camera")
                            ?: "Are you sure to turn on the rear camera?",
                        object : SwtichCameraDialog.ConfirmBack {
                            override fun onConfirmBack() {
                                CallUtils.instance?.videoCallViewModel?.openCamera()
                            }
                        })
                    switchDialog.setCameraText(
                        bu.openCoins.toString(),
                        bu.openDay.toString()
                    )
                    activity?.let {
                        switchDialog.show(it.supportFragmentManager, "")
                    }
                } else {
                    isOpenCamera = true
                    changeCamera()
                }
            } else {
                //                    ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate("Error"));
            }
        }
        CallUtils.instance?.videoCallViewModel?.loadFollowData?.observe(this
        ) { aBoolean ->
            if (aBoolean) {
                isLike = true
                callingBinding!!.frameSort.setImageResource(com.juicy.app.R.drawable.ic_like_gray_mint)
            } else {
                ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Error"))
            }
        }
        CallUtils.instance?.videoCallViewModel?.openCameraData?.observe(this
        ) { aBoolean ->
            if (aBoolean) {
                putBooleanValue(SpKeyPool.IS_OPENED, true)
                putIntValue(SpKeyPool.DIRECTION, 1)
                changeCamera()
            } else {
                onpenCoinDialog("switch_camera")
                ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Activation_Failed"))
            }
        }
        CallUtils.instance?.videoCallViewModel?.addFollowData?.observe(this) {aBoolean ->
            if (aBoolean) {
                initLikeButton(true)
                ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Follow_Success"))
            } else {
                ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Follow_Failed"))
            }
        }
        CallUtils.instance?.videoCallViewModel?.hangUpData?.observe(this) { hand ->
            if (hand == true){
                if (CallUtils.instance?.callInfoModel?.isMatch == true
                    && CallUtils.instance?.callInfoModel?.matchFreeFinish == false
                ) {
                    activity?.resetMatch(true)
                } else {
                    activity?.finish()
                }
            }
        }
        CallUtils.instance?.videoCallViewModel?.sendGiftData?.observe(this
        ) { value ->
            if (value != null) {
                callingBinding!!.extendedFit.visibility = View.INVISIBLE
                sendGift(sendGiftName)
                val jsonObject = JSONObject()
                try {
                    //1.对方id  2.消息内容  3.时间戳
                    jsonObject.put("toUserId", user?.userId)
                    jsonObject.put("content", sendGiftName)
                    jsonObject.put("timestamp", TimeUtils.getNowMills().toString())
                } catch (e: JSONException) {
                }
                SocketUtils.sendMsg("messageEvent", jsonObject)
                val chatBean = com.juicy.common.model.bean.ChatMessageItemBean()
                chatBean.avatar =
                    if (Cache.instance.userInfo?.avatarUrl != null) Cache.instance.userInfo?.avatarUrl else ""
                chatBean.content = giftName
                chatBean.fromUserId = Cache.instance.userInfo?.userId
                chatBean.itemType = 2
                addChatList(chatBean)
            } else {
                onpenCoinDialog("chatting_gift")
                //                    ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate("Error"));
                callingBinding?.connectedAvatar?.isEnabled = true
            }
        }
        CallUtils.instance?.videoCallViewModel?.deleteFollowData?.observe(this
        ) { aBoolean ->
            if (aBoolean) {
                initLikeButton(false)
                ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Cancel_Follow_Success"))
            } else {
                ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Cancel_Follow_Failed"))
            }
        }


    }

    private fun showChatRc() {
        callingBinding!!.maskDynamic.visibility = View.VISIBLE
        if (chatTimer != null && chatTimer?.isDisposed == false) {
            chatTimer?.dispose()
        }
        chatTimer = Observable.timer(5, TimeUnit.SECONDS)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(Consumer<Long> {
                if (callingBinding!!.pasteEdit.isFocused || callingBinding!!.swipeDelete.visibility == View.VISIBLE) {
                    return@Consumer
                }
                callingBinding!!.maskDynamic.visibility = View.GONE
            })
    }

    private fun addChatList(chatBean: com.juicy.common.model.bean.ChatMessageItemBean) {
        if (chatTimer != null && chatTimer?.isDisposed == false) {
            chatTimer?.dispose()
        }
        VideoCallAdapter?.addData(chatBean)
        callingBinding!!.maskDynamic.visibility = View.VISIBLE
        VideoCallAdapter?.adapterAnimation = AlphaInAnimation()
        callingBinding!!.maskDynamic.scrollToPosition(chatList.size - 1)
        chatTimer = Observable.timer(5, TimeUnit.SECONDS)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(Consumer<Long> { //判断当前输入框是否有焦点
                if (callingBinding!!.pasteEdit.isFocused || callingBinding!!.swipeDelete.visibility == View.VISIBLE) {
                    return@Consumer
                }
                callingBinding!!.maskDynamic.visibility = View.GONE
            })
    }

    private fun initLikeButton(isLike: Boolean) {
        this.isLike = isLike
        if (isLike) {
            callingBinding!!.frameSort.setImageResource(com.juicy.app.R.drawable.ic_like_color_jade)
        } else {
            callingBinding!!.frameSort.setImageResource(com.juicy.app.R.drawable.ic_like_gray_mint)
        }
    }

    fun hideKeyboard() {
        activity?.let {
            val imm = it.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            // 隐藏软键盘
            imm.hideSoftInputFromWindow(it.window.decorView.windowToken, 0)
            //去掉焦点
            callingBinding!!.pasteEdit.clearFocus()
        }
    }

    private fun openKeyboard() {
      activity?.let {
          val imm = it.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
          imm.showSoftInput(callingBinding!!.pasteEdit, 0)
      }
    }

    private fun initClick() {
        val emojis = emojis
        callingBinding!!.inactiveBottom.setOnClickListener { v: View? ->
            if (callingBinding!!.swipeDelete.visibility == View.VISIBLE) {
                callingBinding!!.swipeDelete.visibility = View.GONE
                openKeyboard()
            } else {
                hideKeyboard()
                callingBinding!!.swipeDelete.visibility = View.VISIBLE
            }
            showChatRc()
        }
        callingBinding!!.lgShow.setOnClickListener { v: View? ->
            if (callingBinding!!.pasteEdit.isFocused || callingBinding!!.swipeDelete.visibility == View.VISIBLE) {
                callingBinding!!.swipeDelete.visibility = View.GONE
                if (callingBinding!!.pasteEdit.isFocused) {
                    hideKeyboard()
                } else {
                    showChatRc()
                }
            } else {
                //判断当前消息是否显示
                if (callingBinding!!.maskDynamic.visibility == View.VISIBLE) {
                    if (chatTimer != null && !chatTimer!!.isDisposed) {
                        chatTimer!!.dispose()
                    }
                    //隐藏
                    callingBinding!!.maskDynamic.visibility = View.GONE
                } else {
                    showChatRc()
                }
            }
        }
        callingBinding!!.pasteEdit.onFocusChangeListener =
            View.OnFocusChangeListener { v: View?, hasFocus: Boolean ->
                if (!hasFocus) {
                    showChatRc()
                } else {
                    if (callingBinding!!.swipeDelete.visibility == View.VISIBLE) {
                        callingBinding!!.swipeDelete.visibility = View.GONE
                    }
                    showChatRc()
                }
            }
        callingBinding!!.pasteEdit.setOnClickListener {
            if (callingBinding!!.swipeDelete.visibility == View.VISIBLE) {
                callingBinding!!.swipeDelete.visibility = View.GONE
            }
        }
        callingBinding!!.coordinatorLeft.setOnClickListener {
            if (callingBinding!!.pasteEdit.text != null
                && callingBinding!!.pasteEdit.text.toString().isNotEmpty()
            ) {
                val text = callingBinding!!.pasteEdit.text.toString()
                var finalText: String? = null
                var isEmoji = false
                if (callingBinding!!.pasteEdit.text.toString().length >= 2) {
                    val substring = text.substring(text.length - 2)
                    if (emojis.contains(substring)) {
                        isEmoji = true
                    }
                }
                finalText = if (isEmoji) {
                    text.substring(0, text.length - 2)
                } else {
                    text.substring(0, text.length - 1)
                }
                callingBinding!!.pasteEdit.setText(finalText)
                callingBinding!!.pasteEdit.setSelection(finalText.length)
            }
        }
        callingBinding!!.customTitle.camera!!.setOnClickListener { view: View? ->
            if (isOpenCamera) {
                changeCamera()
            } else {
                CallUtils.instance?.videoCallViewModel?.camera
            }
            if (!getBooleanVal(
                    SpKeyPool.OPEN_CAMERA,
                    false
                )!!
            ) putBooleanValue(SpKeyPool.OPEN_CAMERA, true)
            callingBinding!!.customTitle.redPoint!!.visibility = View.GONE
            hideKeyboard()
        }
        callingBinding!!.mdBackground.setOnClickListener { view: View? ->
            if (callingBinding!!.pasteEdit.text.toString().trim { it <= ' ' }.isEmpty()) {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Can not be empty"
                    )
                )
                callingBinding!!.pasteEdit.setText("")
                return@setOnClickListener
            }
            val chatBean = com.juicy.common.model.bean.ChatMessageItemBean()
            chatBean.avatar = Cache.instance.userInfo?.avatarUrl?:""
            chatBean.content = callingBinding!!.pasteEdit.text.toString()
            chatBean.fromUserId = Cache.instance.userInfo!!.userId
            chatBean.itemType = 1
            addChatList(chatBean)
            val jsonObject = JSONObject()
            try {
                //1.对方id  2.消息内容  3.时间戳
                jsonObject.put("toUserId", user!!.userId)
                jsonObject.put("content", callingBinding!!.pasteEdit.text.toString())
                jsonObject.put(
                    "timestamp",
                    TimeUtils.getNowMills().toString()
                )
            } catch (e: JSONException) {
            }
            SocketUtils.sendMsg("messageEvent", jsonObject)
            callingBinding!!.pasteEdit.setText("")
        }

        callingBinding!!.reloadList.setOnClickListener {
            hideKeyboard()
            val handler = Handler()
            handler.postDelayed({
                if (isDestroy(activity)) return@postDelayed
                if (user == null || CallUtils.instance?.callInfoModel == null ||
                    CallUtils.instance?.callInfoModel?.channelName == null) return@postDelayed
                val giftDialog = GiftDialog(false, object : SendGiftCallBack {
                    override fun onSuccess(giftName: String?) {
                        sendGift(giftName)
                        val jsonObject = JSONObject()
                        try {
                            //1.对方id  2.消息内容  3.时间戳
                            if (user != null) {
                                jsonObject.put("toUserId", user!!.userId)
                            }
                            jsonObject.put("content", giftName)
                            jsonObject.put(
                                "timestamp",
                                TimeUtils.getNowMills().toString()
                            )
                        } catch (e: JSONException) {
                        }
                        SocketUtils.sendMsg("messageEvent", jsonObject)
                        val chatBean = com.juicy.common.model.bean.ChatMessageItemBean()
                        if (Cache.instance.userInfo != null) {
                            chatBean.avatar = Cache.instance.userInfo?.avatarUrl?:""
                        }
                        chatBean.content = giftName
                        if (Cache.instance.userInfo != null) {
                            chatBean.fromUserId = Cache.instance.userInfo?.userId?:""
                        }
                        chatBean.itemType = 2
                        addChatList(chatBean)
                    }
                }, user?.userId?:"", CallUtils.instance?.callInfoModel?.channelName?:"")
                activity?.let {
                    giftDialog.show(it.supportFragmentManager, "")
                }
            }, 100) // 延迟时间
        }
        callingBinding!!.extendedUpper.setOnClickListener {
            val settingDialog = HandUpDialog(activity,
                LanguageManager.instance?.getLocalTranslate("Ending_the_call_now_I_will_miss_you")?:"Ending the call now? I will miss you~",
                object : HandUpDialog.ConfirmBack {
                    override fun onConfirmBack() {
                        isOff = false
                        offRoom(Constant.NORMAL)
                    }

                })
            activity?.let {
                settingDialog.show(it.supportFragmentManager, "")
            }
            hideKeyboard()
        }
        callingBinding!!.frameSort.setOnClickListener {
            if (isLike) {
                CallUtils.instance?.videoCallViewModel?.deleteFollow(user?.userId?:"")
            } else {
                CallUtils.instance?.videoCallViewModel?.addFollow(user?.userId?:"")
            }
            hideKeyboard()
        }
        callingBinding!!.pressedOuter.setOnClickListener {
            if (user != null && !user?.userId.isNullOrEmpty()) {
                val blockDialogFragment = BlockDialogFragment(
                    object : BlockCallBack {
                        override fun deleteBlock() {
                        }

                        override fun addBlock() {
                        }

                        override fun addFollow() {
                            initLikeButton(true)
                        }

                        override fun deleteFollow() {
                            initLikeButton(false)
                        }
                    },
                    isLike,
                    user?.userId,
                    user?.nickname,
                    user?.avatarUrl
                )
                activity?.let {
                    blockDialogFragment.show(it.supportFragmentManager, "")
                }
            }
            //关闭键盘
            hideKeyboard()
        }
    }


    private fun initRtcConfig() {
        try {
            val key = getStringValue(SpKeyPool.RTCK_KEY, "")
            rtcEngine = RtcEngine.create(activity,key,object : IRtcEngineEventHandler() {
                    override fun onJoinChannelSuccess(channel: String, uid: Int, elapsed: Int) {
                        super.onJoinChannelSuccess(channel, uid, elapsed)
                        myUid = uid
                        updateJoinTimer()
                        CallUtils.instance?.callInfoModel?.logList?.add(
                            getCreateCallLog(
                                CallUtils.instance?.callInfoModel?.channelName?:"",
                                "join_success",
                                "chatting",
                                "加入频道成功",
                                System.currentTimeMillis()
                            )
                        )
                        CallUtils.instance?.videoCallViewModel?.Join(
                            uid.toString(),
                            CallUtils.instance?.callInfoModel?.channelName?:""
                        )
                    }

                    override fun onUserJoined(uid: Int, elapsed: Int) {
                        super.onUserJoined(uid, elapsed)
                        anchorUid = uid
                        updateJoinTimer()
                        CallUtils.instance?.callInfoModel?.logList?.add(
                            getCreateCallLog(
                                CallUtils.instance?.callInfoModel?.channelName?:"",
                                "user_join",
                                "chatting",
                                "检测到对方加入",
                                System.currentTimeMillis()
                            )
                        )
                        activity!!.runOnUiThread { initVideo(uid) }
                    }

                    override fun onUserOffline(uid: Int, reason: Int) {
                        super.onUserOffline(uid, reason)
                        offRoom(Constant.CL_REMOTE_USER_LEFT)
                        CallUtils.instance?.callInfoModel?.logList?.add(
                            getCreateCallLog(
                                CallUtils.instance?.callInfoModel?.channelName?:"",
                                "exit",
                                "chatting",
                                "退出",
                                System.currentTimeMillis()
                            )
                        )
                    }

                    override fun onFirstRemoteVideoFrame(
                        uid: Int,
                        width: Int,
                        height: Int,
                        elapsed: Int
                    ) {
                        if (activity?.isFinishing == false) {
                            activity?.runOnUiThread {
                                callingBinding!!.dropWithin.visibility = View.GONE
                            }
                        }
                        super.onFirstRemoteVideoFrame(uid, width, height, elapsed)
                    }

                    override fun onConnectionLost() {
                        super.onConnectionLost()
                        offRoom(Constant.CL_EXCEPTION)
                    }

                    override fun onConnectionStateChanged(state: Int, reason: Int) {
                        super.onConnectionStateChanged(state, reason)
                        //                            if (state == 5 && reason == 3) {
//                                offRoom(Constant.CL_EXCEPTION);
//                            }
                    }
                })

            if (rtcEngine != null) {
                //  rtcEngine.adjustPlaybackSignalVolume(300);
                rtcEngine?.enableVideo()
                val textureView = RtcEngine.CreateTextureView(context)
                if (textureView != null) {
                    textureView.outlineProvider = TextureVideoViewOutlineProvider(10f)
                    textureView.clipToOutline = true
                }
                callingBinding!!.customTitle.contentView!!.addView(
                    textureView, FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
                    )
                )
                rtcEngine!!.setupLocalVideo(
                    VideoCanvas(
                        textureView,
                        VideoCanvas.RENDER_MODE_HIDDEN,
                        0
                    )
                )
                rtcEngine!!.joinChannelWithUserAccount(
                    CallUtils.instance?.callInfoModel?.rToken,
                    CallUtils.instance?.callInfoModel?.channelName?:"",
                    Cache.instance.userInfo?.userId?:""
                )
                //  rtcEngine.adjustRecordingSignalVolume(200);
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private var isOff = false
    fun offRoom(hanson: Int) {
        if (!isDestroy(getActivity())) {
            activity?.runOnUiThread {
                if (CallUtils.instance?.callInfoModel?.isMatch == true) {
                    Cache.instance.netStrategy
                }
                if (!isOff) {
                    isOff = true
                    CallUtils.instance?.videoCallViewModel?.hangUp(
                        CallUtils.instance?.callInfoModel?.channelName?:"",
                        hanson,
                        user?.userId?:""
                    )
                }
            }
        }
    }

    private fun initVideo(uid: Int) {
        try {
            val surfaceView = RtcEngine.CreateRendererView(context)

            //获取当前屏幕宽度高度
            val displayMetrics = DisplayMetrics()
            (getActivity()?.getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay.getRealMetrics(
                displayMetrics
            )
            val width = displayMetrics.widthPixels
            val height = displayMetrics.heightPixels
            surfaceView.holder.setFixedSize(width, height)

            callingBinding!!.lgShow.addView(surfaceView, FrameLayout.LayoutParams(width, height))
            if (null != rtcEngine) {
                rtcEngine!!.setupRemoteVideo(
                    VideoCanvas(
                        surfaceView,
                        VideoCanvas.RENDER_MODE_HIDDEN,
                        uid
                    )
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun initUserInfo() {
        if (user == null) return
        initLikeButton(user?.isFriend?:false)
        if (getStrLen(user?.nickname) > 15) {
            callingBinding!!.avatarLarge.text =
                subStrByLen(user?.nickname, 8)
        } else {
            callingBinding!!.avatarLarge.text = user?.nickname
        }
        if (!user?.avatarUrl.isNullOrEmpty()) {
            Glide.with(requireActivity()).load(user?.avatarUrl).placeholder(R.mipmap.img_empty_round).into(callingBinding!!.fluidForeground)
        }else{
            callingBinding!!.fluidForeground.setImageResource(R.mipmap.img_empty_round)
        }
        if (user != null) callingBinding!!.autoTimer.text = user?.age.toString()
        if (user != null && !user?.country.isNullOrEmpty()) {
            callingBinding!!.playGrid.text = user!!.country
        }
    }

    val userInfo: Unit
        get() {
            if (user == null || user?.userId.isNullOrEmpty()) return
            getUserInfo(
                user?.userId?:"",
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                    override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                        super.onNext(t)
                        if (t.data != null) {
                            user = t.data
                            initLikeButton(t.data?.isFriend?:false)
                        }
                    }

                    override fun onError(e: Throwable) {
                        super.onError(e)
                    }
                })
        }

    private var matchFreeCountDownTimer: CountDownTimer? = null

    fun updateJoinTimer() {
        if (myUid != 0 && anchorUid != 0) {
            CallUtils.instance?.callInfoModel?.isAllJopin = true
            cancelWaitJoinTimer()
            activity?.runOnUiThread {
                if (CallUtils.instance?.callInfoModel?.isMatch == true) {
                    //开始倒计时

                    if (matchFreeCountDownTimer == null) {
                        callingBinding!!.largeSecondary.visibility = View.VISIBLE
                        updateFreeText(CallUtils.instance?.callInfoModel?.matchFreeTime?:0)
                        matchFreeCountDownTimer = object : CountDownTimer(
                            ((CallUtils.instance?.callInfoModel?.matchFreeTime?:0) * 1000).toLong(),
                            1000
                        ) {
                            override fun onTick(millisUntilFinished: Long) {
                                val seconds = (millisUntilFinished / 1000).toInt()
                                //更新倒计时
                                updateFreeText(seconds)
                            }

                            override fun onFinish() {
                                // 当倒计时结束时执行此方法
                                // 您可以在此处执行相应的逻辑,如显示"倒计时结束"的提示
                                callingBinding!!.largeSecondary.visibility =
                                    View.GONE
                                CallUtils.instance?.callInfoModel?.matchFreeFinish = true
                            }
                        }

                        matchFreeCountDownTimer?.start()
                    }
                }
            }
        }
    }

    fun updateFreeText(seconds: Int) {
        val timeStr = seconds.toString() + "s"
        val text =
            ("  " + LanguageManager.instance!!.getLocalTranslate("After") + " " + timeStr + ", " +
                    LanguageManager.instance!!.getLocalTranslate("Youll_be_charged") + "   " +
                    CallUtils.instance?.callInfoModel?.matchFreePrice.toString()).toString() + " " +
                    LanguageManager.instance!!.getLocalTranslate("per_minute") + "."
        val spannableString = SpannableString(text)

        // 设置橘黄色
        val orangeColor = Color.parseColor("#FFAA45")

        val timeStrForegroundColorSpan = ForegroundColorSpan(orangeColor)
        val macthFreePriceForegroundColorSpan = ForegroundColorSpan(orangeColor)
        spannableString.setSpan(
            timeStrForegroundColorSpan,
            text.indexOf(timeStr),
            text.indexOf(timeStr) + timeStr.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            macthFreePriceForegroundColorSpan,
            text.indexOf(CallUtils.instance?.callInfoModel?.matchFreePrice.toString()),
            text.indexOf(CallUtils.instance?.callInfoModel?.matchFreePrice.toString()) +
                    (CallUtils.instance?.callInfoModel?.matchFreePrice.toString()?:"").length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )


        val imageDrawable1 = getActivity()?.getDrawable(R.drawable.ic_match_free_icon_gold)
        if (imageDrawable1 != null) {
            imageDrawable1.setBounds(0, 0, 32, 32)
            val imageSpan = ImageSpan(imageDrawable1, ImageSpan.ALIGN_CENTER)
            spannableString.setSpan(
                imageSpan,
                0,
                1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }


        // 添加图片
        val imageDrawable = getActivity()?.getDrawable(com.juicy.app.R.drawable.ic_big_coin_ivory)
        if (imageDrawable != null) {
            imageDrawable.setBounds(0, 0, 32, 32)
            val imageSpan = ImageSpan(imageDrawable, ImageSpan.ALIGN_CENTER)
            spannableString.setSpan(
                imageSpan,
                text.indexOf(CallUtils.instance?.callInfoModel?.matchFreePrice.toString()) - 2,
                text.indexOf(CallUtils.instance?.callInfoModel?.matchFreePrice.toString()) - 1,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        callingBinding!!.viewAvatar.text = spannableString
    }

    override fun initView() {
        callingBinding!!.connectedAvatar.text = LanguageManager.instance!!.getLocalTranslate("Send")
        if (LanguageManager.instance!!.isLanguageForce) {
            context?.let {
                callingBinding!!.stretchLong.background = AppCompatResources.getDrawable(
                    it, R.drawable.bg_video_im_bg_1_olive
                )
                callingBinding!!.thinPanel.background = AppCompatResources.getDrawable(
                   it, R.drawable.bg_video_im_bg_2_charcoal
                )
                callingBinding!!.briefFluid.scaleX = -1f
            }
        }
        callingBinding!!.nextDisconnected.text =
            LanguageManager.instance!!.getLocalTranslate("Recharge")
        callingBinding!!.largeClosed.text =
            LanguageManager.instance!!.getLocalTranslate("Coins_not_enough")

        callingBinding!!.updateSelect.setOnClickListener { view: View? -> }
        callingBinding!!.mdDynamic.setOnClickListener { view: View? ->
            callingBinding!!.updateSelect.visibility = View.GONE
            if (not_coin_timer != null) {
                not_coin_timer!!.cancel()
            }
            not_coin_timer = null
        }
        callingBinding!!.nextDisconnected.setOnClickListener { view: View? ->
            onpenCoinDialog("coin_not_enough")
        }

        callingBinding!!.focusedSmall.setOnClickListener {
            onpenCoinDialog("coin_not_enough")
        }


        //        View decorView = getActivity().getWindow().getDecorView();
//        decorView.setOnApplyWindowInsetsListener((v, insets) -> {
//            int bottomInset = insets.getSystemWindowInsetBottom(); // 获取底部安全距离
//            // 处理您的布局或逻辑
//            ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) callingBinding.syncCopy.getLayoutParams();
//            params.bottomMargin = bottomInset;
//            callingBinding.syncCopy.setLayoutParams(params);
//            return insets; // 返回插入信息
//        });
        userInfo
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        callingBinding = FragmentBinding.inflate(inflater)
        return callingBinding!!.root
    }

    override fun onDestroy() {
        if (chatTimer != null && !chatTimer!!.isDisposed) {
            chatTimer!!.dispose()
        }
        if (giftTimer != null && !giftTimer!!.isDisposed) {
            giftTimer!!.dispose()
        }
        if (giftButtonTimer != null && !giftButtonTimer!!.isDisposed) {
            giftButtonTimer!!.dispose()
        }
        //        callingBinding.largeWithin.clearAnimation();
//        callingBinding.microUpdate.clearAnimation();
        if (rtcEngine != null) {
            rtcEngine!!.leaveChannel()
            RtcEngine.destroy()
            rtcEngine = null
        }
        if (matchFreeCountDownTimer != null) {
            matchFreeCountDownTimer!!.cancel()
            matchFreeCountDownTimer = null
        }
        removeMessageEvent(socketEventBusBack!!)

        activity?.closeTime()
        super.onDestroy()
    }
}