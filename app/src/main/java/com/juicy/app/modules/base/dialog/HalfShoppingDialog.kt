package com.juicy.app.modules.base.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.os.CountDownTimer
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.chad.library.adapter.base.listener.OnItemChildClickListener
import com.juicy.app.R
import com.juicy.app.modules.base.adapter.GoodAdapter
import com.juicy.app.databinding.LayoutBoxBinding
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.model.event.PromotionPayStatusChangeEvent
import com.juicy.common.model.event.PayResultEvent
import com.juicy.common.model.event.NewUserRewardsShowStatusChangeEvent
import com.juicy.common.model.message_event.AppRechargeOrderStatusBean
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.networks.delegate.GetCoinCountInterface.getCoin
import com.juicy.common.networks.delegate.GetCoinGoodsSearchInterface.getCoinGoodsSearch
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.DateMetaUtil.ms2HMS
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.AppDeviceUtils.getScreenSize
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PayUtils.PayUtils
import com.juicy.common.utils.PayUtils.PayUtils.FailOnListen
import com.juicy.common.utils.PayUtils.PayUtils.SuccessOnListen
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.abs

class HalfShoppingDialog : DialogFragment {
    private var halfShoppingBinding: LayoutBoxBinding? = null
    private val coinList: MutableList<com.juicy.common.model.bean.ActivityInfoBean?> = ArrayList()
    private var countDownTimer: CountDownTimer? = null
    private var socketEventBusBack: SocketEventBusBack? = null
    private var selectPosition = 0
    private var callBack: CallBack? = null

    private var isShowRobot = true

    private var mContext: Context? = null

    @JvmField
    var entry: String = ""

    constructor()

    constructor(isShowRobot: Boolean) {
        this.isShowRobot = isShowRobot
    }


    constructor(isShowRobot: Boolean, callBack: CallBack?) {
        this.isShowRobot = isShowRobot
        this.callBack = callBack
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
        mContext = this.context
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(`object`: PromotionPayStatusChangeEvent?) {
//    EventBus.getDefault().post(new CoinsMessage());
        if (!getBooleanVal(SpKeyPool.HAS_EVALUATE, false)!!) {
            putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
            activity?.let {
                if (!isDestroy(it)) {
                    val assessDialog = AssessDialog()
                    if ( isDestroy(it)) return
                    try {
                        if ((it is AppCompatActivity)) {
                            assessDialog.show(it.supportFragmentManager, "")
                        }
                    } catch (e: Exception) {
                    }
                }
            }
        }
        if (Cache.instance.webViewActivity != null) {
            Cache.instance.webViewActivity!!.refreshAgentWeb()
        }
        if (Cache.instance.userPromotionGood != null && selectPosition == 0) {
            EventBus.getDefault().post(NewUserRewardsShowStatusChangeEvent())
        }
        dismiss()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPayEvent(userDialogEvent: PayResultEvent) {
        if (userDialogEvent.isSuccess) {
            dismissAllowingStateLoss()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        halfShoppingBinding = LayoutBoxBinding.inflate(inflater)
        return halfShoppingBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        halfShoppingBinding!!.root.post {
            setDirection()
            halfShoppingBinding!!.emptyBadge.setOnClickListener { v: View? -> dismiss() }

            if (isShowRobot) {
                halfShoppingBinding!!.gridExpand.visibility = View.VISIBLE
            } else {
                halfShoppingBinding!!.gridExpand.visibility = View.GONE
            }
            if (Cache.instance.reviewPkg) {
                halfShoppingBinding!!.gridExpand.visibility = View.GONE
            }
            val layoutParams =
                halfShoppingBinding!!.headerAbove.layoutParams as LinearLayout.LayoutParams
            layoutParams.height =
                ((getScreenSize(mContext)[0] - dip2px(32f) * 2) * 100.0 / 328.0).toInt()
            halfShoppingBinding!!.headerAbove.layoutParams = layoutParams
            halfShoppingBinding!!.headerAbove.loadBanner()
            halfShoppingBinding!!.xxlUpper.addItemDecoration(object :
                RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    super.getItemOffsets(outRect, view, parent, state)
                    outRect[16, 16, 16] = 16
                }
            })
            initData()
        }
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            halfShoppingBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
            halfShoppingBinding!!.xxlUpper.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            halfShoppingBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        val scaleX = if (LanguageManager.instance!!.isLanguageForce) -1f else 1f
        halfShoppingBinding!!.maximalCounter.text = LanguageManager.instance!!.getLocalTranslate("My_Coins")
        halfShoppingBinding!!.emptyBadge.scaleX = scaleX
        halfShoppingBinding!!.playSidebar.text =
            LanguageManager.instance!!.getLocalTranslate("Coin_Store")
        halfShoppingBinding!!.elasticPrimary.text =
            LanguageManager.instance!!.getLocalTranslate("Recharge_or_become_VIP_to_chat_unlimited_times")
        halfShoppingBinding!!.lockedFill.text =
            LanguageManager.instance!!.getLocalTranslate("Customer_Service")
    }

    private fun initData() {
        Cache.instance.payChannle

        if (Cache.instance.userInfo != null) {
            halfShoppingBinding!!.wideClose.text =
                Cache.instance.userInfo!!.availableCoins.toString()

            if (Cache.instance.userInfo!!.vip || Cache.instance.userStratResult == null || Cache.instance.userStratResult!!.isReviewPkg == null ||
                Cache.instance.userStratResult!!.isReviewPkg!! ||
                Cache.instance.userInfo!!.recharge || entry != "im_limit"
            ) {
                halfShoppingBinding!!.elasticPrimary.visibility = View.GONE
            } else {
                halfShoppingBinding!!.elasticPrimary.visibility = View.VISIBLE
            }
        }
        initMyCoins()

        if (Cache.instance.coinGoods != null && !Cache.instance.coinGoods!!.isEmpty()) {
            initGoodsRv(Cache.instance.coinGoods!!)
        }
        resetGoodData()
        halfShoppingBinding!!.gridExpand.setOnClickListener { v: View? ->
            startActivity(
                Intent(
                    requireContext(),
                    com.juicy.app.modules.base.activity.BotActivity::class.java
                )
            )
        }
    }

    private fun resetGoodData() {
        getCoinGoodsSearch(
            context,
            PlayParamsBean(true),
            object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>() {
                override fun onNext(goodsData: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>) {
                    super.onNext(goodsData)
                    if (goodsData.data != null && !goodsData.data!!.isEmpty()) {
                        val coinGoods: MutableList<com.juicy.common.model.bean.ActivityInfoBean> = ArrayList()
                        for (i in goodsData.data!!.indices) {
                            val bhBean = goodsData.data!![i]
                            if (bhBean.type == "1") { //订阅
                                Cache.instance.subscribeGood = bhBean
                            } else {
                                coinGoods.add(bhBean)
                            }
                        }
                        Cache.instance.coinGoods = coinGoods?.toList()?: listOf<com.juicy.common.model.bean.ActivityInfoBean>()

                        PayUtils(ActivityUtils.getTopActivity()).batchQueryPurchases()
                        initGoodsRv(coinGoods)
                    }
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                }
            })
    }

    private fun initMyCoins() {
        getCoin(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>() {
            override fun onNext(coinData: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>) {
                super.onNext(coinData)
                if (Cache.instance.userInfo != null) {
                    if (coinData.data != null && coinData.data!!.availableCoins >= 0) {
                        halfShoppingBinding!!.wideClose.text =
                            coinData.data!!.availableCoins.toInt().toString()
                    }
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }

    private fun initGoodsRv(coinGoods: List<com.juicy.common.model.bean.ActivityInfoBean?>) {
        val gridLayoutManager = GridLayoutManager(
            activity, 2
        )

        //        halfShoppingBinding.xxlUpper.addItemDecoration(new GridSpacingItemDecoration(2, 15, true));
        halfShoppingBinding!!.xxlUpper.layoutManager = gridLayoutManager
        val goodAdapter = GoodAdapter(R.layout.item_good)
        halfShoppingBinding!!.xxlUpper.adapter = goodAdapter
        setData(goodAdapter, coinGoods)
    }

    private fun setData(goodAdapter: GoodAdapter, coinGoods: List<com.juicy.common.model.bean.ActivityInfoBean?>) {
        socketEventBusBack = object : SocketEventBusBack {
            override fun rechargeSuccessBack(appRechargeOrderStatusBean: AppRechargeOrderStatusBean?) {
                if (appRechargeOrderStatusBean?.status == 2 && !coinGoods.isNullOrEmpty() && selectPosition < coinGoods.size) {
                    if (selectPosition == 0 && Cache.instance.userPromotionGood != null && coinGoods[selectPosition]?.promotion == true
                    ) {
                        EventBus.getDefault().post(NewUserRewardsShowStatusChangeEvent())
                    }
                    try {
                        if (isAdded) {
                            dismissAllowingStateLoss()
                        }
                    } catch (e: Exception) {
                    }
                    initMyCoins()
                    EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
                }
            }
        }
        coinList.clear()
        coinList.addAll(coinGoods)
        for (i in coinList.indices) {
            if (coinList[i]?.promotion == true) {
                coinList.removeAt(i)
                break
            }
        }
        setProGood(goodAdapter)

        if (Cache.instance.userInfo != null && Cache.instance.userInfo?.vip == false) {
            if (Cache.instance.subscribeGood != null) {
                for (i in coinList.indices) {
                    if (coinList[i]!!.exchangeCoin == Cache.instance.subscribeGood!!.exchangeCoin) {
                        Cache.instance.subscribeGood!!.isVipItem = true
                        coinList.add(i + 1, Cache.instance.subscribeGood)
                        break
                    }
                }
            }
        }

        goodAdapter.setNewInstance(coinList)
        goodAdapter.addChildClickViewIds(R.id.innerGrid)

        goodAdapter.setOnItemChildClickListener(OnItemChildClickListener { adapter, view, position ->
            if (isFastClick) return@OnItemChildClickListener
            selectPosition = position
            if (coinList[position]!!.isVipItem) {
                val vipDialog = VipDialogFragment(false)
                val currentActivity = currentActivity
                if (currentActivity == null || isDestroy(currentActivity)) return@OnItemChildClickListener
                try {
                    if ((currentActivity is AppCompatActivity)) {
                        vipDialog.show(
                            currentActivity.supportFragmentManager,
                            ""
                        )
                    }
                } catch (e: Exception) {
                }
            } else {
                PayUtils(activity, entry, false, coinList[position], object : SuccessOnListen {
                    override fun onListen() {
                        //    EventBus.getDefault().post(new CoinsMessage());
                        if (!getBooleanVal(SpKeyPool.HAS_EVALUATE, false)!!) {
                            putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
                            if (!isDestroy(activity)) {
                                val assessDialog = AssessDialog()
                                if (activity == null || isDestroy(activity)) return
                                try {
                                    if ((activity is AppCompatActivity)) {
                                        assessDialog.show(activity!!.supportFragmentManager, "")
                                    }
                                } catch (e: Exception) {
                                }
                            }
                        }
                        if (Cache.instance.webViewActivity != null) {
                            Cache.instance.webViewActivity!!.refreshAgentWeb()
                        }
                        if (Cache.instance.userPromotionGood != null && position == 0) {
                            EventBus.getDefault().post(NewUserRewardsShowStatusChangeEvent())
                        }
                        dismiss()
                    }
                }, object : FailOnListen {
                    override fun onListen() {
                    }
                }).openPayDialog()
            }
        })
        addMessageEvent(socketEventBusBack!!)
    }

    private fun setProGood(goodAdapter: GoodAdapter) {
        if (Cache.instance.userPromotionGood != null) {
            coinList.add(0, Cache.instance.userPromotionGood)
            setProTime(Cache.instance.userPromotionGood, goodAdapter)
        }
    }

    private fun setProTime(userPromotionGood: com.juicy.common.model.bean.ActivityInfoBean?, goodAdapter: GoodAdapter) {
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
        if (Cache.instance.userPromotionGood == null) return
        val surplusMillisecond =
            Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
        countDownTimer = object : CountDownTimer(surplusMillisecond, 1000) {
            override fun onTick(l: Long) {
                if (Cache.instance.userPromotionGood != null) {
                    val surplusMillisecond =
                        Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
                    val time = ms2HMS(surplusMillisecond)
                    Cache.instance.userPromotionGood!!.tags = time
                    //                    goodAdapter.setData(0, Cache.getInstance().userPromotionGood);
                    val bean = Cache.instance.userPromotionGood
                    bean?.let {
                        goodAdapter.setNewSaleTag(it)
                        goodAdapter.notifyDataSetChanged()
                    }
                    if (abs((l - surplusMillisecond).toDouble()) > 1000) {
                        setProTime(userPromotionGood, goodAdapter)
                    }
                }
            }

            override fun onFinish() {
                Cache.instance.userPromotionGood = null
                dismiss()
            }
        }.start()
    }

    private var mInitialTouchY = 0f
    @SuppressLint("ClickableViewAccessibility")
    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.BOTTOM)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = getScreenSize(this.context)[1] - ((getScreenSize(
                this.context
            )[1]) * 0.2).toInt()

            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams

            dialog!!.window!!.decorView.setOnTouchListener { v: View?, event: MotionEvent ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN ->                         // 记录按下时的 y 坐标
                        mInitialTouchY = event.y

                    MotionEvent.ACTION_UP -> {
                        // 计算滑动距离
                        val deltaY = event.y - mInitialTouchY
                        // 如果滑动距离大于等于 50pt 则关闭对话框
                        if (deltaY >= dip2px(50f)) {
                            dismiss()
                        }
                    }
                }
                false
            }
        }
    }

    private fun cancelTimer() {
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
    }

    override fun onDestroy() {
       try {
           cancelTimer()
           socketEventBusBack?.let {
               removeMessageEvent(it)
           }
           callBack?.onCallBack()
           EventBus.getDefault().unregister(this)
           super.onDestroy()
       }catch (e:Exception){
       }
    }

    interface CallBack {
        fun onCallBack()
    }
}
