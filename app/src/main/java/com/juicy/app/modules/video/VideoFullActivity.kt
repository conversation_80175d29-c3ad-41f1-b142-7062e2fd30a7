package com.juicy.app.modules.video

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.viewpager2.widget.ViewPager2
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.google.android.exoplayer2.MediaItem
import com.google.gson.Gson
import com.juicy.app.modules.video.adapter.FragmentAdapter
import com.juicy.app.modules.video.inf.IVideoView
import com.juicy.app.databinding.ActivityPickerBinding
import com.juicy.app.R
import com.juicy.app.modules.base.dialog.BlockDialogFragment
import com.juicy.app.modules.base.dialog.BlockDialogFragment.BlockCallBack
import com.juicy.app.modules.base.dialog.VipDialogFragment
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.model.bean.JuicyUserInfoBean
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.model.event.BlockParamsEvent
import com.juicy.common.model.event.FollowParamsEvent
import com.juicy.common.model.event.OnlineStatusChangeEvent
import com.juicy.common.networks.delegate.AddFriendInterface.addToFriendList
import com.juicy.common.networks.delegate.CancelFriendInterface.cancelFriend
import com.juicy.common.networks.delegate.GetCoinGoodsSearchInterface.getCoinGoodsSearch
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.status.UserOnlineStatusService
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PayUtils.PayUtils
import com.juicy.common.utils.QuickDrawable.Companion.create
import io.reactivex.rxjava3.disposables.Disposable
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class VideoFullActivity : BaseActivity(), IVideoView {
    private var userId: String? = null
    private lateinit var activityVideoBinding: ActivityPickerBinding
    private var videoPresenter2: VideoPresenter? = null
    private var videoPresenter: VideoPresenter? = null
    private var userInfo: JuicyUserInfoBean? = null
    private var mLabel: String? = null
    private var mTitle: String? = null
    private var isFollow = false
    val coinsShop: Unit
        get() {
            getCoinGoodsSearch(
                ActivityUtils.getTopActivity(),
                PlayParamsBean(true),
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>() {
                    override fun onSubscribe(d: Disposable) {
                        super.onSubscribe(d)
                    }

                    override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>) {
                        super.onNext(t)


                        if (t.isOk) {
                            if (!t.data.isNullOrEmpty()) {
                                val coinGoods: MutableList<com.juicy.common.model.bean.ActivityInfoBean> =
                                    ArrayList()
                                for (i in t.data!!.indices) {
                                    val bhBean = t.data!![i]
                                    if (bhBean.type == "1") { //订阅
                                        Cache.instance.subscribeGood =
                                            bhBean
                                    } else {
                                        coinGoods.add(bhBean)
                                    }
                                }
                                Cache.instance.coinGoods = coinGoods
                                PayUtils(ActivityUtils.getTopActivity()).batchQueryPurchases()

                                toVipDialog()
                            }
                        }
                    }
                    override fun onComplete() {
                        super.onComplete()
                    }
                    override fun onError(e: Throwable) {
                    }


                })
        }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(followEvent: FollowParamsEvent) {
        Log.d("xxx", "xxxxx")
        if (followEvent.userId == userId) {
            isFollow = followEvent.isFollow
            if (followEvent.isFollow) {
                activityVideoBinding.frameSort.setImageResource(R.drawable.ic_like_color_jade)
            } else {
                activityVideoBinding.frameSort.setImageResource(R.drawable.ic_like_gray_mint)
            }
            userInfo?.isFriend = followEvent.isFollow
        }
    }
    override fun initView() {
        activityVideoBinding.extendedUpper.setOnClickListener { v: View? -> finish() }

        activityVideoBinding.errorMaximal.setOnClickListener { view: View? ->
            if (Cache.instance.subscribeGood != null) {
                toVipDialog()
            } else {
                coinsShop
            }
        }
    }

    fun toVipDialog() {
        val vipDialog = VipDialogFragment(true)
        val currentActivity = currentActivity
        if (currentActivity != null && (currentActivity is AppCompatActivity)) {
            vipDialog.show(currentActivity.supportFragmentManager, "")
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onUserStatus(event: OnlineStatusChangeEvent?) {
        //
        val status = UserOnlineStatusService.instance?.getStatus(userId?:"")
        if (status != null) {
            updateUserStatus(status, userInfo)
        }
        //监听状态
    }
    override fun initData() {
        videoPresenter2 = null;
        mLabel = "";
        mTitle = "";

        val userStatus = intent.getStringExtra(Constant.STATUS)
        videoPresenter = VideoPresenter(this)
        isFollow = intent.getBooleanExtra(Constant.IS_FOLLOW, false)
        val userInfo = Gson().fromJson(
            intent.getStringExtra(Constant.USER_CONTENT),
            com.juicy.common.model.bean.JuicyUserInfoBean::class.java
        )

        val curPosition = intent.getIntExtra(Constant.VIDEO_POSITION, 0)
        this.userInfo = userInfo
        userId = userInfo.userId
        updateUserStatus(userStatus?:"", userInfo)

        updateInfo(userInfo, curPosition)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBlockEvent(event: BlockParamsEvent) {
        //
        if (event.userId == userId) {
            userInfo?.block = event.isBlock
        }
        //监听状态
    }
    private fun updateInfo(userInfo: com.juicy.common.model.bean.JuicyUserInfoBean?, curPosition: Int) {
        if (userInfo == null) {
            return
        }
        val mediaList2: MutableList<com.juicy.common.model.bean.MediaInfoBean> = ArrayList()
        val mediaList: MutableList<MediaItem> = ArrayList()

        activityVideoBinding.fluidForeground.borderColor =
            getColor(com.juicy.app.R.color.white)
        activityVideoBinding.fluidForeground.borderWidth = dip2px(2f)

        activityVideoBinding.avatarLarge.text = userInfo.nickname
        Glide.with(this).load(userInfo.avatarUrl).into(
            activityVideoBinding.fluidForeground
        )
        val ageContry = userInfo.age.toString() + "  " + userInfo.country.toString()

        activityVideoBinding.maximalBtn.text = userInfo.country
        activityVideoBinding.lockedRemove.text = ageContry
        activityVideoBinding.autoTimer.text = "${userInfo.age}"
        if (isFollow) {
            activityVideoBinding.frameSort.setImageResource(R.drawable.ic_like_color_jade)
        } else {
            activityVideoBinding.frameSort.setImageResource(R.drawable.ic_like_gray_mint)
        }
        if (userInfo.gender == 1){
            activityVideoBinding.autoTimer.setBackgroundResource(R.drawable.bg_male_blue)
        }else if(userInfo.gender == 2){
            activityVideoBinding.autoTimer.setBackgroundResource(R.drawable.bg_famel_olive)
        }


        activityVideoBinding.surfaceAcross.text = LanguageManager.instance?.getLocalTranslate("Chat")?:"Chat"
        activityVideoBinding.refreshClear.text = LanguageManager.instance?.getLocalTranslate("Video_Call")?:"Video Call"
        activityVideoBinding.autoBody.text =
            userInfo.vipUnitPrice.toString()
        if (!userInfo.mediaList.isNullOrEmpty()){
            for (mediaBean in userInfo.mediaList!!) {
                if (mediaBean.mediaType == "video") {
                    val mediaItem = MediaItem.fromUri(
                        mediaBean.mediaUrl?:""
                    )
                    mediaList.add(mediaItem)
                    mediaList2.add(mediaBean)
                }
            }
        }

        activityVideoBinding.chipStart.setOnClickListener { view: View? ->
            RouteUtils.routeToConversationActivity(
                this,
                Conversation.ConversationType.PRIVATE,
                userInfo.userId
            )
        }

        activityVideoBinding.frameSort.setOnClickListener { view: View? ->
            if (isFollow) {
                deleteFriend(userInfo.userId, this)
            } else {
                addFriend(userInfo.userId, this)
            }
        }
        activityVideoBinding.hugeSelected.setOnClickListener {}


        activityVideoBinding.sidebarBadge.setOnClickListener(View.OnClickListener {
            if (userInfo.userId.isNullOrEmpty()) return@OnClickListener
            val blockDialogFragment = BlockDialogFragment(object : BlockCallBack {
                override fun deleteBlock() {
                }

                override fun addBlock() {
                }

                override fun addFollow() {
                }

                override fun deleteFollow() {
                }
            }, isFollow, userInfo.userId, userInfo.nickname, userInfo.avatarUrl)
            blockDialogFragment.show(supportFragmentManager, "")
        })
        initVideoVP(mediaList, mediaList2, curPosition)
        initPointLl(mediaList)
        selectPoint(curPosition, mediaList)

        //        if(Cache.getInstance().reviewPkg){
//            activityVideoBinding.errorMaximal.setVisibility(View.GONE);
//            activityVideoBinding.dragDrop.setVisibility(View.GONE);
//            activityVideoBinding.viewFit.setText(LanguageManager.getInstance().getLocalTranslate("Video_Call"));
//        }else{
        // activityVideoBinding.errorMaximal.visibility = View.VISIBLE
        val anchorPrice = userInfo.unitPrice
        val anchorStr = StringBuilder()
        anchorStr.append(anchorPrice)
            .append(LanguageManager.instance?.getLocalTranslate("minute"))
        //  activityVideoBinding.viewFit.text = anchorStr

        //  activityVideoBinding.dragDrop.visibility = View.VISIBLE
        //        }
    }
    fun deleteFriend(userId: String, context: Context?) {
        cancelFriend(com.juicy.common.model.bean.FollowUserIdParamsBean(userId), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(t)

                if (t.data != null && t.data == true) {
                    isFollow = false
                    ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("unFollow_Success"))
                    activityVideoBinding.frameSort.setImageResource(R.drawable.ic_like_gray_mint)
                    EventBus.getDefault().post(FollowParamsEvent(userId, false))
                } else {
                    ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("unFollow_Fail"))
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }
    fun addFriend(userId: String, context: Context?) {
        addToFriendList(com.juicy.common.model.bean.AnchorIDBean(userId), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(t)

                if (t.data != null && t.data == true) {
                    isFollow = true
                    ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Follow_Success"))
                    activityVideoBinding.frameSort.setImageResource(R.drawable.ic_like_color_jade)
                    EventBus.getDefault().post(FollowParamsEvent(userId, true))
                } else {
                    ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Follow_Fail"))
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)

                ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Follow_Fail"))
            }
        })
    }




    private fun initPointLl(mediaList: List<MediaItem>) {
        for (i in mediaList.indices) {
            val point = ImageView(this)
            point.background =
                AppCompatResources.getDrawable(
                    this,
                    com.juicy.app.R.drawable.bg_cricle_gray_turquoise
                )
            activityVideoBinding.emptyRight.addView(point)
            val layoutParams = point.layoutParams as LinearLayout.LayoutParams
            layoutParams.marginEnd = dip2px(2f)
            layoutParams.width = dip2px(4f)
            layoutParams.marginStart = dip2px(2f)

            layoutParams.height = dip2px(4f)
            point.layoutParams = layoutParams
        }
    }



    private fun initVideoVP(
        mediaList: List<MediaItem>,
        mediaList2: List<com.juicy.common.model.bean.MediaInfoBean>,
        curPosition: Int
    ) {
        val videoFragments: MutableList<VideoFragment> = ArrayList()
        for (i in mediaList.indices) {
            videoFragments.add(VideoFragment(mediaList[i], mediaList2[i]))
        }
        val fragmentAdapter =
            FragmentAdapter(supportFragmentManager, lifecycle, videoFragments)
        activityVideoBinding.wrapTab.adapter = fragmentAdapter
        activityVideoBinding.wrapTab.setCurrentItem(curPosition, false)
        if (LanguageManager.instance?.isLanguageForce == true) {
            activityVideoBinding.wrapTab.layoutDirection = View.LAYOUT_DIRECTION_RTL
        }
        activityVideoBinding.wrapTab.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                selectPoint(position, mediaList)
            }
        })
    }

    private fun selectPoint(curPosition: Int, mediaList: List<MediaItem>) {
        for (i in mediaList.indices) {
            if (i == curPosition) {
                activityVideoBinding.emptyRight.getChildAt(i).background =
                    AppCompatResources.getDrawable(
                        this,
                        com.juicy.app.R.drawable.bg_point_white_slate
                    )
                activityVideoBinding.emptyRight.getChildAt(i).layoutParams.width = dip2px(12f)
            } else {
                activityVideoBinding.emptyRight.getChildAt(i).background =
                    AppCompatResources.getDrawable(
                        this,
                        com.juicy.app.R.drawable.bg_cricle_gray_turquoise
                    )
                activityVideoBinding.emptyRight.getChildAt(i).layoutParams.width = dip2px(4f)
            }
        }
    }
    fun updateUserStatus(status: String, userInfo: com.juicy.common.model.bean.JuicyUserInfoBean?) {
        val isEnableCall = UserOnlineStatusService.instance?.getStatusEnableCall(status)?:false
        val color = UserOnlineStatusService.instance?.getStatusColor(status)?:0
        activityVideoBinding.copyLeft.background = create()
            .corner(dip2px(200f).toFloat())
            .bgColor(color)
            .build()
        if (isEnableCall) {
            activityVideoBinding.badgeEnd.setImageResource(com.juicy.app.R.mipmap.ic_anchor_info_online_icon_lemon)
            activityVideoBinding.maximalHeader.setBackgroundResource(com.juicy.app.R.mipmap.ic_anchor_info_call_online_ash)
            activityVideoBinding.textToggle.setOnClickListener {
                videoPresenter?.callVideo( status,userInfo )
            }
//            activityVideoBinding.textToggle.background =
//                AppCompatResources.getDrawable(
//                    this,
//                    R.drawable.bg_login_cream
//                )
        } else {
            activityVideoBinding.badgeEnd.setImageResource(com.juicy.app.R.mipmap.ic_anchor_info_offline_icon_platinum)
            activityVideoBinding.maximalHeader.setBackgroundResource(com.juicy.app.R.mipmap.ic_anchor_info_call_offline_snow)
            activityVideoBinding.textToggle.setOnClickListener {
                videoPresenter?.call(status,userInfo)
            }
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        activityVideoBinding = ActivityPickerBinding.inflate(layoutInflater)
        //        StatusBarUtil.setTranslucentStatus(this);
        setContentView(activityVideoBinding.root)

        super.onCreate(savedInstanceState)
    }

    override fun jumpCallPage() {
    }



    override fun onPause() {
        super.onPause()
    }



    override fun transLocalText() {
        if (LanguageManager.instance?.isLanguageForce == true) {
            activityVideoBinding.extendedUpper.scaleX = -1f
        }
    }
    override fun onResume() {
        super.onResume()
        val status = UserOnlineStatusService.instance?.getStatus(userId?:"")
        if (status != null) {
            updateUserStatus(status, userInfo)
        }
        UserOnlineStatusService.instance?.addUserIdAndForceRefresh(userId?:"")
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}
