package com.juicy.app.modules.wall.adapter

import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R
import com.juicy.app.modules.wall.adapter.TabAdapter.TabVH
import com.juicy.common.config.Cache
import com.juicy.common.utils.AppDeviceUtils.dip2px

class TabAdapter(layoutResId: Int, data: List<String>) :
    BaseQuickAdapter<String?, TabVH>(layoutResId, data.toMutableList()) {
    private var iconNum = 0

    inner class TabVH(view: View) : BaseViewHolder(view) {


        val tabRl: ConstraintLayout =
            view.findViewById(R.id.upCard)
        val star: AppCompatImageView =
            view.findViewById(R.id.copyPrev)
        val tabBg: AppCompatImageView =
            view.findViewById(R.id.mediumConfirm)
        val tabName: TextView =
            view.findViewById(R.id.outerMicro)
    }
    override fun convert(holder: TabVH, item: String?) {
        if (item == null) return
        holder.tabName.text = item
        val lp = ConstraintLayout.LayoutParams(
            ConstraintLayout.LayoutParams.WRAP_CONTENT,
            ConstraintLayout.LayoutParams.WRAP_CONTENT
        )
        if (Cache.instance.currentFirstTab != getItemPosition(item)) {
            holder.tabBg.visibility = View.GONE
            holder.tabName.textSize = 22f
            holder.star.visibility = View.INVISIBLE
            holder.tabName.setPadding(0, dip2px(2f), 0, dip2px(2f))
            lp.setMargins(dip2px(8f), dip2px(14f), dip2px(12f), dip2px(10f))
            holder.tabRl.layoutParams = lp


        } else {
            holder.star.visibility = View.VISIBLE
            holder.tabName.textSize = 22f
            holder.tabName.setPadding(0, dip2px(2f), 0, dip2px(2f))
            lp.setMargins(dip2px(8f), dip2px(14f), dip2px(12f), dip2px(10f))
            holder.tabRl.layoutParams = lp
            holder.tabBg.visibility = View.VISIBLE



        }
    }


}
