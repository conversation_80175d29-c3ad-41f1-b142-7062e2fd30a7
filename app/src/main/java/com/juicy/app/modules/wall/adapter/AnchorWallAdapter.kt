package com.juicy.app.modules.wall.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.juicy.app.R
import com.juicy.app.modules.info.ProfileActivity
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.status.UserOnlineStatusService
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.view.MdBannerView
import com.juicy.common.utils.CallUtil.callHandle
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.AppDeviceUtils.getScreenSize
import com.juicy.common.utils.ImageLoadingUtils.loadImage
import com.juicy.common.utils.PermissionUtil.Companion.checkVideoPermission
import com.juicy.common.utils.PermissionUtil.PermissionCallback
import com.juicy.common.utils.RoundImageView
import com.juicy.common.utils.TranslateUtils.textTrans
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.functions.Function
import io.reactivex.rxjava3.schedulers.Schedulers

class AnchorWallAdapter(var mContext: Context,var list: MutableList<com.juicy.common.model.bean.AnchorItem>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var pontX = 0f
    private var eventX = 0f
    private var mListener: OnImgBigClickListener? = null
    var scrollBack: RobotListLongDialogCallBack? = null


    override fun getItemViewType(position: Int): Int {
        return if (list[position].itemType == 0) {
            0
        } else {
            1
        }
    }

    interface RobotListLongDialogCallBack {
        fun newMessage()
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        if (viewType == 0) {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_meter, parent, false)
            return BannerVH(view)
        } else if (viewType == 1) {
            val view =
                LayoutInflater.from(parent.context).inflate(R.layout.item_content, parent, false)
            return AnchorVH(view)
        }
        throw IllegalArgumentException("Invalid view type")
    }

    interface OnImgBigClickListener {
        fun onItemClick(imgUrl: String?)
    }




    @SuppressLint("ClickableViewAccessibility")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val anchorItem = list[position]



        if (holder is BannerVH) {
        } else if (holder is AnchorVH) {
            val anchor = anchorItem.anchorInfoBean

            val anchorVH = holder
            anchorVH.model = anchor
            if (anchor == null) return

            if (anchor.country != null) {
                anchorVH.country.text = anchor.country
            }
            if (anchor.activityTagUrl != null) {
                anchorVH.tag.visibility = View.VISIBLE
                Glide.with(mContext).load(anchor.activityTagUrl).into(anchorVH.tag)
            } else {
                anchorVH.tag.visibility = View.GONE
            }

            if (anchor.nickname != null) {
                anchorVH.name.text = anchor.nickname
            }
            if (anchor.age != null) {
                anchorVH.age.text = anchor.age.toString()
            }
            if (anchor.gender != null) {
                if (anchor.gender == 1) {
                    anchorVH.age.setTextColor(Color.WHITE)
                    anchorVH.age.setBackgroundResource(com.juicy.app.R.drawable.bg_male_blue)
                } else if (anchor.gender == 2) {
                    anchorVH.age.setTextColor(Color.WHITE)
                    anchorVH.age.setBackgroundResource(com.juicy.app.R.drawable.bg_famel_olive)
                }
            }
            if (anchor.avatar != null) {
                loadImage(mContext, anchor.avatar, anchorVH.avatarV)
            }


            if (anchor.status != null) {
                val isEnableCall = UserOnlineStatusService.instance?.getStatusEnableCall(
                    anchor.status?:""
                )?:false
                val color = UserOnlineStatusService.instance?.getStatusColor(anchor.status?:"")?:0
                if (color != 0){
                    anchorVH.status.setImageResource(color)
                }
                if (isEnableCall) {
                    anchorVH.video.setImageResource(R.drawable.ic_call_online_mist)
                    val layoutParams = anchorVH.video.layoutParams
                    layoutParams.height = dip2px(42f)
                    layoutParams.width = dip2px(42f)
                    anchorVH.video.layoutParams = layoutParams
                    anchorVH.video.setOnClickListener { v: View? ->
                        checkVideoPermission(
                            (mContext as BaseActivity), object : PermissionCallback {
                                override fun complete() {
                                    call(anchor, anchor.status)
                                }
                            })
                    }
                } else {
                    anchorVH.video.setImageResource(R.drawable.ic_call_offline_sage)
                    val layoutParams = anchorVH.video.layoutParams
                    layoutParams.width = dip2px(42f)
                    layoutParams.height = dip2px(42f)

                    anchorVH.video.layoutParams = layoutParams
                    anchorVH.video.setOnClickListener { call(anchor, anchor.status) }
                }
                anchorVH.avatarV.setOnTouchListener { view, motionEvent ->
                    if (motionEvent.action == MotionEvent.ACTION_DOWN) {
                        //当手指按下的时候
                        pontX = motionEvent.x
                    }
                    if (motionEvent.action == MotionEvent.ACTION_MOVE) {
                        //当手指按下并移动的时候
                        eventX = motionEvent.x
                        if (pontX - eventX > 0) {
                            Cache.instance.isMoveLeft = false
                        } else if (eventX - pontX > 0) {
                            Cache.instance.isMoveLeft = true
                        }
                    }
                    false
                }
                anchorVH.avatarV.setOnClickListener { v: View? ->
                    val intent = Intent(
                        mContext,
                        ProfileActivity::class.java
                    )
                    intent.putExtra(Constant.USER_ID, anchor.userId)
                    mContext.startActivity(intent)
                }

            }
        }
    }

    @SuppressLint("CheckResult")
    private fun translateFun(bean: com.juicy.common.model.bean.RobotMessageBean) {
        bean.isTraning = true
        //        notifyDataSetChanged();
        Observable
            .just(1)
            .subscribeOn(Schedulers.newThread())
            .map(Function<Int, String> { integer: Int -> textTrans(bean.content)?:"" }
            )
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ s: String? ->
                if (s != null) {
                    bean.isTraning = false
                    bean.tranString = s
                } else {
                    bean.isTraning = false
                }
                notifyDataSetChanged()
            }, { throwable: Throwable? ->
                bean.isTraning = false
                notifyDataSetChanged()
            })
    }



    override fun getItemCount(): Int {
        return list.size
    }
    fun call(anchor: com.juicy.common.model.bean.AnchorInfoBean, status: String?) {
        callHandle(anchor.callCoins?:0, Constant.POPULAR_WALL, anchor.userId, status, "anchorWall")
    }

    class AnchorVH(view: View) : RecyclerView.ViewHolder(view) {
        // 初始化视图
        var avatarV: RoundImageView =
            view.findViewById(R.id.aroundCut)
        var tag: AppCompatImageView =
            view.findViewById(R.id.switchClose)
        var country: TextView =
            view.findViewById(R.id.playGrid)
        var video: ImageView =
            view.findViewById(R.id.fullAround)
        var status: AppCompatImageView =
            view.findViewById(R.id.coordinatorFlexible)
        var name: AppCompatTextView =
            view.findViewById(R.id.stretchRight)
        var info: LinearLayout =
            view.findViewById(R.id.xsMedium)
        var container: LinearLayout =
            view.findViewById(R.id.timerWithin)
        var model: com.juicy.common.model.bean.AnchorInfoBean? = null

        var age: TextView =
            view.findViewById(R.id.autoTimer)


        init {
            val layoutParams = container.layoutParams as LinearLayout.LayoutParams
            layoutParams.height =
                ((getScreenSize(itemView.context)[0] - dip2px(4f) * 4) * 171.0 / 234.0).toInt()
            container.layoutParams = layoutParams
        }

        companion object {
            fun setOnlineStatues(anchorVH: AnchorVH, anchor: com.juicy.common.model.bean.AnchorInfoBean) {
                if (anchor.status != null) {
                    val isEnableCall = UserOnlineStatusService.instance?.getStatusEnableCall(
                        anchor.status?:""
                    )?:false
                    val color = UserOnlineStatusService.instance?.getStatusColor(anchor.status?:"")?:0
                    if (color != 0){
                        anchorVH.status.setImageResource(color)
                    }

                    if (!isEnableCall) {
                        anchorVH.video.setImageResource(R.drawable.ic_call_offline_sage)
                        val layoutParams = anchorVH.video.layoutParams
                        layoutParams.height = dip2px(36f)
                        layoutParams.width = dip2px(36f)
                        anchorVH.video.layoutParams = layoutParams
                    } else {
                        anchorVH.video.setImageResource(R.drawable.ic_call_online_mist)
                        val layoutParams = anchorVH.video.layoutParams
                        layoutParams.height = dip2px(37f)
                        layoutParams.width = dip2px(37f)

                        anchorVH.video.layoutParams = layoutParams
                    }
                }
            }
        }
    }

    class BannerVH(itemView: View) : RecyclerView.ViewHolder(itemView) {
        // 初始化头部视图
        var bannerView: MdBannerView =
            itemView.findViewById(R.id.headerAbove)

        init {
            val layoutParams = bannerView.layoutParams as LinearLayout.LayoutParams

            bannerView.visibility = View.GONE
            layoutParams.height =
                ((getScreenSize(itemView.context)[0] - dip2px(4f) * 2) * 100.0 / 328.0).toInt()
            bannerView.layoutParams = layoutParams

            bannerView.loadBanner()
        }
    }
}
