package com.juicy.app.modules.video

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.blankj.utilcode.util.ActivityUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.android.exoplayer2.DefaultRenderersFactory
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.source.DefaultMediaSourceFactory
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector
import com.google.android.exoplayer2.trackselection.TrackSelector
import com.google.android.exoplayer2.ui.StyledPlayerView
import com.juicy.app.databinding.ItemBinding
import com.juicy.app.R
import com.juicy.app.modules.base.activity.BaseFragment

class VideoFragment : BaseFragment, Player.Listener {
    private lateinit var binding: ItemBinding
    private var mediaItem2: com.juicy.common.model.bean.MediaInfoBean? = null
    private var mediaItem: MediaItem? = null
    private var player: ExoPlayer? = null



    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = ItemBinding.inflate(inflater)
        return binding.root
    }

    constructor()

    constructor(mediaItem: MediaItem?, mediaInfoBean: com.juicy.common.model.bean.MediaInfoBean?) {
        this.mediaItem = mediaItem
        this.mediaItem2 = mediaInfoBean
    }

    override fun initData() {
    }

    override fun initView() {
        binding.switchOpen.setShowBuffering(StyledPlayerView.SHOW_BUFFERING_ALWAYS)
        binding.switchOpen.controllerAutoShow = false
        binding.switchOpen.setShowShuffleButton(false)
        binding.switchOpen.setShowFastForwardButton(false)
        binding.switchOpen.setShowNextButton(false)
        if (!mediaItem2?.thumbUrl.isNullOrEmpty()) {
            Glide.with(requireContext()).load(mediaItem2?.thumbUrl).apply(RequestOptions().frame(1000000))
                .placeholder(
                    R.drawable.img_empty_big_red
                ).into(
                    binding.unselectedFlexible
                )
        }
        binding.switchOpen.setShowPreviousButton(false)
        binding.switchOpen.setShowRewindButton(false)



    }

    override fun onPause() {
        super.onPause()
        player?.pause()
    }

    override fun onResume() {
        super.onResume()
        startPlayer()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (player != null) {
            //释放播放器
            player?.release()
            player = null
        }
    }

    private fun startPlayer() {
        try {
            if (player == null && mediaItem != null) {
                val trackSelector: TrackSelector =
                    DefaultTrackSelector(ActivityUtils.getTopActivity())
                //创建播放器
                player = ExoPlayer.Builder(ActivityUtils.getTopActivity())
                    .setRenderersFactory(DefaultRenderersFactory(ActivityUtils.getTopActivity()))
                    .setTrackSelector(trackSelector)
                    .setMediaSourceFactory(DefaultMediaSourceFactory(ActivityUtils.getTopActivity()))
                    .build()
                binding.switchOpen.player = player
                player?.repeatMode = ExoPlayer.REPEAT_MODE_ALL
                player?.setMediaItem(mediaItem!!)
                player?.playWhenReady = true
                player?.prepare()

                player?.addListener(this)
            } else {
                player?.play()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onIsLoadingChanged(isLoading: Boolean) {
        super<Player.Listener>.onIsLoadingChanged(isLoading)
        if (!isLoading) {
        }
    }

    override fun onIsPlayingChanged(isPlaying: Boolean) {
        super<Player.Listener>.onIsPlayingChanged(isPlaying)
        if (isPlaying) {
            binding.collapseFlexible.visibility = View.GONE //显示loading
        }
    }
}
