package com.juicy.app.modules.base.adapter

import android.view.View
import androidx.appcompat.widget.AppCompatTextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R
import com.juicy.common.utils.LanguageManager

class ReportItemAdapter(layoutResId: Int) : BaseQuickAdapter<String, ReportItemViewHolder>(layoutResId) {
    override fun convert(holder: ReportItemViewHolder, item: String) {
        holder.itemName.text = LanguageManager.instance!!.getLocalTranslate(item)
    }
}


