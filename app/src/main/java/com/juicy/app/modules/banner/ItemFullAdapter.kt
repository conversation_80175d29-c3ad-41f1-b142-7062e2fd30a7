package com.juicy.app.modules.banner

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.github.chrisbanes.photoview.PhotoView
import com.juicy.app.R
import com.juicy.app.modules.banner.ItemFullAdapter.PhotoViewHolder
import com.youth.banner.adapter.BannerAdapter

class ItemFullAdapter(mData: List<com.juicy.common.model.bean.MediaInfoBean>, private val context: Context) :
    BannerAdapter<com.juicy.common.model.bean.MediaInfoBean, PhotoViewHolder>(mData) {
    var listener: touchBack? = null

    interface touchBack {
        fun close()
    }

    override fun onCreateHolder(parent: ViewGroup, viewType: Int): PhotoViewHolder {
        return PhotoViewHolder(
            LayoutInflater.from(context).inflate(R.layout.item_card, parent, false)
        )
    }


    override fun onBindView(holder: PhotoViewHolder, data: com.juicy.common.model.bean.MediaInfoBean, position: Int, size: Int) {
        Glide.with(context)
            .load(data.mediaUrl)
            .into(holder.photoView)

        holder.photoView.setOnClickListener { v: View? ->
            listener?.close()
        }
    }

    class PhotoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        var photoView: PhotoView =
            itemView.findViewById(R.id.overlaySwipe)
    }
}
