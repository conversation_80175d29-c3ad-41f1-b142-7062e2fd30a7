package com.juicy.app.modules.base.dialog

import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.style.ClickableSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.widget.AppCompatTextView
import androidx.fragment.app.DialogFragment
import com.bumptech.glide.Glide
import com.juicy.app.R
import com.juicy.app.databinding.DialogModalBinding
import com.juicy.common.config.Cache
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.ThreadUtil.execute
import com.juicy.common.utils.TranslateUtils.textTrans

class DerivativeDialog : DialogFragment() {
    private var dialogDerivativeBinding: DialogModalBinding? = null
    private var dismissCallBack: DismissCallBack? = null

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.BOTTOM)
            dialog!!.setCancelable(false)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dialogDerivativeBinding = DialogModalBinding.inflate(inflater)
        return dialogDerivativeBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initView()
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            dialogDerivativeBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            dialogDerivativeBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        dialogDerivativeBinding!!.startCollapsed.text =
            LanguageManager.instance!!.getLocalTranslate("Download_And_Get_Free_Coins")
        dialogDerivativeBinding!!.emptyBelow.text =
            LanguageManager.instance!!.getLocalTranslate("Next_time")
        dialogDerivativeBinding!!.firstAuto.text =
            LanguageManager.instance!!.getLocalTranslate("Click_to_translate")
    }

    private fun initView() {
        val guide = Cache.instance.guide
        if (guide != null) {
            Glide.with(this).load(guide.inIcon).into(
                dialogDerivativeBinding!!.disabledWithin
            )
            val appName = SpannableString(guide.inPkgName)
            appName.setSpan(
                TermsClickSpan(Color.parseColor("#00DDFF")),
                0,
                appName.length,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            val coinsNum = SpannableString(guide.rewardCoins.toString())
            coinsNum.setSpan(
                TermsClickSpan(Color.parseColor("#FF7B42")),
                0,
                coinsNum.length,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            var myCoins: SpannableString? = null
            if (Cache.instance.userInfo != null) {
                myCoins = SpannableString(Cache.instance.userInfo!!.availableCoins.toString())
                myCoins.setSpan(
                    TermsClickSpan(Color.parseColor("#FF7B42")),
                    0,
                    myCoins.length,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }
            val invitation = SpannableString(guide.inviteCode.toString())
            invitation.setSpan(
                TermsClickSpan(Color.parseColor("#6F34FF")),
                0,
                invitation.length,
                Spanned.SPAN_INCLUSIVE_EXCLUSIVE
            )
            dialogDerivativeBinding!!.xlOverlay.text = guide.content
            dialogDerivativeBinding!!.sliderLower.append(getString(R.string.common_ok_59))
            dialogDerivativeBinding!!.sliderLower.append(" ")
            if (myCoins != null) {
                dialogDerivativeBinding!!.sliderLower.append(myCoins)
            }

            dialogDerivativeBinding!!.sliderLower.append(" ")
            dialogDerivativeBinding!!.sliderLower.append(getString(R.string.common_ok_60))
            dialogDerivativeBinding!!.sliderLower.append(" ")
            dialogDerivativeBinding!!.sliderLower.append(appName)
            dialogDerivativeBinding!!.sliderLower.append(" ")
            dialogDerivativeBinding!!.sliderLower.append(getString(R.string.common_ok_61))
            dialogDerivativeBinding!!.centerBadge.append(getString(R.string.common_ok_62))
            dialogDerivativeBinding!!.centerBadge.append(" ")
            dialogDerivativeBinding!!.centerBadge.append(coinsNum)
            dialogDerivativeBinding!!.centerBadge.append(" ")
            dialogDerivativeBinding!!.centerBadge.append(getString(R.string.common_ok_63))
            val text3 = getString(R.string.common_ok_64)
            dialogDerivativeBinding!!.backgroundPanel.text = text3
            val text4 = getString(R.string.msg_invalid)
            dialogDerivativeBinding!!.confirmMedium.text = text4
            val text5 = getString(R.string.common_ok_65)
            dialogDerivativeBinding!!.swipeEnabled.text = text5
            dialogDerivativeBinding!!.addFull.append(getString(R.string.common_ok_66))
            dialogDerivativeBinding!!.addFull.append(" ")
            dialogDerivativeBinding!!.addFull.append(invitation)
            dialogDerivativeBinding!!.addFull.append(" ")
            dialogDerivativeBinding!!.addFull.append(getString(R.string.common_ok_67))
            dialogDerivativeBinding!!.addFull.append(" ")
            dialogDerivativeBinding!!.addFull.append(appName)
            dialogDerivativeBinding!!.addFull.append(" ")
            dialogDerivativeBinding!!.addFull.append(getString(R.string.nav_up))
            dialogDerivativeBinding!!.switchTiny.text = appName
            val stringList: MutableList<String?> = ArrayList()
            stringList.add(guide.content)
            stringList.add(dialogDerivativeBinding!!.sliderLower.text.toString())
            stringList.add(dialogDerivativeBinding!!.centerBadge.text.toString())
            stringList.add(text3)
            stringList.add(text4)
            stringList.add(text5)
            stringList.add(dialogDerivativeBinding!!.addFull.text.toString())
            val uiTextList: MutableList<AppCompatTextView> = ArrayList()
            uiTextList.add(dialogDerivativeBinding!!.xlOverlay)
            uiTextList.add(dialogDerivativeBinding!!.sliderLower)
            uiTextList.add(dialogDerivativeBinding!!.centerBadge)
            uiTextList.add(dialogDerivativeBinding!!.backgroundPanel)
            uiTextList.add(dialogDerivativeBinding!!.confirmMedium)
            uiTextList.add(dialogDerivativeBinding!!.swipeEnabled)
            uiTextList.add(dialogDerivativeBinding!!.addFull)
            dialogDerivativeBinding!!.chooseSecondary.setOnClickListener { view: View? ->
                execute {
                    for (i in stringList.indices) {
                        transalate(stringList[i]!!, uiTextList[i])
                    }
                }
            }
            dialogDerivativeBinding!!.startCollapsed.setOnClickListener { v: View? ->
                if (guide.source == 1 || guide.source == 2) {
                    val uri = Uri.parse(guide.url)
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    context!!.startActivity(intent)
                    dismiss()
                }
            }
            dialogDerivativeBinding!!.emptyBelow.setOnClickListener { view: View? ->
                dismiss()
            }
        }
    }

    @Synchronized
    private fun transalate(text: String, textView: AppCompatTextView) {
        val textTrans = textTrans(text)
        if (textTrans != null && !textTrans.isEmpty()) {
            if (isDestroy(activity)) return
            activity!!.runOnUiThread { textView.text = textTrans }
        }
    }


    internal inner class TermsClickSpan(private val color: Int) : ClickableSpan() {
        override fun onClick(widget: View) {
        }

        override fun updateDrawState(paint: TextPaint) {
            paint.color = color
        }
    }

    override fun dismiss() {
        super.dismiss()
        if (null != dismissCallBack) {
            dismissCallBack!!.onDismiss()
        }
    }

    fun setDismiss(dismissCallBack: DismissCallBack?) {
        this.dismissCallBack = dismissCallBack
    }

    interface DismissCallBack {
        fun onDismiss()
    }
}
