package com.juicy.app.modules.base.dialog

import android.content.Context
import android.graphics.Color
import android.os.CountDownTimer
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.SpanUtils
import com.juicy.app.databinding.LayoutWrapperBinding
import com.juicy.common.model.event.PromotionPayStatusChangeEvent
import com.juicy.common.model.event.PayResultEvent
import com.juicy.common.model.event.NewUserRewardsShowStatusChangeEvent
import com.juicy.common.model.message_event.AppRechargeOrderStatusBean
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.DateMetaUtil.ms2HMS
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PayUtils.PayUtils
import com.juicy.common.utils.PayUtils.PayUtils.FailOnListen
import com.juicy.common.utils.PayUtils.PayUtils.SuccessOnListen
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.abs

class NewUserDialogView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    ConstraintLayout(context, attrs, defStyleAttr) {
    private var userDialogBinding: LayoutWrapperBinding? = null
    private var mAppRechargeOrderStatusBean: AppRechargeOrderStatusBean? = null
    private var socketEventBusBack: SocketEventBusBack? = null
    private var mLabel : String? = null
    private var countDownTimer: CountDownTimer? = null
    private var dismissCallBack: DismissCallBack? = null
    private var mAge = 0
    private var payUtils: PayUtils? = null
    private var mUserName: String? = null

    private var isToPay = false
    private fun init(context: Context) {
        userDialogBinding = LayoutWrapperBinding.inflate(
            LayoutInflater.from(context),
            this, true
        )
        mUserName = Cache.instance.appName
        mAge = 10
        mLabel = LanguageManager.instance!!.getLocalTranslate("Exclusive_for_New_User")
        mAppRechargeOrderStatusBean = null
        //        EventBus.getDefault().register(this);
        // 在这里设置视图的初始状态和行为
        setDirection()
        userDialogBinding!!.beyondMenu.setOnClickListener { v: View? -> }
        userDialogBinding!!.compactNarrow.setOnClickListener { v: View? -> }
        userDialogBinding!!.emptyBelow.setOnClickListener { v: View? -> close() }
        userDialogBinding!!.customMd.text =
            LanguageManager.instance!!.getLocalTranslate("Exclusive_for_New_User")
        initData()
    }


    private val failOnListen: FailOnListen = FailOnListen {}

    init {
        init(context)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(`object`: Any?) {
        //空接收，不然eventbus会报错
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPayEvent(userDialogEvent: PayResultEvent) {
        if (userDialogEvent.isSuccess) {
            close()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(`object`: PromotionPayStatusChangeEvent?) {
        try {
            EventBus.getDefault().post(NewUserRewardsShowStatusChangeEvent())
            if (Cache.instance.webViewActivity != null) {
                Cache.instance.webViewActivity!!.refreshAgentWeb()
            }
            if (!getBooleanVal(SpKeyPool.HAS_EVALUATE, false)!!) {
                putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
                val currentActivity = currentActivity as AppCompatActivity?
                if (!isDestroy(currentActivity)) {
                    val assessDialog = AssessDialog()
                    assessDialog.show(currentActivity!!.supportFragmentManager, "")
                }
            }
            //   Cache.getInstance().userPromotionGood = null;
            close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    private val successOnListen: SuccessOnListen =object :SuccessOnListen {

        override fun onListen() {
            try {
                EventBus.getDefault().post(NewUserRewardsShowStatusChangeEvent())
                if (Cache.instance.webViewActivity != null) {
                    Cache.instance.webViewActivity!!.refreshAgentWeb()
                }
                if (!getBooleanVal(SpKeyPool.HAS_EVALUATE, false)!!) {
                    putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
                    val currentActivity = currentActivity as AppCompatActivity?
                    if (!isDestroy(currentActivity)) {
                        val assessDialog = AssessDialog()
                        assessDialog.show(currentActivity!!.supportFragmentManager, "")
                    }
                }
                //   Cache.getInstance().userPromotionGood = null;
                close()
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            userDialogBinding!!.root.layoutDirection = LAYOUT_DIRECTION_RTL
            userDialogBinding!!.imageCounter.scaleX = -1f
        } else {
            userDialogBinding!!.root.layoutDirection = LAYOUT_DIRECTION_LTR
        }
    }



    fun setProdownTime() {
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
        if (Cache.instance.userPromotionGood == null) return
        val surplusMillisecond =
            Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
        countDownTimer = object : CountDownTimer(surplusMillisecond, 1000) {
            override fun onTick(l: Long) {
                if (Cache.instance.userPromotionGood != null) {
                    val surplusMillisecond =
                        Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
                    val time = ms2HMS(surplusMillisecond)
                    userDialogBinding!!.endFlexible.setTime(time)
                    if (abs((l - surplusMillisecond).toDouble()) > 1000) {
                        setProdownTime()
                    }
                }
            }

            override fun onFinish() {
                Cache.instance.userPromotionGood = null
                close()
            }
        }.start()
    }
    fun initData() {
        Cache.instance.payChannle
        socketEventBusBack = object : SocketEventBusBack {
            override fun rechargeSuccessBack(appRechargeOrderStatusBean: AppRechargeOrderStatusBean?) {
                if (appRechargeOrderStatusBean!!.status == 2) {
                    if (isToPay) {
                        EventBus.getDefault().post(NewUserRewardsShowStatusChangeEvent())
                        close()
                    }
                }
            }
        }
        val userPromotionGood = Cache.instance.userPromotionGood
        if (userPromotionGood != null) {
            if (userPromotionGood.exchangeCoin != null) {
                userDialogBinding!!.viewFit.text = userPromotionGood.exchangeCoin.toString()
            }
            if (userPromotionGood.discount != null) {
                userDialogBinding!!.selectedExpanded.text =
                    "" + (userPromotionGood.discount!!.toDouble() * 100).toInt() + "%"
                userDialogBinding!!.selectedExpanded.setTextColor(Color.parseColor("#FF0000"))
            }
            if (userPromotionGood.price != null) {
                if (userPromotionGood.price!!.toDouble() != userPromotionGood.originalPrice!!.toDouble()) {
                    SpanUtils.with(userDialogBinding!!.flexibleBackground)
                        .append("$" + userPromotionGood.price!!.toDouble())
                        .setFontSize(20, true)
                        .setBold().append(" ")
                        .append("$" + userPromotionGood.originalPrice.toString())
                        .setFontSize(12, true)
                        .setForegroundColor(Color.parseColor("#AF004B"))
                        .setStrikethrough()
                        .setBold()
                        .append(" ")
                        .create()
                } else {
                    SpanUtils.with(userDialogBinding!!.flexibleBackground)
                        .append("$" + userPromotionGood.price!!.toDouble())
                        .setFontSize(20, true)
                        .setBold()
                        .append(" ")
                        .create()
                }
            }
            userDialogBinding!!.flexibleBackground.setOnClickListener { v: View? ->
                if (isFastClick) return@setOnClickListener
                isToPay = true
                val currentActivity =
                    currentActivity as AppCompatActivity?
                payUtils = PayUtils(
                    currentActivity,
                    "pop_promotion",
                    false,
                    userPromotionGood,
                    successOnListen,
                    failOnListen
                )
                payUtils!!.openPayDialog()
            }

            setProdownTime()
            addMessageEvent(socketEventBusBack!!)
        }
    }

    fun close() {
        removeMessageEvent(socketEventBusBack!!)
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
        if (dismissCallBack != null) {
            dismissCallBack!!.onDismiss()
        }

        //回调关闭弹框
    }

    fun setDismiss(dismissCallBack: DismissCallBack?) {
        this.dismissCallBack = dismissCallBack
    }

    interface DismissCallBack {
        fun onDismiss()
    }
}
