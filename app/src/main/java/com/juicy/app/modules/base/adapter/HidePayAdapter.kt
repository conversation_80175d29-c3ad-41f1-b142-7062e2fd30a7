package com.juicy.app.modules.base.adapter

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.juicy.app.R
import com.juicy.common.model.bean.ExtraBean.ChannelInfoBean
import com.juicy.common.utils.AppActivityUtil.currentActivity

class HidePayAdapter(listBeans: List<ChannelInfoBean>, callBack: PayOneAdapter.CallBack) :
    RecyclerView.Adapter<HidePayVH>() {
    var selectPosition: Int = 0
    private val callBack: PayOneAdapter.CallBack
    private var listBeans: List<ChannelInfoBean> = ArrayList()

    init {
        this.listBeans = listBeans
        this.callBack = callBack
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HidePayVH {
        return HidePayVH(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.layout_avatar, parent, false)
        )
    }

    override fun onBindViewHolder(payVH: HidePayVH, @SuppressLint("RecyclerView") position: Int) {
        if (listBeans[position].payChannel != null) {
            payVH.payName.text = listBeans[position].payChannel
        }
        if (listBeans[position].iconUrl != null) {
            Glide.with(currentActivity!!)
                .load(listBeans[position].iconUrl)
                .into(payVH.payIcon)
        }
        payVH.checkBtn.isSelected = position == selectPosition
//        if (payVH.checkBtn.isSelected){
//            payVH.checkBtn.setBackgroundResource(R.drawable.bg_pay_channel_check_selected_lemon)
//        }else{
//            payVH.checkBtn.setBackgroundResource(R.drawable.bg_pay_channel_check_btn_ebony)
//        }
        payVH.itemCl.setOnClickListener {
            selectPosition = position
            callBack.onCallBack(selectPosition)
            notifyDataSetChanged()
        }
        payVH.checkBtn.setOnClickListener {
            selectPosition = position
            callBack.onCallBack(selectPosition)
            notifyDataSetChanged()
        }
    }

    override fun getItemCount(): Int {
        return listBeans.size
    }

    interface CallBack {
        fun onCallBack(selectPresent: Int)
    }
}


class HidePayVH(view: View) : RecyclerView.ViewHolder(view) {
    var payIcon: AppCompatImageView =
        view.findViewById(R.id.aroundCancel)
    var payName: AppCompatTextView =
        view.findViewById(R.id.headerCompact)
    var bg: AppCompatImageView =
        view.findViewById(R.id.fillGrid)
    var itemCl: ConstraintLayout =
        view.findViewById(R.id.overlayPick)

    var checkBtn: Button =
        view.findViewById(R.id.mediumXxl)
}
