package com.juicy.app.modules.video

import com.juicy.app.modules.video.inf.IAnchorVideoPresenter
import com.juicy.app.modules.video.inf.IVideoView
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.CallUtil.callHandle
import com.juicy.common.utils.PermissionUtil.Companion.checkVideoPermission
import com.juicy.common.utils.PermissionUtil.PermissionCallback

class VideoPresenter(private val videoView: IVideoView) : IAnchorVideoPresenter {

    override fun callVideo(status: String?,bean: com.juicy.common.model.bean.JuicyUserInfoBean?) {
        checkVideoPermission((videoView as BaseActivity), object : PermissionCallback {
            override fun complete() {
                call(status,bean)
            }
        })
    }
    fun call(status: String?, bean: com.juicy.common.model.bean.JuicyUserInfoBean?) {
        var unitPrice = -1
        if (bean != null) {
            if (Cache.instance.userInfo != null && Cache.instance.userInfo?.availableCoins != null) {
                if (Cache.instance.userInfo?.vip != null &&
                    Cache.instance.userInfo?.vip ==  true &&
                    bean.vipUnitPrice != null
                ) {
                    unitPrice = bean.vipUnitPrice
                } else if (bean.unitPrice != null) {
                    unitPrice = bean.unitPrice
                }
            }
        }

        callHandle(
            unitPrice,
            Constant.DETAIL_VIDEO_PAGE,
            bean?.userId,
            status,
            "anchor_profile_video_call"
        )
    }



}
