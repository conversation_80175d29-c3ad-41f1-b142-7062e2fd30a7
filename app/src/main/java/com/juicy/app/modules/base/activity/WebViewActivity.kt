package com.juicy.app.modules.base.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.GsonUtils
import com.juicy.app.R
import com.juicy.app.modules.base.dialog.FullShoppingDialog
import com.juicy.common.config.Cache
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation

class WebViewActivity : BaseActivity() {
    //    private AgentWeb agentWeb;
    private var llLayout: LinearLayout? = null
    private var mUrlPath: String? = null
    private var mTitle: String? = null
    private var mOrderNo: String? = null
    private var mTag: String? = null
    private var mResultPage: String? = ""
    private var mUser: String? = null
    private var webView: WebView? = null
    private var mUrl: String? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Cache.instance.webViewActivity = this
        setContentView(R.layout.layout_divider)

        mTag = Cache.instance.appName
        mTitle = Cache.instance.userInfo?.avatar
        mUser = Cache.instance.userInfo?.nickname
        mUrl = Cache.instance.userInfo?.avatarUrl

        //        // 设置状态栏颜色
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            Window window = getWindow();
//            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
//            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
//            window.setStatusBarColor(getResources().getColor(R.color.material_ccff)); // 替换为您的颜色
//        }
        initData()
        llLayout = findViewById(R.id.disabledExpand)
        initWebView()
        val mainPageLayoutParams = llLayout?.getLayoutParams() as RelativeLayout.LayoutParams
        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        llLayout?.setLayoutParams(mainPageLayoutParams)
    }

    @JavascriptInterface
    fun newTppClose() {
        finish()
    }

    @JavascriptInterface
    fun newTppLogEvent(json: String?) {
    }

    @JavascriptInterface
    fun openVipService() {
        RouteUtils.routeToConversationActivity(
            this,
            Conversation.ConversationType.PRIVATE,
            Cache.instance.userStratResult!!.userServiceAccountId
        )
    }

    @JavascriptInterface
    fun recharge() {
        runOnUiThread {
            val fullShoppingDialog = FullShoppingDialog("slot_machine")
            fullShoppingDialog.show(supportFragmentManager, "")
        }
    }

    fun rechargeSource(json: String?) {
    }

    @get:JavascriptInterface
    val supportApi: String
        get() {
            val api: MutableList<String> =
                ArrayList()
            api.add("newTppClose")
            api.add("newTppLogEvent")
            api.add("openVipService")
            api.add("recharge")
            api.add("rechargeSource")
            return GsonUtils.getGson().toJson(api)
        }

    override fun initData() {
        if (null != intent) {
            //1.url地址 2.订单号  3.跳转进入页面
            mUrlPath = intent.getStringExtra("urlPath")
            mOrderNo = intent.getStringExtra("orderNo")
            mResultPage = intent.getStringExtra("mResultPage")
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWebView() {
        webView = WebView(this)
        llLayout!!.addView(webView, LinearLayout.LayoutParams(-1, -1))

        //声明WebSettings子类

//如果访问的页面中要与Javascript交互，则webview必须设置支持Javascript
        webView!!.settings.javaScriptEnabled = true
        webView!!.settings.loadWithOverviewMode = true // 缩放至屏幕的大小
        webView!!.settings.domStorageEnabled = true
        //设置自适应屏幕，两者合用
        webView!!.settings.useWideViewPort = true //将图片调整到适合webview的大小
        webView!!.settings.defaultTextEncodingName = "utf-8" //设置编码格式
        //其他细节操作
        webView!!.settings.cacheMode = WebSettings.LOAD_CACHE_ELSE_NETWORK //关闭webview中缓存
        webView!!.settings.allowFileAccess = true //设置可以访问文件
        webView!!.settings.javaScriptCanOpenWindowsAutomatically = true //支持通过JS打开新窗口
        webView!!.settings.loadsImagesAutomatically = true //支持自动加载图片
        webView!!.webViewClient = AppWebClient(this)

        webView!!.webChromeClient = WebChromeClient()
        webView!!.addJavascriptInterface(this, NAME)
        webView!!.loadUrl(mUrlPath!!)
    }

    fun refreshAgentWeb() {
        if (webView != null) {
            webView!!.loadUrl("javascript:HttpTool.NativeToJs('recharge')")
        }
    }

    override fun initView() {
    }

    override fun onDestroy() {
        super.onDestroy()
        Cache.instance.webViewActivity = null
        webView!!.destroy()
    }

    override fun transLocalText() {
    }

    override fun onPause() {
        super.onPause()
        webView!!.onPause()
    }

    override fun onResume() {
        super.onResume()
        webView!!.onResume()
    }

    companion object {
        const val NAME: String = "JSBridgeService"
        fun startActivity(context: Context, url: String?) {
            val intent = Intent(context, WebViewActivity::class.java)
            intent.putExtra("urlPath", url)
            context.startActivity(intent)
        }
    }
}
