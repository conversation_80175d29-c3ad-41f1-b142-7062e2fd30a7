package com.juicy.app.modules.base.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.juicy.app.R
import com.juicy.common.model.bean.ExtraBean.ChannelInfoBean
import com.juicy.common.utils.LanguageManager
import kotlin.math.max

class PayOneAdapter(
    private val good: com.juicy.common.model.bean.ActivityInfoBean?,
    private val mContext: Context,
    listBeans: List<ChannelInfoBean>,
    callBack: CallBack
) :
    RecyclerView.Adapter<PayOneViewHolder>() {
    var selectPosition: Int = 0
    private val callBack: CallBack
    var listBeans: List<ChannelInfoBean> = ArrayList()

    init {
        this.listBeans = listBeans
        this.callBack = callBack
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PayOneViewHolder {
        return PayOneViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.layout_clock, parent, false)
        )
    }

    override fun onBindViewHolder(payOneViewHolder: PayOneViewHolder, @SuppressLint("RecyclerView") position: Int) {
        if (listBeans[position].itemType == 1) {
            if (listBeans[position].payChannel != null && listBeans[position].payChannel == "GP") {
                payOneViewHolder.recentlyUsed.visibility = View.GONE
            } else {
                payOneViewHolder.recentlyUsed.visibility = View.VISIBLE
                if (listBeans[position].recommendReason != null) payOneViewHolder.recentlyUsed.text =
                    LanguageManager.instance!!.getLocalTranslate(
                        listBeans[position].recommendReason!!
                    )
            }
        } else {
            payOneViewHolder.recentlyUsed.visibility = View.GONE
        }
        if (listBeans[position].payChannel != null) {
            payOneViewHolder.payName.text = listBeans[position].title
        }
        if (listBeans[position].iconUrl != null) {
            Glide.with(mContext)
                .load(listBeans[position].iconUrl)
                .into(payOneViewHolder.payIcon)
        }
        var present = 0
        if (good != null) {
            present =
                if (good.thirdpartyCoinPercent != null && listBeans[position].payChannel != null && listBeans[position].payChannel != "GP") {
                    if (good.promotion != null && good.promotion!!) {
                        max(
                            listBeans[position].promotionPresentCoinRatio.toDouble(),
                            good.thirdpartyCoinPercent!!.toDouble()
                        ).toInt()
                    } else {
                        max(
                            listBeans[position].presentCoinRatio.toDouble(),
                            good.thirdpartyCoinPercent!!.toDouble()
                        ).toInt()
                    }
                } else if (good.extraCoinPercent != null) {
                    if (good.promotion != null && good.promotion!!) {
                        max(
                            listBeans[position].promotionPresentCoinRatio.toDouble(),
                            good.extraCoinPercent!!.toDouble()
                        ).toInt()
                    } else {
                        max(
                            listBeans[position].presentCoinRatio.toDouble(),
                            good.extraCoinPercent!!.toDouble()
                        ).toInt()
                    }
                } else {
                    if (good.promotion != null && good.promotion!!) {
                        listBeans[position].promotionPresentCoinRatio
                    } else {
                        listBeans[position].presentCoinRatio
                    }
                }
        }

        if (present > 0) {
            val builder = StringBuilder()
            builder.append("+").append(present).append("%").append(" ")
                .append(LanguageManager.instance!!.getLocalTranslate("More_Coins"))
            payOneViewHolder.payDiscount.text = builder
            payOneViewHolder.discountLl.visibility = View.VISIBLE
        } else {
            payOneViewHolder.discountLl.visibility = View.GONE
        }
        //            payVH.bg.setBackgroundResource(R.drawable.bg_three_select_white);
        //            payVH.bg.setBackgroundDrawable(null);
        payOneViewHolder.checkBtn.isSelected = position == selectPosition
        if (position == selectPosition) {
            payOneViewHolder.itemCl.background = null
           // payVH.itemCl.setBackgroundResource(R.drawable.ic_layer_pay_select_color_ruby)
           // payVH.checkBtn.setBackgroundResource(R.drawable.bg_pay_channel_check_selected_lemon)
        } else {
           // payVH.checkBtn.setBackgroundResource(R.drawable.bg_pay_channel_check_btn_ebony)
            payOneViewHolder.itemCl.background = null
        }
        payOneViewHolder.itemCl.setOnClickListener { v: View? ->
            if (selectPosition == position) return@setOnClickListener
            selectPosition = position
            callBack.onCallBack(position)
            notifyDataSetChanged()
        }
        payOneViewHolder.checkBtn.setOnClickListener {
            if (selectPosition == position) return@setOnClickListener
            selectPosition = position
            callBack.onCallBack(position)
            notifyDataSetChanged()
        }
    }

    override fun getItemCount(): Int {
        return listBeans.size
    }

    interface CallBack {
        fun onCallBack(selectPresent: Int)
    }
}
