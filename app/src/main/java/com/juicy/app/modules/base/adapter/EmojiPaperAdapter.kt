package com.juicy.app.modules.base.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.juicy.app.R
import com.juicy.common.utils.view.EmojiView.EmojiCallBack

class EmojiPaperAdapter : RecyclerView.Adapter<EmojiPagerViewHolder>() {
    private var emojiList: List<List<String?>> = ArrayList()
    private var emojiCallBack: EmojiCallBack? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EmojiPagerViewHolder {
        return EmojiPagerViewHolder(
            LayoutInflater.from(parent.context).inflate(R.layout.layout_form, parent, false)
        )
    }

    override fun onBindViewHolder(holder: EmojiPagerViewHolder, position: Int) {
        holder.emojiView.setCallBack(emojiCallBack)
        holder.emojiView.setData(emojiList[position].toMutableList())
        holder.emojiView.alpha = 1.0f
    }

    override fun getItemCount(): Int {
        return emojiList.size
    }

    fun setData(emojiList: List<List<String?>>) {
        this.emojiList = emojiList
        notifyDataSetChanged()
    }

    fun setEmojiCallBack(callBack: EmojiCallBack?) {
        this.emojiCallBack = callBack
    }
}

