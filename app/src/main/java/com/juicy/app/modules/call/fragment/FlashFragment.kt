package com.juicy.app.modules.call.fragment

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import androidx.appcompat.app.AppCompatActivity
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.databinding.FragmentPopupBinding
import com.juicy.app.modules.call.VideoCallActivity
import com.juicy.app.modules.base.dialog.HalfShoppingDialog
import com.juicy.app.modules.base.dialog.VipDialogFragment
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.model.event.AvailableCoinsChangeEvent
import com.juicy.common.model.event.PayResultEvent
import com.juicy.common.model.event.StrategyChangeEvent
import com.juicy.common.networks.delegate.GetCoinCountInterface.getCoin
import com.juicy.common.networks.delegate.GetRandomBroadcasterInterface.getRandomBroadcaster
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.AppUtil.checkNetworkToast
import com.juicy.common.utils.DateMetaUtil.getDate
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PermissionUtil.Companion.checkVideoPermission
import com.juicy.common.utils.PermissionUtil.PermissionCallback
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.Timer
import java.util.TimerTask

class FlashFragment : BaseFragment() {
    private var binding: FragmentPopupBinding? = null
    private var broadcasterInfo: com.juicy.common.model.bean.JuicyUserInfoBean? = null
    private var isAdded = false
    private var randomTimer: Timer? = null


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPopupBinding.inflate(inflater, container, false)
        return binding!!.root
    }

    override fun initData() {
        if (!Cache.instance.reviewPkg) {
            if (randomTimer == null) {
                randomTimer = Timer()
                val task: TimerTask = object : TimerTask() {
                    override fun run() {
                        // 定时器执行的操作
                        requireActivity().runOnUiThread { // 在主线程中执行的操作
                            randomBroadcaster
                        }
                    }
                }
                randomTimer!!.schedule(task, 0, 10000)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(event: StrategyChangeEvent?) {
        updateUiData()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onAvailableCoinsEvent(event: AvailableCoinsChangeEvent?) {
        updateUiData()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPayEvent(event: PayResultEvent?) {
        getCoin(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>() {
            override fun onNext(coinData: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>) {
                super.onNext(coinData)
                updateUiData()
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }

    override fun onResume() {
        super.onResume()

        Cache.instance.netStrategy
        updateUiData()
        checkCoins()
    }

    override fun onDestroyView() {
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (randomTimer != null) {
            randomTimer!!.cancel()
            randomTimer = null
        }
    }


    fun toVipDialog() {
        val vipDialog = VipDialogFragment(true)
        val currentActivity = currentActivity
        if (currentActivity != null && (currentActivity is AppCompatActivity)) {
            vipDialog.show(currentActivity.supportFragmentManager, "")
        }
    }


    override fun initView() {
        binding!!.root.post(Runnable {
            if (isDestroy(activity)) return@Runnable
            if (!isAdded() || view == null) return@Runnable
            val mainPageLayoutParams =
                binding!!.contentSelect.layoutParams as RelativeLayout.LayoutParams
            mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
            binding!!.contentSelect.layoutParams = mainPageLayoutParams

            if (LanguageManager.instance!!.isLanguageForce) {
                binding!!.tabHeader.scaleX = -1f
            }
            binding!!.loadingVisible.setOnClickListener { view: View? ->
                toRunFalsh()
            }
            binding!!.iconUp.setOnClickListener {
                toVipDialog()
            }
            binding!!.footerUnderlay.setOnClickListener { view: View? ->
                toRunFalsh()
            }
            binding!!.throughSearch.setOnClickListener { view: View? ->
                ARouter
                    .getInstance()
                    .build(Constant.ANCHOR_INFO_ACTIVITY_ROUTE)
                    .withString(Constant.USER_ID, broadcasterInfo!!.userId)
                    .navigation()
            }

            binding!!.adaptiveOnline.setOnClickListener { view: View? ->
                val evaluateDialog = VipDialogFragment()
                evaluateDialog.entry = "subscribe_match"
                val currentActivity =
                    currentActivity as AppCompatActivity?
                evaluateDialog.show(currentActivity!!.supportFragmentManager, "")
            }

            binding!!.confirmMacro.setOnClickListener { view: View? ->
                val halfShoppingDialog = HalfShoppingDialog()
                halfShoppingDialog.entry = "match_title"
                val currentActivity =
                    currentActivity as AppCompatActivity?
                if (currentActivity != null) {
                    halfShoppingDialog.show(currentActivity.supportFragmentManager, "")
                }
            }

            binding!!.loadingVisible.text = LanguageManager.instance!!.getLocalTranslate("Random_Match")

            binding!!.macroStatic.text =
                LanguageManager.instance!!.getLocalTranslate("match")
            binding!!.stopNear.text = LanguageManager.instance!!.getLocalTranslate("free")
            if (LanguageManager.instance!!.isLanguageForce) {
                val params = binding!!.stopNear.layoutParams as RelativeLayout.LayoutParams
                params.marginStart = dip2px(20f)
                params.addRule(RelativeLayout.CENTER_HORIZONTAL, 0)
                binding!!.stopNear.layoutParams = params
            }
        })
    }

    fun updateUserInfoVip() {
        if (!isAdded() || binding == null) {
            return
        }
        if (Cache.instance.userInfo != null && Cache.instance.userInfo!!.vip) {
            val dateString = getDate("yyyy-MM-dd", Cache.instance.userInfo!!.vipExpiryTime.toLong())
            binding!!.collapsedContent.text =
                LanguageManager.instance!!.getLocalTranslate("Valid_until") + dateString
            binding!!.collapsedContent.visibility = View.VISIBLE
        } else {
            binding!!.collapsedContent.visibility = View.GONE
        }
    }

    private fun checkCoins() {
        getCoin(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>() {
            override fun onNext(coinData: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>) {
                super.onNext(coinData)
                updateUiData()
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }

    fun updateUiData() {
        if (Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.flashChatConfig != null
            && Cache.instance.userStratResult?.flashChatConfig?.isFreeCall == true) {
            binding!!.underlayTop.visibility = View.VISIBLE
            binding!!.undoClosed.visibility = View.GONE
        } else {
            binding!!.underlayTop.visibility = View.GONE
            binding!!.undoClosed.visibility = View.VISIBLE
        }
        //        if(Cache.getInstance().reviewPkg){
//            binding.undoClosed.setVisibility(View.GONE);
//        }
        if (Cache.instance.userInfo != null) {
            val ss = Cache.instance.userInfo!!.availableCoins
            binding!!.rigidForward.text =
                Cache.instance.userInfo!!.availableCoins.toString() + ""
        }

        updateUserInfoVip()
    }

    fun toRunFalsh() {
        //判断是否有网络
        if (checkNetworkToast()) return
        if (Cache.instance.userStratResult == null || Cache.instance.userInfo == null) return
        //判断是否还有免费次数
        //判断金币是否充足
        //正在通话也不能点击
        updateUiData()
        //        if(!Cache.getInstance().reviewPkg){
        val femaleCoins = Cache.instance.userStratResult?.genderMatchCoin?.femaleCoins
        if (Cache.instance.userStratResult?.flashChatConfig?.isFreeCall != true
            && Cache.instance.userInfo!!.availableCoins < (femaleCoins?:0)) {
            val halfShoppingDialog = HalfShoppingDialog()
            halfShoppingDialog.entry = "flash"
            halfShoppingDialog.show(childFragmentManager, "")

            val handler = Handler()
            handler.postDelayed({
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Coins Not Enough"
                    )
                )
            }, 100) // 延迟时间

            return
        }


        //        }
        if (currentActivity !is VideoCallActivity) {
            if (context == null) return
            checkVideoPermission((context as BaseActivity?)!!, object : PermissionCallback {
                override fun complete() {
                    val intent = Intent(
                        requireContext(),
                        VideoCallActivity::class.java
                    )

                    intent.putExtra(Constant.IS_MATCH, true)
                    startActivity(intent)
                }
            })
        }

        //        startActivity(new Intent(requireContext(), FalshRunActivity.class));
    }

    val randomBroadcaster: Unit
        get() {
            getRandomBroadcaster(object :
                ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                    super.onNext(azBaseBean)
                    if (azBaseBean.data != null) {
//                    userData.setValue(azBaseBean.getData());
                        //点击跳转详情页
                        if (isAdded) {
                            broadcasterInfo = azBaseBean.data
                           // binding!!.throughSearch.visibility = View.VISIBLE
                            loadCircleImage(
                                context,
                                broadcasterInfo!!.avatarThumbUrl,
                                binding!!.loadedUndo
                            )
                        }

                        //                    Glide.with(getContext()).load(broadcasterInfo.getAvatarThumbUrl()).placeholder(com.juicy.utils.R.drawable.img_small_empty_indigo).into(binding.loadedUndo);
                    }
                }
            })
        }

    override fun onStart() {
        super.onStart()
        isAdded = true
    }
}