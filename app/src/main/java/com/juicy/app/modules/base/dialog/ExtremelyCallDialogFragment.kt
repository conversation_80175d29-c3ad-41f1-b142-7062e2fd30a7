package com.juicy.app.modules.base.dialog

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.juicy.app.databinding.FragmentProgressBinding
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.AppDeviceUtils.getScreenSize
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.TranslateUtils.textTrans
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.schedulers.Schedulers
import java.util.regex.Pattern

class ExtremelyCallDialogFragment : DialogFragment {
    private var dialogBinding: FragmentProgressBinding? = null

    private var tipsText: String? = null

    constructor()

    constructor(tipsText: String?) {
        this.tipsText = tipsText
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dialogBinding = FragmentProgressBinding.inflate(inflater)
        return dialogBinding!!.root
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.CENTER)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = getScreenSize(this.context)[0] - dip2px(40f)
            dialog!!.window!!.attributes = layoutParams
        }
    }

    @SuppressLint("CheckResult")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()


        val pattern = Pattern.compile("(\\d+)")
        val matcher = pattern.matcher(this.tipsText)

        if (matcher.find()) {
            val numericValue = matcher.group(1).toInt()
            // 后续的处理逻辑
            val newText =
                (LanguageManager.instance!!.getLocalTranslate("Your_call_cut_off_due_to_a_service_problem") + " "
                        + numericValue + " " +
                        LanguageManager.instance!!.getLocalTranslate("added_to_your_account_to_make_up_for_it_Thanks_for_your_support"))
            dialogBinding!!.chooseFocused.text = newText
        } else {
            // 没有找到数字
            dialogBinding!!.chooseFocused.text = tipsText
        }

        //        dialogBinding.chooseFocused.setText(this.tipsText);
        Observable
            .just(1)
            .subscribeOn(Schedulers.newThread())
            .map { integer: Int? ->
                textTrans(tipsText!!
                )?:""
            }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ s: String? ->
                if (s != null && dialogBinding != null) {
                    dialogBinding!!.chooseFocused.text = s
                } else {
                }
            }, { throwable: Throwable? -> })

        dialogBinding!!.customScroll.setOnClickListener { dismiss() }
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            dialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            dialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        dialogBinding!!.badgeMedium.text = LanguageManager.instance?.getLocalTranslate("Its_okay")?:"It's okay"
        dialogBinding!!.thinLeft.text = LanguageManager.instance?.getLocalTranslate("Sorry_Coins")?:"Sorry – Coins"
    }
}