package com.juicy.app.modules.info

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.RelativeLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ActivityUtils
import com.google.gson.Gson
import com.juicy.app.modules.banner.ItemAdapter
import com.juicy.app.modules.banner.ItemFullAdapter
import com.juicy.app.modules.banner.ItemFullAdapter.touchBack
import com.juicy.app.modules.info.adapter.AnchorGiftAdapter
import com.juicy.app.modules.info.adapter.AnchorVideoAdapter
import com.juicy.app.modules.video.VideoFullActivity
import com.juicy.app.databinding.ActivityInputBinding
import com.juicy.app.R
import com.juicy.app.modules.base.dialog.BlockDialogFragment
import com.juicy.app.modules.base.dialog.BlockDialogFragment.BlockCallBack
import com.juicy.app.modules.base.dialog.LoadDialog
import com.juicy.app.modules.base.dialog.VipDialogFragment
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.model.event.BlockParamsEvent
import com.juicy.common.model.event.FollowParamsEvent
import com.juicy.common.model.event.OnlineStatusChangeEvent
import com.juicy.common.networks.delegate.GetCoinGoodsSearchInterface.getCoinGoodsSearch
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.status.UserOnlineStatusService
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.AppUtil.checkNetworkToast
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.AppDeviceUtils.getScreenSize
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PayUtils.PayUtils
import com.juicy.common.utils.QuickDrawable.Companion.create
import com.juicy.common.utils.view.AutoWrapItem
import com.juicy.common.utils.view.AutoWrapLineLayout
import com.youth.banner.config.IndicatorConfig
import com.youth.banner.indicator.CircleIndicator
import io.reactivex.rxjava3.disposables.Disposable
import io.rong.imkit.picture.decoration.GridSpacingItemDecoration
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


@Route(path = Constant.ANCHOR_INFO_ACTIVITY_ROUTE)
class ProfileActivity : BaseActivity(), IAnchorInfoView {
    private var userId: String? = null
    private lateinit var anchorInfoBinding: ActivityInputBinding
    private var presenter: com.juicy.app.modules.info.InfoPresenter? = null
    private var loadDialog: LoadDialog? = null
    private var anchorPrice = 0
    private val photoList: MutableList<com.juicy.common.model.bean.MediaInfoBean> = ArrayList()
    private val videoList: MutableList<com.juicy.common.model.bean.MediaInfoBean> = ArrayList()
    private var giftAdapter: AnchorGiftAdapter? = null
    private var anchorVideoAdapter: AnchorVideoAdapter? = null
    private var userInfo: com.juicy.common.model.bean.JuicyUserInfoBean? = null
    private var status = ""
    private var isFollow = false

    private var isShowBigBannanr = false


    private fun getUserId(): String {
        val intent = this.intent
        userId = intent.getStringExtra(Constant.USER_ID)
        return userId?:""
    }

    override fun onBackPressed() {
        if (isShowBigBannanr) {
            isShowBigBannanr = false
            anchorInfoBinding.avatarActive.visibility = View.GONE
            anchorInfoBinding.openUpload.visibility = View.GONE
            anchorInfoBinding.overlayProgress.visibility = View.GONE
            anchorInfoBinding.onlineSwipe.visibility = View.GONE
            return
        }
        super.onBackPressed()
    }

    override fun initData() {
        presenter = com.juicy.app.modules.info.InfoPresenter(this)
        loadDialog = LoadDialog(this)
    }

    override fun initView() {
        val width = getScreenSize(this)[0].toFloat()
        val height = (width * 441.0f / 360.0f).toInt()
        val imageParams =
            anchorInfoBinding.lastNarrow.layoutParams as RelativeLayout.LayoutParams
        val bannerParams = anchorInfoBinding.headerAbove.layoutParams as RelativeLayout.LayoutParams
        imageParams.height = height
        bannerParams.height = height
        anchorInfoBinding.lastNarrow.layoutParams = imageParams
        anchorInfoBinding.headerAbove.layoutParams = bannerParams
        anchorInfoBinding.emptyBadge.setOnClickListener { v: View? -> finish() }
        anchorInfoBinding.surfaceAcross.text = LanguageManager.instance?.getLocalTranslate("Chat")?:"Chat"

        //默认隐藏布局
        //隐藏VIP价格
        anchorInfoBinding.errorMaximal.visibility = View.INVISIBLE
        //隐藏性别
        anchorInfoBinding.adaptivePanel.visibility = View.INVISIBLE
        anchorInfoBinding.refreshClear.text = LanguageManager.instance?.getLocalTranslate("Video_Call")?:"Video Call"
        anchorInfoBinding.surfaceDefault.text = LanguageManager.instance?.getLocalTranslate("Video_Call")?:"Video Call"
        anchorInfoBinding.errorCalendar.text = LanguageManager.instance?.getLocalTranslate("Chat")?:"Chat"
        //隐藏呼叫按钮
        anchorInfoBinding.textToggle.visibility = View.INVISIBLE



            anchorInfoBinding.frameSort.setOnClickListener { v: View ->
                v.isEnabled = false
                if (isFollow) {
                    presenter?.deleteFriend(userId?:"", this)
                } else {
                    presenter?.addFriend(userId?:"", this)
                }
            }

            anchorInfoBinding.staticSlider.setOnClickListener { v: View? ->
                RouteUtils.routeToConversationActivity(
                    this,
                    Conversation.ConversationType.PRIVATE,
                    userId
                )
            }
        anchorInfoBinding.secondaryFirst.setOnClickListener { v: View? ->
            openBlockDialog(
                userId
            )
        }
        anchorInfoBinding.onlineSwipe.setOnClickListener {
            openBlockDialog(userId)
        }
        anchorInfoBinding.onlineReload.setOnClickListener { v: View? ->
            RouteUtils.routeToConversationActivity(
                this,
                Conversation.ConversationType.PRIVATE,
                userId
            )
        }
        anchorInfoBinding.confirmActive.setOnClickListener { view: View? ->
                if (Cache.instance.subscribeGood != null) {
                    toVipDialog()
                } else {
                    coinsShop
                }
            }


        anchorInfoBinding.errorMaximal.setOnClickListener { view: View? ->
            if (Cache.instance.subscribeGood != null) {
                toVipDialog()
            } else {
                coinsShop
            }
        }
        loadDate()
    }



    fun toVipDialog() {
        val vipDialog = VipDialogFragment(true)
        val currentActivity = currentActivity as AppCompatActivity?
        if (!isDestroy(currentActivity)) {
            currentActivity?.let {
                vipDialog.show(it.supportFragmentManager, "")
            }
        }
    }
    //加载数据
    private fun loadDate() {
        presenter?.getUserInfo(getUserId())
        presenter?.getUserExtraInfo(getUserId())
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFollowEvent(followEvent: FollowParamsEvent) {
        Log.d("xxx", "xxxxx")
        if (followEvent.userId == userId) {
            if (!followEvent.isError) {
                userInfo?.isFriend = followEvent.isFollow
                changeFollowState(followEvent.isFollow)
                Cache.instance.networkCacheManager.putObject("userInfo_$userId", userInfo)
            }
            anchorInfoBinding.frameSort.isEnabled = true
        }
    }
    val coinsShop: Unit
        get() {
            getCoinGoodsSearch(
                ActivityUtils.getTopActivity(),
                PlayParamsBean(true),
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>() {
                    override fun onSubscribe(d: Disposable) {
                        super.onSubscribe(d)
                    }

                    override fun onNext(listBaseBean: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>) {
                        super.onNext(listBaseBean)


                        if (listBaseBean.isOk) {
                            if (listBaseBean.data != null && !listBaseBean.data.isNullOrEmpty()) {
                                val coinGoods: MutableList<com.juicy.common.model.bean.ActivityInfoBean> = ArrayList()
                                for (i in listBaseBean.data!!.indices) {
                                    val bhBean = listBaseBean.data!![i]
                                    if (bhBean.type == "1") { //订阅
                                        Cache.instance.subscribeGood =
                                            bhBean
                                    } else {
                                        coinGoods.add(bhBean)
                                    }
                                }
                                Cache.instance.coinGoods = coinGoods
                                PayUtils(ActivityUtils.getTopActivity()).batchQueryPurchases()

                                toVipDialog()
                            }
                        }
                    }

                    override fun onError(e: Throwable) {
                    }

                    override fun onComplete() {
                        super.onComplete()
                    }
                })
        }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBlockEvent(event: BlockParamsEvent) {
        //
        if (event.userId == userId) {
            userInfo?.block = event.isBlock
            Cache.instance.networkCacheManager.putObject("userInfo_$userId", userInfo)
        }
        //监听状态
    }



    override fun changeFollowState(isFollow: Boolean) {
        this.isFollow = isFollow
        if (isFollow) {
            anchorInfoBinding.frameSort.setImageResource(R.drawable.ic_like_color_jade)
        } else {
            anchorInfoBinding.frameSort.setImageResource(R.drawable.ic_like_gray_mint)
        }
    }

    override fun updateInfo(userInfo: com.juicy.common.model.bean.JuicyUserInfoBean?) {
        //判断当前Activity是否正在销毁
        if (isFinishing) {
            return
        }
        if (userInfo == null) {
            return
        }
        //隐藏呼叫按钮
        anchorInfoBinding.textToggle.visibility = View.VISIBLE

        //        if (Cache.getInstance().reviewPkg) {
//            anchorInfoBinding.normalLeft.setVisibility(View.GONE);
//            anchorInfoBinding.counterChip.setVisibility(View.GONE);
//            anchorInfoBinding.switchBright.setVisibility(View.GONE);
//            anchorInfoBinding.extendedDownload.setVisibility(View.GONE);
//            anchorInfoBinding.abovePreview.setVisibility(View.GONE);
//            anchorInfoBinding.adaptivePanel.setVisibility(View.GONE);
//            anchorInfoBinding.errorMaximal.setVisibility(View.GONE);

//        }else{
        //隐藏VIP价格
        anchorInfoBinding.errorMaximal.visibility = View.VISIBLE
        //隐藏性别
        anchorInfoBinding.adaptivePanel.visibility = View.VISIBLE


        //        }
        this.userInfo = userInfo
        loadCircleImage(this, userInfo.avatarThumbUrl, anchorInfoBinding.acrossLocked)

        anchorInfoBinding.visibleMenu.text = userInfo.nickname
        anchorInfoBinding.maximalBtn.text = userInfo.country
        if (userInfo.about.isNullOrEmpty()) {
            anchorInfoBinding.selectedInfo.visibility = View.GONE
        } else {
            anchorInfoBinding.selectedInfo.visibility = View.VISIBLE
        }
        anchorInfoBinding.selectedInfo.text = userInfo.about
        anchorPrice = userInfo.unitPrice
        anchorInfoBinding.sliderOnline.text = "$anchorPrice"


        //        if(Cache.getInstance().reviewPkg){
//            anchorInfoBinding.viewFit.setText(LanguageManager.getInstance().getLocalTranslate("Video_Call"));
//            anchorInfoBinding.downSelected.setVisibility(View.GONE);
//        }else{
//            anchorInfoBinding.downSelected.setVisibility(View.VISIBLE);

//        val anchorStr = StringBuilder()
//        anchorStr.append(anchorPrice)
//            .append(LanguageManager.instance?.getLocalTranslate("minute"))
//        anchorInfoBinding.viewFit.text = anchorStr
//        anchorInfoBinding.tvCoinsBg.text = anchorStr

        //        }
        anchorInfoBinding.autoTimer.text = userInfo.age.toString()
       // anchorInfoBinding.brightThin.setColorFilter(Color.parseColor("#FFFFFFFF"))
        if (userInfo.gender != null) {
            if (userInfo.gender == 1) {
              //  val boy = AppCompatResources.getDrawable(this, R.drawable.ic_boyl_orange)
              //  anchorInfoBinding.brightThin.setImageDrawable(boy)
                anchorInfoBinding.xsMedium.setBackgroundResource(R.drawable.bg_male_blue)
            } else if (userInfo.gender == 2) {
              //  val girl = AppCompatResources.getDrawable(this, R.drawable.ic_girl_ash)
                //anchorInfoBinding.brightThin.setImageDrawable(girl)
                anchorInfoBinding.xsMedium.setBackgroundResource(R.drawable.bg_famel_olive)
            }
        }

        if (userInfo.switchNotDisturbIm) {
            anchorInfoBinding.onlineReload.isEnabled = false
            anchorInfoBinding.playShort.setImageResource(R.drawable.ic_chat_gray_platinum)
        } else {
            anchorInfoBinding.onlineReload.isEnabled = true
            anchorInfoBinding.playShort.setImageResource(R.drawable.ic_anchor_info_chat_icon_charcoal)
        }
        videoList.clear()
        photoList.clear()
        if (!userInfo.mediaList.isNullOrEmpty()) {
            for (mediaBean in userInfo.mediaList!!) {
                if (mediaBean.mediaType == "photo") {
                    photoList.add(mediaBean)
                } else if (mediaBean.mediaType == "video") {
                    videoList.add(mediaBean)
                }
            }
        }


    //    anchorInfoBinding.autoBody.text = userInfo.vipUnitPrice.toString() + LanguageManager.instance?.getLocalTranslate("minute")
        anchorInfoBinding.autoBody.text = userInfo.vipUnitPrice.toString()
        anchorInfoBinding.distantPicker.text = userInfo.vipUnitPrice.toString()
     //   anchorInfoBinding.distantPicker.text = userInfo.vipUnitPrice.toString() + LanguageManager.instance?.getLocalTranslate("minute")
        if (photoList.size <= 0) {
            val mediaInfoBean = com.juicy.common.model.bean.MediaInfoBean()
            mediaInfoBean.mediaUrl = userInfo.avatarUrl
            photoList.add(mediaInfoBean)
        }
        updateBanner()
        updateVideo()
    }

    private fun updateVideo() {
        if (videoList == null || videoList.isEmpty()) anchorInfoBinding.withinDefault.visibility =
            View.GONE
        if (anchorVideoAdapter == null) {
            anchorVideoAdapter = AnchorVideoAdapter(com.juicy.app.R.layout.item_list)
            val manager = LinearLayoutManager(this)
            manager.orientation = RecyclerView.HORIZONTAL
            anchorInfoBinding.activeBright.layoutManager = manager
            anchorInfoBinding.activeBright.adapter = anchorVideoAdapter
            anchorVideoAdapter?.addChildClickViewIds(com.juicy.app.R.id.backgroundHidden)
            anchorVideoAdapter?.setOnItemChildClickListener { adapter, view, position ->
                val intent = Intent(
                    this@ProfileActivity,
                    VideoFullActivity::class.java
                )
                intent.putExtra(
                    Constant.USER_CONTENT,
                    Gson().toJson(userInfo)
                )
                intent.putExtra(Constant.STATUS, status)
                intent.putExtra(Constant.IS_FOLLOW, isFollow)
                intent.putExtra(Constant.VIDEO_POSITION, position)
                startActivity(intent)
            }
        }
        anchorVideoAdapter?.setNewInstance(videoList)
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onUserStatus(event: OnlineStatusChangeEvent?) {
        //
        val status = UserOnlineStatusService.instance?.getStatus(userId?:"")
        if (status != null) {
            updateUserStatus(status)
        }
        //监听状态
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        anchorInfoBinding = ActivityInputBinding.inflate(layoutInflater)
        //        StatusBarUtil.setTranslucentStatus(this);
        setContentView(anchorInfoBinding.root)
        super.onCreate(savedInstanceState)
    }


    private fun updateBanner() {
        val itemAdapter = ItemAdapter(photoList, this)
        anchorInfoBinding.headerAbove.setAdapter(itemAdapter)

        val itemFullAdapter = ItemFullAdapter(photoList, this)

        anchorInfoBinding.openUpload.setAdapter(itemFullAdapter)
        itemFullAdapter.listener = object :touchBack {
            override fun close() {
                anchorInfoBinding.avatarActive.visibility = View.GONE
                anchorInfoBinding.onlineSwipe.visibility = View.GONE
                anchorInfoBinding.openUpload.visibility = View.GONE
                anchorInfoBinding.overlayProgress.visibility = View.GONE
                isShowBigBannanr = false
            }

        }

        anchorInfoBinding.openUpload.isAutoLoop(false)
        anchorInfoBinding.openUpload.setIndicatorGravity(IndicatorConfig.Direction.CENTER)
        val circleIndicator = CircleIndicator(this)
        anchorInfoBinding.openUpload.setIndicator(circleIndicator)
        if (LanguageManager.instance?.isLanguageForce == true) {
            anchorInfoBinding.headerAbove.layoutDirection = View.LAYOUT_DIRECTION_RTL
            anchorInfoBinding.openUpload.layoutDirection = View.LAYOUT_DIRECTION_RTL
            circleIndicator.layoutDirection = View.LAYOUT_DIRECTION_RTL
            circleIndicator.scaleX = -1f
        }
        anchorInfoBinding.openUpload.setIndicatorWidth(dip2px(5f), dip2px(5f))
        anchorInfoBinding.openUpload.setIndicatorSelectedColor(getColor(com.juicy.app.R.color.white))
        anchorInfoBinding.headerAbove.setLoopTime(5000)
        anchorInfoBinding.avatarActive.setOnClickListener { v: View? ->
            anchorInfoBinding.avatarActive.visibility = View.GONE
            anchorInfoBinding.openUpload.visibility = View.GONE
            anchorInfoBinding.overlayProgress.visibility = View.GONE
            anchorInfoBinding.onlineSwipe.visibility = View.GONE
            isShowBigBannanr = false
        }
        itemAdapter.setOnBannerListener { data: Any?, position: Int ->
            anchorInfoBinding.openUpload.visibility =View.VISIBLE
            anchorInfoBinding.avatarActive.visibility = View.VISIBLE
            anchorInfoBinding.onlineSwipe.visibility = View.VISIBLE
            anchorInfoBinding.overlayProgress.visibility = View.VISIBLE
            anchorInfoBinding.openUpload.setCurrentItem(position + 1, false)
            isShowBigBannanr = true
        }
        itemFullAdapter.setOnBannerListener { data: Any?, position: Int -> }
    }

    override fun openBlockDialog(userId: String?) {
        if (checkNetworkToast()) return
        if (userInfo == null || userInfo?.userId.isNullOrEmpty()) return
        val dialogFragment = BlockDialogFragment(object : BlockCallBack {
            override fun deleteBlock() {
            }

            override fun addBlock() {
            }

            override fun addFollow() {
                changeFollowState(true)
            }

            override fun deleteFollow() {
                changeFollowState(false)
            }
        }, isFollow, userId, userInfo?.nickname, userInfo?.avatarUrl)
        dialogFragment.show(supportFragmentManager, "")
    }


    override fun closeLoading(isClose: Boolean) {
        if (isClose && loadDialog != null) {
            loadDialog?.dismiss()
        } else {
            loadDialog = LoadDialog(this)
            loadDialog?.show()
        }
    }

    override fun updateUserStatus(status: String) {
        this.status = status
        val isEnableCall = UserOnlineStatusService.instance?.getStatusEnableCall(status)?:false
        val color = UserOnlineStatusService.instance?.getStatusColor(status)?:0
        anchorInfoBinding.copyLeft.background = create()
            .corner(dip2px(4f).toFloat())
            .bgColor(color)
            .build()
        if (isEnableCall) {
            anchorInfoBinding.briefWithin.setBackgroundResource(com.juicy.app.R.mipmap.ic_anchor_info_call_online_ash)
            anchorInfoBinding.disabledOverlay.setImageResource(com.juicy.app.R.mipmap.ic_anchor_info_online_icon_lemon)
            anchorInfoBinding.badgeEnd.setImageResource(com.juicy.app.R.mipmap.ic_anchor_info_online_icon_lemon)
            anchorInfoBinding.textToggle.setBackgroundResource(com.juicy.app.R.mipmap.ic_anchor_info_call_online_ash)
            anchorInfoBinding.textToggle.setOnClickListener(View.OnClickListener {
                if (checkNetworkToast()) return@OnClickListener
                presenter?.videoCall(this,userId, status?:"")
            })
            anchorInfoBinding.calendarFrame.setOnClickListener(View.OnClickListener {
                if (checkNetworkToast()) return@OnClickListener
                presenter?.videoCall(this,userId, status)
            })
//            anchorInfoBinding.textToggle.background =
//                AppCompatResources.getDrawable(
//                    this,
//                    com.juicy.app.R.drawable.bg_edit_slate
//                )
//            anchorInfoBinding.calendarFrame.background =
//                AppCompatResources.getDrawable(
//                    this,
//                    com.juicy.app.R.drawable.bg_edit_slate
//                )
        } else {
            anchorInfoBinding.disabledOverlay.setImageResource(com.juicy.app.R.mipmap.ic_anchor_info_offline_icon_platinum)
            anchorInfoBinding.briefWithin.setBackgroundResource(com.juicy.app.R.mipmap.ic_anchor_info_call_offline_snow)
            anchorInfoBinding.badgeEnd.setImageResource(com.juicy.app.R.mipmap.ic_anchor_info_offline_icon_platinum)
            anchorInfoBinding.textToggle.setBackgroundResource(com.juicy.app.R.mipmap.ic_anchor_info_call_offline_snow)
            anchorInfoBinding.textToggle.setOnClickListener {
                presenter?.call(
                    userId,
                    status
                )
            }
            anchorInfoBinding.calendarFrame.setOnClickListener {
                presenter?.call(
                    userId,
                    status
                )
            }
//            anchorInfoBinding.textToggle.background =
//                AppCompatResources.getDrawable(
//                    this,
//                    com.juicy.app.R.drawable.bg_edit_gray_black
//                )
//            anchorInfoBinding.calendarFrame.background =
//                AppCompatResources.getDrawable(
//                    this,
//                    com.juicy.app.R.drawable.bg_edit_gray_black
//                )
        }
        anchorInfoBinding.xlFilter.setTextColor(color)
        anchorInfoBinding.xlFilter.text = status
        if (LanguageManager.instance?.isLanguageForce ==  true) {
            anchorInfoBinding.avatarActive.scaleX = -1f
        }
    }

    override fun updateUserGift(giftList: List<String>?) {
        val newGiftList: MutableList<String> = ArrayList()
        if (giftList != null) {
            for (gift in giftList) {
                if (!gift.startsWith("act_gift_love_")) {
                    newGiftList.add(gift)
                }
            }
        }


        if (newGiftList == null || newGiftList.isEmpty()) {
            anchorInfoBinding.counterChip.visibility = View.GONE
            return
        }
        if (giftAdapter == null) {
            giftAdapter = AnchorGiftAdapter(com.juicy.app.R.layout.item_spinner)
            val manager = GridLayoutManager(this, 4)
            anchorInfoBinding.normalLeft.addItemDecoration(
                GridSpacingItemDecoration(
                    4,
                    dip2px(10f),
                    false
                )
            )
            anchorInfoBinding.normalLeft.layoutManager = manager
            anchorInfoBinding.normalLeft.adapter = giftAdapter
        }
        giftAdapter?.setNewInstance(newGiftList)
    }

    override fun updateUserIm(imList: List<String?>?) {
        if (imList != null) {
            if (imList.isEmpty()) anchorInfoBinding.extendedDownload.visibility =
                View.GONE
            anchorInfoBinding.switchBright.setFillMode(AutoWrapLineLayout.MODE_WRAP_CONTENT)
            for (i in imList.indices) {
                val autoWrapItem = AutoWrapItem(this, imList[i]?:"")
                anchorInfoBinding.switchBright.addView(autoWrapItem)
            }
        } else {
            anchorInfoBinding.extendedDownload.visibility = View.GONE
        }
    }

    override fun onResume() {
        super.onResume()
        //        presenter.getIsFriend(userId);
        val status = UserOnlineStatusService.instance?.getStatus(userId?:"")
        if (status != null) {
            updateUserStatus(status)
        }
        UserOnlineStatusService.instance?.addUserIdAndForceRefresh(getUserId())
    }

    override fun transLocalText() {
        val scaleX = if (LanguageManager.instance?.isLanguageForce == true) -1f else 1f
        anchorInfoBinding.emptyBadge.scaleX = scaleX
        anchorInfoBinding.dragMicro.text =
            LanguageManager.instance?.getLocalTranslate("Video")
        anchorInfoBinding.mainDisabled.text =
            LanguageManager.instance?.getLocalTranslate("Impression")
        anchorInfoBinding.outerInfo.text =
            LanguageManager.instance?.getLocalTranslate("Gift")
        if (LanguageManager.instance?.isLanguageForce == true) {
            anchorInfoBinding.autoBody.layoutDirection = View.LAYOUT_DIRECTION_RTL
        }
    }
}