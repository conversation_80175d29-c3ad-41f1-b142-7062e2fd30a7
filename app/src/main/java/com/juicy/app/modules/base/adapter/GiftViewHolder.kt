package com.juicy.app.modules.base.adapter

import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R

class GiftViewHolder(view: View) : BaseViewHolder(view) {
    var giftIcon: AppCompatImageView = view.findViewById(R.id.dimmedToggle)
    var giftPrice: AppCompatTextView = view.findViewById(R.id.rightCoordinator)
}
