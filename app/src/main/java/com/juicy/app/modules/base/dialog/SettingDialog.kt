package com.juicy.app.modules.base.dialog

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.blankj.utilcode.util.SpanUtils
import com.juicy.app.R
import com.juicy.app.databinding.DialogBoxBinding
import com.juicy.common.utils.LanguageManager

class SettingDialog(
    context: Context?,
    private val settingText: String,
    private val confirmBack: ConfirmBack
) :
    DialogFragment() {
    private var settingBinding: DialogBoxBinding? = null
    private var coins: String? = null
    private var day: String? = null

    override fun onStart() {
        super.onStart()
        initSetting()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.app_dialogtheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // 设置点击外部不关闭
        dialog!!.setCanceledOnTouchOutside(false)

        // 设置返回键不关闭
//        getDialog().setCancelable(false);
        settingBinding = DialogBoxBinding.inflate(inflater)
        return settingBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        setData()
        initEvent()
        if (coins != null && day != null) {
            settingBinding!!.selectedCollapsed.visibility = View.VISIBLE
            SpanUtils.with(settingBinding!!.selectedCollapsed)
                .append(coins!!)
                .append(" ")
                .append(getString(R.string.common_ok_16))
                .append(" ")
                .append("/")
                .append(" ")
                .append(day!!)
                .append(" ")
                .append(getString(R.string.time_now))
                .create()
        }
        settingBinding!!.editLower.text = LanguageManager.instance!!.getLocalTranslate("Cancel")
        settingBinding!!.holderMain.text =
            LanguageManager.instance!!.getLocalTranslate("Confirm")
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            settingBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            settingBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        settingBinding!!.holderMain.text =
            LanguageManager.instance!!.getLocalTranslate("Confirm")
        settingBinding!!.editLower.text = LanguageManager.instance!!.getLocalTranslate("Cancel")
    }

    private fun initEvent() {
        settingBinding!!.editLower.setOnClickListener {
            confirmBack.onCancelBack()
            dismiss()
        }
        settingBinding!!.throughPlay.setOnClickListener {
            confirmBack.onCancelBack()
            dismiss()
        }
        settingBinding!!.holderMain.setOnClickListener {
            confirmBack.onConfirmBack()
            dismiss()
        }
    }

    private fun setData() {
        settingBinding!!.stopDistant.text = settingText
    }

    private fun initSetting() {
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.CENTER)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    fun setCameraText(coins: String?, day: String?) {
        this.coins = coins
        this.day = day
    }

    interface ConfirmBack {
        fun onConfirmBack()
        fun onCancelBack() {}
    }
}
