package com.juicy.app.modules.base.dialog

import android.content.Intent
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.CountDownTimer
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.chad.library.adapter.base.listener.OnItemChildClickListener
import com.juicy.app.R
import com.juicy.app.modules.base.adapter.GoodAdapter
import com.juicy.app.databinding.LayoutMeBinding
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.model.event.PromotionPayStatusChangeEvent
import com.juicy.common.model.event.PayResultEvent
import com.juicy.common.model.event.NewUserRewardsShowStatusChangeEvent
import com.juicy.common.model.message_event.AppRechargeOrderStatusBean
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.networks.delegate.GetCoinCountInterface.getCoin
import com.juicy.common.networks.delegate.GetCoinGoodsSearchInterface.getCoinGoodsSearch
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.DateMetaUtil.ms2HMS
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.AppDeviceUtils.getScreenSize
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PayUtils.PayUtils
import com.juicy.common.utils.PayUtils.PayUtils.FailOnListen
import com.juicy.common.utils.PayUtils.PayUtils.SuccessOnListen
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.abs

class MeShoppingDialog : DialogFragment {
    private var meShoppingBinding: LayoutMeBinding? = null
    private val coinList: MutableList<com.juicy.common.model.bean.ActivityInfoBean?> = ArrayList()
    private var socketEventBusBack: SocketEventBusBack? = null
    private var countDownTimer: CountDownTimer? = null

    var isPerseronShow: Boolean = false
    private var selectPosition = 0

    private var isShowRobot = true
    @JvmField
    var entry: String = ""

    constructor()

    constructor(entry: String) {
        // Required empty public constructor
        this.entry = entry
    }

    constructor(isShowRobot: Boolean) {
        this.isShowRobot = isShowRobot
    }

    constructor(isShowRobot: Boolean, isPerseronShow: Boolean) {
        //
        this.isShowRobot = isShowRobot
        this.isPerseronShow = isPerseronShow
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)

        // 设置对话框的样式
//        if (isPerseronShow) {
//            setStyle(DialogFragment.STYLE_NO_TITLE, R.style.dialog_fullscreendialog);
//        }
    }

    override fun onResume() {
        super.onResume()
        if (isAdded) {
            initMyCoins()
            resetGoodData()
            if (Cache.instance.userInfo != null) {
                meShoppingBinding!!.wideClose.text =
                    Cache.instance.userInfo!!.availableCoins.toString()
            }
        }
    }

    fun setDirection() {
        meShoppingBinding!!.root.post {
            if (LanguageManager.instance!!.isLanguageForce) {
                meShoppingBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
                meShoppingBinding!!.xxlUpper.layoutDirection =
                    View.LAYOUT_DIRECTION_RTL
                //            fullShoppingBinding.vipShopItem.setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
                //            fullShoppingBinding.vipShopItemBg.setScaleX(-1f);
                meShoppingBinding!!.emptyBadge.scaleX = -1f
                meShoppingBinding!!.emptyBadge.setOnClickListener { v: View? -> dismiss() }
            } else {
                meShoppingBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
            }
            val scaleX = if (LanguageManager.instance!!.isLanguageForce) -1f else 1f
            meShoppingBinding!!.maximalCounter.text =
                LanguageManager.instance!!.getLocalTranslate("My_Coins")
            meShoppingBinding!!.elasticPrimary.text =
                LanguageManager.instance!!.getLocalTranslate("Recharge_or_become_VIP_to_chat_unlimited_times")
            meShoppingBinding!!.lockedFill.text =
                LanguageManager.instance!!.getLocalTranslate("Customer_Service")
            meShoppingBinding!!.insideExtended.text =
                LanguageManager.instance!!.getLocalTranslate("Coin_Store")
        }
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPayEvent(userDialogEvent: PayResultEvent) {
        if (userDialogEvent.isSuccess) {
            if (!isPerseronShow) {
                dismissAllowingStateLoss()
            } else {
                initMyCoins()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(`object`: PromotionPayStatusChangeEvent?) {
        if (!getBooleanVal(SpKeyPool.HAS_EVALUATE, false)!!) {
            putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
            if (!isDestroy(activity)) {
                val assessDialog = AssessDialog()
                activity?.let {
                    assessDialog.show(it.supportFragmentManager, "")
                }

            }
        }
        if (Cache.instance.webViewActivity != null) {
            Cache.instance.webViewActivity!!.refreshAgentWeb()
        }

        if (Cache.instance.userPromotionGood != null && selectPosition == 0) {
            EventBus.getDefault().post(NewUserRewardsShowStatusChangeEvent())
        }
        EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        meShoppingBinding = LayoutMeBinding.inflate(inflater)
        return meShoppingBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


        setDirection()


        meShoppingBinding!!.root.post {
            if (isPerseronShow) {
                val mainPageLayoutParams =
                    meShoppingBinding!!.xlScroll.layoutParams as RelativeLayout.LayoutParams
                mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
                meShoppingBinding!!.xlScroll.layoutParams = mainPageLayoutParams

                meShoppingBinding!!.menuOpen.visibility = View.VISIBLE
                meShoppingBinding!!.menuOpen.setOnClickListener { v: View? ->
                    dismiss()
                }
                meShoppingBinding!!.gridPreview.visibility = View.GONE
//
//                val drawableResId = R.drawable.ic_minimum_full_coin_bg_teal
//                fullShoppingBinding!!.wrapperCustom.setBackgroundResource(drawableResId)
                val params =
                    meShoppingBinding!!.wrapperCustom.layoutParams as RelativeLayout.LayoutParams
                params.height = dip2px(234f)
                meShoppingBinding!!.wrapperCustom.layoutParams = params

                //            android:layout_below="@id/head"
                //            android:layout_marginHorizontal="20dp"
                //            android:layout_marginTop="-100dp"
                //            android:layout_marginBottom="60dp"
                //            android:layout_marginStart="20dp"
                //            android:layout_marginTop="15dp"
            } else {
//                val drawableResId = R.drawable.ic_shop_img_top_bg_sage
//                fullShoppingBinding!!.wrapperCustom.setBackgroundResource(drawableResId)
            }
            meShoppingBinding!!.gridExpand.setOnClickListener { v: View? ->
                startActivity(
                    Intent(
                        requireContext(),
                        com.juicy.app.modules.base.activity.BotActivity::class.java
                    )
                )
            }

            if (isShowRobot) {
                meShoppingBinding!!.gridExpand.visibility = View.VISIBLE
            } else {
                meShoppingBinding!!.gridExpand.visibility = View.GONE
            }
            if (Cache.instance.reviewPkg) {
                meShoppingBinding!!.gridExpand.visibility = View.GONE
            }

            meShoppingBinding!!.xxlUpper.addItemDecoration(object :
                RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    super.getItemOffsets(outRect, view, parent, state)
                    outRect[dip2px(8f), dip2px(8f), dip2px(
                        8f
                    )] =
                        dip2px(8f)
                }
            })
            initData()
        }
    }

    private fun initData() {
        Cache.instance.payChannle

        if (Cache.instance.userInfo != null) {
            meShoppingBinding!!.wideClose.text =
                Cache.instance.userInfo!!.availableCoins.toString()
            //            if (Cache.getInstance().userInfo.getVip()) {
//                fullShoppingBinding.vipShopItem.setVisibility(View.GONE);
//            }
            if (Cache.instance.userInfo!!.vip || Cache.instance.userStratResult == null || Cache.instance.userStratResult!!.isReviewPkg == null ||
                Cache.instance.userStratResult!!.isReviewPkg!! ||
                Cache.instance.userInfo!!.recharge || entry != "im_limit"
            ) {
                meShoppingBinding!!.elasticPrimary.visibility = View.GONE
            } else {
                meShoppingBinding!!.elasticPrimary.visibility = View.VISIBLE
            }
        }


        //        initMyCoins();
        val layoutParams = meShoppingBinding!!.headerAbove.layoutParams as LinearLayout.LayoutParams
        layoutParams.height =
            ((getScreenSize(this.context)[0] - dip2px(38f) * 2) * 100.0 / 328.0).toInt()
        meShoppingBinding!!.headerAbove.layoutParams = layoutParams
        meShoppingBinding!!.headerAbove.loadBanner()
        if (Cache.instance.coinGoods != null && !Cache.instance.coinGoods.isNullOrEmpty()) {
            initGoodsRv(Cache.instance.coinGoods!!)
        }


        //        resetGoodData();
//        if (Cache.getInstance().subscribeGood != null) {
//            fullShoppingBinding.barAcross.setText( Cache.getInstance().subscribeGood.getLocalPrice() + "/" + Cache.getInstance().subscribeGood.getValidityUnit());
//            fullShoppingBinding.narrowBeyond.setText("VIP" + " + " + Cache.getInstance().subscribeGood.getExchangeCoin());
//        }
    }

    private fun resetGoodData() {
        getCoinGoodsSearch(
            context,
            PlayParamsBean(true),
            object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>() {
                override fun onNext(goodsData: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>) {
                    super.onNext(goodsData)
                    if (Cache.instance.userInfo != null) {
                        if (goodsData.data != null && !goodsData.data!!.isEmpty()) {
                            val coinGoods: MutableList<com.juicy.common.model.bean.ActivityInfoBean> = ArrayList()
                            for (i in goodsData.data!!.indices) {
                                val bhBean = goodsData.data!![i]
                                if (bhBean.type == "1") { //订阅
                                    Cache.instance.subscribeGood = bhBean
                                } else {
                                    coinGoods.add(bhBean)
                                }
                            }
                            Cache.instance.coinGoods = coinGoods
                            PayUtils(ActivityUtils.getTopActivity()).batchQueryPurchases()


                            initGoodsRv(coinGoods)
                        }
                    }
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                }
            })
    }

    private fun initMyCoins() {
        getCoin(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>() {
            override fun onNext(coinData: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>) {
                super.onNext(coinData)
                if (Cache.instance.userInfo != null) {
                    if (coinData.data != null && coinData.data!!.availableCoins >= 0 &&
                        isDestroy(activity) && meShoppingBinding != null
                    ) {
                        meShoppingBinding!!.wideClose.text =
                            coinData.data!!.availableCoins.toInt().toString()
                    }
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                if (Cache.instance.userInfo != null) {
                    meShoppingBinding!!.wideClose.text =
                        Cache.instance.userInfo!!.availableCoins.toString()
                }
            }
        })
    }

    private fun initGoodsRv(coinGoods: List<com.juicy.common.model.bean.ActivityInfoBean?>) {
        if (isDestroy(activity) || meShoppingBinding == null) return
        val gridLayoutManager = GridLayoutManager(
            activity, 3
        )

        //        fullShoppingBinding.xxlUpper.addItemDecoration(new GridSpacingItemDecoration(2, 15, true));
        meShoppingBinding!!.xxlUpper.layoutManager = gridLayoutManager
        val goodAdapter = GoodAdapter(R.layout.item_good)
        meShoppingBinding!!.xxlUpper.adapter = goodAdapter
        setData(goodAdapter, coinGoods)
    }


    private fun setData(goodAdapter: GoodAdapter, coinGoods: List<com.juicy.common.model.bean.ActivityInfoBean?>) {
        socketEventBusBack = object : SocketEventBusBack {
            override fun rechargeSuccessBack(appRechargeOrderStatusBean: AppRechargeOrderStatusBean?) {
                if (appRechargeOrderStatusBean!!.status == 2) {
                    if (selectPosition == 0 && Cache.instance.userPromotionGood != null) {
                        EventBus.getDefault().post(NewUserRewardsShowStatusChangeEvent())
                    }
                    if (!isPerseronShow) {
                        dismissAllowingStateLoss()
                    }


                    EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
                }
            }
        }
        coinList.clear()
        coinList.addAll(coinGoods)
        for (i in coinList.indices) {
            if (coinList[i]?.promotion == true) {
                coinList.removeAt(i)
                break
            }
        }
        setProGood(goodAdapter)

        if (Cache.instance.userInfo != null && !Cache.instance.userInfo!!.vip) {
            if (Cache.instance.subscribeGood != null) {
                for (i in coinList.indices) {
                    if (coinList[i]!!.exchangeCoin == Cache.instance.subscribeGood!!.exchangeCoin) {
                        Cache.instance.subscribeGood!!.isVipItem = true
                        coinList.add(i + 1, Cache.instance.subscribeGood)
                        break
                    }
                }
            }
        }

        goodAdapter.setNewInstance(coinList)
        goodAdapter.addChildClickViewIds(R.id.innerGrid)
        goodAdapter.setOnItemChildClickListener(OnItemChildClickListener { adapter, view, position ->
            if (isFastClick) return@OnItemChildClickListener
            selectPosition = position
            if (coinList[position]!!.isVipItem) {
                val vipDialog = VipDialogFragment(false)
                val currentActivity =
                    currentActivity as AppCompatActivity?
                vipDialog.show(currentActivity!!.supportFragmentManager, "")
            } else {
                PayUtils(activity, entry, false, coinList[position], object : SuccessOnListen {
                    override fun onListen() {
                        if (!getBooleanVal(SpKeyPool.HAS_EVALUATE, false)!!) {
                            putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
                            if (!isDestroy(activity)) {
                                val assessDialog = AssessDialog()
                                assessDialog.show(activity!!.supportFragmentManager, "")
                            }
                        }
                        if (Cache.instance.webViewActivity != null) {
                            Cache.instance.webViewActivity!!.refreshAgentWeb()
                        }

                        if (Cache.instance.userPromotionGood != null && position == 0) {
                            EventBus.getDefault().post(NewUserRewardsShowStatusChangeEvent())
                        }
                        EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
                    }
                }, object : FailOnListen {
                    override fun onListen() {
                    }
                }).openPayDialog()
            }
        })
        addMessageEvent(socketEventBusBack!!)
    }

    private fun setProGood(goodAdapter: GoodAdapter) {
        if (Cache.instance.userPromotionGood != null) {
            coinList.add(0, Cache.instance.userPromotionGood)
            setProTime(Cache.instance.userPromotionGood, goodAdapter)
        }
    }

    private fun setProTime(userPromotionGood: com.juicy.common.model.bean.ActivityInfoBean?, goodAdapter: GoodAdapter) {
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
        if (Cache.instance.userPromotionGood == null) {
            return
        }
        val surplusMillisecond =
            Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
        countDownTimer = object : CountDownTimer(surplusMillisecond, 1000) {
            override fun onTick(l: Long) {
                if (Cache.instance.userPromotionGood != null && Cache.instance.userInfo!!.availableCoins < 150) {
                    val surplusMillisecond =
                        Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
                    val time = ms2HMS(surplusMillisecond)
                    Cache.instance.userPromotionGood!!.tags = time
                    val bean = Cache.instance.userPromotionGood
                    bean?.let {
                        goodAdapter.setNewSaleTag(it)
                        goodAdapter.notifyDataSetChanged()
                    }

                    if (abs((l - surplusMillisecond).toDouble()) > 1000) {
                        setProTime(userPromotionGood, goodAdapter)
                    }
                }
            }

            override fun onFinish() {
                Cache.instance.userPromotionGood = null
                if (!isPerseronShow) {
                    dismiss()
                }
            }
        }.start()
    }

    private fun cancelTimer() {
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
    }

    private val mInitialTouchY = 0f
    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            val window = dialog!!.window
            if (window != null && isPerseronShow) {
                // 设置全屏
                window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
                window.decorView.setPadding(0, 0, 0, 0)


                // 设置状态栏透明
                window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                window.statusBarColor = Color.TRANSPARENT


                // 设置布局延伸到状态栏并设置状态栏图标为白色
//                View decorView = window.getDecorView();
//                int option = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
//                // 清除 LIGHT_STATUS_BAR 标志，使状态栏图标变为白色
//                option &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
//                decorView.setSystemUiVisibility(option);

                // 设置窗口属性
                val params = window.attributes
                params.width = WindowManager.LayoutParams.MATCH_PARENT
                params.height = WindowManager.LayoutParams.MATCH_PARENT
                params.gravity = Gravity.CENTER
                params.flags = params.flags or WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                params.flags = params.flags or WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                window.attributes = params
            } else {
                // 原有的非全屏逻辑
                window!!.setBackgroundDrawable(null)
                window.setGravity(Gravity.BOTTOM)
                val params = window.attributes
                params.width = WindowManager.LayoutParams.MATCH_PARENT
                params.height = getScreenSize(this.context)[1] - ((getScreenSize(
                    this.context
                )[1]) * 0.2).toInt()
                window.attributes = params
            }
        }
    }

    override fun dismiss() {
        cancelTimer()
        removeMessageEvent(socketEventBusBack!!)
        Cache.instance.fullShoppingDialog = null
        super.dismiss()
    }
}
