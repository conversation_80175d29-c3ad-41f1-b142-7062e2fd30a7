package com.juicy.app.modules.base.dialog

import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.juicy.app.R
import com.juicy.app.databinding.DialogContentBinding
import com.juicy.common.utils.LanguageManager

class DeleteConfirmDialog(
    context: Context?,
    private val settingText: String,
    private val confirmBack: ConfirmBack
) :
    DialogFragment() {
    private var deleteConfirmBinding: DialogContentBinding? = null
    private var coins: String? = null
    private var day: String? = null

    override fun onStart() {
        super.onStart()
        initSetting()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_TITLE, R.style.app_dialogtheme)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // 设置点击外部不关闭
        dialog!!.setCanceledOnTouchOutside(false)

        // 设置返回键不关闭
//        getDialog().setCancelable(false);
        deleteConfirmBinding = DialogContentBinding.inflate(inflater)
        return deleteConfirmBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        setData()
        initEvent()
        deleteConfirmBinding!!.editLower.text = LanguageManager.instance?.getLocalTranslate("No")?:"No"
        deleteConfirmBinding!!.holderMain.text = LanguageManager.instance?.getLocalTranslate("Yes")?:"Yes"
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            deleteConfirmBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            deleteConfirmBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        deleteConfirmBinding!!.holderMain.text = LanguageManager.instance?.getLocalTranslate("Yes")?:"Yes"
        deleteConfirmBinding!!.editLower.text = LanguageManager.instance?.getLocalTranslate("No")?:"No"
    }

    private fun initEvent() {
        deleteConfirmBinding!!.editLower.setOnClickListener {
            confirmBack.onCancelBack()
            dismiss()
        }
        deleteConfirmBinding!!.throughPlay.setOnClickListener {
            confirmBack.onCancelBack()
            dismiss()
        }
        deleteConfirmBinding!!.holderMain.setOnClickListener {
            confirmBack.onConfirmBack()
            dismiss()
        }
    }

    private fun setData() {
        deleteConfirmBinding!!.stopDistant.text = settingText
    }

    private fun initSetting() {
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.CENTER)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    fun setCameraText(coins: String?, day: String?) {
        this.coins = coins
        this.day = day
    }

    interface ConfirmBack {
        fun onConfirmBack()
        fun onCancelBack() {}
    }
}
