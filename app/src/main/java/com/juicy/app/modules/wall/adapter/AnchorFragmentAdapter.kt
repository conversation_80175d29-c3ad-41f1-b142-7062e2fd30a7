package com.juicy.app.modules.wall.adapter

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.juicy.app.modules.wall.WallFragment

class AnchorFragmentAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {
    var wallFragments: List<WallFragment> = ArrayList()
    override fun createFragment(position: Int): Fragment {
        return wallFragments[position]
    }

    override fun getItemCount(): Int {
        return wallFragments.size
    }
}
