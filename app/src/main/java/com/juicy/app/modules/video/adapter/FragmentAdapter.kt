package com.juicy.app.modules.video.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.juicy.app.modules.video.VideoFragment

class FragmentAdapter(
    fragmentManager: FragmentManager,
    lifecycle: Lifecycle,
    private val videoFragments: List<VideoFragment>
) :
    FragmentStateAdapter(fragmentManager, lifecycle) {
    override fun getItemCount(): Int {
        return videoFragments.size
    }
    override fun createFragment(position: Int): Fragment {
        return videoFragments[position]
    }


}
