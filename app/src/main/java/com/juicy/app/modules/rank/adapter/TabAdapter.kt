package com.juicy.app.modules.rank.adapter

import android.view.View
import com.blankj.utilcode.util.SpanUtils
import com.chad.library.adapter.base.BaseQuickAdapter

class TabAdapter(layoutResId: Int, data: List<String>) :
    BaseQuickAdapter<String?, TabVH>(layoutResId, data.toMutableList()) {
    var selectPosition: Int = 0
    override fun convert(holder: TabVH, item: String?) {
        if (item == null) return
        holder.tabName.text = item
        val index = getItemPosition(item);
        if (index == selectPosition) {
            holder.selectView.visibility = View.VISIBLE
            SpanUtils.with(holder.tabName).append(item).setFontSize(22, true).create()
            holder.star.visibility = View.VISIBLE

        } else {
            holder.star.visibility = View.INVISIBLE
            SpanUtils.with(holder.tabName).append(item).setFontSize(22, true).create()
            holder.selectView.visibility = View.GONE

        }
    }
}
