package com.juicy.app.modules.info

import android.app.Activity
import android.content.Context

interface IAnchorInfoPresenter {

    fun addFriend(userId: String, context: Context?)
    fun getUserInfo(userId: String)
    fun getUserExtraInfo(userId: String)
    fun deleteFriend(userId: String, context: Context?)
    fun videoCall(activity: Activity,userId: String?, status: String?)
    //    void getIsFriend(String anchorId );
    fun getUserOnlineStatus(userId: String?)

}
