package com.juicy.app.modules.info.adapter

import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R

class AnchorGiftViewHolder(view: View) : BaseViewHolder(view) {
    var icon: AppCompatImageView =
        view.findViewById(R.id.dimmedToggle)
    var num: AppCompatTextView =
        view.findViewById(R.id.disconnectedStatic)
}