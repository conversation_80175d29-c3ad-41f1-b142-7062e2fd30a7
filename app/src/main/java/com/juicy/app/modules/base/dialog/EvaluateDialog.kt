package com.juicy.app.modules.base.dialog

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.juicy.app.R
import com.juicy.app.databinding.DialogChipBinding
import com.juicy.common.model.bean.AnchorWallTagBean
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.LanguageManager

class EvaluateDialog : DialogFragment {
    private var anchorInfo: com.juicy.common.model.bean.AnchorWallTagBean? = null
    private var dialogBinding: DialogChipBinding? = null
    private var anchorName: String? = null
    private var anchorPhoto: String? = null

    constructor()

    constructor(anchorInfo: com.juicy.common.model.bean.AnchorWallTagBean?, anchorName: String?, anchorPhoto: String?) {
        this.anchorInfo = anchorInfo
        this.anchorName = anchorName
        this.anchorPhoto = anchorPhoto
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dialogBinding = DialogChipBinding.inflate(inflater)
        return dialogBinding!!.root
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.CENTER)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initData()
        initClick()
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            dialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            dialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        dialogBinding!!.hugeNear.text = LanguageManager.instance!!.getLocalTranslate("Praise")
        dialogBinding!!.gridFirst.text = LanguageManager.instance!!.getLocalTranslate("Negative")
    }

    private fun initClick() {
        dialogBinding!!.emptyBelow.setOnClickListener { dismiss() }
        dialogBinding!!.highlightedCard.setOnClickListener(View.OnClickListener {
            if (anchorInfo == null || anchorPhoto == null || anchorName == null) return@OnClickListener
            activity?.let {
                val lastEvaluateDialog = LastEvaluateDialog(anchorInfo, anchorPhoto, anchorName, false)
                lastEvaluateDialog.show(it.supportFragmentManager, "")
            }
            dismiss()
        })
        dialogBinding!!.shadowLower.setOnClickListener(View.OnClickListener {
            if (anchorInfo == null || anchorPhoto == null || anchorName == null) return@OnClickListener
            activity?.let {
                val lastEvaluateDialog = LastEvaluateDialog(anchorInfo, anchorPhoto, anchorName, true)
                lastEvaluateDialog.show(it.supportFragmentManager, "")
            }
            dismiss()
        })
    }

    private fun initData() {
        if (anchorInfo == null) return
        dialogBinding!!.clockLast.text =
            LanguageManager.instance!!.getLocalTranslate("Call_duration") + formatSeconds(
                anchorInfo!!.duration.toLong()
            )

        loadCircleImage(context, anchorPhoto, dialogBinding!!.fluidForeground)
        dialogBinding!!.fluidForeground.borderWidth = dip2px(2f)
        dialogBinding!!.fluidForeground.borderColor = context!!.getColor(R.color.white)
        if (anchorName != null) {
            dialogBinding!!.avatarLarge.text = anchorName
        }
    }

    companion object {
        fun formatSeconds(seconds: Long): String {
            var timeStr = seconds.toString() + "s"

            if (seconds > 60) {
                val second = seconds % 60

                var min = seconds / 60

                timeStr = min.toString() + "m" + second + "s"

                if (min > 60) {
                    min = (seconds / 60) % 60

                    val hour = (seconds / 60) / 60

                    timeStr = hour.toString() + "h" + min + "m" + second + "s"
                }
            }

            return timeStr
        }
    }
}
