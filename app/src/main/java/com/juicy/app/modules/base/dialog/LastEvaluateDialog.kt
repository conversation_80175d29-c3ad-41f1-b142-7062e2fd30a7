package com.juicy.app.modules.base.dialog

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.R
import com.juicy.app.databinding.DialogBodyBinding
import com.juicy.common.networks.Service
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.view.AutoWrapLineLayout
import com.juicy.common.utils.view.LabelTextView
import io.reactivex.rxjava3.schedulers.Schedulers

class LastEvaluateDialog : DialogFragment {
    private var lastDialogBinding: DialogBodyBinding? = null
    private var anchorImg: String? = null
    private var anchorName: String? = null
    private var anchorInfo: com.juicy.common.model.bean.AnchorWallTagBean? = null
    private var isGood = false
    private val labelList: MutableList<String> = ArrayList()


    constructor()

    constructor(anchorInfo: com.juicy.common.model.bean.AnchorWallTagBean?, anchorImg: String?, anchorName: String?, isGood: Boolean) {
        this.anchorInfo = anchorInfo
        this.anchorImg = anchorImg
        this.isGood = isGood
        this.anchorName = anchorName
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initData()
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            lastDialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
            lastDialogBinding!!.avatarLarge.gravity = Gravity.END
        } else {
            lastDialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        lastDialogBinding!!.maximalCounter.text = LanguageManager.instance!!.getLocalTranslate("Impression")
        //        lastDialogBinding.title2.setText(LanguageManager.getInstance().getLocalTranslate("Try_another_girl"));
        lastDialogBinding!!.emptyFocused.text = LanguageManager.instance!!.getLocalTranslate("Next")
    }

    private fun initData() {
        lastDialogBinding!!.clockLast.text =
            LanguageManager.instance?.getLocalTranslate("Call_duration") + formatSeconds(anchorInfo?.duration?.toLong()?:0L)
        //        lastDialogBinding.clockLast
//                .setText(String.format(getActivity()
//                                .getString(R.string.common_ok_47),
//                        formatSeconds(anchorInfo.getDuration())));
        loadCircleImage(activity, anchorImg, lastDialogBinding!!.fluidForeground)
        lastDialogBinding!!.fluidForeground.borderWidth = dip2px(2f)
        lastDialogBinding!!.fluidForeground.borderColor = context?.getColor(R.color.white) ?: Color.WHITE
        lastDialogBinding!!.avatarLarge.text = anchorName
        initLabel(isGood)
        //        initAnchorRc();
        initClick()
    }

    @SuppressLint("CheckResult")
    private fun initClick() {
        lastDialogBinding!!.emptyBelow.setOnClickListener { v: View? -> dismiss() }
        lastDialogBinding!!.emptyFocused.setOnClickListener { view: View? ->
            if (!labelList.isEmpty()) {
                RetrofitManage
                    .instance
                    .create(Service::class.java)
                    .EvaluateSubmit(
                        com.juicy.common.model.bean.AnchorScoreBean(
                            anchorInfo!!.channelName!!,
                            if (isGood) 1 else 0,
                            labelList
                        )
                    )
                    .subscribeOn(Schedulers.io())
                    .observeOn(Schedulers.io())
                    .subscribe { baseBean: com.juicy.common.model.bean.NetResponseBean<*>? ->
                        if (baseBean!!.data != null) {
                            ToastUtils.showShort(
                                LanguageManager.instance!!.getLocalTranslate(
                                    "Evaluation success"
                                )
                            )
                        }
                        <EMAIL>()
                    }
            } else {
                io.rong.imkit.picture.tools.ToastUtils.s(
                    context,
                    LanguageManager.instance!!.getLocalTranslate("Review_cannot_be_empty")
                )
            }
        }
    }

    //    private void initAnchorRc() {
    //        LinearLayoutManager manager = new LinearLayoutManager(getContext());
    //        manager.setOrientation(RecyclerView.HORIZONTAL);
    //        lastDialogBinding.anchorRc.setLayoutManager(manager);
    //        TAnchorAdapter anchorAdapter = new TAnchorAdapter(R.layout.item_box);
    //        anchorAdapter.setNewInstance(anchorInfo.getRecommendList());
    //        lastDialogBinding.anchorRc.setAdapter(anchorAdapter);
    //        anchorAdapter.setAdapterAnimation(new AlphaInAnimation());
    //
    //
    //    }
    private fun initLabel(isGood: Boolean) {
        lastDialogBinding!!.mainUp.setVerticalGap(dip2px(8f))
        lastDialogBinding!!.mainUp.setHorizontalGap(dip2px(8f))
        lastDialogBinding!!.mainUp.setFillMode(AutoWrapLineLayout.MODE_WRAP_CONTENT)
        if (isGood) {
            if (anchorInfo!!.tagList != null && !anchorInfo!!.tagList!!.isEmpty()) {
                for (anchorTag in anchorInfo!!.tagList!!) {
                    addLabelView(anchorTag)
                }
            }
        } else {
            if (anchorInfo!!.badTagList != null && !anchorInfo!!.badTagList!!.isEmpty()) {
                for (anchorTag in anchorInfo!!.badTagList!!) {
                    addLabelView(anchorTag)
                }
            }
        }
        lastDialogBinding!!.mainUp.post { lastDialogBinding!!.mainUp.requestLayout() }
    }

    private fun addLabelView(tag: String) {
       context?.let {
           val labelTextView = LabelTextView(it)
           labelTextView.text = tag
           labelTextView.gravity = Gravity.CENTER
           labelTextView.setTextColor(it.getColor(R.color.white))
           labelTextView.setOnClickListener(View.OnClickListener {
               if (labelTextView.isSelect()) {
                   labelList.remove(labelTextView.text.toString())
                   labelTextView.setSelect(false)
               } else {
                   if (labelList.size >= 3) {
                       ToastUtils.showShort(
                           LanguageManager.instance!!.getLocalTranslate(
                               "just choose three"
                           )
                       )
                       return@OnClickListener
                   }
                   labelList.add(labelTextView.text.toString())
                   labelTextView.setSelect(true)
               }
           })
           lastDialogBinding!!.mainUp.addView(labelTextView)
       }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        lastDialogBinding = DialogBodyBinding.inflate(inflater)
        return lastDialogBinding!!.root
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window!!.setBackgroundDrawable(null)
            dialog!!.window!!.setGravity(Gravity.BOTTOM)
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window!!.attributes = layoutParams
        }
    }

    companion object {
        fun formatSeconds(seconds: Long): String {
            var timeStr = seconds.toString() + "s"

            if (seconds > 60) {
                val second = seconds % 60

                var min = seconds / 60

                timeStr = min.toString() + "m" + second + "s"

                if (min > 60) {
                    min = (seconds / 60) % 60

                    val hour = (seconds / 60) / 60

                    timeStr = hour.toString() + "h" + min + "m" + second + "s"
                }
            }

            return timeStr
        }
    }
}
