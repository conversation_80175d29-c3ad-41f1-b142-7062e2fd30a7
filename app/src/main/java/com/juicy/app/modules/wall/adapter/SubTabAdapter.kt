package com.juicy.app.modules.wall.adapter

import android.view.View
import android.widget.TextView
import com.blankj.utilcode.util.ActivityUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R
import com.juicy.app.modules.wall.adapter.SubTabAdapter.SecondTabVH
import com.juicy.common.config.Cache
import com.juicy.common.utils.AppDeviceUtils.dip2px

class SubTabAdapter(layoutResId: Int, data: List<String>) :
    BaseQuickAdapter<String?, SecondTabVH>(layoutResId, data.toMutableList()) {

    inner class SecondTabVH(view: View) : BaseViewHolder(view) {
        var secondTabName: TextView =
            view.findViewById(R.id.macroInput)
    }
    override fun convert(secondTabVH: SecondTabVH, s: String?) {
        if (s == null) return
        secondTabVH.secondTabName.text = s
        if (Cache.instance.currentSecondTab == getItemPosition(s)) {
            secondTabVH.secondTabName.background = ActivityUtils.getTopActivity()
                .getDrawable(R.drawable.bg_second_tab_amber)
            secondTabVH.secondTabName.setPadding(dip2px(4f), dip2px(0f), dip2px(4f), dip2px(0f))

        } else {
            secondTabVH.secondTabName.background = null
            secondTabVH.secondTabName.setPadding(0, 0, 0, 0)
        }
    }
}
