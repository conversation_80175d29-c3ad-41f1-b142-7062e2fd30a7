package com.juicy.app.modules.call.adapter

import android.view.View
import android.widget.ProgressBar
import android.widget.RelativeLayout
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.view.CircleImageView

class VideoCallItemViewHolder(view: View) : BaseViewHolder(view) {
    var imMessage: AppCompatTextView =
        view.findViewById(R.id.downloadHuge)
    var transitionContent: AppCompatTextView =
        view.findViewById(R.id.connectedLg)
    var line: View = view.findViewById(R.id.stretchLong)
    var gift: AppCompatImageView =
        view.findViewById(R.id.highlightedMedium)
    var anchorPhoto: CircleImageView =
        view.findViewById(R.id.fluidForeground)

    var bgView: RelativeLayout =
        view.findViewById(R.id.forwardAdd)

    var progressBar: ProgressBar =
        view.findViewById(R.id.enabledLg)

    init {
        if (LanguageManager.instance!!.isLanguageForce) {
            bgView.background = AppCompatResources.getDrawable(
                view.context,
                R.drawable.bg_chat_rlt_bg_yellow
            )
        }
    }
}
