package com.juicy.app.modules.wall

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.ToastUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.juicy.app.modules.wall.adapter.AnchorWallAdapter
import com.juicy.app.databinding.LayoutPopupBinding
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.model.event.TabSelectStatusChangeEvent
import com.juicy.common.model.event.CountryChangeEvent
import com.juicy.common.model.event.OnlineStatusChangeEvent
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.networks.delegate.WallSearchInterface.wallSearch
import com.juicy.common.status.UserOnlineStatusService
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.throttle.Throttle.throttle
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class WallFragment : BaseFragment {
    private lateinit var binding: LayoutPopupBinding
    private var gridLayoutManager: GridLayoutManager? = null
    private var isResume = false
    private var isShowBanner = false
    private var isFirst = true
    private var pageName: String? = null
    private val anchorList: MutableList<com.juicy.common.model.bean.AnchorItem> = ArrayList()
    private var isLoadFirst = true
    private var mIsLoadMore = false
    private val page = 0
    private var x1 = 0f
    private var x2 = 0f
    private var isFirstCache = true
    private var currentPageNum = 1
    private var mName: String? = null
    private var adapter: AnchorWallAdapter? = null
    constructor()

    constructor(isShowBanner: Boolean, pageName: String?) {
        this.isShowBanner = isShowBanner
        this.pageName = pageName
        this.isFirst = true
        mIsLoadMore = false
        mName = pageName
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }


    override fun onPause() {
        stopTask()
        isResume = false
        super.onPause()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onUserStatus(event: OnlineStatusChangeEvent?) {
        if (!isAdded) return
        for (item in anchorList) {
            if (item.itemType == 1) {
                val userBean = item.anchorInfoBean
                val status = UserOnlineStatusService.instance?.getStatus(userBean?.userId?:"")
                if (status != null) {
                    userBean?.status = status
                }
            }
        }

        val manager = binding.tabDynamic.layoutManager as GridLayoutManager?
        val visibleCount = manager?.childCount?:0
        for (i in 0 until visibleCount) {
            val child = binding.tabDynamic.getChildAt(i)
            if (child != null) {
                for (item in anchorList) {
                    if (item.itemType == 1) {
                        val userBean = item.anchorInfoBean
                        val viewHolder = binding.tabDynamic.getChildViewHolder(child)
                        if (viewHolder.javaClass == (AnchorWallAdapter.AnchorVH::class.java)) {
                            if ((viewHolder as AnchorWallAdapter.AnchorVH).model != null) {
                                UserOnlineStatusService.instance?.addUserId(viewHolder.model?.userId?:"")
                            }

                            if (viewHolder != null && viewHolder.model != null && viewHolder?.model?.userId == userBean?.userId) {
                                userBean?.let {
                                    AnchorWallAdapter.AnchorVH.setOnlineStatues(viewHolder, it)
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = LayoutPopupBinding.inflate(inflater, container, false)
        return binding.root
    }
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onCountryEvent(countryChangeEvent: CountryChangeEvent?) {
        //
        binding.aroundLarge.autoRefresh()
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onTagChange(event: TabSelectStatusChangeEvent) {
        //
        if (event.page == 0) {
            resetStatueUserId()
        }
    }
    override fun onStop() {
        super.onStop()
    }



    override fun initData() {
        if (Cache.instance.userStratResult == null) {
            ARouter.getInstance()
                .build(Constant.SPLASH_ACTIVITY_ROUTE)
                .addFlags(
                    Intent.FLAG_ACTIVITY_NEW_TASK
                            or Intent.FLAG_ACTIVITY_CLEAR_TASK
                )
                .navigation()
            return
        }

        binding.shadowCoordinator.text = LanguageManager.instance?.getLocalTranslate("No_Data")

        adapter = AnchorWallAdapter(requireContext(),anchorList)
        binding.aroundLarge.autoRefresh()
    }



    fun resetStatueUserId() {
        if (!isAdded) return
        if (!isResume) return

        throttle("anchor_wall_refresh_first", 200,
            {},
            {
                val manager =
                    binding.tabDynamic.layoutManager as GridLayoutManager?
                val visibleCount = manager?.childCount?:0
                for (i in 0 until visibleCount) {
                    val child = binding.tabDynamic.getChildAt(i)
                    if (child != null) {
                        val viewHolder =
                            binding.tabDynamic.getChildViewHolder(child)
                        if (viewHolder.javaClass == (AnchorWallAdapter.AnchorVH::class.java)) {
                            if ((viewHolder as AnchorWallAdapter.AnchorVH).model != null) {
                                UserOnlineStatusService.instance?.addUserId(viewHolder.model?.userId?:"")
                            }
                        }
                    }
                }
                UserOnlineStatusService.instance?.forceRefresh()
            })
    }

    override fun initView() {
        if (isShowBanner) {
//            binding.headerAbove.loadBanner();
            anchorList.add(
                com.juicy.common.model.bean.AnchorItem(
                    0,
                    com.juicy.common.model.bean.BannerItemBean()
                )
            )
        }
        if (Cache.instance.reviewPkg) {
            binding.borderMacro.visibility = View.GONE
        } else {
            binding.borderMacro.visibility = View.VISIBLE
        }



        gridLayoutManager = GridLayoutManager(context, 2)
        gridLayoutManager?.spanSizeLookup =
            object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return if (adapter?.getItemViewType(position) == 1) 1 else 2
                }
            }
        gridLayoutManager?.orientation = LinearLayoutManager.VERTICAL

        binding.tabDynamic.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                outRect[dip2px(4f), dip2px(4f), dip2px(
                    4f
                )] =
                    dip2px(4f)
            }
        })


        //        binding.tabDynamic.addItemDecoration(new GridSpacingItemDecoration(2, DeviceMedaUtils.dip2px(8), true));
        binding.tabDynamic.layoutManager = gridLayoutManager
        binding.tabDynamic.adapter = adapter

        binding.aroundLarge.setEnableLoadMore(true)
        binding.tabDynamic.setOnTouchListener(mOnMoveTouchListener)
        binding.miniCopy.isClickable = false
        binding.miniCopy.isFocusable = false
        binding.aroundLarge.setOnRefreshListener {
            currentPageNum = 1
            binding.aroundLarge.resetNoMoreData()
            loadAnchorDate()
            //                if (isShowBanner) {
            //                    binding.headerAbove.loadBanner();
            //                }
        }
        binding.aroundLarge.setOnLoadMoreListener {
            ++currentPageNum
            loadAnchorDate()
        }
        binding.tabDynamic.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
//                    Log.d("TAG", "onScrollStateChanged: stop");
                    resetStatueUserId()
                }
            }
        })
    }


    override fun onResume() {
        super.onResume()
        isResume = true
        resetStatueUserId()
    }

    /**处理左滑手势 */
    @SuppressLint("ClickableViewAccessibility")
    private val mOnMoveTouchListener =
        View.OnTouchListener { view, motionEvent ->
            if (motionEvent.action == MotionEvent.ACTION_DOWN) {
                //当手指按下的时候
                x1 = motionEvent.x
            }
            if (motionEvent.action == MotionEvent.ACTION_MOVE) {
                //当手指按下并移动的时候
                x2 = motionEvent.x
                if (x1 - x2 > 0) {
                    Cache.instance.isMoveLeft = false
                } else if (x2 - x1 > 0) {
                    Cache.instance.isMoveLeft = true
                }
            }
            false
        }

    fun stopTask() {
    }


    fun loadWallSearch(count: Int) {
        if (Cache.instance.userStratResult == null) {
            return
        }
        wallSearch(
            isShowBanner,
            Cache.instance.userStratResult?.broadcasterWallTagList?.get(Cache.instance.currentFirstTab)?.tagName,
            pageName,
            currentPageNum,
            object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.AnchorInfoBean>>>() {
                override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.AnchorInfoBean>>) {
                    super.onNext(t)
                    if (t != null) {
                        loadSuccess(t)
                    }

                    if (adapter?.itemCount == 0) {
                        binding.miniCopy.visibility = View.VISIBLE
                    } else {
                        binding.miniCopy.visibility = View.GONE
                    }
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                    if (currentPageNum == 1 && count > 0) {
                        loadWallSearch(count - 1)
                    } else {
                        if (currentPageNum == 1) {
                            binding.aroundLarge.finishRefresh(false)
                            binding.aroundLarge.finishLoadMore()
                            if (!isDestroy(activity)) {
                                ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Refresh_failed"))
                            }
                        } else {
                            currentPageNum--
                            binding.aroundLarge.finishRefresh()
                            binding.aroundLarge.finishLoadMore(false)
                        }

                        //                binding.aroundLarge.finishRefresh();
//                binding.aroundLarge.finishLoadMore();
                        if (adapter?.itemCount == 0) {
                            binding.miniCopy.visibility = View.VISIBLE
                        } else {
                            binding.miniCopy.visibility = View.GONE
                        }
                    }
                }
            })
    }


    private fun loadAnchorDate() {
        if (currentPageNum == 1 && isFirstCache) {
            val cacheListBaseBean = cacheWall
            if (cacheListBaseBean?.data != null && !cacheListBaseBean.data.isNullOrEmpty()) {
                cacheListBaseBean?.let {
                    loadSuccess(it)
                }
            }
        }

        loadWallSearch(3)
    }




    @SuppressLint("NotifyDataSetChanged")
    fun loadSuccess(listBaseBean: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.AnchorInfoBean>>) {
        if (!listBaseBean.data.isNullOrEmpty()) {
            isFirstCache = false
            binding.miniCopy.visibility = View.GONE
            if (currentPageNum == 1) {
                if (Cache.instance.userInfo != null) {
                    Cache.instance.networkCacheManager.putObject(
                        ("wall_" + Cache.instance.userStratResult?.broadcasterWallTagList?.get(Cache.instance.currentFirstTab)?.tagName + pageName
                                + Cache.instance.userInfo?.userId) ?: "",
                        listBaseBean
                    )
                }
            }

            val userIds: MutableList<String> = ArrayList()
            val list: MutableList<com.juicy.common.model.bean.AnchorItem> = ArrayList()
            if (!listBaseBean.data.isNullOrEmpty()){
                for (i in listBaseBean.data!!.indices) {
                    list.add(com.juicy.common.model.bean.AnchorItem(1, listBaseBean.data!![i]))
                }
                for (userBean in listBaseBean.data!!) {
                    userIds.add(userBean.userId?:"")
                }
            }

            if (currentPageNum == 1) {
                var bannerItem: com.juicy.common.model.bean.AnchorItem? = null
                if (anchorList.size > 0 && anchorList[0].itemType == 0) {
                    bannerItem = anchorList[0]
                }
                anchorList.clear()
                if (bannerItem != null) {
                    anchorList.add(bannerItem)
                }
                anchorList.addAll(list)
                adapter?.list = anchorList
            } else {
                adapter?.list?.addAll(list)
            }
            adapter?.notifyDataSetChanged()
            UserOnlineStatusService.instance?.addUserIdsAndRefresh(userIds.toList())

            binding.aroundLarge.finishRefresh()
            binding.aroundLarge.finishLoadMore()
        } else {
            if (currentPageNum == 1) {
                var bannerItem: com.juicy.common.model.bean.AnchorItem? = null
                if (anchorList.size > 0 && anchorList[0].itemType == 0) {
                    bannerItem = anchorList[0]
                }
                anchorList.clear()
                if (bannerItem != null) {
                    anchorList.add(bannerItem)
                }
                adapter?.list = anchorList
                binding.miniCopy.visibility = View.VISIBLE
            }
            adapter?.notifyDataSetChanged()
            if (!isLoadFirst) {
                ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("No_Data"))
            }
            isLoadFirst = false
            if (currentPageNum == 1) {
                binding.aroundLarge.finishRefresh()
                binding.aroundLarge.finishLoadMore()
            } else {
                binding.aroundLarge.finishRefresh()
                binding.aroundLarge.finishLoadMoreWithNoMoreData()
            }
        }
    }

    val cacheWall: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.AnchorInfoBean>>?
        get() {
            if (Cache.instance.userInfo == null || Cache.instance.userStratResult == null ||
                Cache.instance.userStratResult?.broadcasterWallTagList.isNullOrEmpty()
            ) {
                return null
            }
            val json =
                Cache.instance.networkCacheManager.get(
                    "wall_" + Cache.instance.userStratResult?.broadcasterWallTagList?.get(Cache.instance.currentFirstTab)?.tagName + pageName
                            + Cache.instance.userInfo?.userId
                )
            if (TextUtils.isEmpty(json)) {
                return null
            }
            val data = Gson()
                .fromJson<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.AnchorInfoBean>>>(
                    json,
                    object :
                        TypeToken<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.AnchorInfoBean>>>() {
                    }.type
                )

            return data
        }

    override fun onDestroy() {
        super.onDestroy()
        stopTask()
    }
}
