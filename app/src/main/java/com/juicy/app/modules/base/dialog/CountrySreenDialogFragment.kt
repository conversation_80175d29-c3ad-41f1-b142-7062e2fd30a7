package com.juicy.app.modules.base.dialog

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.juicy.app.databinding.FragmentGridBinding
import com.juicy.common.model.event.CountryChangeEvent
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.SpSaveUtil.putStringValue
import com.juicy.common.utils.view.AutoWrapLineLayout
import com.juicy.common.utils.view.CountryWrapItem
import com.yesterselga.countrypicker.Country
import org.greenrobot.eventbus.EventBus

class CountrySreenDialogFragment : DialogFragment {
    private var dialogBinding: FragmentGridBinding? = null
    var didBack: CountryCallBack? = null

    constructor()

    constructor(didBack: CountryCallBack?) {
        // Required empty public constructor
        this.didBack = didBack
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialogBinding = FragmentGridBinding.inflate(inflater)
        return dialogBinding!!.root
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window?.setBackgroundDrawable(null)
            dialog!!.window?.setDimAmount(0f)
            dialog!!.window?.setGravity(Gravity.TOP)
            val layoutParams = dialog!!.window?.attributes
            layoutParams?.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams?.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window?.attributes = layoutParams
        }
    }

    fun initData() {
        val allCountry = Country.getAllCountries()

        val simpleArray = Cache.instance.userStratResult?.broadcasterWallRegions
        val currentCountry = getStringValue(SpKeyPool.CURRECTCOUNTRY, "")
        if (simpleArray != null) {
            val dataArray: MutableList<Country> = ArrayList()
            //            String code, String name, String dialCode, int flag
            val aCountry = Country("", "  All  ", "", 0)
            dataArray.add(aCountry)
            for (simpleStr in simpleArray) {
                for (country in allCountry) {
                    if (country.code == simpleStr) {
                        dataArray.add(country)
                    }
                }
            }

            dialogBinding!!.maskUp.setFillMode(AutoWrapLineLayout.MODE_WRAP_CONTENT)

            for (i in dataArray.indices) {
                val country = dataArray[i]
                var isSelect = false
                if (i == 0 && currentCountry === "") {
                    isSelect = true
                } else if (currentCountry == country.code) {
                    isSelect = true
                }

                val autoWrapItem = CountryWrapItem(this.context, country, isSelect)
                dialogBinding!!.maskUp.addView(autoWrapItem)
                autoWrapItem.setOnClickListener { view: View ->
                    val didView = view as? CountryWrapItem?:return@setOnClickListener
                    val code = didView.country.code
                    putStringValue(SpKeyPool.CURRECTCOUNTRY, code)

                    val countryChangeEvent = CountryChangeEvent()
                    EventBus.getDefault().post(countryChangeEvent)

                    didBack?.didSelectBlock()
                    dismiss()
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        initData()
        dialogBinding!!.innerToggle.setOnClickListener { v: View? -> dismiss() }
        dialogBinding!!.surfaceDynamic.setOnClickListener { v: View? -> }
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            dialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
            dialogBinding!!.maskUp.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            dialogBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
    }

    interface CountryCallBack {
        fun didSelectBlock()
    }
}