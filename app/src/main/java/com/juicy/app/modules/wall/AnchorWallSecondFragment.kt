package com.juicy.app.modules.wall

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.android.arouter.launcher.ARouter
import com.juicy.app.R
import com.juicy.app.modules.wall.WallFirstFragment.PagerCallBack
import com.juicy.app.modules.wall.adapter.AnchorFragmentAdapter
import com.juicy.app.modules.wall.adapter.SubTabAdapter
import com.juicy.app.databinding.LayoutCaptionBinding
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.model.event.AnchorisFirstParamEvent
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.LanguageManager
import org.greenrobot.eventbus.EventBus

class AnchorWallSecondFragment : BaseFragment {
    private lateinit var binding: LayoutCaptionBinding
    private val fragments: MutableList<WallFragment> = ArrayList()
    private var currentFragment: WallFragment? = null
    private var anchorFragmentAdapter: AnchorFragmentAdapter? = null
    private val secondNames: MutableList<String> = ArrayList()
    private var adapter: SubTabAdapter? = null
    private val wallFragments: MutableList<WallFragment> = ArrayList()
    private val secondFragments: MutableList<WallFragment> = ArrayList()
    private var currentSecondFragment: WallFragment? = null

    private var isScrolled = false

    private var pagerCallBack: PagerCallBack? = null
    var currentSubPosition: Int = 0
    var pagePosition: Int = 0

    constructor()

    constructor(pagePosition: Int, pagerCallBack: PagerCallBack?) {
        this.pagePosition = pagePosition
        this.pagerCallBack = pagerCallBack
        currentFragment = null
        wallFragments.clear()
        secondFragments.clear()
        currentSecondFragment = null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }



    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = LayoutCaptionBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun initData() {
        try {
            if (null == Cache.instance.userStratResult) {
                ARouter.getInstance()
                    .build(Constant.SPLASH_ACTIVITY_ROUTE)
                    .addFlags(
                        Intent.FLAG_ACTIVITY_NEW_TASK
                                or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    )
                    .navigation()
                return
            }
            if (null != Cache.instance.userStratResult?.broadcasterWallTagList
                && (Cache.instance.userStratResult?.broadcasterWallTagList?.size?:0) > pagePosition
                && Cache.instance.userStratResult?.broadcasterWallTagList?.get(pagePosition) != null
                && !Cache.instance.userStratResult?.broadcasterWallTagList?.get(pagePosition)?.subTagList.isNullOrEmpty()) {
                var subTagList = Cache.instance.userStratResult?.broadcasterWallTagList?.get(pagePosition)?.subTagList?: listOf()
//                if (Cache.instance.strategyResult != null && Cache.instance.strategyResult?.isReviewPkg != null &&
//                    Cache.instance.strategyResult?.isReviewPkg == true) {
//                    subTagList = ArrayList()
//                    subTagList.add("All")
//                }
                var subIndex = 0
                for (name in subTagList) {
                    secondNames.add(name)
                    if (pagePosition == 0) {
                        fragments.add(WallFragment(subIndex == 0, name))
                    } else {
                        fragments.add(WallFragment(false, name))
                    }
                    subIndex++
                }
                adapter = SubTabAdapter(R.layout.item_modal, secondNames)
                anchorFragmentAdapter = AnchorFragmentAdapter(this)
                anchorFragmentAdapter?.wallFragments = fragments
                if (fragments.size <= 1) {
                    binding.disabledExpand.visibility = View.GONE
                } else {
                    binding.disabledExpand.visibility = View.VISIBLE
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            ARouter.getInstance()
                .build(Constant.SPLASH_ACTIVITY_ROUTE)
                .addFlags(
                    Intent.FLAG_ACTIVITY_NEW_TASK
                            or Intent.FLAG_ACTIVITY_CLEAR_TASK
                )
                .navigation()
        }
    }

    fun stopFreshTime() {
        if (!fragments.isEmpty()) {
            fragments[currentSubPosition].stopTask()
        }
    }


    override fun initView() {
        reduceSensitivity()
        val manager = LinearLayoutManager(
            activity
        )
        manager.orientation = LinearLayoutManager.HORIZONTAL
        binding.insidePicker.layoutManager = manager
        if (LanguageManager.instance?.isLanguageForce == true) {
            binding.insidePicker.layoutDirection = View.LAYOUT_DIRECTION_RTL
        }
        if (anchorFragmentAdapter != null) {
            binding.pauseEnd.offscreenPageLimit = 2
            binding.pauseEnd.adapter = anchorFragmentAdapter
            binding.pauseEnd.currentItem = 0
            binding.pauseEnd.registerOnPageChangeCallback(object :
                ViewPager2.OnPageChangeCallback() {
                @SuppressLint("NotifyDataSetChanged")
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    Cache.instance.currentSecondTab = position
                    currentSubPosition = position
                    adapter?.notifyDataSetChanged()
                    binding.insidePicker.scrollToPosition(Cache.instance.currentSecondTab)
                    if (pagePosition == 0) {
                        EventBus.getDefault()
                            .post(AnchorisFirstParamEvent(pagePosition == 0 && position == 0))
                    }
                }

                override fun onPageScrollStateChanged(i: Int) {
                    when (i) {
                        ViewPager.SCROLL_STATE_DRAGGING -> isScrolled = false
                        ViewPager.SCROLL_STATE_SETTLING -> isScrolled = true
                        ViewPager.SCROLL_STATE_IDLE -> {
                            val isMoveLeft =
                                if (LanguageManager.instance?.isLanguageForce == true) !Cache.instance.isMoveLeft else Cache.instance.isMoveLeft
                            if (!isScrolled && isMoveLeft && Cache.instance.currentSecondTab == 0) {
                                if (Cache.instance.currentFirstTab == 0) return
                                pagerCallBack?.onCallBack(--Cache.instance.currentFirstTab)
                            }
                            if (!isScrolled && !isMoveLeft && (Cache.instance.currentSecondTab
                                        == (Cache.instance.userStratResult?.broadcasterWallTagList
                                    ?.get(Cache.instance.currentFirstTab)?.subTagList?.size?:2) - 1)
                            ) {
                                if (Cache.instance.currentFirstTab == (Cache.instance.userStratResult?.broadcasterWallTagList?.size?:2) - 1) return
                                pagerCallBack?.onCallBack(++Cache.instance.currentFirstTab)
                            }
                            isScrolled = true
                        }
                    }
                }
            })
        }
        if (adapter != null) {
            binding.insidePicker.adapter = adapter
            adapter?.addChildClickViewIds(R.id.macroInput)
            adapter?.setOnItemChildClickListener { adapter, view, position ->
                Cache.instance.currentSecondTab = position
                currentSubPosition = position
                adapter.notifyDataSetChanged()
                binding.pauseEnd.setCurrentItem(
                    Cache.instance.currentSecondTab,
                    false
                )
                binding.insidePicker.scrollToPosition(Cache.instance.currentSecondTab)
                if (pagePosition == 0) {
                    EventBus.getDefault()
                        .post(AnchorisFirstParamEvent(pagePosition == 0 && position == 0))
                }
            }
        }
    }

    private fun reduceSensitivity() {
        try {
            val recyclerViewField = ViewPager2::class.java.getDeclaredField("mRecyclerView")
            recyclerViewField.isAccessible = true

            val recyclerView = recyclerViewField[binding.pauseEnd] as RecyclerView

            val touchSlopField = RecyclerView::class.java.getDeclaredField("mTouchSlop")
            touchSlopField.isAccessible = true

            val touchSlop = touchSlopField[recyclerView] as Int
            touchSlopField[recyclerView] = touchSlop * 4 // 增加滑动阈值
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
