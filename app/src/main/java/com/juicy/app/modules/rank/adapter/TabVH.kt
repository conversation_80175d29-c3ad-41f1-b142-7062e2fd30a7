package com.juicy.app.modules.rank.adapter

import android.view.View
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R

class TabVH(itemView: View) : BaseViewHolder(itemView) {
    var tabItem: RelativeLayout =
        itemView.findViewById(R.id.topBottom)
    var tabName: AppCompatTextView =
        itemView.findViewById(R.id.outerMicro)
    var star: AppCompatImageView = itemView.findViewById(R.id.copyPrev)
    var selectView: View =
        itemView.findViewById(R.id.coordinatorToggle)
}