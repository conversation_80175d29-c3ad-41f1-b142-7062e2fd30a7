package com.juicy.app.modules.base.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.AbsoluteSizeSpan
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.juicy.app.R
import com.juicy.app.modules.base.activity.WebViewActivity
import com.juicy.common.model.bean.RobotMessageBean
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.TranslateUtils.textTrans
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.schedulers.Schedulers
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation
import java.util.regex.Pattern

class RobotAdapter(@JvmField var items: MutableList<RobotMessageBean>, var mContext: Context?) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var mListener: OnImgBigClickListener? = null

    @JvmField
    var scrollBack: RobotListLongDialogCallBack? = null

    interface RobotListLongDialogCallBack {
        fun newMessage()
    }

    fun setOnItemClickListener(listener: OnImgBigClickListener?) {
        mListener = listener
    }

    interface OnImgBigClickListener {
        fun onItemClick(imgUrl: String?)
    }

    override fun getItemViewType(position: Int): Int {
        return if (items[position].type == 0) {
            0
        } else if (items[position].type == 1) {
            1
        } else {
            2
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        if (viewType == 0) {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_chip, parent, false)
            return QuestionViewHolder(view)
        } else if (viewType == 1) {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_icon, parent, false)
            return AnswerViewHolder(view)
        } else if (viewType == 2) {
            val view =
                LayoutInflater.from(parent.context).inflate(R.layout.item_image, parent, false)
            return RobotUserViewHolder(view)
        }
        throw IllegalArgumentException("Invalid view type")
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val robotMessageBean = items[position]

        val isAutoTranslate =
            getBooleanVal(SpKeyPool.IS_AUTO_TRANSLATE, false)!!

        if (holder is QuestionViewHolder) {
            holder.tv_question.text = robotMessageBean.robotBean!!.content
            val containerView = holder.ll_question

            containerView.removeAllViews()
            var originalText = robotMessageBean.robotBean!!.content

            if (robotMessageBean?.robotBean != null && robotMessageBean.robotBean!!.getfaqInfoList() != null) {
                for (i in robotMessageBean.robotBean!!.getfaqInfoList()!!.indices) {
                    val subModel = robotMessageBean.robotBean!!.getfaqInfoList()!![i]

                    val questionLb = TextView(containerView.context)
                    questionLb.setTextColor(Color.parseColor("#FF2CC0FF"))
                    questionLb.textSize = 14f
                    questionLb.text = subModel.question
                    questionLb.isClickable = true
                    questionLb.setOnClickListener { v: View? -> questtionTap(subModel) }
                    var layoutParams = LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                    questionLb.layoutParams = layoutParams
                    containerView.addView(questionLb)
                    originalText = """
                        $originalText
                        ${subModel.question}
                        """.trimIndent()
                }
            }
            robotMessageBean.content = originalText!!

            if (isAutoTranslate && !robotMessageBean.isTraning && robotMessageBean.tranString == null) {
                holder.translate_content.visibility = View.GONE
                holder.translate_btn_content.visibility = View.VISIBLE
                holder.tranlate_btn_text.text =
                    LanguageManager.instance!!.getLocalTranslate("Translating") + "..."
                translateFun(robotMessageBean)
            }

            holder.translate_btn_content.setOnClickListener { v: View? ->
                holder.translate_content.visibility =
                    View.GONE
                holder.translate_btn_content.visibility =
                    View.VISIBLE
                holder.tranlate_btn_text.text =
                    LanguageManager.instance!!.getLocalTranslate("Translating") + "..."
                translateFun(robotMessageBean)
            }

            if (robotMessageBean.tranString != null) {
                holder.translate_content.visibility = View.VISIBLE
                holder.translate_text.text = robotMessageBean.tranString
                holder.translate_btn_content.visibility = View.GONE
            } else if (isAutoTranslate && robotMessageBean.isTraning) {
                holder.translate_content.visibility = View.GONE
                holder.translate_btn_content.visibility = View.VISIBLE
                holder.tranlate_btn_text.text =
                    LanguageManager.instance!!.getLocalTranslate("Translating") + "..."
            } else if (!isAutoTranslate && robotMessageBean.isTraning) {
                holder.translate_content.visibility = View.GONE
                holder.translate_btn_content.visibility = View.VISIBLE
                holder.tranlate_btn_text.text =
                    LanguageManager.instance!!.getLocalTranslate("Translating") + "..."
            } else {
                holder.translate_content.visibility = View.GONE
                holder.translate_btn_content.visibility = View.VISIBLE
                holder.tranlate_btn_text.text =
                    LanguageManager.instance!!.getLocalTranslate("Click_to_translate")
            }
        } else if (holder is AnswerViewHolder) {
            val originalText = robotMessageBean.appAQInfoBean!!.messageAnswer!!.content
            robotMessageBean.content = originalText!!
            val spannableStringBuilder = SpannableStringBuilder(originalText)
            val textColorSpan = ForegroundColorSpan(Color.parseColor("#FFFFFFFF"))
            spannableStringBuilder.setSpan(
                textColorSpan,
                0,
                spannableStringBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            val textSizeSpan = AbsoluteSizeSpan(13, true)
            spannableStringBuilder.setSpan(
                textSizeSpan,
                0,
                spannableStringBuilder.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            if (robotMessageBean.appAQInfoBean!!.isViewExample) {
                holder.emplexyText.visibility = View.VISIBLE
                holder.emplexyText.setOnClickListener { v: View? ->
                    //展示大图片
                    mListener!!.onItemClick(robotMessageBean.appAQInfoBean!!.imageUrl)
                }
            } else {
                holder.emplexyText.visibility = View.GONE
            }

            if (robotMessageBean.isHelp == 0) {
                holder.helpIcon.setImageResource(R.drawable.ic_helpful_turquoise)
            } else if (robotMessageBean.isHelp == 1) {
                holder.unhelpIcon.setImageResource(R.drawable.ic_unhelpful_teal)
            }

            holder.helpItem.setOnClickListener { v: View? ->
                if (robotMessageBean.isHelp == -1) {
                    robotMessageBean.isHelp = 0
                    holder.helpIcon.setImageResource(R.drawable.ic_helpful_turquoise)
                }
            }

            holder.unhelpItem.setOnClickListener { v: View? ->
                if (robotMessageBean.isHelp == -1) {
                    robotMessageBean.isHelp = 1
                    holder.unhelpIcon.setImageResource(R.drawable.ic_unhelpful_teal)
                }
            }



            for (i in robotMessageBean.appAQInfoBean!!.messageAnswer!!.answerEventBean!!.indices) {
                val answerEventHandleList =
                    robotMessageBean.appAQInfoBean!!.messageAnswer!!.answerEventBean!![i]

                val keyword2 = answerEventHandleList.matchStr
                val pattern2 = Pattern.compile(Pattern.quote(keyword2), Pattern.CASE_INSENSITIVE)
                val matcher2 = pattern2.matcher(originalText)
                while (matcher2.find()) {
                    val start = matcher2.start()
                    val end = matcher2.end()

                    val clickableSpan: ClickableSpan = object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            if (isFastClick) {
                                return
                            }
                            //跳转响应位置
                            handleTap(
                                answerEventHandleList.handleType.toInt(),
                                answerEventHandleList.toUrl
                            )
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            super.updateDrawState(ds)
                            ds.color = Color.parseColor("#FF2CC0FF") // 菊花色
                            ds.isUnderlineText = true // 添加下划线
                        }
                    }
                    spannableStringBuilder.setSpan(
                        clickableSpan,
                        start,
                        end,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            }

            holder.tv_answer.text = spannableStringBuilder
            holder.tv_answer.movementMethod = LinkMovementMethod.getInstance()

            if (isAutoTranslate && !robotMessageBean.isTraning && robotMessageBean.tranString == null) {
                holder.translate_content.visibility = View.GONE
                holder.translate_btn_content.visibility = View.VISIBLE
                holder.tranlate_btn_text.text =
                    LanguageManager.instance!!.getLocalTranslate("Translating") + "..."
                translateFun(robotMessageBean)
            }

            holder.translate_btn_content.setOnClickListener { v: View? ->
                holder.translate_content.visibility =
                    View.GONE
                holder.translate_btn_content.visibility =
                    View.VISIBLE
                holder.tranlate_btn_text.text =
                    LanguageManager.instance!!.getLocalTranslate("Translating") + "..."
                translateFun(robotMessageBean)
            }

            if (robotMessageBean.tranString != null) {
                holder.translate_content.visibility = View.VISIBLE
                holder.translate_text.text = robotMessageBean.tranString
                holder.translate_btn_content.visibility = View.GONE
            } else if (isAutoTranslate && robotMessageBean.isTraning) {
                holder.translate_content.visibility = View.GONE
                holder.translate_btn_content.visibility = View.VISIBLE
                holder.tranlate_btn_text.text =
                    LanguageManager.instance!!.getLocalTranslate("Translating") + "..."
            } else if (!isAutoTranslate && robotMessageBean.isTraning) {
                holder.translate_content.visibility = View.GONE
                holder.translate_btn_content.visibility = View.VISIBLE
                holder.tranlate_btn_text.text =
                    LanguageManager.instance!!.getLocalTranslate("Translating") + "..."
            } else {
                holder.translate_content.visibility = View.GONE
                holder.translate_btn_content.visibility = View.VISIBLE
                holder.tranlate_btn_text.text =
                    LanguageManager.instance!!.getLocalTranslate("Click_to_translate")
            }
        } else if (holder is RobotUserViewHolder) {
            holder.user_text.text = robotMessageBean.content
            if (Cache.instance.userInfo != null && Cache.instance.userInfo!!.avatarThumbUrl != null) {
                loadCircleImage(
                    holder.itemView.context,
                    Cache.instance.userInfo!!.avatarThumbUrl,
                    holder.avatar
                )
            }
        }
    }

    @SuppressLint("CheckResult")
    private fun translateFun(bean: com.juicy.common.model.bean.RobotMessageBean) {
        bean.isTraning = true
        //        notifyDataSetChanged();
        Observable
            .just(1)
            .subscribeOn(Schedulers.newThread())
            .map { integer: Int? -> textTrans(bean.content)?:"" }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ s: String? ->
                if (s != null) {
                    bean.isTraning = false
                    bean.tranString = s
                } else {
                    bean.isTraning = false
                }
                notifyDataSetChanged()
            }, { throwable: Throwable? ->
                bean.isTraning = false
                notifyDataSetChanged()
            })
    }

    override fun getItemCount(): Int {
        return items.size
    }

    private var isTaping = false
    fun questtionTap(subModel: com.juicy.common.model.bean.AppAQInfoBean) {
        if (isTaping) return
        isTaping = true
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({ isTaping = false }, 1000)

        if (subModel.type == 2L) { //跳转网页
            handleTap(subModel.handleType.toInt(), subModel.toUrl)
        } else {
            val robotMessageBean = com.juicy.common.model.bean.RobotMessageBean()
            robotMessageBean.type = 2
            if (subModel.question != null) {
                robotMessageBean.content = subModel.question!!.replace("*  ", "")
            }
            items.add(robotMessageBean)


            notifyItemInserted(items.size - 1)
            if (scrollBack != null) {
                scrollBack!!.newMessage()
            }


            val handler2 = Handler()
            handler2.postDelayed({
                val answerModel = com.juicy.common.model.bean.RobotMessageBean()
                answerModel.type = 1
                answerModel.appAQInfoBean = subModel
                items.add(answerModel)
                // tableView.reloadData();
                notifyItemInserted(items.size - 1)
                if (scrollBack != null) {
                    scrollBack!!.newMessage()
                }
            }, 1000)
        }
    }

    fun handleTap(handleType: Int, toUrl: String?) {
        if (mContext == null) return
        var intent = Intent(mContext, WebViewActivity::class.java)
        if (handleType == 3) {
            if (getStringValue(SpKeyPool.TPP_OPEN_TYPE, "") == "0") {
                intent.putExtra("urlPath", toUrl)
                intent.putExtra("orderNo", "")
                intent.putExtra("mResultPage", "CoreActivity")
                mContext!!.startActivity(intent)
            } else {
                //跳转到外部浏览器
                val uri = Uri.parse(toUrl)
                Log.v(
                    Constant.GlobalTppEvent.CLICK,
                    "source：" + Constant.GlobalTppSource.BANNER + "open：browser"
                )
                intent = Intent(Intent.ACTION_VIEW, uri)
                mContext!!.startActivity(intent)
            }
        } else if (handleType == 2) {
            val uri = Uri.parse(toUrl)
            Log.v(
                Constant.GlobalTppEvent.CLICK,
                "source：" + Constant.GlobalTppSource.BANNER + "open：browser"
            )
            intent = Intent(Intent.ACTION_VIEW, uri)
            mContext!!.startActivity(intent)
        } else if (handleType == 1) {
            if (Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.userServiceAccountId != null) {
                RouteUtils.routeToConversationActivity(
                    mContext,
                    Conversation.ConversationType.PRIVATE,
                    Cache.instance.userStratResult!!.userServiceAccountId
                )
            }
        }
    }

    class QuestionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        // 初始化视图
        var tv_question: TextView =
            itemView.findViewById(R.id.secondaryRefresh)
        var ll_question: LinearLayout =
            itemView.findViewById(R.id.hideHuge)
        var tranlate_btn_text: TextView =
            itemView.findViewById(R.id.toggleNested)
        var translate_btn_content: LinearLayout =
            itemView.findViewById(R.id.backgroundThin)

        var translate_content: LinearLayout = itemView.findViewById(R.id.minimalCounter)
        var translate_text: TextView = itemView.findViewById(R.id.saveSurface)
    }

    class AnswerViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        // 初始化头部视图
        var tv_answer: TextView =
            itemView.findViewById(R.id.recyclerSmall)
        var tranlate_btn_text: TextView =
            itemView.findViewById(R.id.toggleNested)
        var translate_btn_content: LinearLayout =
            itemView.findViewById(R.id.backgroundThin)

        var translate_content: LinearLayout =
            itemView.findViewById(R.id.minimalCounter)
        var translate_text: TextView =
            itemView.findViewById(R.id.saveSurface)

        var helpItem: RelativeLayout = itemView.findViewById(R.id.dimmedTitle)
        var unhelpItem: RelativeLayout = itemView.findViewById(R.id.stretchConfirm)

        var helpIcon: ImageView = itemView.findViewById(R.id.narrowUnchecked)
        var unhelpIcon: ImageView = itemView.findViewById(R.id.redoEmpty)

        var helpText: TextView = itemView.findViewById(R.id.highlightedIcon)
        var unhelpText: TextView = itemView.findViewById(R.id.longRefresh)

        var emplexyText: TextView = itemView.findViewById(R.id.counterLoaded)
    }

    class RobotUserViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        // 初始化头部视图
        var user_text: TextView =
            itemView.findViewById(R.id.recyclerSmall)
        var avatar: ImageView =
            itemView.findViewById(R.id.hiddenMicro)
        var text_pop: RelativeLayout =
            itemView.findViewById(R.id.calendarInner)

        init {
            if (LanguageManager.instance!!.isLanguageForce) {
                text_pop.setBackgroundResource(R.drawable.bg_robot_a_rtl_item_bg_sapphire)
            }
        }
    }
}
