package com.juicy.app.modules.info

import android.app.Activity
import android.content.Context
import com.blankj.utilcode.util.ToastUtils
import com.juicy.common.model.event.FollowParamsEvent
import com.juicy.common.networks.delegate.AddFriendInterface.addToFriendList
import com.juicy.common.networks.delegate.CancelFriendInterface.cancelFriend
import com.juicy.common.networks.delegate.GetUserExtraInfoInterface
import com.juicy.common.networks.delegate.GetUserExtraInfoInterface.getUserExtraInfo
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.GetUserOnlineStatusInterface.getUserStatus
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.CallUtil.callHandle
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PermissionUtil.Companion.checkVideoPermission
import com.juicy.common.utils.PermissionUtil.PermissionCallback
import io.reactivex.rxjava3.disposables.Disposable
import org.greenrobot.eventbus.EventBus

class InfoPresenter(private val anchorInfoView: com.juicy.app.modules.info.IAnchorInfoView) :
    com.juicy.app.modules.info.IAnchorInfoPresenter {
    private var user: com.juicy.common.model.bean.JuicyUserInfoBean? = null

    override fun getUserInfo(userId: String) {
        user = getCacheUserInfo(userId)
        if (user != null) {
            anchorInfoView.updateInfo(user)
            anchorInfoView.changeFollowState(user?.isFriend?:false)

        }
        //        if(user == null)
//            anchorInfoView.closeLoading(false);
        getUserInfo(userId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(userBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                super.onNext(userBean)
                if (userBean.data != null) {
                    user = userBean.data
                    anchorInfoView.updateInfo(userBean.data)
                    anchorInfoView.changeFollowState(userBean.data?.isFriend?:false)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                e.printStackTrace()
                //                anchorInfoView.closeLoading(true);
            }

            override fun onComplete() {
                super.onComplete()
                //                anchorInfoView.closeLoading(true);
            }
        })
    }

    override fun getUserExtraInfo(userId: String) {
        var isUpdate = false
        val ciBean = GetUserExtraInfoInterface.getUserExtraInfo(userId)
        if (ciBean != null) {
            if (!ciBean.giftList.isNullOrEmpty()) {
                anchorInfoView.updateUserGift(ciBean.giftList)
                isUpdate = true
            }
            if (ciBean.giftList != null && !ciBean.labelsList.isNullOrEmpty()) {
                anchorInfoView.updateUserIm(ciBean.labelsList)
                isUpdate = true
            }
        }

        val finalIsUpdate = isUpdate
        getUserExtraInfo(userId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.GiftItemBean>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(ciBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.GiftItemBean>) {
                if (ciBaseBean.data != null) {
                    if (finalIsUpdate) {
                        return
                    }
                    if (!ciBaseBean.data?.giftList.isNullOrEmpty()) {
                        anchorInfoView.updateUserGift(ciBaseBean.data?.giftList)
                    } else {
                        anchorInfoView.updateUserGift(null)
                    }
                    if (ciBaseBean.data?.giftList != null && !ciBaseBean.data?.labelsList.isNullOrEmpty()) {
                        anchorInfoView.updateUserIm(ciBaseBean.data?.labelsList)
                    } else {
                        anchorInfoView.updateUserIm(null)
                    }
                }
                super.onNext(ciBaseBean)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }

    override fun addFriend(userId: String, context: Context?) {
        addToFriendList(com.juicy.common.model.bean.AnchorIDBean(userId), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(booleanBaseBean: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(booleanBaseBean)

                if (booleanBaseBean.data != null && booleanBaseBean.data == true) {
//                    anchorInfoView.changeFollowState(true);
                    EventBus.getDefault().post(FollowParamsEvent(userId, true))
                    ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Follow_Success"))
                } else {
                    val followEvent = FollowParamsEvent(userId, false)
                    followEvent.isError = true
                    EventBus.getDefault().post(followEvent)
                    ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Follow_Fail"))
                }

                //                anchorInfoView.closeLoading(true);
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                //                anchorInfoView.closeLoading(true);
                ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Follow_Fail"))
                val followEvent = FollowParamsEvent(userId, false)
                followEvent.isError = true
                EventBus.getDefault().post(followEvent)
            }
        })
    }

    override fun deleteFriend(userId: String, context: Context?) {
//        anchorInfoView.closeLoading(false);
        cancelFriend(com.juicy.common.model.bean.FollowUserIdParamsBean(userId), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onNext(booleanBaseBean: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(booleanBaseBean)

                if (booleanBaseBean.data != null && booleanBaseBean.data == true) {
//                    anchorInfoView.changeFollowState(false);
                    EventBus.getDefault().post(FollowParamsEvent(userId, false))
                    ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("unFollow_Success"))
                } else {
                    val followEvent = FollowParamsEvent(userId, false)
                    followEvent.isError = true
                    EventBus.getDefault().post(followEvent)
                    ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("unFollow_Fail"))
                }
                //                anchorInfoView.closeLoading(true);
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                //                anchorInfoView.closeLoading(false);
                val followEvent = FollowParamsEvent(userId, false)
                followEvent.isError = true
                EventBus.getDefault().post(followEvent)
            }
        })
    }


    //    @Override
    //    public void getIsFriend(String anchorId) {
    //        GetFriendsListInterface.getFriendsList(new AyBean(100, 1), new ObservableCallBack<NetBean<List<BbBean>>>() {
    //            @Override
    //            public void onNext(NetBean<List<BbBean>> followList) {
    //                super.onNext(followList);
    //                if (followList.getData() != null && !followList.getData().isEmpty() && anchorId != null) {
    //                    for (BbBean user :
    //                            followList.getData()) {
    //                        if (user.getUserId() != null && user.getUserId().equals(anchorId)) {
    //                            anchorInfoView.changeFollowState(true);
    //                            return;
    //                        }
    //
    //
    //                    }
    //                    anchorInfoView.changeFollowState(false);
    //                }
    //
    //            }
    //
    //            @Override
    //            public void onError(Throwable e) {
    //                super.onError(e);
    //            }
    //
    //            @Override
    //            public void onComplete() {
    //                super.onComplete();
    //            }
    //        });
    //    }
    override fun getUserOnlineStatus(userId: String?) {
        getUserStatus(userId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<String>>() {
            override fun onNext(stringBaseBean: com.juicy.common.model.bean.NetResponseBean<String>) {
                super.onNext(stringBaseBean)
                if (stringBaseBean.data != null && !stringBaseBean.data.isNullOrEmpty()) {
                    anchorInfoView.updateUserStatus(stringBaseBean.data?:"")
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }

    override fun videoCall(activity: Activity,userId: String?, status: String?) {
        status?.let {
            checkVideoPermission(activity, object : PermissionCallback {
                override fun complete() {
                    call(userId, it)
                }
            })
        }
    }

    fun call(userId: String?, status: String?) {
        var unitPrice = -1
        if (user != null) {
            if (Cache.instance.userInfo != null && Cache.instance.userInfo?.availableCoins != null) {
                if (Cache.instance.userInfo?.vip != null &&
                    Cache.instance.userInfo?.vip == true && user?.vipUnitPrice != null
                ) {
                    unitPrice = user?.vipUnitPrice?:0
                } else if (user?.unitPrice != null) {
                    unitPrice = user?.unitPrice?:0
                }
            }
        }

        callHandle(unitPrice, Constant.DETAIL_PAGE, userId, status, "anchor_profile_call")
    }
}
