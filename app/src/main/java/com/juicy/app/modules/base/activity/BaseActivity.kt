package com.juicy.app.modules.base.activity

import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Process
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import com.alibaba.android.arouter.launcher.ARouter
import com.juicy.common.videoCall.CallUtils
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.LanguageTool.localeLanguageCodeCountryFunc
import com.juicy.common.utils.LogBeanFactory.getCreateCallLog
import com.juicy.common.utils.PermissionUtil.SimplePermissionCallback
import com.juicy.common.utils.SpSaveUtil.getStringValue
import io.rong.imkit.utils.StatusBarUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

abstract class BaseActivity : AppCompatActivity() {
    private var permissionCallback: SimplePermissionCallback? = null
    private var permissionCallback2: SimplePermissionCallback? = null
    private var mTitle: String? = null
    private var mUser: String? = null
    private var mPayUtils: CallUtils? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        setDirection()

        super.onCreate(savedInstanceState)

        StatusBarUtil.setTranslucentStatus(this)

        EventBus.getDefault().register(this)
        //强制竖屏
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT //强制竖屏
        if (getStringValue(SpKeyPool.LANGUAGE_LAST, "") != localeLanguageCodeCountryFunc) {
            ARouter.getInstance()
                .build(Constant.SPLASH_ACTIVITY_ROUTE)
                .addFlags(
                    Intent.FLAG_ACTIVITY_NEW_TASK
                            or Intent.FLAG_ACTIVITY_CLEAR_TASK
                )
                .navigation()
            Process.killProcess(Process.myPid())
            System.exit(0)
        }
        initData()
        initView()
        transLocalText()
    }

    protected abstract fun initData()
    protected abstract fun initView()

    protected abstract fun transLocalText()


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(`object`: Any?) {
        //空接收，不然eventbus会报错
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (permissionCallback != null) {
            if (grantResults.size >= 1 && permissions.size >= 1) {
                var isGrant = true
                for (i in grantResults.indices) {
                    if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                        isGrant = false
                        break
                    }
                }
                if (isGrant) {
                    permissionCallback!!.onGranted()
                } else {
                    if (!ActivityCompat.shouldShowRequestPermissionRationale(
                            this,
                            permissions[0]
                        )
                    ) {
                        permissionCallback!!.OnNotAsk(requestCode, permissions, grantResults)
                    } else {
                        permissionCallback!!.onDenied()
                    }
                }
            }
        }
    }

    private fun  handelData(){
        mTitle = "Base"
        mUser = "1"
        mPayUtils = null
    }


    //    @Override
    //    public void onSaveInstanceState(@NonNull Bundle outState, @NonNull PersistableBundle outPersistentState) {
    //        super.onSaveInstanceState(outState, outPersistentState);
    //        restartApp();
    //    }
    fun setPermissionCallback(permissionCallback: SimplePermissionCallback?) {
        this.permissionCallback = permissionCallback
    }

    /**
     * 重启应用
     */
    fun restartApp() {
        handelData()
        try {
            // 记录日志
            if (CallUtils.instance != null &&
                CallUtils.instance?.callInfoModel != null
            ) {
                CallUtils.instance?.callInfoModel?.logList?.add(
                    getCreateCallLog(
                        "", "restart", "state_lost",
                        "状态丢失重启应用", System.currentTimeMillis()
                    )
                )
            }

            // 清理状态
            if (CallUtils.instance != null) {
                CallUtils.instance?.callInfoModel = null
                CallUtils.instance?.videoCallViewModel = null
            }

            // 使用 ARouter 重启应用
            ARouter.getInstance()
                .build(Constant.SPLASH_ACTIVITY_ROUTE)
                .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                .navigation()

            finish()
            Process.killProcess(Process.myPid())
            System.exit(0)
        } catch (e: Exception) {
            e.printStackTrace()
            // 备用重启方案
            val intent = packageManager.getLaunchIntentForPackage(
                packageName
            )
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                startActivity(intent)
                finish()
                System.exit(0)
            }
        }
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            window.decorView.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            window.decorView.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
    }
}
