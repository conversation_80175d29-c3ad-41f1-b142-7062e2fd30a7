package com.juicy.app.modules.base.adapter

import android.widget.ImageView
import com.blankj.utilcode.util.ActivityUtils
import com.juicy.app.R
import com.juicy.common.model.bean.BannerItemBean
import com.juicy.common.utils.ImageLoadingUtils.loadImage
import com.youth.banner.adapter.BannerImageAdapter
import com.youth.banner.holder.BannerImageHolder

class BannerAdapter(mData: List<com.juicy.common.model.bean.BannerItemBean?>?, private val callBack: CallBack) :
    BannerImageAdapter<com.juicy.common.model.bean.BannerItemBean?>(mData) {
    private val isMove = false

    override fun onBindView(holder: BannerImageHolder, data: com.juicy.common.model.bean.BannerItemBean?, position: Int, size: Int) {
        holder.imageView.scaleType = ImageView.ScaleType.CENTER_CROP

        if (data?.bizType != null &&
            data.bizType == "-10024"
        ) {
            //读取本地图片
            holder.imageView.setImageResource(R.mipmap.bg_community_banner_teal)
        } else {
            loadImage(
                ActivityUtils.getTopActivity(),
                data!!.pic,
                R.drawable.img_banner_empty_crimson,
                holder.imageView,
                false
            )
        }

        //        ImageLoadingUtils.loadImage(ActivityUtils.getTopActivity(),data.getPic(),holder.imageView);
        holder.imageView.setOnClickListener {
            callBack.onCallBack(
                data,
                position
            )
        }
    }

    interface CallBack {
        fun onCallBack(bannerData: com.juicy.common.model.bean.BannerItemBean, position: Int)
    }
}
