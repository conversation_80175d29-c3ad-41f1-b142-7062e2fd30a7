package com.juicy.app.modules.call.fragment

import android.graphics.Color
import android.media.MediaPlayer
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.juicy.app.R
import com.juicy.app.databinding.FragmentTextBinding
import com.juicy.app.modules.call.VideoCallActivity
import com.juicy.common.videoCall.CallUtils
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.model.message_event.AppOnCallBean
import com.juicy.common.model.message_event.AppOnHangUpBean
import com.juicy.common.model.message_event.AppOnPickupBean
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.LogBeanFactory.getCreateCallLog
import jp.wasabeef.glide.transformations.BlurTransformation

class CallFragment : BaseFragment() {
    private var callBinding: FragmentTextBinding? = null


    //  private final long[] pattern = {200L, 200L, 600L, 800L};
    private var appOnCallBean: AppOnCallBean? = null
    private var user: com.juicy.common.model.bean.JuicyUserInfoBean? = null
    //铃声播放器
    private var mediaPlayer: MediaPlayer? = null
    // 震动处理
    private var socketEventBusBack: SocketEventBusBack? = null
    private var activity: VideoCallActivity? = null

    override fun initData() {
        activity = getActivity() as? VideoCallActivity?
        mediaPlayer = MediaPlayer.create(context, R.raw.ic_oldcall_cyan)
        mediaPlayer?.isLooping = true
        initEvent()
        //开始请求数据
        CallUtils.instance?.createChannel()
        CallUtils.getCallViewModel()?.loadCacheUserData(CallUtils.instance?.callInfoModel?.anchorId)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }



    private fun initEvent() {


        CallUtils.getCallViewModel()?.hangUpData?.observe(
            this,
            Observer<Boolean> { aBoolean: Boolean? ->
                aBoolean?.let {
                    if (it){
                        requireActivity().finish()
                    }
                }

            })
        socketEventBusBack = object : SocketEventBusBack {
            override fun hangup(appOnHangUpBean: AppOnHangUpBean?) {
                super.hangup(appOnHangUpBean)
                callBinding!!.compactBorder.isEnabled = false
                ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("The_user_is_not_available_now_please_try_again_later"))
                CallUtils.instance?.hangUp(Constant.UNANSWERED_CALL, Constant.CL_REMOTE_USER_LEFT)
                activity?.finish()
            }

            override fun pickUp(appOnPickupBean: AppOnPickupBean?) {
                super.pickUp(appOnPickupBean)
                if (user != null) {
                    if (CallUtils.instance?.callInfoModel != null) {
                        CallUtils.instance?.callInfoModel?.callType = (Constant.OUTGOING_CALL)
                        activity?.jumpCallingPage(user)
                    }
                }
            }
        }
        addMessageEvent(socketEventBusBack!!)
        //监听挂断
        CallUtils.getCallViewModel()?.createCallError?.observe(this,
            Observer<String> { error: String? ->
                if (activity?.isFinishing == true || error == null) {
                    return@Observer
                }
                ToastUtils.showShort(
                    LanguageManager.instance?.getLocalTranslate(
                        error?:""
                    )
                )
                if (CallUtils.instance?.callInfoModel != null) {
                    CallUtils.instance?.callInfoModel?.logList?.add(
                        getCreateCallLog(
                            "",
                            "call_resp",
                            "error",
                            "创建通话频道失败",
                            System.currentTimeMillis()
                        )
                    )
                }
                activity?.finish()
            })
        CallUtils.getCallViewModel()?.userData?.observe(
            this,
            Observer<com.juicy.common.model.bean.JuicyUserInfoBean> { user: com.juicy.common.model.bean.JuicyUserInfoBean ->
                initUser(user)
                this.user = user
                if (CallUtils.instance?.callInfoModel != null) {
                    CallUtils.instance?.callInfoModel?.anchorName = (
                            user.nickname
                            )
                    CallUtils.instance?.callInfoModel?.anchorPhoto = (
                            user.avatarUrl
                            )
                }
            })
        //监听创建频道结果
        CallUtils.getCallViewModel()?.createCallData?.observe(
            this,
            Observer<AppOnCallBean> { appOnCallBean: AppOnCallBean? ->
                <EMAIL> = appOnCallBean
                if (activity?.isFinishing == true) {
                    return@Observer
                }
                if (appOnCallBean != null && CallUtils.instance?.callInfoModel != null) {
                    if (appOnCallBean.channelName != null && appOnCallBean.channelName == Constant.CALLING_USER_IS_BUSY_CODE.toString()) {
                        CallUtils.instance?.callInfoModel?.logList?.add(
                            getCreateCallLog(
                                appOnCallBean.channelName?:"",
                                "call_resp",
                                "calling user is busy",
                                "创建通话频道失败",
                                System.currentTimeMillis()
                            )
                        )
                        ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("User_is_Busy"))
                        activity?.finish()
                    } else {
                        CallUtils.instance?.callInfoModel?.logList?.add(
                            getCreateCallLog(
                                appOnCallBean.channelName?:"",
                                "call_resp",
                                "ok",
                                "创建通话频道成功",
                                System.currentTimeMillis()
                            )
                        )
                        CallUtils.instance?.callInfoModel?.channelName = (appOnCallBean.channelName?:"")
                        CallUtils.instance?.callInfoModel?.rToken = (appOnCallBean.rtcToken?:"")
                        mediaPlayer?.start()
                    //  VibratorUtils.getInstance().getVibrator().vibrate(pattern, 1);
                    }
                } else {
                    ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("calling_user_is_busy"))
                    if (CallUtils.instance?.callInfoModel != null) {
                        CallUtils.instance?.callInfoModel?.logList?.add(
                            getCreateCallLog(
                                "",
                                "call_resp",
                                "error",
                                "创建通话频道失败",
                                System.currentTimeMillis()
                            )
                        )
                    }
                    activity?.finish()
                }
            })
    }
    override fun initView() {
        if (Cache.instance.reviewPkg) callBinding!!.copyPanel.visibility =
            View.INVISIBLE
        callBinding!!.compactBorder.setOnClickListener { v: View? ->
            if (CallUtils.instance?.callInfoModel != null) {
                CallUtils.instance?.callInfoModel?.callType = (Constant.CANCELLED_CALL)
                if (appOnCallBean != null) {
                    callBinding!!.compactBorder.isEnabled = false
                    CallUtils.instance?.hangUp(
                        Constant.CANCELLED_CALL,
                        Constant.NORMAL
                    )
                }
                activity?.finish()
            }
        }
    }

    private fun initUser2(user: com.juicy.common.model.bean.JuicyUserInfoBean){
        callBinding!!.reloadDisabled.text = user.age.toString()
        if (user.country != null) {
            callBinding!!.fieldUpdate.text = user.country
        }
        val price =
            if (Cache.instance.userInfo != null && Cache.instance.userInfo?.vip != null && Cache.instance.userInfo?.vip == true) {
                user.vipUnitPrice
            } else {
                user.unitPrice
            }
        val anchorPay = LanguageManager.instance?.getLocalTranslate("You_will_be_charged") + " " + price + " " +LanguageManager.instance?.getLocalTranslate("coins_per_minute")
//
//        // 创建富文本
        // val text = anchorPay
        val ssb =  SpannableStringBuilder(anchorPay)
        val start = anchorPay.indexOf(price.toString())
        val end = start + price.toString().length
        ssb.setSpan( ForegroundColorSpan(Color.parseColor("#FFFA5151")), start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
        callBinding!!.copyPanel.text = ssb

    }

    private fun initUser(user: com.juicy.common.model.bean.JuicyUserInfoBean) {
        loadCircleImage(activity, user.avatarUrl, callBinding!!.wrapClosed)
        Glide.with(this)
            .load(user.avatarUrl)
            .apply(RequestOptions.bitmapTransform(BlurTransformation(20)))
            .into(callBinding!!.xlAdd)

        if (user.nickname != null) {
            callBinding!!.hiddenForeground.text = user.nickname
        }
        if (user.gender == 1) {
            callBinding!!.reloadDisabled.setBackgroundResource(com.juicy.app.R.drawable.bg_male_blue)
            // callBinding!!.rightThin.setImageResource(R.drawable.ic_call_sex_1_navy)
        } else {
            // callBinding!!.rightThin.setImageResource(R.drawable.ic_call_sex_0_lead)
            callBinding!!.reloadDisabled.setBackgroundResource(com.juicy.app.R.drawable.bg_famel_olive)
        }
        initUser2(user);
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        callBinding = FragmentTextBinding.inflate(inflater)
        return callBinding!!.root
    }

    override fun onDestroy() {
        removeMessageEvent(socketEventBusBack!!)
        if (mediaPlayer != null) {
            mediaPlayer?.release()
            mediaPlayer = null
        }
        //    VibratorUtils.getInstance().getVibrator().cancel();
        activity?.closeTime()
        super.onDestroy()
    }
}