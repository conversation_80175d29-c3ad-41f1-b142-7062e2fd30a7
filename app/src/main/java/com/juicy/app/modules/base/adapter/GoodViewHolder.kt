package com.juicy.app.modules.base.adapter

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R
import com.juicy.common.utils.LanguageManager

class GoodViewHolder(view: View) : BaseViewHolder(view) {
    var good: com.juicy.common.model.bean.ActivityInfoBean? = null
    var tvCoin: AppCompatTextView
    var goodTag: AppCompatTextView =
        view.findViewById(R.id.cancelChip)
    var extra: AppCompatTextView
    var goodItem: ConstraintLayout
    var icon: AppCompatImageView
    var clVipKing: ConstraintLayout
    var vipAddText: TextView
    var vipTextIcon: ImageView
    var tvOff: AppCompatTextView
    var buy: AppCompatTextView


    init {
        if (LanguageManager.instance!!.isLanguageForce) {
            val params = goodTag.layoutParams as ConstraintLayout.LayoutParams
            params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
            params.endToEnd = ConstraintLayout.LayoutParams.UNSET
            goodTag.layoutParams = params
        }
        buy = view.findViewById(R.id.maskMicro)
        extra = view.findViewById(R.id.offlineRigid)
        icon = view.findViewById(R.id.mediumLast)
        clVipKing = view.findViewById(R.id.progressDimmed)
        goodItem = view.findViewById(R.id.innerGrid)
        tvCoin = view.findViewById(R.id.bodyCard)
        tvOff = view.findViewById(R.id.selectedExpanded)
        vipAddText = view.findViewById(R.id.upSelect)
        vipTextIcon = view.findViewById(R.id.clearError)

    }
}