package com.juicy.app.modules.base.dialog

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.os.CountDownTimer
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.chad.library.adapter.base.listener.OnItemChildClickListener
import com.juicy.app.CoreActivity
import com.juicy.app.R
import com.juicy.app.modules.base.adapter.GoodAdapter
import com.juicy.app.databinding.ItemShopBinding
import com.juicy.common.model.bean.PlayParamsBean
import com.juicy.common.model.event.PromotionPayStatusChangeEvent
import com.juicy.common.model.event.NewUserRewardsShowStatusChangeEvent
import com.juicy.common.model.message_event.AppRechargeOrderStatusBean
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.networks.delegate.GetCoinGoodsSearchInterface.getCoinGoodsSearch
import com.juicy.common.networks.delegate.GetRechargeInfoInterface.rechargeInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.DateMetaUtil.ms2HMS
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.AppDeviceUtils.getScreenSize
import com.juicy.common.utils.GlideImageUtil.load
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PayUtils.PayUtils
import com.juicy.common.utils.PayUtils.PayUtils.FailOnListen
import com.juicy.common.utils.PayUtils.PayUtils.SuccessOnListen
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.abs

class AnchorShoppingDialog : DialogFragment {
    private var anchorImgUrl: String? = null
    private var nickName: String? = null
    private var anchorFullShopBinding: ItemShopBinding? = null
    private val coinList: MutableList<com.juicy.common.model.bean.ActivityInfoBean?> = ArrayList()
    private var socketEventBusBack: SocketEventBusBack? = null
    private var countDownTimer: CountDownTimer? = null
    private var invitationId = ""
    private var selectPosition = -1

    @JvmField
    var entry: String = "after_free_call"


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(`object`: PromotionPayStatusChangeEvent?) {
        EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
        if (getBooleanVal(SpKeyPool.HAS_EVALUATE, false) == false) {
            putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
            activity?.let {
                if (!isDestroy(it)) {
                    val assessDialog = AssessDialog()
                    assessDialog.show(it.supportFragmentManager, "")
                }
            }
        }
        if (coinList[selectPosition]?.promotion == true && Cache.instance.userPromotionGood != null && selectPosition == 0) {
            Cache.instance.userPromotionGood = null
        }
        dismiss()
    }

    constructor()

    constructor(invitationId: String, nickName: String?, anchorImgUrl: String?) {
        this.anchorImgUrl = anchorImgUrl
        this.invitationId = invitationId
        this.nickName = nickName
    }

    constructor(nickName: String?, anchorImgUrl: String?) {
        this.nickName = nickName
        this.anchorImgUrl = anchorImgUrl
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        anchorFullShopBinding = ItemShopBinding.inflate(inflater)
        return anchorFullShopBinding!!.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setDirection()
        //        anchorFullShopBinding.emptyBadge.setOnClickListener(v -> dismiss());
        initData()
        initView()
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            anchorFullShopBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_RTL
            anchorFullShopBinding!!.xxlUpper.layoutDirection = View.LAYOUT_DIRECTION_RTL
        } else {
            anchorFullShopBinding!!.root.layoutDirection = View.LAYOUT_DIRECTION_LTR
        }
        val scaleX = if (LanguageManager.instance!!.isLanguageForce) -1f else 1f
        //        anchorFullShopBinding.thinLeft.setText(LanguageManager.getInstance().getLocalTranslate("Coin_Store"));
//        anchorFullShopBinding.emptyBadge.setScaleX(scaleX);
        anchorFullShopBinding!!.updateConfirm.text =
            LanguageManager.instance!!.getLocalTranslate("Recharge_to_call_back")
        anchorFullShopBinding!!.lockedFill.text =
            LanguageManager.instance!!.getLocalTranslate("Customer_Service")
    }

    private fun initView() {
        if (invitationId.isEmpty()) {
            anchorFullShopBinding!!.avatarLarge.visibility = View.VISIBLE
            anchorFullShopBinding!!.wrapClosed.visibility = View.VISIBLE
            anchorFullShopBinding!!.updateConfirm.visibility = View.VISIBLE
            anchorFullShopBinding!!.sortLoading.visibility = View.GONE
            anchorFullShopBinding!!.avatarLarge.append(LanguageManager.instance!!.getLocalTranslate("Do_you_like"))
            if (nickName != null) {
                anchorFullShopBinding!!.avatarLarge.append(nickName)
            }
            anchorFullShopBinding!!.wrapClosed.borderColor = activity?.getColor(R.color.white)?:0
            anchorFullShopBinding!!.wrapClosed.borderWidth = dip2px(2f)

            load(context, anchorImgUrl, anchorFullShopBinding!!.wrapClosed)
        } else {
            anchorFullShopBinding!!.avatarLarge.visibility = View.VISIBLE
            anchorFullShopBinding!!.wrapClosed.visibility = View.VISIBLE
            anchorFullShopBinding!!.updateConfirm.visibility = View.GONE
            anchorFullShopBinding!!.sortLoading.visibility = View.VISIBLE
            anchorFullShopBinding!!.avatarLarge.text =
                LanguageManager.instance!!.getLocalTranslate("Getting_discounts")
            anchorFullShopBinding!!.sortLoading.text =
                LanguageManager.instance!!.getLocalTranslate("Get_more_10_discount")
            load(context, anchorImgUrl, anchorFullShopBinding!!.wrapClosed)
        }

        if (Cache.instance.reviewPkg) {
            anchorFullShopBinding!!.gridExpand.visibility = View.GONE
        }

        anchorFullShopBinding!!.sliderBar.setOnClickListener {
            dismiss()
        }

        anchorFullShopBinding!!.gridExpand.setOnClickListener { v: View? ->
            startActivity(
                Intent(
                    requireContext(),
                    com.juicy.app.modules.base.activity.BotActivity::class.java
                )
            )
        }


        if (invitationId != "") {
            anchorFullShopBinding!!.headerAbove.visibility = View.VISIBLE
            anchorFullShopBinding!!.headerAbove.loadBanner()
        }

        anchorFullShopBinding!!.xxlUpper.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                outRect[dip2px(8f), dip2px(8f), dip2px(8f)] =
                    dip2px(8f)
            }
        })
    }

    private fun initData() {
        Cache.instance.payChannle
        if (!invitationId.isEmpty()) {
            rechargeInfo(
                com.juicy.common.model.bean.InvitationGoodParamsBean(invitationId),
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>() {
                    override fun onNext(goodsData: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>) {
                        super.onNext(goodsData)
                        if (goodsData.data != null && !goodsData.data!!.isEmpty()) {
                            initGoodsRv(goodsData.data!!)
                        }
                    }

                    override fun onError(e: Throwable) {
                        super.onError(e)
                    }
                })
        } else {
            if (Cache.instance.coinGoods != null && !Cache.instance.coinGoods!!.isEmpty()) {
                initGoodsRv(Cache.instance.coinGoods!!)
            }
            resetGoodData()
        }
    }

    private fun resetGoodData() {
        getCoinGoodsSearch(
            context,
            PlayParamsBean(true),
            object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>>() {
                override fun onNext(goodsData: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.ActivityInfoBean>>) {
                    super.onNext(goodsData)
                    if (goodsData.data != null && !goodsData.data!!.isEmpty()) {
                        val coinGoods: MutableList<com.juicy.common.model.bean.ActivityInfoBean> = ArrayList()
                        for (i in goodsData.data!!.indices) {
                            val bhBean = goodsData.data!![i]
                            if (bhBean.type == "1") { //订阅
                                Cache.instance.subscribeGood = bhBean
                            } else {
                                coinGoods.add(bhBean)
                            }
                        }
                        Cache.instance.coinGoods = coinGoods
                        PayUtils(ActivityUtils.getTopActivity()).batchQueryPurchases()
                        initGoodsRv(coinGoods)
                    }
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                }
            })
    }

    private fun initGoodsRv(coinGoods: List<com.juicy.common.model.bean.ActivityInfoBean?>) {
        val gridLayoutManager = GridLayoutManager(
            activity, 2
        )

        anchorFullShopBinding!!.xxlUpper.layoutManager = gridLayoutManager
        val goodAdapter = GoodAdapter(R.layout.item_good)
        anchorFullShopBinding!!.xxlUpper.adapter = goodAdapter
        setData(goodAdapter, coinGoods)
    }

    private fun setData(goodAdapter: GoodAdapter, coinGoods: List<com.juicy.common.model.bean.ActivityInfoBean?>) {
        socketEventBusBack = object : SocketEventBusBack {
            override fun rechargeSuccessBack(appRechargeOrderStatusBean: AppRechargeOrderStatusBean?) {
                if (appRechargeOrderStatusBean!!.status == 2) {
                    if (null != appRechargeOrderStatusBean.orderNo && null != appRechargeOrderStatusBean.code && !Cache.instance.thirdPayGRLogMap.isEmpty()) {
                        if (Cache.instance.thirdPayGRLogMap.containsKey(
                                appRechargeOrderStatusBean.orderNo
                            )
                        ) {
                            if (selectPosition == 0 && Cache.instance.userPromotionGood != null && coinGoods[selectPosition]!!.promotion!!
                            ) {
                                EventBus.getDefault().post(NewUserRewardsShowStatusChangeEvent())
                            }
                            dismissAllowingStateLoss()
                        }
                    }
                    EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
                }
            }
        }
        coinList.clear()
        coinList.addAll(coinGoods)
        for (i in coinList.indices) {
            if (coinList[i]?.promotion == true) {
                coinList.removeAt(i)
                break
            }
        }
        setProGood(goodAdapter)
        if (Cache.instance.userInfo != null && Cache.instance.userInfo?.vip == false) {
            if (Cache.instance.subscribeGood != null) {
                for (i in coinList.indices) {
                    if (coinList[i]?.exchangeCoin == Cache.instance.subscribeGood?.exchangeCoin) {
                        Cache.instance.subscribeGood?.isVipItem = true
                        coinList.add(i + 1, Cache.instance.subscribeGood)
                        break
                    }
                }
            }
        }
        goodAdapter.setNewInstance(coinList)
        goodAdapter.addChildClickViewIds(R.id.innerGrid)
        goodAdapter.setOnItemChildClickListener(OnItemChildClickListener { _, _, position ->
            if (isFastClick) return@OnItemChildClickListener
            selectPosition = position
            if (coinList[position]?.isVipItem ==  true) {
                val vipDialog = VipDialogFragment(false)
                val currentActivity =
                    currentActivity as CoreActivity? ?:return@OnItemChildClickListener
                vipDialog.show(currentActivity.supportFragmentManager, "")
            } else {
                if (invitationId.isEmpty()) {
                    if (activity == null) return@OnItemChildClickListener
                    PayUtils(activity, entry, false, coinList[position], object : SuccessOnListen {
                        override fun onListen() {
                            EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
                            if (getBooleanVal(SpKeyPool.HAS_EVALUATE, false) == false) {
                                putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
                                if (!isDestroy(activity)) {
                                    val assessDialog = AssessDialog()
                                    assessDialog.show(activity!!.supportFragmentManager, "")
                                }
                            }
                            if (Cache.instance.userPromotionGood != null && position == 0) {
                                EventBus.getDefault()
                                    .post(NewUserRewardsShowStatusChangeEvent())
                            }
                            dismiss()
                        }
                    }) { }.openPayDialog()
                } else {
                    if (activity == null)return@OnItemChildClickListener
                    PayUtils(activity, entry, coinList[position], object : SuccessOnListen {
                        override fun onListen() {
                            EventBus.getDefault().post(com.juicy.common.model.bean.MyCoinsNumMessage())
                            if (getBooleanVal(SpKeyPool.HAS_EVALUATE, false) == false) {
                                putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
                                if (!isDestroy(activity)) {
                                    val assessDialog = AssessDialog()
                                    assessDialog.show(activity!!.supportFragmentManager, "")
                                }
                            }
                            if (coinList[position]?.promotion == true && Cache.instance.userPromotionGood != null && position == 0) {
                                Cache.instance.userPromotionGood = null
                            }
                            dismiss()
                        }
                    }, object : FailOnListen {
                        override fun onListen() {
                        }
                    }, invitationId, true).openPayDialog()
                }
            }
        })
        addMessageEvent(socketEventBusBack!!)
    }

    private fun setProGood(goodAdapter: GoodAdapter) {
        if (Cache.instance.userPromotionGood != null) {
            coinList.add(0, Cache.instance.userPromotionGood)
            setProTime(Cache.instance.userPromotionGood, goodAdapter)
        }
    }

    private fun setProTime(userPromotionGood: com.juicy.common.model.bean.ActivityInfoBean?, goodAdapter: GoodAdapter) {
        if (countDownTimer != null) {
            countDownTimer?.cancel()
            countDownTimer = null
        }
        if (Cache.instance.userPromotionGood == null) return
        val surplusMillisecond =
            Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
        countDownTimer = object : CountDownTimer(surplusMillisecond, 1000) {
            @SuppressLint("NotifyDataSetChanged")
            override fun onTick(l: Long) {
                if (Cache.instance.userPromotionGood != null) {
                    val surplusMillisecond =
                        Cache.instance.userPromotionGood!!.surplusMillisecond - (System.currentTimeMillis() - Cache.instance.userPromotionCreateTime)
                    val time = ms2HMS(surplusMillisecond)
                    Cache.instance.userPromotionGood!!.tags = time
                    //                    goodAdapter.setData(0, Cache.getInstance().userPromotionGood);
                    val bean = Cache.instance.userPromotionGood
                    bean?.let {
                        goodAdapter.setNewSaleTag(it)
                        goodAdapter.notifyDataSetChanged()
                    }

                    if (abs((l - surplusMillisecond).toDouble()) > 1000) {
                        setProTime(userPromotionGood, goodAdapter)
                    }
                }
            }

            override fun onFinish() {
                Cache.instance.userPromotionGood = null
                dismiss()
            }
        }.start()
    }


    private fun cancelTimer() {
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
    }

    private var mInitialTouchY = 0f
    @SuppressLint("ClickableViewAccessibility")
    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            dialog!!.window?.setBackgroundDrawable(null)
            dialog!!.window?.setGravity(Gravity.BOTTOM)
            val layoutParams = dialog!!.window?.attributes
            layoutParams?.height = getScreenSize(this.context)[1] - ((getScreenSize(
                this.context
            )[1]) * 0.2).toInt()
            layoutParams?.width = WindowManager.LayoutParams.MATCH_PARENT
            dialog!!.window?.attributes = layoutParams

            dialog!!.window?.decorView?.setOnTouchListener { v: View?, event: MotionEvent ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN ->                         // 记录按下时的 y 坐标
                        mInitialTouchY = event.y

                    MotionEvent.ACTION_UP -> {
                        // 计算滑动距离
                        val deltaY = event.y - mInitialTouchY
                        // 如果滑动距离大于等于 50pt 则关闭对话框
                        if (deltaY >= dip2px(50f)) {
                            dismiss()
                        }
                    }
                }
                false
            }
        }
    }

    override fun dismiss() {
        cancelTimer()
        removeMessageEvent(socketEventBusBack!!)
        super.dismiss()
    }
}
