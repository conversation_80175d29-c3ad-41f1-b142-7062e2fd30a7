package com.juicy.app.modules.call.adapter

import android.annotation.SuppressLint
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ImageSpan
import android.util.LayoutDirection
import android.view.Gravity
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.text.TextUtilsCompat
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.app.R
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant.giftResMap
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.TranslateUtils.textTrans
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.functions.Function
import io.reactivex.rxjava3.schedulers.Schedulers
import java.util.Locale

class VideoCallAdapter(layoutResId: Int) : BaseQuickAdapter<com.juicy.common.model.bean.ChatMessageItemBean?, VideoCallItemViewHolder>(layoutResId) {
    @SuppressLint("CheckResult")
    override fun convert(holder: VideoCallItemViewHolder, item: com.juicy.common.model.bean.ChatMessageItemBean?) {
        if (item == null) return
        if (item.avatar != null) Glide.with(context).load(item.avatar).into(holder.anchorPhoto)
        holder.anchorPhoto.setOnClickListener { v: View? -> }
        if (TextUtilsCompat.getLayoutDirectionFromLocale(Locale.getDefault()) == LayoutDirection.RTL) {
            holder.imMessage.gravity = Gravity.RIGHT
        }
        if (item.itemType == 1) {
            if (item.content != null) holder.imMessage.text = item.content
            holder.gift.visibility = View.GONE
            holder.transitionContent.visibility = View.GONE

            if (item.fromUserId != null && Cache.instance.userInfo != null && Cache.instance.userInfo!!.userId != null && item.fromUserId != Cache.instance.userInfo!!.userId) {
                if (item.translateContent != null) {
                    holder.line.visibility = View.VISIBLE
                    holder.transitionContent.visibility = View.VISIBLE
                    setTransContent(holder, item.translateContent)
                    holder.progressBar.visibility = View.GONE
                } else {
                    holder.progressBar.visibility = View.GONE
                    holder.line.visibility = View.GONE
                    if (item.content != null) {
                        Observable
                            .just(1)
                            .subscribeOn(Schedulers.newThread())
                            .map(Function<Int, String> { integer: Int ->
                                textTrans(
                                    item.content?:""
                                )?:""
                            }
                            ).observeOn(AndroidSchedulers.mainThread())
                            .subscribe(
                                { s: String? ->
                                    if (s != null && holder != null) {
                                        holder.line.visibility = View.VISIBLE
                                        holder.transitionContent.visibility = View.VISIBLE
                                        setTransContent(holder, s)
                                        item.translateContent = s
                                        holder.progressBar.visibility = View.GONE
                                        recyclerView.scrollToPosition(itemCount - 1)
                                    } else {
                                        ToastUtils.showShort(
                                            LanguageManager.instance!!.getLocalTranslate(
                                                "Translate Failed"
                                            )
                                        )
                                    }
                                },
                                { throwable: Throwable? -> })
                    }
                }
            } else {
                holder.progressBar.visibility = View.GONE
                holder.line.visibility = View.GONE
            }
        } else if (item.itemType == 2) {
            holder.line.visibility = View.GONE
            holder.transitionContent.visibility = View.GONE
            holder.progressBar.visibility = View.GONE
            holder.gift.visibility = View.VISIBLE
            holder.imMessage.text =
                LanguageManager.instance!!.getLocalTranslate("I_send_your_a_gift")
            if (item.content != null && giftResMap!![item.content!!] != null) {
                holder.gift.setImageResource(giftResMap!![item.content!!]!!)
            }
        }
    }

    private fun setTransContent(videoCallItemViewHolder: VideoCallItemViewHolder, content: String?) {
        val builder = SpannableStringBuilder()

        // 添加图片
        val drawable = ContextCompat.getDrawable(context, R.mipmap.ic_trans_ok_tan)
        if (drawable != null) {
            drawable.setBounds(0, 2, dip2px(14f), dip2px(12f))
            val imageSpan = ImageSpan(drawable, ImageSpan.ALIGN_BASELINE)
            builder.append(" ") // 为图片预留位置
            builder.setSpan(imageSpan, 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }

        // 添加文本内容
        builder.append(" ").append(content) // 添加一个空格作为间隔

        // 设置富文本
        videoCallItemViewHolder.transitionContent.text = builder
    }
}

