package com.juicy.app.modules.base.activity

import android.annotation.TargetApi
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.net.Uri
import android.os.Build
import android.text.TextUtils
import android.util.Log
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient

class AppWebClient(private val mContext: Activity) : WebViewClient() {
    private val HTTP_SCHEMES: List<String?> = mutableListOf<String?>("http", "https")
    override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
        Log.d(TAG, "shouldOverrideUrlLoading url1=$url")
        if (shouldOverrideUrlLoadingInner(view, url)) {
            return true
        }
        return super.shouldOverrideUrlLoading(view, url)
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest?): Boolean {
        val url = if (request != null && request.url != null) request.url.toString() else ""
        Log.d(
            TAG, "shouldOverrideUrlLoading url=" + (request?.url?.toString()
                ?: "")
        )
        if (shouldOverrideUrlLoadingInner(view, url)) {
            return true
        }
        return super.shouldOverrideUrlLoading(view, request)
    }

    /**
     * Parse the url and open it by system function.
     * case 1: deal "intent://xxxx" url.
     * case 2: deal custom scheme. url
     *
     * @param view: WebView
     * @param url
     * @return
     */
    private fun shouldOverrideUrlLoadingInner(view: WebView, url: String): Boolean {
        if (!TextUtils.isEmpty(url)) {
            val uri = Uri.parse(url)
            if (uri != null) {
                if ("intent" == uri.scheme) {
                    try {
                        val intent = Intent.parseUri(uri.toString(), Intent.URI_INTENT_SCHEME)
                        if (intent != null) {
                            val pm = mContext.packageManager

                            val info: ResolveInfo? = null
                            pm?.resolveActivity(
                                intent,
                                PackageManager.MATCH_DEFAULT_ONLY
                            )
                            if (info != null) {
                                mContext.startActivity(
                                    Intent.parseUri(
                                        uri.toString(),
                                        Intent.URI_INTENT_SCHEME
                                    )
                                )
                                return true
                            } else {
                                val fallbackUrl = intent.getStringExtra("browser_fallback_url")
                                if (!TextUtils.isEmpty(fallbackUrl)) {
                                    if (fallbackUrl!!.startsWith("market://")) startAppMarketWithUrl(
                                        mContext,
                                        fallbackUrl,
                                        false
                                    )
                                    else view.loadUrl(fallbackUrl)
                                    return true
                                }
                            }
                        }
                    } catch (e: Exception) {
                    }
                }
                if (!HTTP_SCHEMES.contains(uri.scheme)) {
                    startUrl(mContext, url, true)
                    return true
                }
            }
        }

        return false
    }

    companion object {
        private const val TAG = "TestWebViewClient"
        fun startUrl(context: Context?, url: String?, isNewTask: Boolean) {
            if (context != null && !TextUtils.isEmpty(url)) {
                try {
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                    if (isNewTask) {
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    context.startActivity(intent)
                } catch (e: Exception) {
                }
            }
        }

        fun hasActivity(context: Context, intent: Intent, packageName: String): Boolean {
            val pm = context.packageManager ?: return false
            val appList = pm.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)

            for (info in appList) {
                if (info.activityInfo.packageName == packageName) return true
            }
            return false
        }

        fun startAppMarketWithUrl(context: Context, url: String?, forceUseGoogle: Boolean) {
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                if (forceUseGoogle || hasActivity(
                        context,
                        intent,
                        "com.android.vending"
                    )
                ) intent.setPackage("com.android.vending")
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
            } catch (e: Exception) {
                try {
                    startUrl(context, url, true)
                } catch (e1: Exception) {
                }
            }
        }
    }
}
