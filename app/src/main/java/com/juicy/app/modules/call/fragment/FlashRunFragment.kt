package com.juicy.app.modules.call.fragment

import android.graphics.Color
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.content.res.AppCompatResources
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.R
import com.juicy.app.databinding.FragmentRunBinding
import com.juicy.app.modules.call.VideoCallActivity
import com.juicy.common.videoCall.CallUtils
import com.juicy.common.videoCall.CallUtils.StartFlashEventBack
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.model.bean.VideoCallInfoBean
import com.juicy.common.model.message_event.AppOnHangUpBean
import com.juicy.common.model.message_event.AppOnPickupBean
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.networks.delegate.GetCoinCountInterface.getCoin
import com.juicy.common.networks.delegate.GetProcedureInterface.GetProcedure
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.ImageLoadingUtils.loadImage
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.SpSaveUtil.getStringValue
import java.util.Timer
import java.util.TimerTask

class FlashRunFragment : BaseFragment() {
    private var socketEventBusBack: SocketEventBusBack? = null
    private var binding: FragmentRunBinding? = null
    private var matchWheels: List<String>? = null
    private var matchWheelsTimer: Timer? = null
    private var matchWheelIndex = 0
    private var isHaveResult = false //是否匹配到结果
    private var isHavePreResult = false //匹配到了结果但是还没请求个人信息
    private var countDownTimer: CountDownTimer? = null // 30s倒计时器
    private var isHaveCloseTips = false
    private var falshResult: VideoCallInfoBean? = null
    private var resultUserId = ""
    private var resultCountDownTimer: CountDownTimer? = null
    private var activity: VideoCallActivity? = null

    private var isAdded = false
    override fun initData() {
        activity = getActivity() as VideoCallActivity?


        randomBroadcasterText
        startFalsh()

        socketEventBusBack = object : SocketEventBusBack {
            override fun hangup(appOnHangUpBean: AppOnHangUpBean?) {
                super.hangup(appOnHangUpBean)
                if (falshResult == null) return
                if (falshResult!!.channelName == appOnHangUpBean!!.channelName) {
                    if (appOnHangUpBean.fromUserId == resultUserId) {
                        //中途挂断。//重新发起匹配
                        ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("The_anchor_has_left_and_will_be_matched_again_for_you"))
                        if (!isMyClose) {
                            activity?.resetMatch(true)
                        }
                    } else {
                        //用户挂断
                        if (!isMyClose) {
                            ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("User_hung_up_please_try_again"))
                            back()
                        }
                    }
                }
            }

            override fun pickUp(appOnPickupBean: AppOnPickupBean?) {
                super.pickUp(appOnPickupBean)
                if (falshResult == null) return
                val myUserId = getStringValue(SpKeyPool.USER_ID_DEVICE, "")
                if (!isHavePreResult && falshResult!!.channelName == appOnPickupBean!!.channelName && myUserId == appOnPickupBean.toUserId) {
                    getUserInfo(appOnPickupBean.fromUserId!!)
                }
            }
        }
        socketEventBusBack?.let {
            addMessageEvent(it)
        }
    }

    private var isMyClose = false
    override fun initView() {
        binding!!.closeWide.setOnClickListener { view: View? ->
            activieBack()
        }
        if (LanguageManager.instance!!.isLanguageForce) {
            binding!!.closeWide.scaleX = -1f
        }
        binding!!.tabLast.text = LanguageManager.instance!!.getLocalTranslate("Matching")
    }

    private var isHaveClose = false

    fun activieBack() {
        isMyClose = true
        back()
    }

    fun back() {
        if (!isHaveClose) {
            isHaveClose = true

            stopFalsh()
            if (getActivity() != null) {
                CallUtils.instance?.macthClientSessionId = ""
                getActivity()?.finish()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentRunBinding.inflate(inflater)
        return binding!!.root
    }

    override fun onDestroy() {
        super.onDestroy()
        removeMessageEvent(socketEventBusBack!!)
        if (matchWheelsTimer != null) {
            matchWheelsTimer!!.cancel()
            matchWheelsTimer = null
        }
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
        if (resultCountDownTimer != null) {
            resultCountDownTimer!!.cancel()
            resultCountDownTimer = null
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
    }

    fun startFalsh() {
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
        }

        countDownTimer = object : CountDownTimer(30000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
            }

            override fun onFinish() {
                // 当倒计时结束时执行此方法
                // 您可以在此处执行相应的逻辑,如显示"倒计时结束"的提示
                if (!isHaveCloseTips) {
                    isHaveCloseTips = true
                    ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Theres_no_users_available_nowplease_try_again_later"))
                }
                back()
            }
        }
        if (countDownTimer != null) {
            countDownTimer?.start()
        }


        CallUtils.instance?.startFalsh(object : StartFlashEventBack {
            override fun onSuccess(data: VideoCallInfoBean?) {
                falshResult = data
                if (falshResult!!.toUserId == null || falshResult!!.toUserId == "0") {
                    //等待匹配
                } else {
                    //直接请求个人信息
                    getUserInfo(falshResult!!.toUserId!!)
                }
            }

            override fun onError() {
                if (Cache.instance.reviewPkg) {
                    //如果是审核模式机型等待
                    return
                }
                back()
            }
        })
    }

    fun stopFalsh() {
        if (CallUtils.instance?.macthClientSessionId != null) {
            CallUtils.instance?.stopFalsh(CallUtils.instance?.macthClientSessionId?:"")
        }
    }

    fun getUserInfo(userId: String) {
        isHavePreResult = true
        resultUserId = userId

        //刷新金币
        initMyCoins()
        //刷新配置
        Cache.instance.netStrategy
        getUserInfo(userId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
            override fun onNext(userDataBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                super.onNext(userDataBean)

                if (userDataBean.data != null) {
                    if (CallUtils.instance?.videoCallViewModel == null) {
                        return
                    }
                    CallUtils.instance?.videoCallViewModel?.userData
                    CallUtils.instance?.videoCallViewModel?.userData?.setValue(userDataBean.data)
                    //匹配完成。跳转到直播界面
                    isHaveResult = true
                    loadingVideoUserInfo(userDataBean.data!!)
                } else {
                    //报错
                    ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Theres_no_users_available_nowplease_try_again_later"))
                    back()
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                //报错
                ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Theres_no_users_available_nowplease_try_again_later"))
                back()
            }
        })
    }


    private fun initMyCoins() {
        getCoin(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>>() {
            override fun onNext(coinData: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AvailableCoinsBean>) {
                super.onNext(coinData)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }
        })
    }

    fun loadingVideoUserInfo(user: com.juicy.common.model.bean.JuicyUserInfoBean) {
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
        if (CallUtils.instance?.videoCallViewModel == null) {
            return
        }
        if (!isAdded() || context == null) return
        binding!!.xsDistant.visibility = View.VISIBLE
        binding!!.fullBack.visibility = View.INVISIBLE
        binding!!.tabLast.text = LanguageManager.instance!!.getLocalTranslate("Connecting")
        binding!!.openInfo.visibility = View.GONE

        binding!!.closeWide.visibility = View.GONE
        activity?.isMatchCanBack = false

        binding!!.stopAround.text = user.nickname
        if (user.country != null) {
            binding!!.playGrid.text = user.country
        }
        if (user.age != null) {
            binding!!.autoTimer.text = user.age.toString()
        }
        loadImage(this.context, user.avatarThumbUrl, binding!!.longCalendar)
        //        Glide.with(getContext()).load(user.getAvatarThumbUrl()).placeholder(com.juicy.utils.R.drawable.img_small_empty_indigo).into(binding.longCalendar);
        if (user.gender != null) {
            if (user.gender == 1) {
                binding!!.autoTimer.setTextColor(Color.parseColor("#FF00DDFF"))
                context?.let {
                    binding!!.brightThin.setImageDrawable(
                        AppCompatResources.getDrawable(
                          it, R.drawable.ic_boyl_orange
                        )
                    )
                }
              
            } else if (user.gender == 2) {
                binding!!.autoTimer.setTextColor(ActivityUtils.getTopActivity().resources.getColor(R.color.material_33aa))
                context?.let {
                    binding!!.brightThin.setImageDrawable(
                        AppCompatResources.getDrawable(
                            it, R.drawable.ic_girl_ash
                        )
                    )
                }
             
            }
        }

        resultCountDownTimer = object : CountDownTimer(2000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
            }

            override fun onFinish() {
                // 当倒计时结束时执行此方法
                // 您可以在此处执行相应的逻辑,如显示"倒计时结束"的提示
                toVideo(user)
            }
        }

        resultCountDownTimer?.start()
    }


    fun toVideo(user: com.juicy.common.model.bean.JuicyUserInfoBean) {
        //跳转视频界面
        CallUtils.instance?.callInfoModel?.anchorId = user.userId
         CallUtils.instance?.callInfoModel?.callType = Constant.OUTGOING_CALL
         CallUtils.instance?.callInfoModel?.anchorName = (user.nickname)
         CallUtils.instance?.callInfoModel?.channelName = falshResult?.channelName?:""
         CallUtils.instance?.callInfoModel?.rToken = falshResult?.rtcToken?:""
         CallUtils.instance?.callInfoModel?.anchorPhoto = (user.avatarUrl)
         CallUtils.instance?.callInfoModel?.isMatch = true
         CallUtils.instance?.callInfoModel?.matchFreeTime = falshResult?.callFreeSeconds?:0
         CallUtils.instance?.callInfoModel?.isFree = false
        if (Cache.instance.userInfo?.vip == true) {
             CallUtils.instance?.callInfoModel?.matchFreePrice = user.vipUnitPrice
        } else {
             CallUtils.instance?.callInfoModel?.matchFreePrice = user.unitPrice
        }

        activity?.jumpCallingPage(user)
    }

    val randomBroadcasterText: Unit
        get() {
            GetProcedure(object :
                ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<String>>>() {
                override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<List<String>>) {
                    super.onNext(azBaseBean)
                    if (azBaseBean.data != null) {
                        matchWheels = azBaseBean.data
                        if (matchWheelsTimer == null) {
                            matchWheelsTimer = Timer()
                            val task: TimerTask = object : TimerTask() {
                                override fun run() {
                                    // 定时器执行的操作
                                    if (isAdded()) {
                                        requireActivity().runOnUiThread { // 在主线程中执行的操作
                                            if (!isHaveResult) {
                                                if (matchWheelIndex >= matchWheels!!.size) {
                                                    matchWheelIndex = 0
                                                }
                                                binding!!.openInfo.text =
                                                    matchWheels!![matchWheelIndex]
                                                matchWheelIndex = matchWheelIndex + 1
                                            }
                                        }
                                    }
                                }
                            }
                            matchWheelsTimer!!.schedule(task, 0, 5000)
                        }
                    }
                }
            })
        }

    override fun onStart() {
        super.onStart()
        isAdded = true
    }
}