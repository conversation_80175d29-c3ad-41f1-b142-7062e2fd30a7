package com.juicy.app.modules.call

import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.PersistableBundle
import android.view.WindowManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.R
import com.juicy.app.modules.call.fragment.BeCallFragment
import com.juicy.app.modules.call.fragment.CallFragment
import com.juicy.app.modules.call.fragment.CallingFragment
import com.juicy.app.modules.call.fragment.FlashRunFragment
import com.juicy.common.videoCall.CallInfoModel
import com.juicy.common.videoCall.CallUtils

import com.juicy.common.videoCall.VideoCallViewModel

import com.juicy.app.modules.base.dialog.HalfShoppingDialog
import com.juicy.app.modules.base.dialog.MessageNoticationDialog.Companion.close
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.model.event.CallEvent

import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.LogBeanFactory.getCreateCallLog
import org.greenrobot.eventbus.EventBus

@Route(path = Constant.VIDEO_CALL_ACTIVITY_ROUTE)
class VideoCallActivity : BaseActivity() {
    private var isMatch = false
    var isMatchCanBack: Boolean = true
    private var mCallingFragment: CallingFragment? = null

    private var flashRunFragment: FlashRunFragment? = null

    private var mSelect: Int = 0

    private var isCalling = false
    override fun onBackPressed() {
        if (isMatch && isMatchCanBack) {
            flashRunFragment?.activieBack()
            super.onBackPressed()
        } else {
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        mCallingFragment = null
        setContentView(R.layout.activity)
        //        StatusBarUtil.setTranslucentStatus(this);
        if (savedInstanceState != null) {
            if (savedInstanceState.containsKey("isCalling")) {
                isCalling = savedInstanceState.getInt("isCalling") == 1
            }
        }
        super.onCreate(savedInstanceState)
        initWindow()
        mSelect = 2
        //关闭消息弹窗
        close()
    }
    private fun initWindow() {
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        window.setFlags(
            WindowManager.LayoutParams.FLAG_SECURE,
            WindowManager.LayoutParams.FLAG_SECURE
        )
    }


    override fun onSaveInstanceState(outState: Bundle, outPersistentState: PersistableBundle) {
        if (isCalling) {
            outState.putInt("isCalling", 1)
            CallUtils.instance?.callInfoModel?.callType  = (Constant.CANCELLED_CALL)
            CallUtils.instance?.hangUp(Constant.CANCELLED_CALL, Constant.NORMAL)
            restartApp()
        }
        super.onSaveInstanceState(outState, outPersistentState)
    }

    private var callStatus: String? = null
    override fun initData() {
        if (isCalling) {
            return
        }

        if (!intent.getBooleanExtra(Constant.IS_MATCH, false)) {
            CallUtils.instance?.callInfoModel = CallInfoModel()
            CallUtils.instance?.videoCallViewModel = VideoCallViewModel()

            CallUtils.instance?.callInfoModel?.anchorId = intent.getStringExtra(Constant.ANCHOR_ID)?:""
            callStatus = intent.getStringExtra(Constant.CALL_STATUS)
            if (null != intent.getStringExtra(Constant.CHANNEL_NAME)) {
                CallUtils.instance?.callInfoModel?.channelName =
                    intent.getStringExtra(Constant.CHANNEL_NAME)?:""
            }
            if (intent.getStringExtra(Constant.CALL_SOURCE) != null) {
                CallUtils.instance?.callInfoModel?.callSource =
                    intent.getStringExtra(Constant.CALL_SOURCE)?:""
            }
            if (null != intent.getStringExtra(Constant.RTC_TOKEN)) {
                CallUtils.instance?.callInfoModel?.rToken =
                    intent.getStringExtra(Constant.RTC_TOKEN)?:""
            }
            try {
                if (callStatus != null && callStatus == Constant.CALL_CREATE) {
                    supportFragmentManager.beginTransaction()
                        .replace(R.id.foregroundMedium, CallFragment()).commit()
                } else {
                    supportFragmentManager.beginTransaction()
                        .replace(R.id.foregroundMedium, BeCallFragment()).commit()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }


            CallUtils.instance?.callInfoModel?.isFree =
                intent.getBooleanExtra(Constant.IS_FREE, false)
            CallUtils.instance?.videoCallViewModel?.hangUpData?.observe(this) {
                if (it == true){
                    finish()
                }
            }
            CallUtils.instance?.countDownTimer = object : CountDownTimer(30000, 1000) {
                override fun onTick(l: Long) {
                }

                override fun onFinish() {
                    //当前是呼出还是呼入？
                    if (CallUtils.instance?.callInfoModel == null) {
                        return
                    }
                    CallUtils.instance?.callInfoModel?.callType =
                        if (callStatus == Constant.CALL_CREATE) Constant.UNANSWERED_CALL else Constant.MISSED_CALL
                    CallUtils.instance?.videoCallViewModel?.hangUp(
                        CallUtils.instance?.callInfoModel?.channelName?:"",
                        Constant.CL_CALL_TIMEOUT,
                        CallUtils.instance?.callInfoModel?.anchorId?:""
                    )
                    if (callStatus == Constant.CALL_CREATE) {
                        ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("The_user_is_not_available_now_please_try_again_later"))
                    }
                }
            }.start()

            if (callStatus == Constant.CALL_CREATE) {
                CallUtils.instance?.tipCountDownTimer = object : CountDownTimer(10000, 50000) {
                    override fun onTick(l: Long) {
                    }

                    override fun onFinish() {
                        ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("The_users_phone_may_not_be_aroundplease_try_again_later"))
                    }
                }.start()
            }
        } else {
            isMatch = true
            resetMatch(false)
        }
    }

    fun resetMatch(isReset: Boolean) {
        isMatchCanBack = true
        var femaleCoins: Int = 0
        if (Cache.instance.userStratResult != null && Cache.instance.userStratResult?.genderMatchCoin != null
            && Cache.instance.userStratResult?.genderMatchCoin?.femaleCoins != null) {
            femaleCoins = Cache.instance.userStratResult?.genderMatchCoin?.femaleCoins?:0
        }
        if (!Cache.instance.reviewPkg && Cache.instance.userStratResult != null && Cache.instance.userStratResult?.flashChatConfig != null
            && Cache.instance.userStratResult?.flashChatConfig?.isFreeCall != null && Cache.instance.userInfo != null
            && Cache.instance.userInfo?.availableCoins != null && Cache.instance.userStratResult?.flashChatConfig?.isFreeCall != true
            && (Cache.instance.userInfo?.availableCoins?:0) < femaleCoins) {
            finish()

            val halfShoppingDialog = HalfShoppingDialog()
            halfShoppingDialog.entry = "match_result_call"
            halfShoppingDialog.show(this.supportFragmentManager, "")
            val handler = Handler()
            handler.postDelayed({
                ToastUtils.showShort(
                    LanguageManager.instance?.getLocalTranslate("Coins_Not_Enough")
                )
            }, 100) // 延迟时间
        } else {
            if (isReset) {
                match20OutingDeal()
            }

            CallUtils.instance?.callInfoModel = CallInfoModel()

            CallUtils.instance?.videoCallViewModel = VideoCallViewModel()
            flashRunFragment = FlashRunFragment()
            try {
                supportFragmentManager.beginTransaction().replace(
                    R.id.foregroundMedium,
                    flashRunFragment!!, "FlashRunFragmentTag"
                ).commit()
            } catch (e: Exception) {
            }
        }
    }



    override fun initView() {
    }
    fun closeTime() {
        if (CallUtils.instance?.countDownTimer != null) {
            CallUtils.instance?.countDownTimer?.cancel()
            CallUtils.instance?.countDownTimer = null
        }
        if (CallUtils.instance?.tipCountDownTimer != null) {
            CallUtils.instance?.tipCountDownTimer?.cancel()
            CallUtils.instance?.tipCountDownTimer = null
        }
    }


    fun jumpCallingPage(user: com.juicy.common.model.bean.JuicyUserInfoBean?) {
        isCalling = true
        try {
            supportFragmentManager.beginTransaction()
                .replace(R.id.foregroundMedium, CallingFragment(user)).commitAllowingStateLoss()
        } catch (e: Exception) {
        }
    }

    fun match20OutingDeal() {
        if (CallUtils.instance?.callInfoModel != null && !CallUtils.instance?.callInfoModel?.channelName.isNullOrEmpty()) {
            CallUtils.instance?.callInfoModel?.logList?.add(
                getCreateCallLog(
                    CallUtils.instance?.callInfoModel?.channelName?:"",
                    "hangup",
                    "calling",
                    "退出通话页面",
                    System.currentTimeMillis()
                )
            )
            val callEvent = CallEvent()
            callEvent.callType = CallUtils.instance?.callInfoModel?.callType?:""
            callEvent.anchorPhoto = CallUtils.instance?.callInfoModel?.anchorPhoto?:""
            callEvent.logList = CallUtils.instance?.callInfoModel?.logList?: mutableListOf()
            callEvent.channelName = CallUtils.instance?.callInfoModel?.channelName?:""
            callEvent.userId = CallUtils.instance?.callInfoModel?.anchorId?:""
            callEvent.nickName = CallUtils.instance?.callInfoModel?.anchorName?:""
            callEvent.isMatch20Out = true
            callEvent.isMatch = true

            EventBus.getDefault().post(callEvent)
        }
    }

    override fun onDestroy() {
        if (CallUtils.instance?.callInfoModel != null && !CallUtils.instance?.callInfoModel?.channelName.isNullOrEmpty()) {
            CallUtils.instance?.callInfoModel?.logList?.add(
                getCreateCallLog(
                    CallUtils.instance?.callInfoModel?.channelName?:"",
                    "hangup",
                    "calling",
                    "退出通话页面",
                    System.currentTimeMillis()
                )
            )
            val callEvent = CallEvent()
            callEvent.logList = CallUtils.instance?.callInfoModel?.logList?: mutableListOf()
            callEvent.channelName = CallUtils.instance?.callInfoModel?.channelName?:""
            callEvent.callType = CallUtils.instance?.callInfoModel?.callType?:""
            callEvent.anchorPhoto = CallUtils.instance?.callInfoModel?.anchorPhoto?:""
            callEvent.nickName = CallUtils.instance?.callInfoModel?.anchorName?:""
            callEvent.userId = CallUtils.instance?.callInfoModel?.anchorId?:""
            callEvent.isAllJoin = CallUtils.instance?.callInfoModel?.isAllJopin?:false
            EventBus.getDefault().post(callEvent)
        }
        closeTime()

        CallUtils.instance?.callInfoModel = null
        CallUtils.instance?.videoCallViewModel = null

        super.onDestroy()
    }

    override fun transLocalText() {
    }
}