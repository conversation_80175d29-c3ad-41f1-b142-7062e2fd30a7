package com.juicy.app.modules.base.adapter

import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter

class EmojiAdapter(layoutResId: Int) : BaseQuickAdapter<String?, EmojiViewHolder>(layoutResId) {
    override fun convert(holder: EmojiViewHolder, item: String?) {
        holder.emoji.text = item
        // 设置文本渲染属性
        holder.emoji.paint.isAntiAlias = true // 开启抗锯齿
        holder.emoji.paint.isFilterBitmap = true // 开启位图过滤
        holder.emoji.setLayerType(View.LAYER_TYPE_SOFTWARE, null) // 使用软件渲染
        holder.emoji.alpha = 1.0f // 设置不透明
    }
}



