package com.juicy.app.modules.base.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import com.juicy.app.databinding.DialogProgressBinding
import com.juicy.common.utils.LanguageManager

class JuicyLoadDialog(private val context: Context) : Dialog(context) {
    private var binding: DialogProgressBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogProgressBinding.inflate(layoutInflater)
        setContentView(binding!!.root)
        binding!!.syncToggle.text = LanguageManager.instance!!.getLocalTranslate("Loading")
    }
}
