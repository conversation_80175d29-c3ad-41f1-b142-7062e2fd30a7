package com.juicy.app.modules.rank

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import com.chad.library.adapter.base.animation.AlphaInAnimation
import com.google.gson.Gson
import com.juicy.app.R
import com.juicy.app.modules.rank.adapter.ItemAdapter
import com.juicy.app.databinding.LayoutButtonBinding
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.RankInfoBean
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.networks.request.RetrofitManage.requestBody
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.GlideImageUtil.load
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.view.CircleImageView
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.schedulers.Schedulers
import java.util.ArrayDeque

class RankFm : BaseFragment {
    private lateinit var binding: LayoutButtonBinding
    private val rankList: MutableList<RankInfoBean.RankDataBean> = ArrayList()
    private var itemAdapter: ItemAdapter? = null
    private val dataCount = 50
    private var fragmentName = ""



    //    private LoadDialog loadDialog;
    constructor()

    constructor(fragmentName: String) {
        this.fragmentName = fragmentName
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        itemAdapter = ItemAdapter(R.layout.item_rank)

        //        loadDialog = new LoadDialog(getContext());
    }

    override fun initData() {
        loadData(fragmentName)
        //        loadDialog.show();
    }

    private fun loadData(fragmentName: String) {
        when (fragmentName) {
            CHARM -> {
                binding.focusedSuccess.visibility = View.GONE
                loadCharm()
            }

            RICK -> {
                binding.focusedSuccess.visibility = View.VISIBLE
                loadRick()
            }
        }
    }

    @SuppressLint("CheckResult")
    private fun loadCharm() {
        val cacheBean = cacheCham
        if (cacheBean != null) {
            if (cacheBean.rankData != null && !cacheBean.rankData.isNullOrEmpty()) {
                val queue = ArrayDeque(cacheBean.rankData?: listOf())
                setFrontAnchor(queue)
                if (!queue.isEmpty()) {
                    rankList.addAll(queue)
                    itemAdapter?.setNewInstance(rankList)
                }
            }
            if (!isDestroy(activity)) {
                (activity as UserRankActivity).setRankTime(cacheBean.monthName)
            }
        }

        RetrofitManage.instance
            .create(Service::class.java)
            .rankSearch(requestBody(com.juicy.common.model.bean.CountParamBean(dataCount)))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ btBaseBean: com.juicy.common.model.bean.NetResponseBean<RankInfoBean> ->
                if (btBaseBean.isOk) {
                    if (btBaseBean.data != null) {
                        if (btBaseBean.data?.rankData != null && !btBaseBean.data?.rankData.isNullOrEmpty()) {
                            if (Cache.instance.userInfo != null) {
                                Cache.instance.networkCacheManager.putObject(
                                    "Charm_" + Cache.instance.userInfo?.userId,
                                    btBaseBean.data
                                )
                            }

                            val queue = ArrayDeque(btBaseBean.data?.rankData?: listOf())
                            setFrontAnchor(queue)
                            if (!queue.isEmpty()) {
                                rankList.addAll(queue)
                                itemAdapter?.setNewInstance(rankList)
                            }
                        }
                        if (!isDestroy(activity)) {
                            (activity as UserRankActivity).setRankTime(btBaseBean.data?.monthName)
                        }
                    }
                }
            }, {
                //                        loadDialog.dismiss();
            })
    }

    val cacheCham: RankInfoBean?
        get() {
            if (Cache.instance.userInfo == null) {
                return null
            }
            val json =
                Cache.instance.networkCacheManager.get("Charm_" + Cache.instance.userInfo?.userId)
            if (TextUtils.isEmpty(json)) {
                return null
            }
            val bean = Gson().fromJson(json, RankInfoBean::class.java)

            return bean
        }

    val cacheRich: RankInfoBean?
        get() {
            if (Cache.instance.userInfo == null) {
                return null
            }
            val json =
                Cache.instance.networkCacheManager.get("Rich_" + Cache.instance.userInfo?.userId)
            if (TextUtils.isEmpty(json)) {
                return null
            }
            val bean = Gson().fromJson(json, RankInfoBean::class.java)

            return bean
        }

    @SuppressLint("SetTextI18n")
    private fun setUserView(data: RankInfoBean) {
        if (Cache.instance.userInfo != null && Cache.instance.userInfo?.nickname != null) {
            binding.xxlXl.text = Cache.instance.userInfo?.nickname
        }

        load(context, Cache.instance.userInfo?.avatarUrl, binding.btnStretch)
        if (data.sortNo.isNullOrEmpty()) {
            binding.swipeToggle.text = "50+"
        } else {
            if ((data.sortNo?.toInt()?:0) <= 50) {
                binding.swipeToggle.text = data.sortNo
            } else {
                binding.swipeToggle.text = "50+"
            }
        }
    }

    @SuppressLint("CheckResult")
    private fun loadRick() {
        val cacheBean = cacheRich
        if (cacheBean != null) {
            if (cacheBean.rankData != null && !cacheBean.rankData.isNullOrEmpty()) {
                val queue = ArrayDeque(cacheBean.rankData?: listOf())
                setFrontAnchor(queue)
                if (!queue.isEmpty()) {
                    rankList.addAll(queue)
                    itemAdapter?.setNewInstance(rankList)
                }
                setUserView(cacheBean)
            }
        }

        RetrofitManage.instance
            .create(Service::class.java)
            .userSearch(requestBody(com.juicy.common.model.bean.CountParamBean(dataCount)))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ btBaseBean: com.juicy.common.model.bean.NetResponseBean<RankInfoBean> ->
                if (btBaseBean.isOk) {
                    if (btBaseBean.data != null) {
                        if (btBaseBean.data?.rankData != null && !btBaseBean.data?.rankData.isNullOrEmpty()) {
                            if (Cache.instance.userInfo != null) {
                                Cache.instance.networkCacheManager.putObject(
                                    "Rich_" + Cache.instance.userInfo?.userId,
                                    btBaseBean.data
                                )
                            }
                            val queue = ArrayDeque(btBaseBean.data?.rankData?: listOf())
                            setFrontAnchor(queue)
                            if (!queue.isEmpty()) {
                                rankList.addAll(queue)
                                itemAdapter?.setNewInstance(rankList)
                            }
                            btBaseBean.data?.let {
                                setUserView(it)
                            }
                        }
                    }
                }
            }, {
                //                        loadDialog.dismiss();
            })
    }

    private fun setFrontAnchor(queue: ArrayDeque<RankInfoBean.RankDataBean>) {
        val circleImageViews: MutableList<CircleImageView> = ArrayList()
        val anchorNameViews: MutableList<AppCompatTextView> = ArrayList()
        val headList: MutableList<RelativeLayout> = ArrayList()
        val dataList: MutableList<RankInfoBean.RankDataBean> = ArrayList()
        headList.add(binding.primaryChip)
        headList.add(binding.thinShort)
        headList.add(binding.selectActive)
        circleImageViews.add(binding.removeAbove)
        circleImageViews.add(binding.previewInside)
        circleImageViews.add(binding.aroundLeft)
        anchorNameViews.add(binding.resetChoose)
        anchorNameViews.add(binding.overlayClear)
        anchorNameViews.add(binding.connectedForward)
        while (!queue.isEmpty()) {
            if (dataList.size == 3) {
                break
            }
            dataList.add(queue.pop())
        }
        for (i in dataList.indices) {
            val finalI = i
            load(context, dataList[finalI].avatar, circleImageViews[i])
            anchorNameViews[i].text = dataList[finalI].nickname
            if (fragmentName == CHARM) {
                headList[i].setOnClickListener { view: View? ->
                    ARouter
                        .getInstance()
                        .build(Constant.ANCHOR_INFO_ACTIVITY_ROUTE)
                        .withString(
                            Constant.USER_ID,
                            dataList[finalI].userId
                        )
                        .navigation()
                }
            }
        }
    }

    override fun initView() {
        val manager = LinearLayoutManager(
            context
        )
        manager.orientation = RecyclerView.VERTICAL
        binding.toggleUnfocused.layoutManager = manager
        binding.toggleUnfocused.adapter = itemAdapter
        itemAdapter?.adapterAnimation = AlphaInAnimation()
        if (fragmentName == CHARM) {
            itemAdapter?.isCharm = true
            itemAdapter?.addChildClickViewIds(R.id.thickToggle)

            itemAdapter?.setOnItemChildClickListener { adapter, view, position ->
                ARouter
                    .getInstance()
                    .build(Constant.ANCHOR_INFO_ACTIVITY_ROUTE)
                    .withString(Constant.USER_ID, rankList[position].userId)
                    .navigation()
            }
        }
        if (LanguageManager.instance?.isLanguageForce == true) {
            binding.updateUnfocused.scaleX = -1f
            binding.briefHeader.scaleX = -1f
            binding.xxlXl.gravity = Gravity.END
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = LayoutButtonBinding.inflate(inflater)
        return binding.root
    }

    companion object {
        const val CHARM: String = "charm"
        const val RICK: String = "rick"
    }
}