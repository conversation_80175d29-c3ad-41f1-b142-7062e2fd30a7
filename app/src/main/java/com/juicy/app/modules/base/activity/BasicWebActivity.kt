package com.juicy.app.modules.base.activity

import android.os.Bundle
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.BarUtils
import com.juicy.app.databinding.ActivityTextBinding
import com.juicy.common.utils.LanguageManager

class BasicWebActivity : BaseActivity() {
    private var binding: ActivityTextBinding? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        binding = ActivityTextBinding.inflate(layoutInflater)
        setContentView(binding!!.root)

        super.onCreate(savedInstanceState)
    }
    override fun initView() {
        val layoutParams =
            binding!!.primarySync.layoutParams as ConstraintLayout.LayoutParams
        layoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        binding?.primarySync?.layoutParams = layoutParams
        binding?.underlayShort?.setOnClickListener { view: View? -> finish() }
    }
    override fun initData() {
    }

    override fun onResume() {
        super.onResume()
        val url = intent.getStringExtra("url")
        val title = intent.getStringExtra("title")
        binding!!.nestedDisabled.loadUrl(url!!)
        binding!!.switchSelect.text = title
    }

    override fun transLocalText() {
        val scaleX = if (LanguageManager.instance!!.isLanguageForce) -1f else 1f
        binding!!.startEnabled.scaleX = scaleX
    }
}