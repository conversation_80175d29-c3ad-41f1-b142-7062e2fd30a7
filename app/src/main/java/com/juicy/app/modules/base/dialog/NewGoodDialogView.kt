package com.juicy.app.modules.base.dialog

import android.content.Context
import android.graphics.Color
import android.os.CountDownTimer
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.SpanUtils
import com.bumptech.glide.Glide
import com.juicy.app.R
import com.juicy.app.databinding.DialogFooterBinding
import com.juicy.common.model.event.PromotionPayStatusChangeEvent
import com.juicy.common.model.event.PayResultEvent
import com.juicy.common.model.event.DialogShowStatusEvent
import com.juicy.common.model.message_event.AppRechargeOrderStatusBean
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.addMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.Companion.removeMessageEvent
import com.juicy.common.networks.request.SocketUtil.SocketModelImp.SocketEventBusBack
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.DateMetaUtil.ms2HMS
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PayUtils.PayUtils
import com.juicy.common.utils.PayUtils.PayUtils.FailOnListen
import com.juicy.common.utils.PayUtils.PayUtils.SuccessOnListen
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.getIntValue
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import com.juicy.common.utils.SpSaveUtil.putIntValue
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.abs

class NewGoodDialogView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    ConstraintLayout(context, attrs, defStyleAttr) {
    private var salasProDialogBinding: DialogFooterBinding? = null
    private var socketEventBusBack: SocketEventBusBack? = null
    private var mTitle: String? = null
    private var countDownTimer: CountDownTimer? = null
    private var dismissCallBack: DismissCallBack? = null
    private var mPayUtils: PayUtils? = null

    init {
        init(context)
    }

    private fun init(context: Context) {
        mTitle = "New Good"
        salasProDialogBinding = DialogFooterBinding.inflate(
            LayoutInflater.from(context),
            this, true
        )
        setDirection()
        salasProDialogBinding!!.beyondMenu.setOnClickListener { v: View? -> }
        salasProDialogBinding!!.emptyBelow.setOnClickListener { v: View? -> close() }
        salasProDialogBinding!!.compactNarrow.setOnClickListener { v: View? -> }
        initData()
        mPayUtils = null
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(`object`: PromotionPayStatusChangeEvent?) {
        putIntValue(SpKeyPool.MY_SA_NUM, getIntValue(SpKeyPool.MY_SA_NUM, 0) - 1)
        initBuyNum()
        if (Cache.instance.webViewActivity != null) {
            Cache.instance.webViewActivity!!.refreshAgentWeb()
        }
        if (!getBooleanVal(SpKeyPool.HAS_EVALUATE, false)!!) {
            putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
            val currentActivity = currentActivity as AppCompatActivity?
            if (!isDestroy(currentActivity)) {
                val assessDialog = AssessDialog()
                assessDialog.show(currentActivity!!.supportFragmentManager, "")
            }
        }

        if (getIntValue(SpKeyPool.MY_SA_NUM, 0) <= 0) {
            EventBus.getDefault().post(DialogShowStatusEvent(false))
            close()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPayEvent(userDialogEvent: PayResultEvent) {
        if (userDialogEvent.isSuccess) {
            close()
        }
    }

    fun setDirection() {
        if (LanguageManager.instance!!.isLanguageForce) {
            salasProDialogBinding!!.root.layoutDirection = LAYOUT_DIRECTION_RTL
            val currentActivity = currentActivity as AppCompatActivity?
            salasProDialogBinding!!.secondaryScroll.background =
                context.getDrawable(R.drawable.bg_num_bg_mirror_khaki)
        } else {
            salasProDialogBinding!!.root.layoutDirection = LAYOUT_DIRECTION_LTR
        }
    }

    fun initData() {
        Cache.instance.payChannle
        socketEventBusBack = object : SocketEventBusBack {
            override fun rechargeSuccessBack(appRechargeOrderStatusBean: AppRechargeOrderStatusBean?) {
                if (appRechargeOrderStatusBean!!.status == 2) {
                    putIntValue(SpKeyPool.MY_SA_NUM, getIntValue(SpKeyPool.MY_SA_NUM, 0) - 1)
                    initBuyNum()
                    if (getIntValue(SpKeyPool.MY_SA_NUM, 0) <= 0) {
                        EventBus.getDefault().post(DialogShowStatusEvent(false))
                        close()
                    }
                }
            }
        }
        val salePromotionGood = Cache.instance.salePromotionGood
        if (salePromotionGood != null) {
            if (salePromotionGood.activityName != null) {
                salasProDialogBinding!!.panelFront.text = salePromotionGood.activityName
            }
            if (salePromotionGood.activityPic != null) {
                Glide.with(this).load(salePromotionGood.activityPic).into(
                    salasProDialogBinding!!.btnFar
                )
            }
            initBuyNum()

            if (salePromotionGood.exchangeCoin != null) {
                salasProDialogBinding!!.viewFit.text = salePromotionGood.exchangeCoin.toString()
            }
            if (salePromotionGood.extraCoin != null) {
                salasProDialogBinding!!.playBack.text = "+" + salePromotionGood.extraCoin.toString()
            }
            if (salePromotionGood.price != null) {
                if (salePromotionGood.price!!.toDouble() != salePromotionGood.originalPrice!!.toDouble()) {
                    SpanUtils.with(salasProDialogBinding!!.flexibleBackground)
                        .append("$" + salePromotionGood.price!!.toDouble())
                        .setFontSize(20, true)
                        .setBold().append(" ")
                        .append("$" + salePromotionGood.originalPrice.toString())
                        .setFontSize(12, true)
                        .setForegroundColor(Color.parseColor("#AF004B"))
                        .setStrikethrough()
                        .setBold()
                        .append(" ")
                        .create()
                } else {
                    SpanUtils.with(salasProDialogBinding!!.flexibleBackground)
                        .append("$" + salePromotionGood.price!!.toDouble())
                        .setFontSize(20, true)
                        .setBold()
                        .append(" ")
                        .create()
                }
            }
            salasProDialogBinding!!.flexibleBackground.setOnClickListener { v: View? ->
                if (isFastClick) return@setOnClickListener
                val currentActivity =
                    currentActivity as AppCompatActivity?
                PayUtils(
                    currentActivity,
                    "push_special_offer",
                    false,
                    salePromotionGood,
                    object : SuccessOnListen {
                        override fun onListen() {
                            putIntValue(
                                SpKeyPool.MY_SA_NUM,
                                getIntValue(SpKeyPool.MY_SA_NUM, 0) - 1
                            )
                            initBuyNum()
                            if (Cache.instance.webViewActivity != null) {
                                Cache.instance.webViewActivity!!.refreshAgentWeb()
                            }
                            if (!getBooleanVal(SpKeyPool.HAS_EVALUATE, false)!!) {
                                putBooleanValue(SpKeyPool.HAS_EVALUATE, true)
                                if (!isDestroy(currentActivity)) {
                                    val assessDialog = AssessDialog()
                                    assessDialog.show(currentActivity!!.supportFragmentManager, "")
                                }
                            }

                            if (getIntValue(SpKeyPool.MY_SA_NUM, 0) <= 0) {
                                EventBus.getDefault()
                                    .post(DialogShowStatusEvent(false))
                                close()
                            }
                        }
                    },
                    object : FailOnListen {
                        override fun onListen() {
                        }
                    }).openPayDialog()
            }
            setCountDownTimer()
            addMessageEvent(socketEventBusBack!!)
        }
    }

    fun setCountDownTimer() {
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
        if (Cache.instance.salePromotionGood == null) {
            return
        }
        val remainMilliseconds =
            Cache.instance.salePromotionGood!!.remainMilliseconds - (System.currentTimeMillis() - Cache.instance.salePromotionCreateTime)
        countDownTimer = object : CountDownTimer(remainMilliseconds, 1000) {
            override fun onTick(l: Long) {
                if (Cache.instance.salePromotionGood != null) {
                    val remainMilliseconds =
                        Cache.instance.salePromotionGood!!.remainMilliseconds - (System.currentTimeMillis() - Cache.instance.salePromotionCreateTime)

                    val time = ms2HMS(remainMilliseconds)
                    salasProDialogBinding!!.customIcon.setTime(time)
                    if (abs((l - remainMilliseconds).toDouble()) > 1000) {
                        setCountDownTimer()
                    }
                }
            }

            override fun onFinish() {
                Cache.instance.salePromotionGood = null
                close()
            }
        }.start()
    }

    private fun initBuyNum() {
        if (Cache.instance.salePromotionGood == null) {
            return
        }
        val count =
            Cache.instance.salePromotionGood!!.capableRechargeNum!! - Cache.instance.salePromotionGood!!.rechargeNum!!
        if (count <= 1) {
            salasProDialogBinding!!.secondaryScroll.visibility = GONE
        } else {
            salasProDialogBinding!!.secondaryScroll.visibility = VISIBLE
            salasProDialogBinding!!.secondaryScroll.text = "x$count"
        }
    }


    fun close() {
        removeMessageEvent(socketEventBusBack!!)
        if (countDownTimer != null) {
            countDownTimer!!.cancel()
            countDownTimer = null
        }
        if (dismissCallBack != null) {
            dismissCallBack!!.onDismiss()
        }
    }

    fun setDismiss(dismissCallBack: DismissCallBack?) {
        this.dismissCallBack = dismissCallBack
    }

    interface DismissCallBack {
        fun onDismiss()
    }
}
