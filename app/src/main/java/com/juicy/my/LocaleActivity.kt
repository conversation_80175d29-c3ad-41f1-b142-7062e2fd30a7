package com.juicy.my

import android.os.Bundle
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.BarUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.juicy.app.R
import com.juicy.app.databinding.ActivityAvatarBinding
import com.juicy.my.Adapter.LanguageListAdapter
import com.juicy.app.modules.base.dialog.SettingDialog
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.utils.LanguageManager
import org.greenrobot.eventbus.EventBus

class LocaleActivity : BaseActivity() {
    private lateinit var binding: ActivityAvatarBinding

    private val beanList: MutableList<com.juicy.common.model.bean.LanguageItemBean> = ArrayList()
    private var languageListAdapter: LanguageListAdapter? = null

    private val languageCountryCode = HashMap<String, String>()
    override fun onCreate(savedInstanceState: Bundle?) {
        binding = ActivityAvatarBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initSetting()
        super.onCreate(savedInstanceState)
    }

    override fun initData() {
        languageCountryCode["en"] = "US"
        languageCountryCode["ar"] = "SA"
        languageCountryCode["es"] = "ES"
        languageCountryCode["tr"] = "TR"
        languageCountryCode["ko"] = "KR"
        languageCountryCode["de"] = "DE"
        languageCountryCode["ja"] = "JP"
        languageCountryCode["it"] = "IT"
        languageCountryCode["hi"] = "IN"
        languageCountryCode["zh-tw"] = "TW"
        languageCountryCode["th"] = "TH"
        languageCountryCode["fr"] = "FR"
        languageCountryCode["pt"] = "PT"

        val ss: List<String?> = mutableListOf<String?>(
            "en",
            "ar",
            "es",
            "tr",
            "ko",
            "de",
            "ja",
            "it",
            "hi",
            "zh-tw",
            "th",
            "fr",
            "pt"
        )
        val supportLanguage: MutableList<String?> = ArrayList()
        supportLanguage.addAll(ss)

        //        String language = HCSocketManager.shared.getDeviceLanguage();
        var language = LanguageManager.instance?.translateLanguageCode

        if (mutableListOf<String?>("ar", "he", "fa", "ur").contains(language)) {
            language = "ar"
        }

        if (!supportLanguage.contains(language)) {
            language = "en"
        }

        if (supportLanguage.contains(language)) {
            val index = supportLanguage.indexOf(language)
            supportLanguage.removeAt(index)
            supportLanguage.add(0, language)
        }

        val languageFullName: MutableMap<String?, String> = HashMap()
        languageFullName["en"] = "English"
        languageFullName["ar"] = "العربية"
        languageFullName["es"] = "Español"
        languageFullName["tr"] = "Türkçe"
        languageFullName["ko"] = "한국어"
        languageFullName["de"] = "Deutsch"
        languageFullName["ja"] = "日本語"
        languageFullName["it"] = "Italiano"
        languageFullName["hi"] = "हिन्दी"
        languageFullName["zh-tw"] = "繁體中文"
        languageFullName["th"] = "ไทย"
        languageFullName["fr"] = "Français"
        languageFullName["pt"] = "Português"

        val languageEnglishName: MutableMap<String?, String> = HashMap()
        languageEnglishName["en"] = translateString("English")
        languageEnglishName["ar"] = translateString("Arabic")
        languageEnglishName["es"] = translateString("Spanish")
        languageEnglishName["tr"] = translateString("Turkish")
        languageEnglishName["ko"] = translateString("Korean")
        languageEnglishName["de"] = translateString("German")
        languageEnglishName["ja"] = translateString("Japanese")
        languageEnglishName["it"] = translateString("Italian")
        languageEnglishName["hi"] = translateString("Hindi")
        languageEnglishName["zh-tw"] = translateString("Traditional Chinese")
        languageEnglishName["th"] = translateString("Thai")
        languageEnglishName["fr"] = translateString("French")
        languageEnglishName["pt"] = translateString("Portuguese")

        beanList.clear()
        for (str in supportLanguage) {
            val model = com.juicy.common.model.bean.LanguageItemBean()
            model.languageCode = str
            model.languageText = languageFullName[str]
            model.tanslateText = languageEnglishName[str]
            if (beanList.size == 0) {
                model.isChecked = true
            }
            beanList.add(model)
        }

        languageListAdapter?.setNewInstance(beanList)
    }

    private fun translateString(text: String): String {
        // Implement your own translation logic here
        return LanguageManager.instance?.getLocalTranslate(text)?:""
    }

    private fun initSetting() {
        val manager = LinearLayoutManager(this@LocaleActivity)
        manager.orientation = LinearLayoutManager.VERTICAL
        binding.clearTiny.layoutManager = manager
        languageListAdapter = LanguageListAdapter(R.layout.item_margin)
        binding.clearTiny.adapter = languageListAdapter
        languageListAdapter?.setOnItemClickListener(object :OnItemClickListener{
            override fun onItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
                //切换
                if (beanList[position].isChecked) {
                    return
                }

                val settingDialog = SettingDialog(
                    this@LocaleActivity,
                    LanguageManager.instance?.getLocalTranslate("Are_you_sure_to_switch_the") + " " + beanList[position].tanslateText + "?",
                    object : SettingDialog.ConfirmBack {
                        override fun onConfirmBack() {
                            LanguageManager.instance?.changeLanguage(beanList[position].languageCode?:"")
                            EventBus.getDefault().post(beanList[position])


                            //发送eventbus 到主页切换
                        }
                    })
                settingDialog.show(supportFragmentManager, "")
            }

        })
    }

    override fun initView() {
        val mainPageLayoutParams =
            binding.connectedUnlocked.layoutParams as ConstraintLayout.LayoutParams
        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        binding.connectedUnlocked.layoutParams = mainPageLayoutParams
        binding.cancelMedium.setOnClickListener { view: View? -> finish() }
    }


    override fun transLocalText() {
        val scaleX = if (LanguageManager.instance?.isLanguageForce == true) -1f else 1f
        binding.bodyLg.scaleX = scaleX

        binding.wideThick.text = LanguageManager.instance?.getLocalTranslate("Language")
    }
}