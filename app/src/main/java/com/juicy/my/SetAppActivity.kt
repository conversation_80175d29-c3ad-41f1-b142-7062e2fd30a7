package com.juicy.my

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.databinding.ActivityListBinding
import com.juicy.app.R
import com.juicy.app.modules.base.dialog.DeleteConfirmDialog
import com.juicy.app.modules.base.dialog.DeleteDialog
import com.juicy.app.modules.base.dialog.LoadDialog
import com.juicy.app.modules.base.dialog.JuicyLoadDialog
import com.juicy.app.modules.base.dialog.SettingDialog
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.DisturbPatchParam
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.networks.delegate.DeleteAccountInterface.deleteAccount
import com.juicy.common.networks.delegate.DisturbInterface.disturb
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.db.CallDatabase
import com.juicy.common.status.UserOnlineStatusService.Companion.exitToLogin
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.ButtonClickUtils.setInterval
import com.juicy.common.utils.DialogShowUtils.showDialogOutsideUnClose
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.SpSaveUtil.putBooleanValue
import com.juicy.common.utils.SpSaveUtil.putStringValue
import com.juicy.common.utils.ThreadUtil.execute
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers

class SetAppActivity : BaseActivity() {
    private val TAG: String = javaClass.simpleName
    private lateinit var binding: ActivityListBinding
    private val disturbPatchParam: DisturbPatchParam? = null
    private var userInfo: com.juicy.common.model.bean.JuicyUserInfoBean? = null
    private var juicyLoadDialog: JuicyLoadDialog? = null
    private var deleteDialog: DeleteDialog? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        binding = ActivityListBinding.inflate(layoutInflater)
        setContentView(binding.root)
        super.onCreate(savedInstanceState)
    }


    override fun initData() {
        if (!getStringValue(SpKeyPool.USER_ID_DEVICE, "").isNullOrEmpty()) {
            val userId = getStringValue(SpKeyPool.USER_ID_DEVICE, null)
            val loadDialog = LoadDialog(this@SetAppActivity)


            userInfo = Cache.instance.userInfo
            if (userInfo != null) {
                binding.throughBack.text = Cache.instance.userInfo?.userId
                setCallSwitch(Cache.instance.userInfo?.switchNotDisturbCall?:false)
                setMessageSwitch(Cache.instance.userInfo?.switchNotDisturbIm?:false)
            } else {
                //先读取缓存
                userInfo = getCacheUserInfo(userId?:"")
                if (userInfo != null) {
                    Cache.instance.userInfo = userInfo
                    binding.throughBack.text =
                        Cache.instance.userInfo?.userId
                    setCallSwitch(Cache.instance.userInfo?.switchNotDisturbCall?:false)
                    setMessageSwitch(Cache.instance.userInfo?.switchNotDisturbIm?:false)
                } else {
                    loadDialog.show()
                }
            }



            getUserInfo(userId?:"", object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                    if (t != null) {
                        if (t.isOk && t.data != null) {
                            userInfo = t.data
                            Cache.instance.userInfo = userInfo
                            binding.throughBack.text =
                                Cache.instance.userInfo?.userId
                            setCallSwitch(Cache.instance.userInfo?.switchNotDisturbCall?:false)
                            setMessageSwitch(Cache.instance.userInfo?.switchNotDisturbIm?:false)
                        }
                        //                        else if (Constant.TOKEN_EXPIRE_CODES.contains(azBaseBean.getCode())) {
//                            ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate("Token_expired_please_login_again"));
//                            UserOnlineStatusService.exitToLogin();
//                        }
                    }
                    loadDialog.dismiss()
                }

                override fun onError(e: Throwable) {
                    Log.e(TAG, "setting user info error: " + e.message)
                    loadDialog.dismiss()
                }
            })
        }
        setTranslateSwitch(getBooleanVal(SpKeyPool.IS_AUTO_TRANSLATE, false)?:false)
    }

    private fun setTranslateSwitch(isSwitch: Boolean) {
        if (isSwitch) {
            binding.badgeCut.setImageResource(R.drawable.ic_open_switch_gray)
        } else {
            binding.badgeCut.setImageResource(R.drawable.ic_close_switch_lime)
        }
    }

    private fun setCallSwitch(isSwitch: Boolean) {
        if (isSwitch) {
            binding.connectedVisible.setImageResource(R.drawable.ic_open_switch_gray)
        } else {
            binding.connectedVisible.setImageResource(R.drawable.ic_close_switch_lime)
        }
    }

    private fun setMessageSwitch(isSwitch: Boolean) {
        if (isSwitch) {
            binding.fitPick.setImageResource(R.drawable.ic_open_switch_gray)
        } else {
            binding.fitPick.setImageResource(R.drawable.ic_close_switch_lime)
        }
    }

    @SuppressLint("CheckResult")
    override fun initView() {
        val mainPageLayoutParams =
            binding.connectedUnlocked.layoutParams as ConstraintLayout.LayoutParams
        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        binding.connectedUnlocked.layoutParams = mainPageLayoutParams

        binding.cancelMedium.setOnClickListener { view: View? -> finish() }

        binding.fullCancel.setOnClickListener { view: View? ->
            //复制id
            // 获取系统剪贴板
            val clipboard =
                getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            // 创建一个剪贴数据集，包含一个普通文本数据条目（需要复制的数据）
            val clipData =
                ClipData.newPlainText(null, binding.throughBack.text)
            // 把数据集设置（复制）到剪贴板
            clipboard.setPrimaryClip(clipData)
            ToastUtils.showShort(
                LanguageManager.instance?.getLocalTranslate(
                    "Copy Success"
                )
            )
        }

        binding.overlayDelete.setOnClickListener { view: View? ->
            deleteDialog = DeleteDialog(object : SettingDialog.ConfirmBack {
                override fun onConfirmBack() {
                    deleteDialog?.dismiss()
                    val settingDialog =
                        DeleteConfirmDialog(
                            this@SetAppActivity,
                            LanguageManager.instance?.getLocalTranslate("Are_your_confirm_to_delete_your_account")?:"Are your confirm to delete your account?",
                            object : DeleteConfirmDialog.ConfirmBack {
                                override fun onConfirmBack() {
                                    deleteAccount(object :
                                        ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
                                        override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                                            super.onNext(t)
                                            if (t.data != null && t.data == true) {
                                                putStringValue(
                                                    SpKeyPool.CURRECTCOUNTRY,
                                                    ""
                                                )
                                                putStringValue(
                                                    SpKeyPool.SUBSCRIPTIONDATE,
                                                    ""
                                                )


                                                putBooleanValue(
                                                    SpKeyPool.IS_AUTO_TRANSLATE,
                                                    false
                                                )
                                                //删除促销信息
                                                Cache.instance.userPromotionGood =
                                                    null
                                                Cache.instance.salePromotionGood =
                                                    null
                                                execute {
                                                    CallDatabase.instance?.userDao()?.deleteAll()
                                                }
                                                exitToLogin()
                                            }
                                        }

                                        override fun onError(e: Throwable) {
                                            super.onError(e)
                                            ToastUtils.showShort(
                                                LanguageManager.instance?.getLocalTranslate(
                                                    "The network request failed"
                                                )
                                            )
                                        }
                                    })
                                }
                            })
                    settingDialog.show(supportFragmentManager, "")
                }
            })
            deleteDialog?.show(supportFragmentManager, "")
        }


        //退出登录接口
        binding.firstChecked.setOnClickListener { view: View? ->
            if (isFastClick) {
                return@setOnClickListener
            }
            juicyLoadDialog = JuicyLoadDialog(this@SetAppActivity)
            showDialogOutsideUnClose(juicyLoadDialog!!)
            RetrofitManage.instance.create(Service::class.java)
                .signOut()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe({ baseBean: Any ->
                    if (baseBean != null) {
                        juicyLoadDialog?.dismiss()
                        putStringValue(SpKeyPool.CURRECTCOUNTRY, "")
                        exitToLogin()
                    }
                }, {errord ->
                    Log.e("SettingMedia","errord $errord")
                    juicyLoadDialog?.dismiss()
                    ToastUtils.showShort(
                        LanguageManager.instance?.getLocalTranslate(
                            errord.message ?:"The network request failed"
                        )
                    )
                    //                            Log.e(TAG, "logout error" + throwable.getMessage());
                })
        }

        binding.badgeCut.setOnClickListener { view: View? ->
            val isTranslateSelect =
                getBooleanVal(SpKeyPool.IS_AUTO_TRANSLATE, false)?:false
            setTranslateSwitch(!isTranslateSelect)
            putBooleanValue(SpKeyPool.IS_AUTO_TRANSLATE, !isTranslateSelect)
        }

        binding.connectedVisible.setOnClickListener { view: View? ->
            if (isFastClick) {
                return@setOnClickListener
            }
            val str = LanguageManager.instance?.getLocalTranslate("Wont_get_others_calls_Turn_on_anyway")?:"Won’t get others’ calls. Turn on anyway?"
            initDisturb(true,str)
        }

        binding.fitPick.setOnClickListener { view: View? ->
            setInterval(1000)
            if (isFastClick) {
                return@setOnClickListener
            }
            val str = LanguageManager.instance?.getLocalTranslate("Strangers_cant_message_you_Are_you_sure_to_turn_it_on")?:"Strangers can’t message you. Are you sure to turn it on?"
            initDisturb(false,str)
        }

        binding.innerSwitch.setOnClickListener { view: View? ->
            startActivity(
                Intent(
                    this,
                    LocaleActivity::class.java
                )
            )
        }

        val languageEnglishName: MutableMap<String?, String> = HashMap()
        languageEnglishName["en"] = translateString("English")
        languageEnglishName["ar"] = translateString("Arabic")
        languageEnglishName["es"] = translateString("Spanish")
        languageEnglishName["tr"] = translateString("Turkish")
        languageEnglishName["ko"] = translateString("Korean")
        languageEnglishName["de"] = translateString("German")
        languageEnglishName["ja"] = translateString("Japanese")
        languageEnglishName["it"] = translateString("Italian")
        languageEnglishName["hi"] = translateString("Hindi")
        languageEnglishName["zh-tw"] = translateString("Traditional Chinese")
        languageEnglishName["th"] = translateString("Thai")
        languageEnglishName["fr"] = translateString("French")
        languageEnglishName["pt"] = translateString("Portuguese")

        var languageCode = LanguageManager.instance?.translateLanguageCode

        if (mutableListOf<String?>("ar", "he", "fa", "ur").contains(languageCode)) {
            languageCode = "ar"
        }

        if (LanguageManager.instance?.supportLanguage?.contains(languageCode) == false) {
            languageCode = "en"
        }

        val LanguageText = languageEnglishName[languageCode]

        binding.briefMenu.text = LanguageText
    }

    private fun translateString(text: String): String {
        // Implement your own translation logic here
        return LanguageManager.instance?.getLocalTranslate(text)?:""
    }

    private fun initDisturb(isNotCallBtn: Boolean,title: String) {
        requestDisturb(isNotCallBtn,title)
    }

    private fun requestDisturb(isNotCallBtn: Boolean,title:String) {
        if (isNotCallBtn) {
            val isNotCall = userInfo?.switchNotDisturbCall?:false
            if (isNotCall) {
                disturb(true)
            } else {
                val settingDialog = SettingDialog(
                    this@SetAppActivity,
                    title,
                    object : SettingDialog.ConfirmBack {
                        override fun onConfirmBack() {
                            disturb(true)
                        }
                    })
                settingDialog.show(supportFragmentManager, "")
            }
        } else {
            val isNotMsg = userInfo?.switchNotDisturbIm?:false
            if (isNotMsg) {
                disturb(false)
            } else {
                val settingDialog = SettingDialog(
                    this@SetAppActivity,
                    LanguageManager.instance?.getLocalTranslate("You_will_not_receive_messages_from_strangers_Are_you_sure_to_turn_it_on")?:"",
                    object : SettingDialog.ConfirmBack {
                        override fun onConfirmBack() {
                            disturb(false)
                        }
                    })
                settingDialog.show(supportFragmentManager, "")
            }
        }
    }

    private fun disturb(isCall: Boolean) {
        val loadDialog = LoadDialog(this@SetAppActivity)
        loadDialog.show()

        val param = DisturbPatchParam()
        if (isCall) {
            param.isSwitchNotDisturbCall = (!(userInfo?.switchNotDisturbCall?:true))
            param.isSwitchNotDisturbIm = userInfo?.switchNotDisturbIm?:true
        } else {
            param.isSwitchNotDisturbCall = (userInfo?.switchNotDisturbCall?:true)
            param.isSwitchNotDisturbIm = !(userInfo?.switchNotDisturbIm?:true)
        }

        disturb(param, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(t)
                if (t.data != null) {
                    if (isCall) {
                        userInfo?.switchNotDisturbCall = !(userInfo?.switchNotDisturbCall?:false)
                        setCallSwitch(userInfo?.switchNotDisturbCall?:false)
                    } else {
                        userInfo?.switchNotDisturbIm = !(userInfo?.switchNotDisturbIm?:false)
                        setMessageSwitch(userInfo?.switchNotDisturbIm?:false)
                    }
                }
                loadDialog.dismiss()
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                loadDialog.dismiss()
            }

            override fun onComplete() {
                super.onComplete()
            }
        })
    }

    override fun transLocalText() {
        val scaleX = if (LanguageManager.instance?.isLanguageForce == true) -1f else 1f
        binding.bodyLg.scaleX = scaleX
        binding.pressedInput.scaleX = scaleX

        binding.wideThick.text = LanguageManager.instance?.getLocalTranslate("Setting")
        binding.beyondPick.text =
            LanguageManager.instance?.getLocalTranslate("Auto_Translate")
        binding.leftFrame.text =
            LanguageManager.instance?.getLocalTranslate("Do_not_disturb_Call")
        binding.filterInput.text =
            LanguageManager.instance?.getLocalTranslate("Do_not_disturb_Message")
        binding.overlayElastic.text = LanguageManager.instance?.getLocalTranslate("ID")
        binding.overlayDelete.text =
            LanguageManager.instance?.getLocalTranslate("Delete_account")
        binding.thumbnailAround.text = LanguageManager.instance?.getLocalTranslate("Logout")
        binding.footerSwipe.text =
            LanguageManager.instance?.getLocalTranslate("Language")
    }
}