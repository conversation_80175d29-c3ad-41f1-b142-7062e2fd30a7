package com.juicy.my

import android.annotation.SuppressLint
import android.app.Activity
import android.app.DatePickerDialog
import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.text.Editable
import android.text.InputType
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.juicy.app.R
import com.juicy.app.databinding.ActivityPopupBinding
import com.juicy.my.Adapter.PhotoAdapter
import com.juicy.app.modules.base.dialog.LoadDialog
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.model.bean.MediaActionBean.Companion.getInsertCr
import com.juicy.common.model.event.RegEditUserEvent
import com.juicy.common.networks.delegate.DeleteMediaInterface.delete
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.GiveAvatarToOssInterface.getAvatarToOss
import com.juicy.common.networks.delegate.GiveUpdateAvatarInterface.getUpdateAvatar
import com.juicy.common.networks.delegate.GiveUserBasicInfoInterface.SaveUserInfo
import com.juicy.common.networks.delegate.InsertMediaInterface.insert
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.DateMetaUtil.formatDate
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.AppUtil.checkNetworkToast
import com.juicy.common.utils.ImageSelectHelper.OnImageSelectListener
import com.juicy.common.utils.ImageSelectHelper.selectImage
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PermissionUtil.Companion.checkPhotoPermission
import com.juicy.common.utils.PermissionUtil.PermissionCallback
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.yesterselga.countrypicker.Country
import com.yesterselga.countrypicker.CountryPicker
import com.yesterselga.countrypicker.Theme
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.functions.Consumer
import io.reactivex.rxjava3.functions.Function
import io.reactivex.rxjava3.schedulers.Schedulers
import org.greenrobot.eventbus.EventBus
import java.util.Calendar
import java.util.Date

class EditUserInfoActivity : BaseActivity() {
    private lateinit var binding: ActivityPopupBinding
    private var selectImg = ""
    private var photoAdapter: PhotoAdapter? = null
    private val imgList: MutableList<String?> = ArrayList()
    private var photoListCount = 4
    private var curNum = 0
    private var textCount = 0
    private val imgIdList: MutableList<String?> = ArrayList()
    private var mContext: Context? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        binding = ActivityPopupBinding.inflate(layoutInflater)
        setContentView(binding.root)
        //        StatusBarUtil.setTranslucentStatus(this);
        initSetting()
        mContext = this
        super.onCreate(savedInstanceState)
    }

    private fun initSetting() {
        photoAdapter = PhotoAdapter(R.layout.item_popup)
        if (LanguageManager.instance?.isLanguageForce == true) {
            binding.toggleEdit.scaleX = -1f
        }
        binding.shadowVisible.editText?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            }

            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            }

            override fun afterTextChanged(editable: Editable) {
                if (editable.toString().length > 18) {
                    editable.delete(18, editable.length)
                }
            }
        })
        binding.cardFlexible.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            }

            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {
            }

            override fun afterTextChanged(editable: Editable) {
                if (editable.toString().length > 300) {
                    editable.delete(300, editable.length)
                }
                runOnUiThread {
                    textCount = editable.length
                    binding.belowReload.text = "$textCount/300"
                }
            }
        })
        binding.shadowVisible.titleName?.text =
            LanguageManager.instance?.getLocalTranslate("Nick_Name")
        binding.shadowVisible.rightGone?.visibility = View.VISIBLE
        binding.previewPlay.titleName?.text =
            LanguageManager.instance?.getLocalTranslate("Date_of_Birth")
        binding.previewPlay.editText?.isCursorVisible = false
        binding.previewPlay.editText?.inputType = InputType.TYPE_NULL

        binding.pagerMicro.titleName?.text =
            LanguageManager.instance?.getLocalTranslate("Country")
        binding.extendedInner.text = LanguageManager.instance?.getLocalTranslate("Personal_Signature")
        binding.pagerMicro.editText?.isCursorVisible = false
        binding.pagerMicro.editText?.inputType = InputType.TYPE_NULL
        val manager = LinearLayoutManager(this)
        manager.orientation = LinearLayoutManager.HORIZONTAL
        binding.staticCancel.layoutManager = manager
        binding.staticCancel.adapter = photoAdapter
        binding.cardFlexible.onFocusChangeListener =
            View.OnFocusChangeListener { view, b ->
//                if (b) {
//                    binding.longExpanded.setImageResource(com.juicy.utils.R.drawable.bg_setting_button_2_beige)
//                    binding.extendedInner.setTextColor(Color.parseColor("#2C2E33"))
//                } else {
//                    binding.longExpanded.setImageResource(com.juicy.utils.R.drawable.bg_setting_button_salmon)
//                    binding.extendedInner.setTextColor(Color.parseColor("#AAAAAA"))
//                }
            }
        binding.pagerMicro.editText?.onFocusChangeListener =
            View.OnFocusChangeListener { view, b ->
//                if (b) {
//                    openCountryDialog()
//                    binding.pagerMicro.editBg?.setImageResource(com.juicy.utils.R.drawable.bg_setting_button_2_beige)
//                    binding.pagerMicro.titleName?.setTextColor(Color.parseColor("#2C2E33"))
//                } else {
//                    binding.pagerMicro.editBg?.setImageResource(com.juicy.utils.R.drawable.bg_setting_button_salmon)
//                    binding.pagerMicro.titleName?.setTextColor(Color.parseColor("#AAAAAA"))
//                }
            }
        binding.previewPlay.editText?.onFocusChangeListener =
            View.OnFocusChangeListener { view, b ->
//                if (b) {
//                    openBrithDialog()
//                    binding.previewPlay.editBg?.setImageResource(com.juicy.utils.R.drawable.bg_setting_button_2_beige)
//                    binding.previewPlay.titleName?.setTextColor(Color.parseColor("#2C2E33"))
//                } else {
//                    binding.previewPlay.editBg?.setImageResource(com.juicy.utils.R.drawable.bg_setting_button_salmon)
//                    binding.previewPlay.titleName?.setTextColor(Color.parseColor("#AAAAAA"))
//                }
            }

        binding.downloadSort.setOnClickListener { view: View? ->
            hideKeyboard()
        }
    }

    private fun hideKeyboard() {
        val view = this.currentFocus
        if (view != null) {
            val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(view.windowToken, 0)
        }
    }

    override fun initData() {
        requestUserInfo()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initPhoto() {
        photoListCount = 4
        if (!Cache.instance.userInfo?.mediaList.isNullOrEmpty()) {
            imgList.clear()
            imgIdList.clear()
            for (i in Cache.instance.userInfo?.mediaList!!.indices) {
                if (Cache.instance.userInfo?.mediaList!![i].mediaType == "photo") {
                    imgIdList.add(Cache.instance.userInfo?.mediaList!![i].mediaId)
                    imgList.add(Cache.instance.userInfo?.mediaList!![i].mediaUrl)
                    photoListCount -= 1
                    if (photoListCount == 0) {
                        binding.offlineNear.visibility = View.GONE
                    } else {
                        binding.offlineNear.visibility = View.VISIBLE
                    }
                }
            }
            photoAdapter?.setList(imgList)
            photoAdapter?.notifyDataSetChanged()
        }
    }

    override fun initView() {
        if (isDestroy(this)) return

        val mainPageLayoutParams =
            binding.xlScroll.layoutParams as RelativeLayout.LayoutParams
        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        binding.xlScroll.layoutParams = mainPageLayoutParams

        photoListCount = 4
        binding.belowReload.text = "$textCount/300"
        binding.offlineNear.setOnClickListener { v: View? -> openImgWindow(true) }
        binding.previewSelect.borderWidth = dip2px(2f)
        binding.previewSelect.borderColor = getColor(R.color.white)
        binding.toggleEdit.setOnClickListener { v: View? -> finish() }
        binding.hugeSelected.setOnClickListener { v: View? -> openImgWindow(false) }
        binding.rightBrief.setOnClickListener { v: View? -> submit() }
        binding.pagerMicro.editText?.setOnClickListener { v: View? -> openCountryDialog() }
        binding.previewPlay.editText?.setOnClickListener { v: View? -> openBrithDialog() }
        if (Cache.instance.userInfo != null) {
            Glide.with(this@EditUserInfoActivity)
                .load(Cache.instance.userInfo?.avatarUrl)
                .into(binding.previewSelect)

            if (Cache.instance.userInfo?.nickname != null) {
                binding.shadowVisible.editText?.setText(Cache.instance.userInfo?.nickname)
            }
            if (Cache.instance.userInfo?.birthday != null) {
                binding.previewPlay.editText?.setText(Cache.instance.userInfo?.birthday)
            }
            if (Cache.instance.userInfo?.country != null) {
                binding.pagerMicro.editText?.setText(Cache.instance.userInfo?.country)
            }
            if (Cache.instance.userInfo?.about != null) {
                binding.cardFlexible.setText(Cache.instance.userInfo?.about)
                textCount = (Cache.instance.userInfo?.about?:"").length
            }
            initPhoto()
        }

        photoAdapter?.addChildClickViewIds(R.id.coordinatorLeft)
        photoAdapter?.setOnItemChildClickListener { adapter, view, position ->
            val dialog = LoadDialog(this@EditUserInfoActivity)
            dialog.show()
            delete(
                imgIdList[position],
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.MediaInfoBean>>>() {
                    override fun onSubscribe(d: Disposable) {
                        super.onSubscribe(d)
                    }

                    override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.MediaInfoBean>>) {
                        super.onNext(t)
                        if (isDestroy(mContext as Activity?)) return
                        if (t.data != null) {
                            Cache.instance.userInfo?.mediaList?.add(
                                (t.data as? com.juicy.common.model.bean.MediaInfoBean) ?: com.juicy.common.model.bean.MediaInfoBean()
                            )
                            initPhoto()
                        }
                        dialog.dismiss()
                    }

                    override fun onError(e: Throwable) {
                        super.onError(e)
                        dialog.dismiss()
                    }
                })
        }


        val decorView = window.decorView
        decorView.viewTreeObserver.addOnGlobalLayoutListener {
            val rect = Rect()
            //获取窗体的可视区域
            decorView.getWindowVisibleDisplayFrame(rect)
            //获取不可视区域高度，
            //在键盘没有弹起时，main.getRootView().getHeight()调节度应该和rect.bottom高度一样
            val mainInvisibleHeight = decorView.rootView.height - rect.bottom
            val screenHeight = decorView.rootView.height //屏幕高度
            //不可见区域大于屏幕本身高度的1/4
            val params =
                binding.cutFlexible.layoutParams as LinearLayout.LayoutParams
            if (mainInvisibleHeight > screenHeight / 4) {
                if (params.bottomMargin == 0) { //因为onGlobalLayout方法会频繁回调，这里要判断下，不重复设置

                    params.bottomMargin = mainInvisibleHeight
                    binding.cutFlexible.layoutParams = params
                }
            } else {
                if (params.bottomMargin != 0) { //因为onGlobalLayout方法会频繁回调，这里要判断下，不重复设置
                    params.bottomMargin = 0
                    binding.cutFlexible.layoutParams = params
                }
            }
        }
    }

    private fun submit() {
        if (binding.shadowVisible.editText?.text.toString().trim { it <= ' ' }.isEmpty()) {
            ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Please_type_in_your_name"))
            return
        } else if (binding.previewPlay.editText?.text.toString().trim { it <= ' ' }.isEmpty()) {
            ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Please_enter_your_birthday"))
            return
        } else if (binding.pagerMicro.editText?.text.toString().trim { it <= ' ' }.isEmpty()) {
            ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Please_enter_your_country"))
            return
        }
        if (checkNetworkToast()) return
        //        else if (binding.cardFlexible.getText().toString().trim().isEmpty()) {
//            ToastUtils.s(this, LanguageManager.getInstance().getLocalTranslate("Please_enter_your_personal_description"));
//            return;
//        }
        val loadDialog = LoadDialog(this)
        loadDialog.show()
        val userInfo = com.juicy.common.model.bean.EditUserInfoBean(
            binding.cardFlexible.text.toString(),
            binding.previewPlay.editText?.text.toString(),
            binding.pagerMicro.editText?.text.toString(),
            binding.shadowVisible.editText?.text.toString()
        )
        SaveUserInfo(this, userInfo, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(booleanBaseBean: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(booleanBaseBean)
                runOnUiThread(Runnable {
                    if (isDestroy(mContext as Activity?)) return@Runnable
                    if (selectImg.isEmpty()) {
                        loadDialog.dismiss()
                        ToastUtils.showShort(
                            LanguageManager.instance?.getLocalTranslate(
                                "Edit successfully"
                            )
                        )

                        finish()
                    } else {
                        updatePhoto(loadDialog, selectImg)
                    }
                })
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                loadDialog.dismiss()
            }
        })
    }

    private fun openCountryDialog() {
        val allCountry = Country.getAllCountries()
        val nonChinaCountries: MutableList<Country> = ArrayList()
        for (country in allCountry) {
            if (country.name == "China" || country.code == "CN") {
            } else {
                nonChinaCountries.add(country)
            }
        }
        val picker = CountryPicker.newInstance(
            LanguageManager.instance?.getLocalTranslate("Select_Country"),
            Theme.LIGHT
        )
        picker.setCountriesList(nonChinaCountries)
        picker.setListener { name: String?, code: String?, dialCode: String?, flagDrawableResID: Int ->
            binding.pagerMicro.editText?.setText(name)
            picker.dismiss()
        }
        picker.show(supportFragmentManager, "COUNTRY_PICKER")
    }

    private fun openBrithDialog() {
        var year = 0
        var month = 0
        var day = 0
        if (Cache.instance.userInfo != null && binding.previewPlay.editText?.text.toString().isNotEmpty()
        ) {
            val time = binding.previewPlay.editText?.text.toString().split("-".toRegex())
                .dropLastWhile { it.isEmpty() }.toTypedArray()
            year = time[0].toInt()
            month = time[1].toInt() - 1
            day = time[2].toInt()
        }

        //实例化DatePickerDialog对象
        val datePickerDialog = DatePickerDialog(
            this,
            { view, year, monthOfYear, dayOfMonth ->

                //选择完日期后会调用该回调函数
                val calendar = Calendar.getInstance()
                calendar[Calendar.YEAR] = year
                calendar[Calendar.MONTH] = monthOfYear
                calendar[Calendar.DAY_OF_MONTH] = dayOfMonth
                val current = Calendar.getInstance()
                current.time = Date(System.currentTimeMillis())
                val age = current[Calendar.YEAR] - year
                if (age >= 18) {
                    binding.previewPlay.editText?.setText(
                        formatDate(
                            calendar.time,
                            "yyyy-MM-dd"
                        )
                    )
                } else {
                    ToastUtils.showShort(
                        LanguageManager.instance?.getLocalTranslate(
                            "You must be 18 or older"
                        )
                    )
                }
            }, year, month, day
        )
        //弹出选择日期对话框
        datePickerDialog.show()
    }

    private fun openImgWindow(isMultiple: Boolean) {
        checkPhotoPermission(this, object : PermissionCallback {
            override fun complete() {
                choosePhoto(isMultiple)
            }
        })
    }

    private fun choosePhoto(isMultiple: Boolean) {
        if (isMultiple) {
            selectImage(this, photoListCount, false, object : OnImageSelectListener {
                override fun onSelected(paths: List<String>?) {
                    // 处理选择的图片路径
                    if (!paths.isNullOrEmpty()) {
                        updatePhotoList(paths)
                    }
                }

                override fun onCancel() {
                    // 用户取消选择
                }
            })
        } else {
            selectImage(this, 1, false, object : OnImageSelectListener {
                override fun onSelected(paths: List<String>?) {
                    // 处理选择的图片路径
                    if (!paths.isNullOrEmpty()) {
                        selectImg = paths[0]
                        mContext?.let {
                            Glide.with(it).load(selectImg).into(binding.previewSelect)
                        }
                        EventBus.getDefault().post(
                            RegEditUserEvent(
                                getStringValue(SpKeyPool.USER_ID_DEVICE, "")?:"",
                                Cache.instance.userInfo?.nickname?:"",
                                selectImg
                            )
                        )
                    }
                }

                override fun onCancel() {
                    // 用户取消选择
                }
            })
        }
    }

    private fun requestUserInfo() {
        if (!getStringValue(SpKeyPool.USER_ID_DEVICE, "").isNullOrEmpty()) {
            val dialog = LoadDialog(this)
            dialog.show()
            getUserInfo(
                getStringValue(SpKeyPool.USER_ID_DEVICE, "")?:"",
                object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                    override fun onSubscribe(d: Disposable) {
                        super.onSubscribe(d)
                    }

                    override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                        super.onNext(t)
                        if (isDestroy(mContext as Activity?)) return
                        if (t.data != null) {
                            Cache.instance.userInfo = t.data
                            initView()
                        }
                        dialog.dismiss()
                    }

                    override fun onError(e: Throwable) {
                        super.onError(e)
                        dialog.dismiss()
                    }
                })
        }
    }

    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

    }

    @SuppressLint("CheckResult")
    fun updatePhotoList(imgList: List<String>) {
        val loadDialog = LoadDialog(this)
        loadDialog.show()
        for (i in imgList.indices) {
            curNum = i
            Observable.just(i)
                .subscribeOn(Schedulers.io())
                .map(Function { integer ->
                    synchronized(baseContext) {
                        if (isDestroy(mContext as Activity?)) return@Function ""
                        val avatarToOss = getAvatarToOss(imgList[integer])
                        if (avatarToOss != null) {
                            return@Function avatarToOss.filename?:""
                        }
                        return@Function ""
                    }
                }).observeOn(AndroidSchedulers.mainThread())
                .subscribe(Consumer { fileName ->
                    if (isDestroy(mContext as Activity?)) return@Consumer
                    insertPhoto(imgList, loadDialog, fileName)
                }) {
                    ToastUtils.showShort(
                        LanguageManager.instance?.getLocalTranslate(
                            "Image upload failed"
                        )
                    )
                    loadDialog.dismiss()
                }
        }
    }

    private fun insertPhoto(imgList: List<String>, loadDialog: LoadDialog, photoFile: String) {
        insert(getInsertCr(photoFile), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.MediaInfoBean>>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.MediaInfoBean>>) {
                super.onNext(t)
                if (isDestroy(mContext as Activity?)) return
                if (curNum == imgList.size - 1) {
                    loadDialog.dismiss()
                }
                if (!t.data.isNullOrEmpty()) {
                    Cache.instance.userInfo?.mediaList?.add(t.data as? com.juicy.common.model.bean.MediaInfoBean
                        ?: com.juicy.common.model.bean.MediaInfoBean()
                    )
                    initPhoto()
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                loadDialog.dismiss()
            }
        })
    }

    @SuppressLint("CheckResult")
    fun updatePhoto(loadDialog: LoadDialog, compress: String?) {
        Observable.just(1)
            .subscribeOn(Schedulers.io())
            .map(Function {
                if (isDestroy(mContext as Activity?)) return@Function ""
                getAvatarToOss(compress?:"")?: com.juicy.common.model.bean.ImageInfoBean()
            })
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ avatarToOss ->
                if (avatarToOss != null && avatarToOss is com.juicy.common.model.bean.ImageInfoBean) {
                    getUpdateAvatar(
                        com.juicy.common.model.bean.AvatarUploadBean(avatarToOss.filename ?: ""),
                        object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorMedialBean>>() {
                            override fun onSubscribe(d: Disposable) {
                                super.onSubscribe(d)
                            }

                            override fun onNext(cfBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorMedialBean>) {
                                super.onNext(cfBaseBean)
                                if (isDestroy(mContext as Activity?)) return
                                if (cfBaseBean?.data != null) {
                                    Cache.instance.userInfo?.avatarUrl =
                                        cfBaseBean.data?.thumbUrl?:""
                                    ToastUtils.showShort(
                                        LanguageManager.instance?.getLocalTranslate(
                                            "Edit successfully"
                                        )
                                    )
                                    loadDialog.dismiss()
                                    finish()
                                } else {
                                    ToastUtils.showShort(
                                        LanguageManager.instance?.getLocalTranslate(
                                            "Avatar updated fail"
                                        )
                                    )
                                    loadDialog.dismiss()
                                }
                            }

                            override fun onError(e: Throwable) {
                                ToastUtils.showShort(
                                    LanguageManager.instance?.getLocalTranslate(
                                        "Avatar updated fail"
                                    )
                                )
                                loadDialog.dismiss()

                                super.onError(e)
                            }

                            override fun onComplete() {
                                super.onComplete()
                            }
                        })
                }
            }, {
                ToastUtils.showShort(
                    LanguageManager.instance?.getLocalTranslate(
                        "Avatar updated fail"
                    )
                )
                loadDialog.dismiss()
            })
    }

    override fun transLocalText() {
        val scaleX = if (LanguageManager.instance?.isLanguageForce == true) -1f else 1f
        binding.toggleEdit.scaleX = scaleX
        binding.closedHeader.text = LanguageManager.instance?.getLocalTranslate("Edit_Profile")
        binding.previewPlay.editText?.textAlignment =
            if (LanguageManager.instance?.isLanguageForce == true) View.TEXT_ALIGNMENT_VIEW_START else View.TEXT_ALIGNMENT_VIEW_END
        binding.rightBrief.text = LanguageManager.instance?.getLocalTranslate("Submit")
        //        binding..setText(LanguageManager.getInstance().getLocalTranslate("My_Level"));
    }
}