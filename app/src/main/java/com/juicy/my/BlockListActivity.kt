package com.juicy.my

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ToastUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.juicy.app.R
import com.juicy.app.databinding.ActivityAudioBinding
import com.juicy.my.Adapter.BlockListAdapter
import com.juicy.my.Adapter.BlockListAdapter.OnUnBlockClickListener
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.model.event.BlockParamsEvent
import com.juicy.common.networks.delegate.CancelBlockInterface.cancelBlock
import com.juicy.common.networks.delegate.GetBlockListInterface.blockList
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.LanguageManager
import io.reactivex.rxjava3.disposables.Disposable
import io.rong.imlib.RongIMClient
import org.greenrobot.eventbus.EventBus

class BlockListActivity : BaseActivity() {
    private lateinit var binding: ActivityAudioBinding
    private var blockList: MutableList<com.juicy.common.model.bean.SimpleUserInfoBean> = ArrayList()
    private var blockListAdapter: BlockListAdapter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        binding = ActivityAudioBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initSetting()
        super.onCreate(savedInstanceState)
    }

    private fun initSetting() {
        val manager = LinearLayoutManager(this@BlockListActivity)
        manager.orientation = LinearLayoutManager.VERTICAL
        binding.imageClear.layoutManager = manager
        blockListAdapter = BlockListAdapter(R.layout.item_title)
        binding.imageClear.adapter = blockListAdapter
        //        blockListAdapter.setAdapterAnimation(new SlideInLeftAnimation());
        blockListAdapter?.setOnItemClickListener { adapter: BaseQuickAdapter<*, *>?, view: View?, position: Int ->
            ARouter.getInstance()
                .build(Constant.ANCHOR_INFO_ACTIVITY_ROUTE)
                .withString(
                    Constant.USER_ID,
                    blockList[position].broadcasterId
                )
                .navigation(this@BlockListActivity)
        }

        blockListAdapter?.onUnBlockClickListener =
            object : OnUnBlockClickListener {
                override fun onUnBlockItemClick(anchor: com.juicy.common.model.bean.SimpleUserInfoBean) {
                    val blockInfo = com.juicy.common.model.bean.AnchorBlockBean(
                        anchor?.broadcasterId?.toLong() ?: 0
                    )
                    cancelBlock(blockInfo, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
                        override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                            super.onNext(t)
                            if (t.data != null && t.data == true) {
                                blockList.remove(anchor)
                                blockListAdapter?.setNewInstance(blockList)
                                initData()
                                //取消拉黑
                                EventBus.getDefault().post(BlockParamsEvent(anchor.broadcasterId?:"", false))
                                RongIMClient.getInstance()
                                    .removeFromBlacklist(anchor.broadcasterId, null)
                                ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Cancel_Block_Success"))
                            } else {
                                ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Cancel_Block_Failed"))
                            }
                        }

                        override fun onError(e: Throwable) {
                            super.onError(e)
                            ToastUtils.showShort(LanguageManager.instance?.getLocalTranslate("Cancel_Block_Failed"))
                        }
                    })
                }

            }

    }

    override fun initData() {
        if (Cache.instance.reviewPkg) {
            binding.borderMacro.visibility = View.GONE
        } else {
            binding.borderMacro.visibility = View.VISIBLE
        }
        binding.gridDistant.text = LanguageManager.instance?.getLocalTranslate("No_Data")

        val cacheBean: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.SimpleUserInfoBean>>? = cacheBlock
        if (cacheBean != null) {
            blockList = cacheBean.data?.toMutableList()?: mutableListOf()
            blockListAdapter?.setNewInstance(blockList)
            if (blockList.isNullOrEmpty()) {
                binding.topExpanded.visibility = View.VISIBLE
            } else {
                binding.topExpanded.visibility = View.GONE
            }
        }
        blockList(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.SimpleUserInfoBean>>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.SimpleUserInfoBean>>) {
                super.onNext(t)
                if (t.data != null) {
                    if (Cache.instance.userInfo != null) {
                        val json = Gson().toJson(
                            t,
                            object : TypeToken<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.SimpleUserInfoBean?>?>?>() {
                            }.type
                        )
                        Cache.instance.networkCacheManager.put(
                            "BlockList_" + Cache.instance.userInfo?.userId,
                            json
                        )
                    }


                    blockList = t.data?.toMutableList()?: mutableListOf()
                    blockListAdapter?.setNewInstance(blockList)
                    if (blockList.isEmpty()) {
                        binding.topExpanded.visibility = View.VISIBLE
                    } else {
                        binding.topExpanded.visibility = View.GONE
                    }
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }

            override fun onComplete() {
                super.onComplete()
            }
        })
    }

    override fun initView() {
        val mainPageLayoutParams =
            binding.upXxl.layoutParams as ConstraintLayout.LayoutParams
        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        binding.upXxl.layoutParams = mainPageLayoutParams

        binding.thickEdit.setOnClickListener { v: View? -> finish() }
    }

    override fun onResume() {
        super.onResume()
        initData()
    }

    override fun transLocalText() {
        val scaleX = if (LanguageManager.instance?.isLanguageForce == true) -1f else 1f
        binding.thickEdit.scaleX = scaleX
        binding.autoClose.text =
            LanguageManager.instance?.getLocalTranslate("Blocked_List")
    }

    val cacheBlock: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.SimpleUserInfoBean>>?
        get() {
            if (Cache.instance.userInfo == null) {
                return null
            }
            val json =
                Cache.instance.networkCacheManager.get("BlockList_" + Cache.instance.userInfo?.userId)
            if (TextUtils.isEmpty(json)) {
                return null
            }
            val data = Gson()
                .fromJson<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.SimpleUserInfoBean>>>(
                    json,
                    object :
                        TypeToken<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.SimpleUserInfoBean?>?>?>() {
                    }.type
                )

            return data
        }
}