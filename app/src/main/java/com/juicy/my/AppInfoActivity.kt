package com.juicy.my

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.BarUtils
import com.juicy.app.databinding.ActivityToggleBinding
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.config.ApiPool
import com.juicy.common.config.Cache
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.LanguageManager

class AppInfoActivity : BaseActivity() {
    private lateinit var binding: ActivityToggleBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        binding = ActivityToggleBinding.inflate(layoutInflater)
        setContentView(binding.root)
        super.onCreate(savedInstanceState)
    }

    override fun initData() {
        binding.expandedLong.text =
            "V" + Cache.instance.buildVersionName
    }

    override fun initView() {
        val mainPageLayoutParams =
            binding.primarySync.layoutParams as ConstraintLayout.LayoutParams
        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        binding.primarySync.layoutParams = mainPageLayoutParams

        binding.underlayShort.setOnClickListener { view: View? -> finish() }
        binding.menuMinimal.setOnClickListener { view: View? ->
            //条款
            if (isFastClick) {
                return@setOnClickListener
            }
            intentRongActivity(
                LanguageManager.instance?.getLocalTranslate("Terms_amp_Conditions")?:"",
                ApiPool.TERM_CONDITIONS
            )
        }

        binding.innerRigid.setOnClickListener { view: View? ->
            //隐私协议
            if (isFastClick) {
                return@setOnClickListener
            }
            intentRongActivity(
                LanguageManager.instance?.getLocalTranslate("Privacy_Policy")?:"",
                ApiPool.PRIVACY
            )
        }

        binding.headerMinimal.setOnClickListener { view: View? ->
            if (isFastClick) {
                return@setOnClickListener
            }
            //商店
            try {
                val googlePlayPackageName = "com.android.vending"
                //val googlePlayPackageName = "com.live.milklive"
                val intent = Intent(Intent.ACTION_VIEW)
                intent.setData(Uri.parse("market://details?id=" + this.packageName))
                intent.setPackage(googlePlayPackageName)
                if (intent.resolveActivity(this.packageManager) != null) {
                    this.startActivity(intent)
                } else {
                    val intent2 = Intent(Intent.ACTION_VIEW)
                    intent2.setData(Uri.parse("https://play.google.com/store/apps/details?id=" + this.packageName))
                    if (intent2.resolveActivity(this.packageManager) != null) {
                        this.startActivity(intent2)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun intentRongActivity(title: String, url: String) {
        val intent = Intent(
            baseContext,
            com.juicy.app.modules.base.activity.BasicWebActivity::class.java
        )
        intent.putExtra("title", title)
        intent.putExtra("url", url)

        startActivity(intent)
    }

    override fun transLocalText() {
        val scaleX = if (LanguageManager.instance?.isLanguageForce == true) -1f else 1f
        binding.startEnabled.scaleX = scaleX
        binding.primaryBorder.scaleX = scaleX
        binding.previewCompact.scaleX = scaleX
        binding.forwardFirst.scaleX = scaleX

        binding.switchSelect.text = LanguageManager.instance?.getLocalTranslate("About")
        binding.insideReleased.text = LanguageManager.instance?.getLocalTranslate("Terms_of_Service")
        binding.besideLong.text = LanguageManager.instance?.getLocalTranslate("Privacy_Policy")
        binding.largeAuto.text = LanguageManager.instance?.getLocalTranslate("Rate_Us")
    }
}