package com.juicy.my

import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.BarUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.juicy.app.R
import com.juicy.app.databinding.ActivitySpacerBinding
import com.juicy.my.Adapter.LevelAdapter
import com.juicy.app.modules.base.dialog.FullShoppingDialog
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.networks.delegate.LevelListInterface.getLevelList
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.SpSaveUtil.getStringValue

class TierActivity : BaseActivity() {
    private lateinit var binding: ActivitySpacerBinding

    private var adapter: LevelAdapter? = null

    private var levelInfoBean: com.juicy.common.model.bean.LevelInfoBean? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        binding = ActivitySpacerBinding.inflate(layoutInflater)
        setContentView(binding.root)

        super.onCreate(savedInstanceState)
    }

    override fun onResume() {
        super.onResume()
        netUserData
    }

    override fun initData() {
    }

    val netUserData: Unit
        get() {
            val cacheBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.LevelInfoBean>? = cacheLevel
            if (cacheBean != null) {
                levelInfoBean = cacheBean.data
                updateUI()
            }
            getLevelList(object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.LevelInfoBean>>() {
                override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.LevelInfoBean>) {
                    if (t.isOk && t.data != null) {
                        if (Cache.instance.userInfo != null) {
                            val json = Gson().toJson(
                                t,
                                object :
                                    TypeToken<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.LevelInfoBean?>?>() {}.type
                            )
                            Cache.instance.networkCacheManager.put(
                                "level_" + Cache.instance.userInfo?.userId,
                                json
                            )
                        }
                        levelInfoBean = t.data
                        updateUI()
                    } else if (Constant.TOKEN_EXPIRE_CODES.contains(
                            t.code
                        )
                    ) {
                    }
                }

                override fun onError(e: Throwable) {
                    Log.e("LevelActivity", "user level error" + e.message)
                }
            })
        }

    val cacheLevel: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.LevelInfoBean>?
        get() {
            if (Cache.instance.userInfo == null) {
                return null
            }
            val json =
                Cache.instance.networkCacheManager.get("level_" + Cache.instance.userInfo?.userId)
            if (TextUtils.isEmpty(json)) {
                return null
            }
            val data = Gson()
                .fromJson<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.LevelInfoBean>>(
                    json,
                    object : TypeToken<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.LevelInfoBean>>() {}.type
                )

            return data
        }

    override fun initView() {
        val mainPageLayoutParams =
            binding.primarySync.layoutParams as ConstraintLayout.LayoutParams
        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        binding.primarySync.layoutParams = mainPageLayoutParams

        binding.underlayShort.setOnClickListener { view: View? -> finish() }
        adapter = LevelAdapter(R.layout.item_border)
        val layoutManager: RecyclerView.LayoutManager = LinearLayoutManager(
            this, LinearLayoutManager.VERTICAL, false
        )
        binding.hiddenRight.layoutManager = layoutManager
        binding.hiddenRight.adapter = adapter

        binding.xxlDynamic.setOnClickListener { view: View? ->
            val fullShoppingDialog = FullShoppingDialog("vip_level_up")
            fullShoppingDialog.show(this.supportFragmentManager, "")
        }
        if (!getStringValue(SpKeyPool.USER_ID_DEVICE, "").isNullOrEmpty()) {
            val userId = getStringValue(SpKeyPool.USER_ID_DEVICE, "") ?: ""
            val user = getCacheUserInfo(userId)
            loadCircleImage(this, user?.avatarUrl, binding.clearLoaded)
        }
    }

    fun updateUI() {
        binding.switchReset.text = "Lv" + levelInfoBean?.level
        adapter?.setNewInstance(levelInfoBean?.userLevelList?.toMutableList()?: mutableListOf())
        binding.enabledFill.text = "Lv${levelInfoBean?.level}"
        val nextLevel = ((levelInfoBean?.level?:0) + 1).toInt()
        binding.contentHeader.text = "Lv$nextLevel"

        binding.gridToggle.text =
            LanguageManager.instance?.getLocalTranslate("Top_UP") + levelInfoBean?.needRechargeCoins + LanguageManager.instance?.getLocalTranslate(
                " coins to level up."
            )

        var currentLevelRechargedCoins = 0
        var nextLevelRechargedCoins = 0
        var isMax = false

        if (!levelInfoBean?.userLevelList.isNullOrEmpty()) {
            for (i in levelInfoBean?.userLevelList?.indices!!) {
                val model = levelInfoBean!!.userLevelList!![i]

                if (model.level == levelInfoBean!!.level) {
                    currentLevelRechargedCoins = model.rechargedCoins.toInt()

                    if (i == (levelInfoBean!!.userLevelList!!.size - 1)) {
                        isMax = true
                        binding.contentHeader.text = "Max"
                        binding.gridToggle.text =
                            LanguageManager.instance!!.getLocalTranslate("Youve_reached_the_highest_level")
                    }
                } else if (model.level == (levelInfoBean!!.level + 1)) {
                    nextLevelRechargedCoins = model.rechargedCoins.toInt()
                }
            }
        }

        if (isMax) {
            //全满
            val view = binding.counterCollapse // 获取要设置宽度的子View
            val noView = binding.dynamicInactive

            val percentage = 1.0f // 设置子View的宽度占父视图的百分比（例如50%）

            val layoutParams1 = noView.layoutParams as LinearLayout.LayoutParams
            layoutParams1.width = 0 // 设置宽度为0，以便通过权重来控制宽度
            layoutParams1.weight = 1 - percentage // 设置权重为百分比
            noView.layoutParams = layoutParams1 // 应用新的布局参数

            val layoutParams = view.layoutParams as LinearLayout.LayoutParams
            layoutParams.width = 0 // 设置宽度为0，以便通过权重来控制宽度
            layoutParams.weight = percentage // 设置权重为百分比

            view.layoutParams = layoutParams // 应用新的布局参数
        } else {
            val alldifference = nextLevelRechargedCoins - currentLevelRechargedCoins
            val selfDifference = alldifference - (levelInfoBean?.needRechargeCoins?:0).toInt()
            val ex = selfDifference.toFloat() / alldifference.toFloat()
            //按比例
            val view = binding.counterCollapse // 获取要设置宽度的子View
            val noView = binding.dynamicInactive

            val percentage = ex // 设置子View的宽度占父视图的百分比（例如50%）

            val layoutParams1 = noView.layoutParams as LinearLayout.LayoutParams
            layoutParams1.width = 0 // 设置宽度为0，以便通过权重来控制宽度
            layoutParams1.weight = 1 - percentage // 设置权重为百分比
            noView.layoutParams = layoutParams1 // 应用新的布局参数

            val layoutParams = view.layoutParams as LinearLayout.LayoutParams
            layoutParams.width = 0 // 设置宽度为0，以便通过权重来控制宽度
            layoutParams.weight = percentage // 设置权重为百分比

            view.layoutParams = layoutParams // 应用新的布局参数
        }
    }

    override fun transLocalText() {
        val scaleX = if (LanguageManager.instance?.isLanguageForce == true) -1f else 1f
        binding.startEnabled.scaleX = scaleX
        binding.switchSelect.text = LanguageManager.instance?.getLocalTranslate("My_Level")
        binding.footerReleased.text = LanguageManager.instance?.getLocalTranslate("Level")
        binding.redoMaximal.text = LanguageManager.instance?.getLocalTranslate("Topup_Amout")
        binding.offlineAvatar.text = LanguageManager.instance?.getLocalTranslate("Top_Up_Now")
    }
}