package com.juicy.my.Adapter

import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.app.R

class PhotoAdapter(layoutResId: Int) : BaseQuickAdapter<String?, PhotoViewHolder>(layoutResId) {
    override fun convert(holder: PhotoViewHolder, item: String?) {
        if (!item.isNullOrEmpty()){
            Glide.with(context).load(item).placeholder(R.drawable.img_empty_big_full_pearl).into(holder.photo)
        }
    }
}



