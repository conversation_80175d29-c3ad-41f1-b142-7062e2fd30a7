package com.juicy.my.Adapter

import android.view.View
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatImageView
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class PhotoViewHolder(view: View) : BaseViewHolder(view) {
    var photo: ImageView =
        view.findViewById(com.juicy.app.R.id.stretchSecondary)
    var delete: AppCompatImageView =
        view.findViewById(com.juicy.app.R.id.coordinatorLeft)
}