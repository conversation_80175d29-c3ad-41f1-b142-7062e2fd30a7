package com.juicy.my.Adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.app.R

class LanguageListAdapter(layoutResId: Int) :
    BaseQuickAdapter<com.juicy.common.model.bean.LanguageItemBean, LanguageItemViewHolder>(layoutResId) {
    override fun convert(holder: LanguageItemViewHolder, item: com.juicy.common.model.bean.LanguageItemBean) {
        if (item.languageCode != null) {
            holder.title.text = item.languageText
        }
        if (item.languageText != null) {
            holder.translate.text = item.tanslateText
        }
        if (item.isChecked) {
            holder.checkBox.setImageResource(R.drawable.ic_language_sel_lemon)
        } else {
            holder.checkBox.setImageResource(R.drawable.ic_language_nor_ruby)
        }
    }
}




