package com.juicy.my.Adapter

import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R
import com.juicy.common.utils.LanguageManager.Companion.instance
import com.juicy.common.utils.view.CircleImageView

class BlockItemViewHolder(view: View) : BaseViewHolder(view) {

    var anchorAge: AppCompatTextView =
        view.findViewById(R.id.miniInner)
    var anchorPhoto: CircleImageView =
        view.findViewById(R.id.fluidForeground)
    var anchorName: AppCompatTextView =
        view.findViewById(R.id.avatarLarge)
    var anchorCountry: AppCompatTextView =
        view.findViewById(R.id.outerWide)
    var unlock_btn: TextView =
        view.findViewById(R.id.coordinatorHide)

    init {
        if (instance?.isLanguageForce == true) {
            unlock_btn.text = instance?.getLocalTranslate("UnBlock")
        }
    }
}
