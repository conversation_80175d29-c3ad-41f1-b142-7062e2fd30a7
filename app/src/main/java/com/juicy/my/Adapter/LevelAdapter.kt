package com.juicy.my.Adapter

import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.Keep
import androidx.appcompat.widget.AppCompatTextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R
import com.juicy.my.Adapter.LevelAdapter.LevelVH

class LevelAdapter(layoutResId: Int) : BaseQuickAdapter<com.juicy.common.model.bean.MyLevelInfoBean, LevelVH>(layoutResId) {

    private fun getDrawableBackground(level:Int):Int{
        when(level){
            0 -> return  R.drawable.bg_level0_mist
            1 -> return  R.drawable.bg_level1_lime
            2 -> return  R.drawable.bg_level2_ebony
            3 -> return  R.drawable.bg_level3_crimson
            4 -> return  R.drawable.bg_level4_jade
            5 -> return  R.drawable.bg_level5_tan
            6 -> return  R.drawable.bg_level6_tan
            7 -> return  R.drawable.bg_level7_slate
            8 -> return  R.drawable.bg_level8_rose
            9 -> return  R.drawable.bg_level9_ebony
            10 -> return  R.drawable.bg_level10_olive
        }
        return R.drawable.bg_level0_mist
    }

    override fun convert(holder: LevelVH, item: com.juicy.common.model.bean.MyLevelInfoBean) {
        //        countryVH.countryName.setText(country.getName());

//        val imageName = "level_" + item.level
//        val resourceId = context.resources.getIdentifier(imageName, "drawable", context.packageName)
//        holder.iconImgView.setImageResource(resourceId)

        val index = data.indexOf(item)
        if (index == data.size - 1){
            holder.llLast.visibility = View.VISIBLE
            holder.llNormal.visibility = View.GONE
            holder.lastIcon.setBackgroundResource(getDrawableBackground(item.level.toInt()))
            holder.lastIcon.text = "Lv${item.level}"
            holder.lastText.text = "" + item.rechargedCoins
        }else{
            holder.llLast.visibility = View.GONE
            holder.llNormal.visibility = View.VISIBLE
            holder.tvIcon.setBackgroundResource(getDrawableBackground(item.level.toInt()))
            holder.tvIcon.text = "Lv${item.level}"
            holder.textView.text = "" + item.rechargedCoins
        }
//        if (index % 2 == 0) {
//            holder.itemLeftView.background =
//                ColorDrawable(Color.parseColor("#FFFFFF"))
//            holder.itemRightView.background =
//                ColorDrawable(Color.parseColor("#FFFFFF"))
//        } else {
//            holder.itemLeftView.background =
//                ColorDrawable(Color.parseColor("#E9E7FE"))
//            holder.itemRightView.background =
//                ColorDrawable(Color.parseColor("#E9E7FE"))
//        }
    }

    fun call(country: com.juicy.common.model.bean.MyLevelInfoBean?) {
        //点击
    }


    inner class LevelVH(view: View) : BaseViewHolder(view) {
        var itemLeftView: View =
            view.findViewById(R.id.endSelect)
        var itemRightView: View =
            view.findViewById(R.id.pauseNested)
//        var iconImgView: ImageView =
//            view.findViewById(R.id.distantUp)
        var textView: TextView =
            view.findViewById(R.id.syncContent)
        var tvIcon:AppCompatTextView = view.findViewById(R.id.successLeft)

        var llNormal:LinearLayout = view.findViewById(R.id.syncReleased)
        var llLast:LinearLayout = view.findViewById(R.id.shadowInactive)
        var lastIcon:AppCompatTextView = view.findViewById(R.id.defaultRecycler)
        var lastText:TextView = view.findViewById(R.id.removeSm)
    }
}
