package com.juicy.my.Adapter

import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.common.utils.LanguageManager.Companion.instance

class BlockListAdapter(layoutResId: Int) : BaseQuickAdapter<com.juicy.common.model.bean.SimpleUserInfoBean, BlockItemViewHolder>(layoutResId) {
    var onUnBlockClickListener: OnUnBlockClickListener? = null

    interface OnUnBlockClickListener {
        fun onUnBlockItemClick(anchor: com.juicy.common.model.bean.SimpleUserInfoBean)
    }

    override fun convert(holder: BlockItemViewHolder, item: com.juicy.common.model.bean.SimpleUserInfoBean) {
        if (item != null) {

            if (item.avatar != null) {
                Glide.with(context)
                    .load(item.avatar)
                    .into(holder.anchorPhoto)
            }
            if (item.getAge() != null) {
                holder.anchorAge.text = item.getAge().toString()
            }
            if (item.registerCountry != null) {
                holder.anchorCountry.text = item.registerCountry
            }
            if (item.nickName != null) {
                holder.anchorName.text = item.nickName
            }
            holder.unlock_btn.text = instance?.getLocalTranslate("UnBlock")
            holder.unlock_btn.setOnClickListener {
                if (onUnBlockClickListener != null) {
                    onUnBlockClickListener?.onUnBlockItemClick(item)
                }
            }
        }
    }
}


