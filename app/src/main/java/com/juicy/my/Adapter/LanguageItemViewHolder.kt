package com.juicy.my.Adapter

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R

class LanguageItemViewHolder(view: View) : BaseViewHolder(view) {
    var title: TextView =
        view.findViewById(R.id.rigidEmpty)
    var translate: TextView =
        view.findViewById(R.id.acrossCollapsed)
    var checkBox: ImageView =
        view.findViewById(R.id.overlayMask)
}