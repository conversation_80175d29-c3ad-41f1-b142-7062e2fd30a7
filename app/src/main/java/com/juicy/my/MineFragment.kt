package com.juicy.my

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.R
import com.juicy.app.databinding.FragmentMarginBinding
import com.juicy.Login.AuthActivity
import com.juicy.app.modules.base.dialog.LoadDialog
import com.juicy.app.modules.base.dialog.MeShoppingDialog
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.model.event.RegEditUserEvent
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.GiveAvatarToOssInterface.getAvatarToOss
import com.juicy.common.networks.delegate.GiveUpdateAvatarInterface.getUpdateAvatar
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.AppDeviceUtils.getScreenSize
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.ImageSelectHelper.OnImageSelectListener
import com.juicy.common.utils.ImageSelectHelper.selectImage
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PermissionUtil.Companion.checkPhotoPermission
import com.juicy.common.utils.PermissionUtil.PermissionCallback
import com.juicy.common.utils.SpSaveUtil.getStringValue
import com.juicy.common.utils.TextUtils.getStrLen
import com.juicy.common.utils.TextUtils.subStrByLen
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


class MineFragment : BaseFragment() {
    private val TAG: String = javaClass.simpleName
    private lateinit var binding: FragmentMarginBinding
    private var userInfo: com.juicy.common.model.bean.JuicyUserInfoBean? = null

    private var isAdded = false
    private var mContext: Context? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentMarginBinding.inflate(layoutInflater)
        mContext = context
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(evevnt: com.juicy.common.model.bean.LocalPriceChangeBean?) {
        if (Cache.instance.userInfo != null) initUserInfo(Cache.instance.userInfo!!)
    }

    override fun onResume() {
        super.onResume()
        if (!isAdded()) return
        //获取用户数据
        if (!getStringValue(SpKeyPool.USER_ID_DEVICE, "").isNullOrEmpty()) {
            val userId = getStringValue(SpKeyPool.USER_ID_DEVICE, "")?:""
            requestUserInfo(userId)
        } else {
            //退到登录页面
            startActivity(Intent(requireContext(), AuthActivity::class.java))
            Log.e("MinePage", "onResume: user id null")
        }

        if (Cache.instance.reviewPkg) {
            //审核模式隐藏客服
            binding.hugeLeft.visibility = View.GONE
        }
        loadBanner()
    }

    fun loadBanner() {
        if (isAdded && binding != null) {
            binding.headerAbove.loadBanner()
        }
    }

    override fun initData() {
    }

    override fun initView() {
        binding.root.post(Runnable {
            if (isDestroy(activity)) return@Runnable
            if (!isAdded() || view == null) return@Runnable
            val mainPageLayoutParams =
                binding.compactUpload.layoutParams as ConstraintLayout.LayoutParams
            mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
            binding.compactUpload.layoutParams = mainPageLayoutParams

            binding.closedLeft.setOnClickListener { view: View? -> }
            binding.thickFlexible.text = LanguageManager.instance?.getLocalTranslate("Edit_data")
            binding.closeExpand.text = LanguageManager.instance?.getLocalTranslate("My_Coins")
            binding.downloadAround.text = LanguageManager.instance?.getLocalTranslate("My_Coins")
//            binding.farBottom.borderColor = activity?.getColor(R.color.white)?: Color.WHITE
//            binding.farBottom.borderWidth = dip2px(3f)


            binding.narrowBeyond.text = "VIP"
            if (LanguageManager.instance?.isLanguageForce == true) {
                binding.errorNested.scaleX = -1f
                binding.inputUnlocked.rotation = -20f
                binding.surfaceOnline.scaleX = -1f
                binding.longAcross.scaleX = -1f
            }else{
                binding.inputUnlocked.rotation = 20f
            }

            binding.hiddenSlider.icon?.setImageResource(R.drawable.ic_profile_level_icon_gray)
            binding.hiddenSlider.name?.text =
                LanguageManager.instance?.getLocalTranslate("My_Level")
            binding.miniSelect.layoutDirection = View.LAYOUT_DIRECTION_LTR
            binding.hugeLeft.icon?.setImageResource(R.drawable.ic_profile_custom_bronze)
            binding.hugeLeft.name?.text =
                LanguageManager.instance?.getLocalTranslate("Customer_Service")
            binding.listMiddle.icon?.setImageResource(R.drawable.ic_profile_question_beige)
            binding.listMiddle.name?.text = LanguageManager.instance?.getLocalTranslate("About")
            binding.collapsedUp.icon?.setImageResource(R.drawable.ic_profile_block_silver)
            binding.collapsedUp.name?.text =
                LanguageManager.instance?.getLocalTranslate("Blocked_List")
            binding.outsideOffline.icon?.setImageResource(R.drawable.ic_profile_setting_gold)
            binding.outsideOffline.name?.text =
                LanguageManager.instance?.getLocalTranslate("Setting")
            binding.listMiddle.rightText?.text = "V" + Cache.instance.buildVersionName
            binding.cardPressed.setOnClickListener { view: View? ->
                val meShoppingDialog = MeShoppingDialog(true, true)
                meShoppingDialog.entry = "user_center"
                meShoppingDialog.show(childFragmentManager, "")
            }
            if (Cache.instance.reviewPkg) {
                binding.hugeLeft.visibility = View.GONE
            }
            binding.deleteDrag.setOnClickListener { view: View? ->
                startActivity(
                    Intent(
                        requireContext(),
                        com.juicy.app.modules.base.activity.PremiumActivity::class.java
                    )
                )
            }
            binding.hiddenSlider.setOnClickListener { view: View? ->
                startActivity(
                    Intent(
                        requireContext(),
                        TierActivity::class.java
                    )
                )
            }

            binding.hugeLeft.setOnClickListener { view: View? ->
                startActivity(
                    Intent(
                        requireContext(),
                        com.juicy.app.modules.base.activity.BotActivity::class.java
                    )
                )
            }
            binding.collapsedUp.setOnClickListener { view: View? ->
                //黑名单
                startActivity(
                    Intent(
                        requireContext(),
                        BlockListActivity::class.java
                    )
                )
            }
            binding.outsideOffline.setOnClickListener { view: View? ->
                //设置
                startActivity(
                    Intent(
                        requireContext(),
                        SetAppActivity::class.java
                    )
                )
            }
            binding.listMiddle.setOnClickListener { view: View? ->
                //关于
                startActivity(
                    Intent(
                        requireContext(),
                        AppInfoActivity::class.java
                    )
                )
            }
            binding.editBtn.setOnClickListener { view: View? ->
                //编辑页面
                startActivity(
                    Intent(
                        requireContext(),
                        EditUserInfoActivity::class.java
                    )
                )
            }
            binding.farBottom.setOnClickListener { view: View? ->
                checkPhotoPermission()
            }

            val layoutParams = binding.headerAbove.layoutParams as ConstraintLayout.LayoutParams
            layoutParams.height =
                ((getScreenSize(mContext)[0] - dip2px(32f) * 2) * 100.0 / 328.0).toInt()
            binding.headerAbove.layoutParams = layoutParams
            binding.headerAbove.loadBanner()
        })
    }

    private fun checkPhotoPermission() {
        //请求存储权限
//        choosePhoto();
        if (activity == null) return
        val ac = activity as BaseActivity?
        ac?.let {
            checkPhotoPermission(it, object : PermissionCallback {
                override fun complete() {
                    choosePhoto()
                }
            })
        }

    }

    private fun choosePhoto() {
        selectImage(this.activity, 1, false, object : OnImageSelectListener {
            override fun onSelected(paths: List<String>?) {
                updatePhoto(paths?.get(0))
            }

            override fun onCancel() {
            }
        })
    }

    fun requestUserInfo(userId: String) {
        val user = getCacheUserInfo(userId)
        if (user != null) {
            Cache.instance.userInfo = user

            if (isAdded()) {
                userInfo = user
                userInfo?.let {
                    initUserInfo(it)
                }

            }
        }
        //获取用户数据
        getUserInfo(userId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
            override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                if (azBaseBean != null) {
                    if (azBaseBean.isOk && azBaseBean.data != null) {
                        Cache.instance.userInfo = azBaseBean.data

                        if (isAdded()) {
                            userInfo = azBaseBean.data
                            userInfo?.let {
                                initUserInfo(it)
                            }

                        }
                    }
                    //                    else if (Constant.TOKEN_EXPIRE_CODES.contains(azBaseBean.getCode())) {
//                        ToastUtils.showShort(LanguageManager.getInstance().getLocalTranslate("Token_expired_please_login_again"));
//                        exitToLogin();
//                    }
                }
            }

            override fun onError(e: Throwable) {
                Log.e(TAG, "mine user info error" + e.message)
            }
        })
    }

    override fun onStart() {
        super.onStart()
        isAdded = true
    }

    fun initUserInfo(userInfo: com.juicy.common.model.bean.JuicyUserInfoBean) {
        if (!isAdded()) return
        loadCircleImage(this.context, userInfo.avatarUrl, binding.farBottom)
        //        Glide.with(this)
//                .load(userInfo.getAvatarUrl())
//                .placeholder(com.juicy.utils.R.drawable.img_small_empty_indigo)
//                .into(binding.farBottom);
        if (getStrLen(userInfo.nickname) > 24) {
            binding.copyDown.text = subStrByLen(userInfo.nickname, 24)
        } else {
            binding.copyDown.text = userInfo.nickname
        }
        binding.autoTimer.text = userInfo.age.toString()
        binding.maximalBtn.text = userInfo.country
        binding.unselectedNext.text = userInfo.availableCoins.toString()

        binding.hiddenSlider.rightText?.text = "Lv" + userInfo.level
        val vip = userInfo.vip
        if (vip) {
            val dateString = "2025-01-01"
            binding.narrowBeyond.text =
                LanguageManager.instance?.getLocalTranslate("Valid_until") + dateString
            binding.narrowBeyond.setTextColor(Color.parseColor("#FFF2B300"))
            binding.barAcross.visibility = View.GONE
            binding.topBright.visibility = View.GONE
            binding.pickerEdit.visibility = View.GONE
            binding.autoAuto.text = LanguageManager.instance?.getLocalTranslate("Activated_VIP")?:"Activated VIP"
        } else {
            if (Cache.instance.subscribeGood != null) {
                binding.barAcross.text =
                    Cache.instance.subscribeGood?.localPrice + "/" + LanguageManager.instance?.getLocalTranslate(
                        "month"
                    )
                binding.narrowBeyond.text =
                    "VIP" + " + " + Cache.instance.subscribeGood?.exchangeCoin
                binding.narrowBeyond.setTextColor(Color.BLACK)
                binding.barAcross.visibility = View.VISIBLE
                binding.topBright.visibility = View.VISIBLE
                binding.pickerEdit.visibility = View.VISIBLE
                binding.autoAuto.text = LanguageManager.instance?.getLocalTranslate("Unavailable_VIP")?:"Unavailable VIP"
            }
        }


        //性别
        if (userInfo.gender == Constant.SEX_MAN) {
            //男

            binding.brightThin.setImageResource(R.drawable.ic_gender_boy_pink)
        } else if (userInfo.gender == Constant.SEX_WOMAN) {
            //女
            binding.brightThin.setImageResource(R.drawable.ic_gender_girl_silver)
        }
        var isVip = userInfo.vip
        //isVip = true
        if (isVip) {
            binding.inputUnlocked.visibility = View.VISIBLE
        } else {
            binding.inputUnlocked.visibility = View.GONE
        }
    }

    @SuppressLint("CheckResult")
    fun updatePhoto(compress: String?) {
        var loadDialog:LoadDialog ?= null
        activity?.let {
            loadDialog = LoadDialog(it)
            loadDialog?.show()
        }

        Observable.just(1)
            .subscribeOn(Schedulers.io())
            .map {
                getAvatarToOss(
                    compress
                )?: com.juicy.common.model.bean.ImageInfoBean()
            }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ avatarToOss ->
                if (avatarToOss != null) {
                    getUpdateAvatar(
                        com.juicy.common.model.bean.AvatarUploadBean(avatarToOss.filename ?: ""),
                        object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorMedialBean>>() {
                            override fun onSubscribe(d: Disposable) {
                                super.onSubscribe(d)
                            }

                            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.AnchorMedialBean>) {
                                super.onNext(t)
                                if (t?.data != null) {
                                    Cache.instance.userInfo?.avatarUrl = t.data?.thumbUrl?:""
                                    Cache.instance.userInfo?.let {
                                        initUserInfo(it)
                                    }

                                    ToastUtils.showShort(
                                        LanguageManager.instance?.getLocalTranslate(
                                            "Avatar updated successfully"
                                        )
                                    )
                                    EventBus.getDefault().post(
                                        RegEditUserEvent(
                                            getStringValue(SpKeyPool.USER_ID_DEVICE, "")?:"",
                                            Cache.instance.userInfo?.nickname?:"",
                                            t.data?.thumbUrl?:""
                                        )
                                    )
                                } else {
                                    ToastUtils.showShort(
                                        LanguageManager.instance?.getLocalTranslate(
                                            "Avatar updated fail"
                                        )
                                    )
                                }
                            }

                            override fun onError(e: Throwable) {
                                ToastUtils.showShort(
                                    LanguageManager.instance?.getLocalTranslate(
                                        "Avatar updated fail"
                                    )
                                )
                                super.onError(e)
                            }

                            override fun onComplete() {
                                super.onComplete()
                            }
                        })
                }
                loadDialog?.dismiss()
            }, {
                ToastUtils.showShort(
                    LanguageManager.instance?.getLocalTranslate(
                        "Avatar updated fail"
                    )
                )
                loadDialog?.dismiss()
            })
    }
}