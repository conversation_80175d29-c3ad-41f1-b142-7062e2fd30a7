package com.juicy.message.dialog

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.juicy.app.databinding.DialogLabelBinding
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.LanguageManager.Companion.instance

class ConversationMenuDialog // Required empty public constructor
    (private val isTop: <PERSON>olean, var didBack: ConversationListLongDialogCallBack) :
    DialogFragment() {
    interface ConversationListLongDialogCallBack {
        fun didSelectTop()
        fun didSelectDelete()
    }


    private var dialogBinding: DialogLabelBinding? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dialogBinding = DialogLabelBinding.inflate(inflater)
        return dialogBinding!!.root
    }

    override fun onStart() {
        super.onStart()
        if (dialog != null) {
            val layoutParams = dialog!!.window!!.attributes
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.width = dip2px(this.context!!, 270f)
            dialog!!.window!!.attributes = layoutParams
            dialog!!.window!!.setGravity(Gravity.CENTER)
            dialog!!.window!!.setBackgroundDrawable(null)

        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialogBinding!!.fluidBeside.text =
            if (isTop) instance!!.getLocalTranslate("Remove_from_top") else instance!!.getLocalTranslate(
                "Stick on Top"
            )

        dialogBinding!!.fitUnfocused.setOnClickListener { v: View? ->
            didBack.didSelectDelete()
            dismiss()
        }
        dialogBinding!!.toggleContent.text =
            instance!!.getLocalTranslate("Remove_from_chat_list")
        dialogBinding!!.wideMain.setOnClickListener { v: View? ->
            didBack.didSelectTop()
            dismiss()
        }
    }


}