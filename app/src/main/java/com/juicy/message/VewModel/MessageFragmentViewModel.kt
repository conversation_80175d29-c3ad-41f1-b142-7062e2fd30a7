package com.juicy.message.VewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.juicy.common.model.bean.PageParamsBean
import com.juicy.common.networks.delegate.GetFriendsListInterface.getFriendsList
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.db.User
import com.juicy.common.status.UserOnlineStatusService.Companion.instance

class MessageFragmentViewModel : ViewModel() {

    var userList: List<User> = ArrayList()
    var followList: MutableList<com.juicy.common.model.bean.BaseUserInfoBean> = ArrayList()
    var page: Int = 1
    var userData: MutableLiveData<Int> = MutableLiveData()
    var loadFollowData: MutableLiveData<Int> = MutableLiveData() //-2,-1 失败，0 成功 1 无更多数据




    fun loadFollowData() {
        getFriendsList(PageParamsBean(10, page), object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.BaseUserInfoBean>>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<List<com.juicy.common.model.bean.BaseUserInfoBean>>) {
                super.onNext(t)
                if (!t.data.isNullOrEmpty()) {
                    if (page == 1) {
                        followList = t.data?.toMutableList()?: mutableListOf()
                        loadFollowData.postValue(0)
                    } else {
                        var list = followList
                        if (list == null) {
                            list = ArrayList()
                        }
                        list.addAll(t.data!!)
                        followList = list
                        loadFollowData.postValue(0)
                    }

                    val userIds: MutableList<String> = ArrayList()
                    for (userBean in t.data!!) {
                        userIds.add(userBean.userId?:"")
                    }
                    instance?.addUserIdsAndRefresh(userIds.toList()?: listOf())
                    return
                }

                loadFollowData.postValue(1)
            }



            override fun onComplete() {
                super.onComplete()
                //k
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                if (page != 1) {
                    loadFollowData.postValue(-1)
                } else {
                    loadFollowData.postValue(-2)
                }

                if (page != 1) {
                    page--
                }
            }
        })
    }
}
