package com.juicy.message.VewModel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.ObservableCallBack

class MessageInfoViewModel : ViewModel() {
    var statusData: MutableLiveData<String>? = null
        get() {
            if (field == null) {
                field = MutableLiveData()
            }
            return field
        }
        private set
     var isFollowData: MutableLiveData<Boolean> = MutableLiveData<Boolean>()
    var userData: MutableLiveData<com.juicy.common.model.bean.JuicyUserInfoBean?> = MutableLiveData<com.juicy.common.model.bean.JuicyUserInfoBean?>()
    private fun inquire(data: List<com.juicy.common.model.bean.BaseUserInfoBean>, id: String) {
        if (data.isNotEmpty()) {
            for (user in data) {
                if (user.userId == id) {
                    isFollowData.postValue(true)
                    return
                }
            }
        }
    }
    fun updateUser(id: String) {
        val azBean = getCacheUserInfo(id)
        if (azBean != null) {
            userData.postValue(azBean)
            isFollowData.postValue(azBean.isFriend)
            return
        }

        getUserInfo(id, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                super.onNext(t)
                if (t.data != null) {
                    userData.postValue(t.data)
                    isFollowData.postValue(t.data?.isFriend)
                    return
                }
                userData.postValue(null)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                userData.postValue(null)
            }
        })
    }



}
