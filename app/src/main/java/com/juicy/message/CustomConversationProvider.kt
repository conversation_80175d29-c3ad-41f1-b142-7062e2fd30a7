package com.juicy.message

import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.juicy.app.R
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.config.Cache
import com.juicy.common.utils.DateMetaUtil.formatDate
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import com.juicy.common.utils.LanguageManager
import io.rong.imkit.conversationlist.model.BaseUiConversation
import io.rong.imkit.conversationlist.provider.BaseConversationProvider
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder

class CustomConversationProvider : BaseConversationProvider() {
    override fun bindViewHolder(
        holder: ViewHolder,
        uiConversation: BaseUiConversation,
        position: Int,
        list: List<BaseUiConversation>,
        listener: IViewProviderListener<BaseUiConversation>?
    ) {
        super.bindViewHolder(holder, uiConversation, position, list, listener)
        val topBg = holder.getView<View>(R.id.hugeReload)
        val line = holder.getView<View>(R.id.divider)
        val line2 = holder.getView<View>(R.id.xxlSecondary)

        val rcConversationItem = holder.getView<ConstraintLayout>(R.id.rc_conversation_item)
        if (list[position].mCore.isTop) {
            topBg.visibility = View.VISIBLE
            line.visibility = View.GONE
            line2.visibility = View.GONE
            rcConversationItem.background =
                holder.context.getDrawable(R.drawable.bg_top_bg_platinum)
        } else {
            line.visibility = View.VISIBLE
            topBg.visibility = View.VISIBLE
            line2.visibility = View.VISIBLE
            rcConversationItem.background = null
        }
        if (LanguageManager.instance!!.isLanguageForce) {
            (holder.getView<View>(R.id.rc_conversation_title) as TextView).gravity =
                Gravity.END
            (holder.getView<View>(R.id.rc_conversation_content) as TextView).gravity =
                Gravity.END
            //            ((RelativeLayout)holder.getView(R.id.rc_conversation_unread)).setLayoutDirection(View.LAYOUT_DIRECTION_RTL);
        }
        val user = getCacheUserInfo(uiConversation.mCore.targetId)

        if (user != null) {
            holder.setText(R.id.rc_conversation_title, user.nickname)
            loadCircleImage(
                holder.context,
                user.avatarThumbUrl,
                holder.getView(R.id.rc_conversation_portrait)
            )
        } else {
            loadCircleImage(
                holder.context,
                list[position].mCore.portraitUrl,
                holder.getView(R.id.rc_conversation_portrait)
            )
        }

        var isSystem = false
        //判断是否是系统Id
        if (Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.topOfficialUserIds != null &&
            Cache.instance.userStratResult!!.topOfficialUserIds!!.contains(uiConversation.mCore.targetId)
        ) {
            isSystem = true
        }
        if (Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.broadcasterFollowOfficialUserIds != null &&
            Cache.instance.userStratResult!!.broadcasterFollowOfficialUserIds!!.contains(
                uiConversation.mCore.targetId
            )
        ) {
            isSystem = true
        }
        if (Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.userServiceAccountId != null &&
            Cache.instance.userStratResult!!.userServiceAccountId == uiConversation.mCore.targetId
        ) {
            isSystem = true
        }
        val systemImageView = holder.getView<ImageView>(R.id.tinyLarge)
        if (isSystem) {
            systemImageView.visibility = View.VISIBLE
        } else {
            systemImageView.visibility = View.INVISIBLE
        }

        val dateView = holder.getView<TextView>(R.id.rc_conversation_date)

        dateView.text =
            formatDate(uiConversation.mCore.sentTime, System.currentTimeMillis())

        if (LanguageManager.instance!!.isLanguageForce) {
            dateView.gravity = Gravity.START
        }
        //        String date = CustomRongDateUtils.getConversationListFormatDate(uiConversation.mCore.getSentTime(), holder.getContext());
//        holder.setText(R.id.rc_conversation_date, date);
    }


    fun formatDate(endTime: Long, currentTime: Long): String {
        val nd = (1000 * 24 * 60 * 60).toLong()
        val nh = (1000 * 60 * 60).toLong()
        val nm = (1000 * 60).toLong()
        val time: String
        val diff = currentTime - endTime
        // 计算差多少天
        val day = diff / nd
        // 计算差多少小时
        val hour = diff % nd / nh
        // 计算差多少分钟
        val min = diff % nd % nh / nm
        //判断相差多少小时
        time = if (hour < 1) {
            //显示多少分钟之前
            "$min m ago"
        } else if (day < 1) {
            "$hour h ago"
        } else if (day < 7) {
            "$day d ago"
        } else {
            formatDate(endTime)
        }
        return time
    }
}
