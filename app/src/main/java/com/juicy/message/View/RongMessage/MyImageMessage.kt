package com.juicy.message.View.RongMessage

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableString
import android.view.View
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.juicy.message.CustomRongDateUtils.getCallRecordTime
import com.juicy.app.JuicyApplication.Companion.juicyApplication
import com.juicy.common.utils.AppDeviceUtils.getScreenSize
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.view.TextureVideoViewOutlineProvider
import io.rong.common.RLog
import io.rong.imkit.IMCenter
import io.rong.imkit.R
import io.rong.imkit.conversation.messgelist.provider.ImageMessageItemProvider
import io.rong.imkit.feature.resend.ResendManager
import io.rong.imkit.model.UiMessage
import io.rong.imkit.picture.tools.ScreenUtils
import io.rong.imkit.utils.RongDateUtils
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.message.HistoryDividerMessage
import io.rong.message.ImageMessage
import kotlin.math.min

class MyImageMessage : ImageMessageItemProvider() {
    private var minSize: Int? = null
    private var maxSize: Int? = null
    override fun getSummarySpannable(context: Context, imageMessage: ImageMessage): Spannable {
        val content = "[" + LanguageManager.instance!!.getLocalTranslate("image") + "]"
        return SpannableString(content)
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        message: ImageMessage,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
//        super.bindMessageContentViewHolder(holder, parentHolder, message, uiMessage, position, list, listener);

        val view = holder.getView<View>(R.id.rc_image) as ImageView
        if (!this.checkViewsValid(*arrayOf<View>(view))) {
            RLog.e("ImageMessageItemProvide", "checkViewsValid error," + uiMessage.objectName)
        } else {
            val thumUri = message.thumUri
            //            Uri thumUri = message.getLocalUri() == null ? message.getRemoteUri() : message.getLocalUri();
            if (uiMessage.state != 2 && (uiMessage.state != 1 || !ResendManager.getInstance().needResend(uiMessage.messageId))
            ) {
                holder.setVisible(R.id.rl_progress, false)
                holder.setVisible(R.id.main_bg, false)
            } else {
                holder.setVisible(R.id.rl_progress, true)
                holder.setVisible(R.id.main_bg, true)
                holder.setText(R.id.tv_progress, uiMessage.progress.toString() + "%")
            }


            val originalUri = if (message.remoteUri != null) message.remoteUri else message.localUri
            if (originalUri != null) {
//                view.setImageResource(com.juicy.utils.R.drawable.img_empty_big_full_pearl);

                (Glide.with(view).load(originalUri)
                    .error(if (uiMessage.message.messageDirection == Message.MessageDirection.SEND)
                        R.drawable.rc_send_thumb_image_broken else R.drawable.rc_received_thumb_image_broken))
                    .apply(RequestOptions.bitmapTransform(RoundedCorners(ScreenUtils.dip2px(IMCenter.getInstance().context, 6.0f))).override(Int.MIN_VALUE, Int.MIN_VALUE))
                    .into(object :CustomTarget<Drawable>(){
                        override fun onResourceReady(
                            resource: Drawable,
                            transition: Transition<in Drawable>?
                        ) {
                            measureLayoutParams(holder.getView(R.id.rl_content), resource)
                            view.setImageDrawable(resource)
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                           val params = view.layoutParams
                            params.height = ScreenUtils.dip2px(view.context, 35.0f)
                            params.width = ScreenUtils.dip2px(view.context, 35.0f)
                            view.layoutParams = params
                            view.setImageResource(if (uiMessage.message.messageDirection == Message.MessageDirection.SEND) R.drawable.rc_send_thumb_image_broken else R.drawable.rc_received_thumb_image_broken)
                        }

                    })
            } else if (thumUri != null && thumUri.path != null) {
                (Glide.with(view).load(thumUri.path)
                    .error(if (uiMessage.message.messageDirection == Message.MessageDirection.SEND) R.drawable.rc_send_thumb_image_broken
                    else R.drawable.rc_received_thumb_image_broken))
                    .apply(RequestOptions.bitmapTransform(RoundedCorners(ScreenUtils.dip2px(IMCenter.getInstance().context, 6.0f))).override(Int.MIN_VALUE, Int.MIN_VALUE))
                    .into(object :CustomTarget<Drawable>(){
                        override fun onResourceReady(
                            resource: Drawable,
                            transition: Transition<in Drawable>?
                        ) {
                            measureLayoutParams(holder.getView(R.id.rl_content), resource)
                            view.setImageDrawable(resource)
                        }

                        override fun onLoadCleared(placeholder: Drawable?) {
                            val params = view.layoutParams
                            params.height = ScreenUtils.dip2px(view.context, 35.0f)
                            params.width = ScreenUtils.dip2px(view.context, 35.0f)
                            view.layoutParams = params
                            view.setImageResource(if (uiMessage.message.messageDirection == Message.MessageDirection.SEND) R.drawable.rc_send_thumb_image_broken else R.drawable.rc_received_thumb_image_broken)
                        }

                    })
            } else {
                val params = view.layoutParams
                params.height = ScreenUtils.dip2px(view.context, 35.0f)
                params.width = ScreenUtils.dip2px(view.context, 35.0f)
                view.layoutParams = params
                view.setImageResource(if (uiMessage.message.messageDirection == Message.MessageDirection.SEND) R.drawable.rc_send_thumb_image_broken else R.drawable.rc_received_thumb_image_broken)
            }
        }
    }

    override fun bindViewHolder(
        holder: ViewHolder,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        super.bindViewHolder(holder, uiMessage, position, list, listener)
        initTime(holder, position, list, uiMessage.message)
    }

    private fun initTime(
        holder: ViewHolder,
        position: Int,
        data: List<UiMessage>,
        message: Message
    ) {
        val time = getCallRecordTime(message.sentTime)
        holder.setText(R.id.rc_time, time)
        if (position == 0) {
            holder.setVisible(R.id.rc_time, message.content !is HistoryDividerMessage)
        } else {
            val pre = data[position - 1]
            if (pre.message != null && RongDateUtils.isShowChatTime(
                    holder.context,
                    message.sentTime,
                    pre.message.sentTime,
                    180
                )
            ) {
                holder.setVisible(R.id.rc_time, true)
            } else {
                holder.setVisible(R.id.rc_time, false)
            }
        }
    }

    private fun measureLayoutParams(view: View?, drawable: Drawable) {
        if (view != null) {
            val width = drawable.intrinsicWidth
            val height = drawable.intrinsicHeight
            if (this.minSize == null) {
                this.minSize = THUMB_COMPRESSED_MIN_SIZE
            }

            if (this.maxSize == null) {
                this.maxSize = THUMB_COMPRESSED_SIZE
            }

            val finalWidth: Int
            val finalHeight: Int
            if (width >= minSize!! && height >= minSize!!) {
                if (width < maxSize!! && height < maxSize!!) {
                    finalWidth = width
                    finalHeight = height
                } else if (width > height) {
                    if (width.toFloat() * 1.0f / height.toFloat() <= maxSize!!.toFloat() * 1.0f / minSize!!.toFloat()) {
                        finalWidth = maxSize!!
                        finalHeight =
                            (maxSize!!.toFloat() * 1.0f / width.toFloat() * height.toFloat()).toInt()
                    } else {
                        finalWidth = maxSize!!
                        finalHeight = minSize!!
                    }
                } else if (height.toFloat() * 1.0f / width.toFloat() <= maxSize!!.toFloat() * 1.0f / minSize!!.toFloat()) {
                    finalHeight = maxSize!!
                    finalWidth =
                        (maxSize!!.toFloat() * 1.0f / height.toFloat() * width.toFloat()).toInt()
                } else {
                    finalHeight = maxSize!!
                    finalWidth = minSize!!
                }
            } else if (width < height) {
                finalWidth = minSize!!
                finalHeight = min(
                    (minSize!!.toFloat() * 1.0f / width.toFloat() * height.toFloat()).toInt()
                        .toDouble(),
                    maxSize!!.toDouble()
                ).toInt()
            } else {
                finalHeight = minSize!!
                finalWidth = min(
                    (minSize!!.toFloat() * 1.0f / height.toFloat() * width.toFloat()).toInt()
                        .toDouble(),
                    maxSize!!.toDouble()
                ).toInt()
            }

            val params = view.layoutParams
            params.height = ScreenUtils.dip2px(view.context, (finalHeight / 2).toFloat())
            params.width = ScreenUtils.dip2px(view.context, (finalWidth / 2).toFloat())
            view.layoutParams = params
            view.outlineProvider = TextureVideoViewOutlineProvider(5f)
            view.clipToOutline = true
        }
    }

    companion object {
        private val THUMB_COMPRESSED_SIZE =
            (getScreenSize(juicyApplication)[0] * 0.2 * 16.0 / 9.0).toInt()
        private val THUMB_COMPRESSED_MIN_SIZE = (getScreenSize(juicyApplication)[0] * 0.2).toInt()
    }
}