package com.juicy.message.View.RongMessage

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.RotateAnimation
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.res.ResourcesCompat
import com.blankj.utilcode.util.ToastUtils
import com.juicy.message.CustomRongDateUtils.getCallRecordTime
import com.juicy.app.R
import com.juicy.common.config.Cache
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.SpSaveUtil.getBooleanVal
import com.juicy.common.utils.TextViewUtil.setTextColorGradient
import com.juicy.common.utils.TranslateUtils.textTrans
import com.juicy.common.utils.throttle.Throttle.throttle
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.functions.Function
import io.reactivex.rxjava3.schedulers.Schedulers
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversation.messgelist.provider.TextMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.utils.RongDateUtils
import io.rong.imkit.utils.RouteUtils
import io.rong.imkit.utils.TextViewUtils
import io.rong.imkit.widget.ILinkClickListener
import io.rong.imkit.widget.LinkTextViewMovementMethod
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.message.HistoryDividerMessage
import io.rong.message.TextMessage
import java.util.Locale

class MyTextMessage : TextMessageItemProvider() {
    override fun onCreateMessageContentViewHolder(viewGroup: ViewGroup, viewType: Int): ViewHolder {
        val textView = LayoutInflater.from(viewGroup.context)
            .inflate(R.layout.rc_text_message_item, viewGroup, false)
        return ViewHolder(viewGroup.context, textView)
    }

    override fun bindViewHolder(
        holder: ViewHolder,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        val isSender =
            (uiMessage
                .message
                .messageDirection
                    == Message.MessageDirection.SEND)
        holder.getView<View>(io.rong.imkit.R.id.rc_content).background =
            null as Drawable?
        super.bindViewHolder(holder, uiMessage, position, list, listener)
        holder.getView<View>(io.rong.imkit.R.id.rc_content).background =
            null as Drawable?
        initTime(holder, position, list, uiMessage.message)
    }

    private fun initTime(
        holder: ViewHolder,
        position: Int,
        data: List<UiMessage>,
        message: Message
    ) {
        val time = getCallRecordTime(message.sentTime)
        holder.setText(io.rong.imkit.R.id.rc_time, time)
        if (position == 0) {
            holder.setVisible(io.rong.imkit.R.id.rc_time, message.content !is HistoryDividerMessage)
        } else {
            val pre = data[position - 1]
            if (pre.message != null && RongDateUtils.isShowChatTime(
                    holder.context,
                    message.sentTime,
                    pre.message.sentTime,
                    180
                )
            ) {
                holder.setVisible(io.rong.imkit.R.id.rc_time, true)
            } else {
                holder.setVisible(io.rong.imkit.R.id.rc_time, false)
            }
        }
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        message: TextMessage,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        mConfig.showContentBubble = false
        uiMessage.isChange = true

        val rcContent = parentHolder.getView<FrameLayout>(R.id.rc_content)
        rcContent.background = null
        val sendText = holder.getView<AppCompatTextView>(R.id.firstPicker)
        val translateBtnText = holder.getView<AppCompatTextView>(R.id.saveSurface)
        translateBtnText.text = LanguageManager.instance!!.getLocalTranslate("Click_to_translate")
        val translateClick = holder.getView<LinearLayout>(R.id.copyDynamic)
        val translateText = holder.getView<AppCompatTextView>(R.id.startPager)
        val translateIcon = holder.getView<AppCompatImageView>(R.id.contentBeyond)
        val translateLl = holder.getView<LinearLayout>(R.id.throughEnd)
        val lineView = holder.getView<LinearLayout>(R.id.stretchLong)
        val loading = holder.getView<ImageView>(R.id.middleLg)
        val textContent = holder.getView<LinearLayout>(R.id.foregroundRefresh)
        val rootView = holder.getView<View>(R.id.closedLeft)
        translateLl.visibility = View.GONE
        lineView.visibility = View.GONE
        translateText.visibility = View.GONE
        sendText.tag = uiMessage.messageId
        //        holder.getView(io.rong.imkit.R.id.rc_content).setBackground((Drawable)null);
        val isSender =
            (uiMessage
                .message
                .messageDirection
                    == Message.MessageDirection.SEND)

        textContent.setBackgroundResource(
            if (isSender)
                if (LanguageManager.instance!!.isLanguageForce) R.drawable.bg_text_message_self_rlt_bg_violet else R.drawable.bg_text_message_self_bg_ash
            else
                if (LanguageManager.instance!!.isLanguageForce) R.drawable.bg_text_message_user_rlt_bg_teal else R.drawable.bg_text_message_user_bg_silver
        )

        if (uiMessage.contentSpannable == null && message.content != null) {
            val spannable = TextViewUtils.getSpannable(
                message.content
            ) { spannable1: SpannableStringBuilder? ->
                uiMessage.contentSpannable =
                    spannable1
                if (sendText.tag == uiMessage.messageId) {
                    sendText.post { sendText.text = uiMessage.contentSpannable }
                }
            }
            uiMessage.contentSpannable = spannable
        }
        if (uiMessage.contentSpannable != null) {
            sendText.text = uiMessage.contentSpannable
        }

        sendText.movementMethod =
            LinkTextViewMovementMethod(ILinkClickListener { link: String ->
                var result = false
                if (RongConfigCenter.conversationConfig().conversationClickListener != null) {
                    result = RongConfigCenter.conversationConfig().conversationClickListener
                        .onMessageLinkClick(holder.context, link, uiMessage.message)
                }
                if (result) return@ILinkClickListener true
                val str = link.lowercase(Locale.getDefault())
                if (str.startsWith("http") || str.startsWith("https")) {
                    RouteUtils.routeToWebActivity(sendText.context, link)
                    result = true
                }
                result
            })
        sendText.setOnClickListener { view: View? ->
            val parent = rootView.parent
            if (parent is View) {
                (parent as View).performClick()
            }
        }
        translateClick.setOnClickListener(View.OnClickListener {
            if (isFastClick) {
                return@OnClickListener
            }
            throttle("text_tranlate") {
                translateLl.visibility = View.VISIBLE
                translateFun(
                    uiMessage.message.messageId,
                    sendText.text.toString(),
                    translateClick,
                    translateBtnText,
                    translateIcon,
                    lineView,
                    translateText,
                    loading
                )
            }
        })
        sendText.setOnLongClickListener { view: View? ->
            val parent = rootView.parent
            parent is View && (parent as View).performLongClick()
        }
        if (uiMessage.userInfo != null) {
            val isCounterpartMessage = uiMessage.userInfo.userId == Cache.instance.userInfo!!.userId
            if (!isCounterpartMessage) {
                sendText.setTextColor(
                    ResourcesCompat.getColor(
                        sendText.context.resources,
                        R.color.white,
                        sendText.context.theme
                    )
                )
                //                rootView.setBackground(AppCompatResources.getDrawable(holder.getContext(), R.drawable.rc_ic_bubble_left));
                val autoTranslate = getBooleanVal(SpKeyPool.IS_AUTO_TRANSLATE, false)
                val chacheModel: ChatMessageModel? = ChatMessageCache.Companion.instance?.getCacheMessage(uiMessage.message.messageId)
                if (autoTranslate!!) {
                    if (chacheModel != null) {
                        if (chacheModel.tranStatus == 0) {
                            translateLl.visibility = View.VISIBLE
                            translateText.visibility = View.VISIBLE
                            translateText.text = chacheModel.tanslation
                            translateClick.visibility = View.GONE
                            loading.visibility = View.GONE
                            loading.clearAnimation()
                        } else if (chacheModel.tranStatus == 2) {
                            ToastUtils.showShort("Translate Failed")
                            translateBtnText.text =
                                LanguageManager.instance!!.getLocalTranslate("Translation_failedplease_try_again")
//                            setTextColorGradient(
//                                translateBtnText,
//                                Color.parseColor("#FF007BFF"),
//                                Color.parseColor("#E31EFF")
//                            )
                            translateIcon.setImageResource(com.juicy.app.R.drawable.anim_translate_reset_mint)
                            translateClick.visibility = View.VISIBLE
                            lineView.visibility = View.GONE
                            loading.visibility = View.GONE
                            loading.clearAnimation()
                        } else if (chacheModel.tranStatus == 1) {
                            loading.visibility = View.GONE
                            translateClick.visibility = View.GONE
                            loading.clearAnimation()
                            translateLl.visibility = View.VISIBLE
                            loading.visibility = View.VISIBLE
                            val animation: Animation = RotateAnimation(
                                0f,
                                360f,
                                Animation.RELATIVE_TO_SELF,
                                0.5f,
                                Animation.RELATIVE_TO_SELF,
                                0.5f
                            )
                            animation.duration = 3000
                            animation.repeatCount = 50
                            animation.fillAfter = true

                            loading.startAnimation(animation)

                            translateBtnText.text =
                                LanguageManager.instance!!.getLocalTranslate("Translating")
//                            setTextColorGradient(
//                                translateBtnText,
//                                Color.parseColor("#08F6FF"),
//                                Color.parseColor("#E31EFF")
//                            )
                            lineView.visibility = View.VISIBLE

                            val model = ChatMessageModel(uiMessage.messageId, null, 3)
                            model.translateClick = translateClick
                            model.translateBtnText = translateBtnText
                            model.translateIcon = translateIcon
                            model.line = lineView
                            model.loading = loading
                            model.animation = animation
                            ChatMessageCache.Companion.instance.setCacheMessage(model)

                            //                            ChatMessageCache.getInstance().setCacheMessage(uiMessage.getMessageId(),null,3);
                        }
                    } else {
                        translateLl.visibility = View.VISIBLE
                        translateFun(
                            uiMessage.message.messageId,
                            sendText.text.toString(),
                            translateClick,
                            translateBtnText,
                            translateIcon,
                            lineView,
                            translateText,
                            loading
                        )
                    }
                } else { //不自动翻译，缓存
                    if (chacheModel != null) {
                        if (chacheModel.tranStatus == 0) {
                            translateLl.visibility = View.VISIBLE
                            translateText.visibility = View.VISIBLE
                            translateText.text = chacheModel.tanslation
                            translateClick.visibility = View.GONE
                            loading.visibility = View.GONE
                            loading.clearAnimation()
                        } else if (chacheModel.tranStatus == 2) {
                            ToastUtils.showShort("Translate Failed")
                            translateBtnText.text =
                                LanguageManager.instance!!.getLocalTranslate("Translation_failedplease_try_again")
//                            setTextColorGradient(
//                                translateBtnText,
//                                Color.parseColor("#08F6FF"),
//                                Color.parseColor("#E31EFF")
//                            )
                            translateIcon.setImageResource(com.juicy.app.R.drawable.anim_translate_reset_mint)
                            lineView.visibility = View.GONE
                            translateClick.visibility = View.VISIBLE
                            loading.visibility = View.GONE
                            loading.clearAnimation()
                        } else if (chacheModel.tranStatus == 1) {
                            loading.visibility = View.GONE
                            translateClick.visibility = View.GONE
                            loading.clearAnimation()
                            translateLl.visibility = View.VISIBLE
                            loading.visibility = View.VISIBLE
                            val animation: Animation = RotateAnimation(
                                0f,
                                360f,
                                Animation.RELATIVE_TO_SELF,
                                0.5f,
                                Animation.RELATIVE_TO_SELF,
                                0.5f
                            )
                            animation.duration = 3000
                            animation.repeatCount = 50
                            animation.fillAfter = true

                            loading.startAnimation(animation)

                            translateBtnText.text =
                                LanguageManager.instance!!.getLocalTranslate("Translating")
//                            setTextColorGradient(
//                                translateBtnText,
//                                Color.parseColor("#08F6FF"),
//                                Color.parseColor("#E31EFF")
//                            )
                            lineView.visibility = View.VISIBLE

                            val model = ChatMessageModel(uiMessage.messageId, null, 3)
                            model.translateClick = translateClick
                            model.translateBtnText = translateBtnText
                            model.translateIcon = translateIcon
                            model.line = lineView
                            model.loading = loading
                            model.animation = animation
                            ChatMessageCache.Companion.instance.setCacheMessage(model)

                            //                            ChatMessageCache.getInstance().setCacheMessage(uiMessage.getMessageId(),null,3);
                        }
                    } else {
                        translateClick.visibility = View.VISIBLE
                        translateBtnText.text =
                            LanguageManager.instance!!.getLocalTranslate("Click_to_translate")
                        loading.visibility = View.GONE
                        loading.clearAnimation()
                    }
                }
            } else {
                sendText.setTextColor(
                    ResourcesCompat.getColor(
                        sendText.context.resources,
                        R.color.black,
                        sendText.context.theme
                    )
                )
                //                rootView.setBackground(AppCompatResources.getDrawable(holder.getContext(), R.drawable.rc_ic_bubble_right));
                translateLl.visibility = View.GONE
                lineView.visibility = View.GONE
                translateClick.visibility = View.GONE

                //                RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) textContent.getLayoutParams();
//                params.addRule(RelativeLayout.ALIGN_PARENT_END);
//                textContent.setLayoutParams(params);
            }
        }

//        setTextColorGradient(
//            translateBtnText,
//            Color.parseColor("#FF08F6FF"),
//            Color.parseColor("#FFE31EFF")
//        )
    }


    @SuppressLint("CheckResult")
    private fun translateFun(
        messageId: Int,
        text: String?,
        translateClick: LinearLayout,
        translateBtnText: AppCompatTextView,
        translateIcon: AppCompatImageView,
        line: LinearLayout,
        translateText: AppCompatTextView,
        loading: ImageView
    ) {
        if (text == null || text.isEmpty()) return
        loading.visibility = View.VISIBLE
        translateClick.visibility = View.GONE
        val animation: Animation = RotateAnimation(
            0f,
            360f,
            Animation.RELATIVE_TO_SELF,
            0.5f,
            Animation.RELATIVE_TO_SELF,
            0.5f
        )
        animation.duration = 3000
        animation.repeatCount = 50
        animation.fillAfter = true

        loading.startAnimation(animation)

        translateBtnText.text = LanguageManager.instance!!.getLocalTranslate("Translating")
        setTextColorGradient(
            translateBtnText,
            Color.parseColor("#08F6FF"),
            Color.parseColor("#E31EFF")
        )
        line.visibility = View.VISIBLE

        ChatMessageCache.Companion.instance.setCacheMessage(messageId, null, 1)

        Observable
            .just<Int>(1)
            .subscribeOn(Schedulers.newThread())
            .map<String>(Function<Int, String> { integer: Int? -> textTrans(text)?:"" }
            )
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe({ s: String? ->
                if (s != null) {
                    val model: ChatMessageModel? =
                        ChatMessageCache.Companion.instance?.getCacheMessage(messageId)
                    if (model != null && model.tranStatus == 3) {
                        if (model.loading != null) {
                            model.translateText!!.visibility = View.VISIBLE
                            model.translateText!!.text = s
                            model.translateClick!!.visibility = View.GONE
                            model.loading!!.visibility = View.GONE
                            model.animation!!.cancel()
                            model.loading!!.clearAnimation()
                        }
                        ChatMessageCache.Companion.instance.setCacheMessage(messageId, s, 0)
                        //发送让列表重新刷新
                        return@subscribe
                    } else {
                        ChatMessageCache.Companion.instance.setCacheMessage(messageId, s, 0)
                    }
                    translateText.visibility = View.VISIBLE
                    translateText.text = s
                    translateClick.visibility = View.GONE
                } else {
                    val model: ChatMessageModel? =
                        ChatMessageCache.Companion.instance.getCacheMessage(messageId)
                    if (model != null && model.tranStatus == 3) {
                        if (model.loading != null) {
                            model.translateBtnText!!.text =
                                LanguageManager.instance!!.getLocalTranslate("Translation_failedplease_try_again")
                            setTextColorGradient(
                                model.translateBtnText,
                                Color.parseColor("#08F6FF"),
                                Color.parseColor("#E31EFF")
                            )
                            model.translateIcon!!.setImageResource(com.juicy.app.R.drawable.anim_translate_reset_mint)
                            model.line!!.visibility = View.GONE
                            model.loading!!.visibility = View.GONE
                            model.animation!!.cancel()
                            model.loading!!.clearAnimation()
                        }
                        ChatMessageCache.Companion.instance.setCacheMessage(messageId, null, 2)
                        //发送让列表重新刷新
                        return@subscribe
                    } else {
                        ChatMessageCache.Companion.instance.setCacheMessage(messageId, null, 2)
                    }

                    ToastUtils.showShort("Translate Failed")

                    translateBtnText.text =
                        LanguageManager.instance!!.getLocalTranslate("Translation_failedplease_try_again")
                    setTextColorGradient(
                        translateBtnText,
                        Color.parseColor("#08F6FF"),
                        Color.parseColor("#E31EFF")
                    )
                    translateIcon.setImageResource(com.juicy.app.R.drawable.anim_translate_reset_mint)
                    line.visibility = View.GONE
                }
                loading.visibility = View.GONE
                animation.cancel()
                loading.clearAnimation()
            }, { throwable: Throwable? ->
                val model: ChatMessageModel? =
                    ChatMessageCache.Companion.instance.getCacheMessage(messageId)
                if (model != null && model.tranStatus == 3) {
                    if (model.loading != null) {
                        model.translateBtnText!!.text =
                            LanguageManager.instance!!.getLocalTranslate("Translation_failedplease_try_again")
                        setTextColorGradient(
                            model.translateBtnText,
                            Color.parseColor("#08F6FF"),
                            Color.parseColor("#E31EFF")
                        )
                        model.translateIcon!!.setImageResource(com.juicy.app.R.drawable.anim_translate_reset_mint)
                        model.line!!.visibility = View.GONE
                        model.loading!!.visibility = View.GONE
                        model.animation!!.cancel()
                        model.loading!!.clearAnimation()
                    }
                    ChatMessageCache.Companion.instance.setCacheMessage(messageId, null, 2)
                    //发送让列表重新刷新
                    return@subscribe
                } else {
                    ChatMessageCache.Companion.instance.setCacheMessage(messageId, null, 2)
                }

                loading.visibility = View.GONE
                animation.cancel()
                loading.clearAnimation()
                translateBtnText.text =
                    LanguageManager.instance!!.getLocalTranslate("Translation_failedplease_try_again")
                setTextColorGradient(
                    translateBtnText,
                    Color.parseColor("#08F6FF"),
                    Color.parseColor("#E31EFF")
                )
                translateIcon.setImageResource(com.juicy.app.R.drawable.anim_translate_reset_mint)
                line.visibility = View.GONE
            })
    }
}
