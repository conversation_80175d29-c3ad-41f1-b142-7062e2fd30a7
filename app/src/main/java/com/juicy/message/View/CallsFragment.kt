package com.juicy.message.View

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.android.arouter.facade.annotation.Route
import com.juicy.message.adapter.TabAdapter
import com.juicy.message.adapter.PagerAdapter
import com.juicy.app.databinding.ItemButtonBinding
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.LanguageManager

@Route(path = Constant.MESSAGE_FRAGMENT_ROUTE)
class CallsFragment : BaseFragment() {
    private var messageFragmentBinding: ItemButtonBinding? = null
    private val fragmentBases: MutableList<Fragment> = ArrayList()
    private val tabs: MutableList<String> = ArrayList()
    private var messageTabAdapter: TabAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        messageFragmentBinding = ItemButtonBinding.inflate(inflater)
        return messageFragmentBinding!!.root
    }

    override fun initData() {
        messageTabAdapter = TabAdapter(
            tabs,
            object : TabAdapter.CallBack {
                override fun onCallBack(position: Int) {
                    messageFragmentBinding!!.autoHeader.currentItem = position
                }

            }
        )
        fragmentBases.add(CalledFragment())
        tabs.add(LanguageManager.instance!!.getLocalTranslate("Calls"))
    }

    override fun initView() {
        val linearLayoutManager = LinearLayoutManager(
            context
        )
        linearLayoutManager.orientation = RecyclerView.HORIZONTAL
        messageFragmentBinding!!.narrowLoading.layoutManager = linearLayoutManager
        messageFragmentBinding!!.narrowLoading.adapter = messageTabAdapter
        messageFragmentBinding!!.narrowLoading.isNestedScrollingEnabled = false

        val pagerAdapter = PagerAdapter(childFragmentManager, lifecycle)
        pagerAdapter.setFragmentBases(fragmentBases)
        messageFragmentBinding!!.autoHeader.adapter = pagerAdapter
        messageFragmentBinding!!.autoHeader.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                messageTabAdapter!!.selectPosition = position
                messageTabAdapter!!.notifyDataSetChanged()
            }
        })
        setRedPoint(Cache.instance.unreadCount > 0)
    }

    fun setRedPoint(isShow: Boolean) {
        if (messageTabAdapter != null) {
            messageTabAdapter!!.notifyItemChanged(0)
        }
    }
}
