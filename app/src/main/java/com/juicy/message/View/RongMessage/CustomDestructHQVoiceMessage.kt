package com.juicy.message.View.RongMessage

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableString
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.juicy.app.R
import com.juicy.message.CustomRongDateUtils.getCallRecordTime
import com.juicy.common.utils.LanguageManager
import io.rong.imkit.conversation.messgelist.provider.HQVoiceMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.utils.RongDateUtils
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.message.HQVoiceMessage
import io.rong.message.HistoryDividerMessage

class CustomDestructHQVoiceMessage : HQVoiceMessageItemProvider() {

    override fun getSummarySpannable(context: Context, conversation: Conversation): Spannable {
        val content = "[" + LanguageManager.instance!!.getLocalTranslate("voice") + "]"
        return SpannableString(content)
    }
    override fun getSummarySpannable(context: Context, message: HQVoiceMessage): Spannable {
        val content = "[" + LanguageManager.instance!!.getLocalTranslate("voice") + "]"
        return SpannableString(content)
    }






    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        message: HQVoiceMessage,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        super.bindMessageContentViewHolder(
            holder,
            parentHolder,
            message,
            uiMessage,
            position,
            list,
            listener
        )

        val isSender =
            (uiMessage
                .message
                .messageDirection
                    == Message.MessageDirection.SEND)
        val textContent = holder.getView<LinearLayout>(R.id.rc_voice_bg)
        textContent.setBackgroundResource(
            if (isSender)
                if (LanguageManager.instance!!.isLanguageForce) R.drawable.bg_text_message_self_rlt_bg_violet else R.drawable.bg_text_message_self_bg_ash
            else
                if (LanguageManager.instance!!.isLanguageForce) R.drawable.bg_text_message_user_rlt_bg_teal else R.drawable.bg_text_message_user_bg_silver
        )

        if (LanguageManager.instance!!.isLanguageForce) {
            val textView = holder.getView<TextView>(R.id.rc_duration)
            val leftImg = holder.getView<ImageView>(R.id.rc_voice)
            val rightImg = holder.getView<ImageView>(R.id.rc_voice_send)
            if (!isSender) {
                textView.gravity = Gravity.END
                leftImg.scaleX = -1f
                leftImg.visibility = View.VISIBLE
                rightImg.visibility = View.GONE

            } else {
                textView.gravity = Gravity.START
                rightImg.scaleX = -1f
                leftImg.visibility = View.GONE
                rightImg.visibility = View.VISIBLE

            }
        }
    }

    override fun bindViewHolder(
        holder: ViewHolder,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        holder.getView<View>(R.id.rc_content).background =
            null as Drawable?
        super.bindViewHolder(holder, uiMessage, position, list, listener)
        holder.getView<View>(R.id.rc_content).background =
            null as Drawable?
        initTime(holder, position, list, uiMessage.message)
    }


    private fun initTime(
        holder: ViewHolder,
        position: Int,
        data: List<UiMessage>,
        message: Message
    ) {
        val time = getCallRecordTime(message.sentTime)
        holder.setText(R.id.rc_time, time)
        if (position == 0) {
            holder.setVisible(R.id.rc_time, message.content !is HistoryDividerMessage)
        } else {
            val pre = data[position - 1]
            if (pre.message != null && RongDateUtils.isShowChatTime(
                    holder.context,
                    message.sentTime,
                    pre.message.sentTime,
                    180
                )
            ) {
                holder.setVisible(R.id.rc_time, true)
            } else {
                holder.setVisible(R.id.rc_time, false)
            }
        }
    }
}
