package com.juicy.message.View.RongMessage

import io.rong.imkit.conversation.extension.DefaultExtensionConfig
import io.rong.imkit.conversation.extension.component.plugin.FilePlugin
import io.rong.imkit.conversation.extension.component.plugin.IPluginModule
import io.rong.imkit.conversation.extension.component.plugin.ImagePlugin
import io.rong.imlib.model.Conversation

class MyExtensionConfig : DefaultExtensionConfig() {
    override fun getPluginModules(
        conversationType: Conversation.ConversationType,
        targetId: String
    ): List<IPluginModule> {
        val pluginModules = super.getPluginModules(conversationType, targetId)
        val iterator = pluginModules.listIterator()

        // 删除扩展项
        while (iterator.hasNext()) {
            val integer = iterator.next()
            // 以删除 FilePlugin 为例
            if (integer is FilePlugin) {
                iterator.remove()
            }
            if (integer is ImagePlugin) {
                iterator.remove()
            }
        }
        iterator.add(MyImagePlugin())

        return pluginModules
    }
}
