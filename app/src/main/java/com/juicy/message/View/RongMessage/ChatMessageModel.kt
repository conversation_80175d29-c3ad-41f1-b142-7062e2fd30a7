package com.juicy.message.View.RongMessage

import android.view.animation.Animation
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView

class ChatMessageModel(
    var messageId: Int, var tanslation: String?, //翻译状态
    var tranStatus: Int // -1 未翻译 0 成功 1 正在翻译 2 失败
) {
    var translateBtnText: AppCompatTextView? = null
    var translateIcon: AppCompatImageView? = null
    var animation: Animation? = null
    var line: LinearLayout? = null
    var translateText: AppCompatTextView? = null
    var loading: ImageView? = null
    var translateClick: LinearLayout? = null

}
