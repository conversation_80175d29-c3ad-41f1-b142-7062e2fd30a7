package com.juicy.message.View.RongMessage

import android.net.Uri
import com.juicy.common.networks.Service
import com.juicy.common.model.bean.UserIdParamsBean
import com.juicy.common.networks.request.RetrofitManage
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import io.rong.imkit.userinfo.UserDataProvider
import io.rong.imlib.model.UserInfo
import java.io.IOException


/**
 * 融云用户信息提供者
 */
class UserInfoProvider : UserDataProvider.UserInfoProvider {
    override fun getUserInfo(userId: String): UserInfo {
        try {
            val azBean = getCacheUserInfo(userId)
            if (azBean != null) {
                val userInfo = UserInfo(userId, azBean.nickname, Uri.parse(azBean.avatarThumbUrl))
                getUserInfo(userId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                    override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                        super.onNext(azBaseBean)
                    }
                })
                return userInfo
            }
            val execute = RetrofitManage
                .instance
                .create(Service::class.java)
                .getUserInfo2(UserIdParamsBean(userId))
                .execute()
            if (execute.body() != null && execute.body()!!.data != null) {
                Cache.instance.networkCacheManager.putObject(
                    "userInfo_$userId",
                    execute.body()!!.data
                )
                return UserInfo(
                    userId, execute.body()!!.data!!.nickname, Uri.parse(
                        execute.body()!!.data!!.avatarUrl
                    )
                )
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return UserInfo(userId ?: "", "", Uri.parse(""))
    }
}
