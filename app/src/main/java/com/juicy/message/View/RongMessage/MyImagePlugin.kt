package com.juicy.message.View.RongMessage

import android.content.Context
import androidx.fragment.app.Fragment
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PermissionUtil.Companion.checkPhotoPermission
import com.juicy.common.utils.PermissionUtil.PermissionCallback
import io.rong.imkit.conversation.extension.RongExtension
import io.rong.imkit.conversation.extension.component.plugin.ImagePlugin

class MyImagePlugin : ImagePlugin() {
    override fun onClick(currentFragment: Fragment, extension: RongExtension, index: Int) {
        val currentActivity = currentActivity
        if (currentActivity != null && currentActivity is BaseActivity) {
            checkPhotoPermission(currentActivity, object : PermissionCallback {
                override fun complete() {
                    <EMAIL>(currentFragment, extension, index)
                }
            })
        } else {
            super.onClick(currentFragment, extension, index)
        }
    }

    override fun obtainTitle(context: Context): String {
        return LanguageManager.instance?.getLocalTranslate("pic")?:"pic"
    }
}
