package com.juicy.message.View.RongMessage

class ChatMessageCache {

    fun getCacheMessage(messageId: Int): ChatMessageModel? {
        return messageCacheMap[messageId.toString()]
    }
    private val messageCacheMap: MutableMap<String, ChatMessageModel?> = HashMap()
    fun cleanCache() {
        messageCacheMap.clear()
    }
    fun setCacheMessage(messageId: Int, tanslation: String?, Status: Int) {
        val chatMessageModel = ChatMessageModel(messageId, tanslation, Status)
        if (messageCacheMap[messageId.toString()] == null) {
            if (messageCacheMap.size > 200) {
                messageCacheMap.clear()
            }
        }
        messageCacheMap[messageId.toString()] = chatMessageModel
    }
    fun setCacheMessage(chatMessageModel: ChatMessageModel) {
        if (messageCacheMap[chatMessageModel.messageId.toString()] == null) {
            if (messageCacheMap.size > 200) {
                messageCacheMap.clear()
            }
        }
        messageCacheMap[chatMessageModel.messageId.toString()] = chatMessageModel
    }




    companion object {
        private var cache: ChatMessageCache? = null
        val instance: ChatMessageCache
            get() {
                if (cache == null) {
                    cache = ChatMessageCache()
                }
                return cache!!
            }
    }
}
