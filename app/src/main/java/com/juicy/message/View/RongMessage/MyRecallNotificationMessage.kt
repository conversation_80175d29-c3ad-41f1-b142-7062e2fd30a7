package com.juicy.message.View.RongMessage

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import com.juicy.message.CustomRongDateUtils.getCallRecordTime
import com.juicy.common.utils.LanguageManager
import io.rong.imkit.R
import io.rong.imkit.conversation.messgelist.provider.RecallNotificationMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.utils.RongDateUtils
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.message.HistoryDividerMessage
import io.rong.message.RecallNotificationMessage

class MyRecallNotificationMessage : RecallNotificationMessageItemProvider() {
    override fun getSummarySpannable(context: Context, data: RecallNotificationMessage): Spannable {
        val content = LanguageManager.instance!!.getLocalTranslate("You_have_recalled_a_message")
        return SpannableString(content)
    }

    override fun getSummarySpannable(context: Context, conversation: Conversation): Spannable {
        val content = LanguageManager.instance!!.getLocalTranslate("You_have_recalled_a_message")
        return SpannableString(content)
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        content: RecallNotificationMessage,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        super.bindMessageContentViewHolder(
            holder,
            parentHolder,
            content,
            uiMessage,
            position,
            list,
            listener
        )
        holder.setText(
            R.id.rc_msg,
            LanguageManager.instance!!.getLocalTranslate("You_have_recalled_a_message")
        )
        holder.setText(R.id.rc_edit, LanguageManager.instance!!.getLocalTranslate("Edit"))
    }

    override fun bindViewHolder(
        holder: ViewHolder,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        super.bindViewHolder(holder, uiMessage, position, list, listener)
        initTime(holder, position, list, uiMessage.message)
    }

    private fun initTime(
        holder: ViewHolder,
        position: Int,
        data: List<UiMessage>,
        message: Message
    ) {
        val time = getCallRecordTime(message.sentTime)
        holder.setText(R.id.rc_time, time)
        if (position == 0) {
            holder.setVisible(R.id.rc_time, message.content !is HistoryDividerMessage)
        } else {
            val pre = data[position - 1]
            if (pre.message != null && RongDateUtils.isShowChatTime(
                    holder.context,
                    message.sentTime,
                    pre.message.sentTime,
                    180
                )
            ) {
                holder.setVisible(R.id.rc_time, true)
            } else {
                holder.setVisible(R.id.rc_time, false)
            }
        }
    }
}
