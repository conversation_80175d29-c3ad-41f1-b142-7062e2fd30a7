package com.juicy.message.View

import android.content.Context
import android.content.Intent
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.juicy.app.R
import com.juicy.common.utils.LanguageManager

class CustomerServiceHeaderView : ConstraintLayout {
    private var rootView: ConstraintLayout? = null

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    private fun init(context: Context) {
        rootView = LayoutInflater.from(context).inflate(
            R.layout.item_counter,
            this, true
        ) as ConstraintLayout
        // 如果需要设置标题文本
        val titleView = rootView!!.findViewById<TextView>(R.id.rc_conversation_title)
        if (titleView != null) {
            titleView.text = LanguageManager.instance!!.getLocalTranslate("Customer_Service")
        }

        // 设置点击事件
        setOnClickListener { v: View? ->
            context.startActivity(
                Intent(
                    context,
                    com.juicy.app.modules.base.activity.BotActivity::class.java
                )
            )
        }

        // 设置语言
    }
}