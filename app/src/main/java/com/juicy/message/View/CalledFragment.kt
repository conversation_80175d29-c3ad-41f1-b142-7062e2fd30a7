package com.juicy.message.View

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.juicy.app.R
import com.juicy.message.VewModel.MessageFragmentViewModel
import com.juicy.message.adapter.CallsLogAdapter
import com.juicy.app.databinding.LayoutBinding
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.db.CallDatabase
import com.juicy.common.db.User
import com.juicy.common.config.Cache
import com.juicy.common.utils.LanguageManager
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener
import com.scwang.smartrefresh.layout.listener.OnRefreshListener

class CalledFragment : BaseFragment() {
    var page: Int = 1
    private var calledLayoutBinding: LayoutBinding? = null
    private var callsLogAdapter: CallsLogAdapter? = null
    private var callViewModel: MessageFragmentViewModel? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        calledLayoutBinding = LayoutBinding.inflate(inflater)
        return calledLayoutBinding!!.root
    }

    override fun initData() {
        if (Cache.instance.reviewPkg) {
            calledLayoutBinding!!.borderMacro.visibility = View.GONE
        } else {
            calledLayoutBinding!!.borderMacro.visibility = View.VISIBLE
        }

        calledLayoutBinding!!.gridDistant.text =
            LanguageManager.instance!!.getLocalTranslate("No_Data")

        initRc()
        activity?.let {
            callViewModel = ViewModelProvider(it).get(
                MessageFragmentViewModel::class.java
            )
        }

        callViewModel?.userData?.observe(this) { status: Int ->
            if (status == -1) {
                calledLayoutBinding!!.smallOutside.finishRefresh(false)
                calledLayoutBinding!!.smallOutside.finishLoadMore(false)
            } else if (status == 1) {
                if (page == 1) {
                    calledLayoutBinding!!.smallOutside.finishRefresh(true)
                } else {
                    calledLayoutBinding!!.smallOutside.finishLoadMoreWithNoMoreData()
                }
            } else {
                calledLayoutBinding!!.smallOutside.finishRefresh(true)
                calledLayoutBinding!!.smallOutside.finishLoadMore(true)
            }
            if (!callViewModel?.userList.isNullOrEmpty()) {
                if (callViewModel?.userList?.isNotEmpty() == true) {
                    calledLayoutBinding!!.topExpanded.visibility = View.GONE
                } else {
                    calledLayoutBinding!!.topExpanded.visibility = View.VISIBLE
                }

                callsLogAdapter?.setList(callViewModel?.userList)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (isAdded) {
            calledLayoutBinding?.smallOutside?.resetNoMoreData()
            calledLayoutBinding?.smallOutside?.autoRefresh()
        }
    }

    val calledData: Unit
        get() {
            val calledData = CallDatabase.instance?.userDao()?.queryAllUser(page)
            calledData?.observe(this) { users: List<User> ->
                if (page == 1) {
                    callViewModel?.userList = users
                    callViewModel?.userData?.postValue(0)
                } else {
                    val list = callViewModel?.userList?.toMutableList()?: mutableListOf()
                    if (!calledData.value.isNullOrEmpty()) {
                        list.addAll(calledData.value!!)
                        callViewModel!!.userList = list
                        callViewModel!!.userData?.postValue(0)
                    } else {
                        callViewModel!!.userList = list
                        callViewModel!!.userData?.postValue(1)
                    }
                }
            }
        }


    private fun initRc() {
        val manager = LinearLayoutManager(
            context
        )
        manager.orientation = RecyclerView.VERTICAL
        callsLogAdapter = CallsLogAdapter(R.layout.item_text)
        calledLayoutBinding!!.btnCenter.layoutManager = manager
        calledLayoutBinding!!.btnCenter.adapter = callsLogAdapter
    }

    override fun initView() {
        calledLayoutBinding!!.smallOutside.setEnableLoadMore(true)
        calledLayoutBinding!!.smallOutside.setOnRefreshListener(object : OnRefreshListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                if (callViewModel != null) {
                    page = 1
                    calledData
                }
            }
        })
        calledLayoutBinding!!.smallOutside.setOnLoadMoreListener(object : OnLoadMoreListener {
            override fun onLoadMore(refreshLayout: RefreshLayout) {
                if (callViewModel != null) {
                    page++
                    calledData
                }
            }
        })
        val onlyChild = calledLayoutBinding!!.smallOutside.getChildAt(0)
        calledLayoutBinding!!.smallOutside.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY -> // 如果满足就是到底部了,加载更多
            if (onlyChild.height <= scrollY + calledLayoutBinding!!.smallOutside.height) {
                calledLayoutBinding!!.smallOutside.autoLoadMore()
            }
        }
        //        calledLayoutBinding.smallOutside.autoRefresh();
    }
}
