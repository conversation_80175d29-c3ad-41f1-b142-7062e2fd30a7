package com.juicy.message.View

import android.Manifest
import android.annotation.SuppressLint
import android.os.Bundle
import android.os.Handler
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import com.google.gson.Gson
import com.juicy.message.View.RongMessage.ChatMessageCache
import com.juicy.app.modules.base.dialog.FullShoppingDialog
import com.juicy.app.modules.base.dialog.GiftAnimationFragment.Companion.GiftAnimationFragmentShow
import com.juicy.app.modules.base.dialog.GiftDialog.GiftDialog
import com.juicy.app.modules.base.dialog.GiftDialog.GiftDialog.SendGiftCallBack
import com.juicy.common.model.event.SubscribeStatusChangeEvent
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.LimitCreateImSessionInterface.createImSession
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.rcMessage.SJMsg
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.Constant.giftResMap
import com.juicy.common.config.Constant.nativeOfficeUserList
import com.juicy.common.config.SpKeyPool
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PermissionUtil.Companion.showMicrophoneRequestPermissionFailedAlter
import com.juicy.common.utils.SpSaveUtil.getStringValue
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.core.Observable
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers
import io.rong.imkit.IMCenter
import io.rong.imkit.MessageInterceptor
import io.rong.imkit.MessageItemLongClickAction
import io.rong.imkit.MessageItemLongClickActionManager
import io.rong.imkit.R
import io.rong.imkit.RongIM
import io.rong.imkit.conversation.ConversationFragment
import io.rong.imkit.conversation.messgelist.viewmodel.MessageItemLongClickBean
import io.rong.imkit.event.uievent.ShowLongClickDialogEvent
import io.rong.imkit.model.UiMessage
import io.rong.imkit.widget.dialog.OptionsPopupDialog
import io.rong.imkit.widget.refresh.SmartRefreshLayout
import io.rong.imlib.IRongCallback
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.Message.ReceivedStatus
import io.rong.imlib.model.Message.SentStatus
import io.rong.imlib.model.MessageContent
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.Collections
import java.util.concurrent.TimeUnit

class ChatFragment : ConversationFragment {
    private var id = ""
    private var currentGift = ""

    //    private AppCompatImageView giftBg;
    //    private AppCompatImageView gift;
    private var sendGiftNum: AppCompatTextView? = null
    private var sendGift: RelativeLayout? = null
    private var giftImg: AppCompatImageView? = null
    private var giftCount = 1
    private var giftTimer: Disposable? = null
    private var nickName = ""
    private var limitBg:AppCompatImageView? = null
    private var limitIcon:AppCompatImageView ?= null

    var isLimit: Boolean = false
    var limitChatNow: Boolean = false

    var sendMessageBack: sendMessageBack? = null

    var onLimitBack: onLimitBack? = null



    constructor()

    constructor(id: String, nickName: String) {
        this.id = id
        this.nickName = nickName
    }

    //订阅成功
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSubscribeEvent(countryEvent: SubscribeStatusChangeEvent?) {
        //
        if (isLimit) {
            //获取个人信息
            val userId = getStringValue(SpKeyPool.USER_ID_DEVICE, "")
            requestUserInfo(userId?:"")
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onCoinsMessage(countryEvent: com.juicy.common.model.bean.MyCoinsNumMessage?) {
        //
        if (isLimit) {
            //获取个人信息
            val userId = getStringValue(SpKeyPool.USER_ID_DEVICE, "")
            requestUserInfo(userId?:"")
            checkChatLimit()
        }
    }

    override fun onViewLongClick(clickType: Int, data: UiMessage): Boolean {
        if (clickType == -6) {
            return super.onViewLongClick(clickType, data)
        }
        if (isDestroy(activity)) return false
        activity?.runOnUiThread {
            val clickActions = MessageItemLongClickActionManager
                .getInstance().messageItemLongClickActions
            val iterator = clickActions.iterator()
            val delActionTitle = getString(R.string.rc_dialog_item_message_more)
            while (iterator.hasNext()) {
                val clickAction = iterator.next()
                val isDelAction = delActionTitle == clickAction.getTitle(context)
                if (isDelAction) {
                    iterator.remove()
                    break
                }
            }
            //        return super.onViewLongClick(clickType, data);
            val messageItemLongClickActions =
                MessageItemLongClickActionManager.getInstance().getMessageItemLongClickActions(data)
            val event = ShowLongClickDialogEvent(
                MessageItemLongClickBean(
                    messageItemLongClickActions,
                    data
                )
            )
            val bean = event.bean
            //        final List<MessageItemLongClickAction> messageItemLongClickActions = bean.getMessageItemLongClickActions();
            messageItemLongClickActions.sortWith(Comparator { lhs, rhs -> rhs.priority - lhs.priority })
            val titles: MutableList<String?> = ArrayList<String?>()
            val var5: Iterator<*> = messageItemLongClickActions.iterator()

            val delActionTitle1 = getString(R.string.rc_dialog_item_message_delete)
            val copyActionTitle = getString(R.string.rc_dialog_item_message_copy)
            val recallActionTitle = getString(R.string.rc_dialog_item_message_recall)
            val quoteActionTitle =
                getString(R.string.rc_dialog_item_message_reference)
            while (var5.hasNext()) {
                val action = var5.next() as MessageItemLongClickAction
                if (action.getTitle(context) === delActionTitle1) {
                    titles.add(LanguageManager.instance?.getLocalTranslate("Delete"))
                } else if (action.getTitle(context) === copyActionTitle) {
                    titles.add(LanguageManager.instance?.getLocalTranslate("Copy"))
                } else if (action.getTitle(context) === recallActionTitle) {
                    titles.add(LanguageManager.instance?.getLocalTranslate("Recall"))
                } else if (action.getTitle(context) === quoteActionTitle) {
                    titles.add(LanguageManager.instance?.getLocalTranslate("Quote"))
                } else {
                    titles.add(action.getTitle(context))
                }
            }

            val dialog = OptionsPopupDialog.newInstance(
                context,
                titles.toTypedArray<String?>()
            ).setOptionsPopupDialogListener { which ->
                (messageItemLongClickActions[which] as MessageItemLongClickAction).listener.onMessageItemLongClick(
                    context, bean.uiMessage
                )
            }
            MessageItemLongClickActionManager.getInstance().longClickDialog = dialog
            MessageItemLongClickActionManager.getInstance().longClickMessage =
                bean.uiMessage.message
            dialog.setOnDismissListener {
                MessageItemLongClickActionManager.getInstance().longClickDialog =
                    null as OptionsPopupDialog?
                MessageItemLongClickActionManager.getInstance().longClickMessage =
                    null as Message?
            }
            dialog.show()
        }


        return false
    }

    @SuppressLint("CutPasteId")
    fun checkChatLimit() {
        if (mRongExtension == null || mRongExtension.inputPanel == null || mRongExtension.inputPanel.rootView == null) {
            return
        }
        val pressToSpeech =
            mRongExtension.inputPanel.rootView.findViewById<TextView>(com.juicy.app.R.id.press_to_speech_btn)
        pressToSpeech.text = LanguageManager.instance?.getLocalTranslate("Press_to_speech")

        mRongExtension.inputPanel.rootView.findViewById<View>(com.juicy.app.R.id.input_panel_add_btn).isEnabled =
            true
        mRongExtension.inputPanel.rootView.findViewById<View>(com.juicy.app.R.id.input_panel_emoji_btn).isEnabled =
            true
        var isLimit = false
        limitChatNow = false
        if (Cache.instance.userInfo == null) {
            return
        }
        if (Cache.instance.userStratResult?.isSwitchIMLimit == true) {
            isLimit = true
        }
        if (Cache.instance.userInfo?.recharge == true ||
            Cache.instance.userInfo?.vip == true
        ) {
            isLimit = false
        }

        if(isLimit){
            limitBg?.visibility = View.VISIBLE
            limitIcon?.visibility = View.VISIBLE
        }else{
            limitBg?.visibility = View.GONE
            limitIcon?.visibility = View.GONE
        }

        val targetId = id

        var isSystem = false
        if (Cache.instance.userStratResult != null &&
            Cache.instance.userStratResult?.userServiceAccountId == null
        ) {
            isSystem = id == (Cache.instance.userStratResult?.userServiceAccountId ?: "")
        }
        val list = Cache.instance.userStratResult?.topOfficialUserIds
        if (!list.isNullOrEmpty()){
            for (i in list.indices) {
                val topOfficialUserID = list[i]
                if (id == topOfficialUserID) {
                    isSystem = true
                    break
                }
            }
        }

        if (Cache.instance.userStratResult != null && Cache.instance.userStratResult?.isReviewPkg != null &&
            Cache.instance.userStratResult?.isReviewPkg == true
        ) {
            isLimit = false
        }

        if (isSystem) {
            isLimit = false
        } else {
            val imSList = Cache.instance.userStratResult?.imSessionBroadcasterIds
            if (!imSList.isNullOrEmpty()){
                for (i in imSList.indices) {
                    val anchorId = "" + imSList[i]
                    if (targetId == anchorId) {
                        isLimit = false
                        break
                    }
                }
            }
            if (isLimit) {
                this.isLimit = true
                if ((Cache.instance.userStratResult?.imSessionBalance ?: 0) > 0) {
                    //显示剩余数量
                    mRongExtension.inputPanel.rootView.findViewById<View>(com.juicy.app.R.id.chat_limit_text).visibility =
                        View.VISIBLE
                    val limitText =
                        mRongExtension.inputPanel.rootView.findViewById<TextView>(com.juicy.app.R.id.chat_limit_text)
                    val str = StringBuilder().append(LanguageManager.instance?.getLocalTranslate("You_have")).append(" #### ").append(LanguageManager.instance?.getLocalTranslate(
                        "free chat."
                    ))?:""
                    val newStr = (str.toString()).replace(
                        "####",
                        Cache.instance.userStratResult?.imSessionBalance.toString()
                    )
                    limitText.text = newStr
                    onLimitBack?.canShow(false)
                    mRongExtension.inputPanel.editText.addTextChangedListener(object : TextWatcher {
                        override fun beforeTextChanged(
                            charSequence: CharSequence,
                            start: Int,
                            count: Int,
                            after: Int
                        ) {
                            // 在文本变化之前被调用
                            limitText.visibility = View.GONE
                        }

                        override fun onTextChanged(
                            charSequence: CharSequence,
                            start: Int,
                            before: Int,
                            count: Int
                        ) {
                            // 在文本变化时被调用
                        }

                        override fun afterTextChanged(editable: Editable) {
                        }
                    })
                    limitBg?.visibility = View.GONE
                    limitIcon?.visibility = View.GONE
                } else {
                    limitBg?.visibility = View.VISIBLE
                    limitIcon?.visibility = View.VISIBLE

                    //不能发送，显示充值按钮
                    limitChatNow = true
                    mRongExtension.inputPanel.rootView.findViewById<View>(com.juicy.app.R.id.input_panel_add_btn).isEnabled =
                        false
                    mRongExtension.inputPanel.rootView.findViewById<View>(com.juicy.app.R.id.input_panel_emoji_btn).isEnabled =
                        false
                    onLimitBack?.canShow(true)
                    mRongExtension.inputPanel.rootView.findViewById<View>(com.juicy.app.R.id.thickBrief).visibility =
                        View.VISIBLE
                    mRongExtension.inputPanel.rootView.findViewById<View>(com.juicy.app.R.id.thickBrief)
                        .setOnClickListener { view: View? ->
                            showFullShopping()
                        }
                }
            } else {
                limitBg?.visibility = View.GONE
                limitIcon?.visibility = View.GONE
                //不限制
                mRongExtension.inputPanel.rootView.findViewById<View>(com.juicy.app.R.id.thickBrief).visibility =
                    View.GONE
                onLimitBack?.canShow(false)
                val limitText =
                    mRongExtension.inputPanel.rootView.findViewById<TextView>(com.juicy.app.R.id.chat_limit_text)
                limitText.visibility = View.GONE
            }
            mRongExtension.inputPanel.rootView.findViewById<View>(com.juicy.app.R.id.edit_btn).isEnabled =
                !limitChatNow
        }
        if (!isLimit) {
            mRongExtension.inputPanel.rootView.findViewById<View>(com.juicy.app.R.id.thickBrief).visibility =
                View.GONE
            onLimitBack?.canShow(false)
            val limitText =
                mRongExtension.inputPanel.rootView.findViewById<TextView>(com.juicy.app.R.id.chat_limit_text)
            limitText.visibility = View.GONE
        }
    }

    fun showFullShopping() {
        val fullShoppingDialog = FullShoppingDialog("im_limit")
        val currentActivity = currentActivity as AppCompatActivity?
        if (currentActivity != null) {
            fullShoppingDialog.show(currentActivity.supportFragmentManager, "")
        }
    }

    //刷新自己用户信息
    private fun requestUserInfo(userId: String) {
        //获取用户数据

        getUserInfo(userId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                if (t != null) {
                    if (t.isOk && t.data != null) {
                        Cache.instance.userInfo = t.data

                        checkChatLimit()
                    } else if (Constant.TOKEN_EXPIRE_CODES.contains(t.code)) {
                    }
                }
            }

            override fun onError(e: Throwable) {
                Log.e("ChatFragment", "mine user info error" + e.message)
            }
        })
    }

    fun createImSessionLimit() {
        val targetId = id
        createImSession(targetId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ImSessionBlanceBean>>() {
            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.ImSessionBlanceBean>) {
                if (t.isOk && t.data != null) {
                    Cache.instance.netStrategy
                } else if (Constant.TOKEN_EXPIRE_CODES.contains(t.code)) {
                }
            }

            override fun onError(e: Throwable) {
                Log.e("ChatFrag", "user level error" + e.message)
            }
        })
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        ChatMessageCache.instance.cleanCache()

        val smartRefreshLayout = view.findViewById<SmartRefreshLayout>(com.juicy.app.R.id.rc_refresh)
        smartRefreshLayout.background = null
        sendGiftNum = view.findViewById(com.juicy.app.R.id.shortInput)
        sendGift = view.findViewById(com.juicy.app.R.id.updateSelected)
        giftImg = view.findViewById(com.juicy.app.R.id.responsiveLower)
        limitBg = view.findViewById(com.juicy.app.R.id.dynamicCenter)
        limitIcon = view.findViewById(com.juicy.app.R.id.selectedThin)
        val giftButton = view.findViewById<AppCompatImageView>(com.juicy.app.R.id.reloadList)
        if (excludeOfficial(id)) {
            giftButton.visibility = View.GONE
        }

        giftButton.setOnClickListener { v: View? ->
            hideExten()
            val handler = Handler()
            handler.postDelayed({
                val giftDialog = GiftDialog(true, object : SendGiftCallBack {
                    override fun onSuccess(giftName: String?) {
                        val giftMsg = com.juicy.common.model.bean.GiftEventBean(
                            giftName ?: "",
                            Cache.instance.userInfo?.userId ?: "",
                            nickName
                        )
                        val s = Gson().toJson(giftMsg)
                        val messageContent = SJMsg.obtain(
                            s,
                            "gift",
                            Cache.instance.userInfo?.userId?:"",
                            id,
                            ""
                        )
                        val conversationType =
                            Conversation.ConversationType.PRIVATE
                        val message =
                            Message.obtain(
                                id,
                                conversationType,
                                messageContent
                            )
                        IMCenter.getInstance().sendMessage(
                            message,
                            giftName,
                            giftName,
                            object : IRongCallback.ISendMessageCallback {
                                override fun onAttached(message: Message) {
                                }

                                override fun onSuccess(message: Message) {
                                    sendGift(giftName?:"")
                                }

                                override fun onError(
                                    message: Message,
                                    errorCode: RongIMClient.ErrorCode
                                ) {
                                }
                            })
                    }
                }, id, "")
                val currentActivity =
                    currentActivity as AppCompatActivity?
                if (currentActivity != null) {
                    giftDialog.show(currentActivity.supportFragmentManager, "")
                }
            }, 100) // 延迟时间
        }


        RongIM.getInstance().setMessageInterceptor(object : MessageInterceptor {
            override fun interceptReceivedMessage(
                message: Message,
                i: Int,
                b: Boolean,
                b1: Boolean
            ): Boolean {
                return false
            }

            override fun interceptOnInsertOutgoingMessage(
                type: Conversation.ConversationType,
                targetId: String,
                sentStatus: SentStatus,
                content: MessageContent,
                sentTime: Long
            ): Boolean {
                return false
            }

            override fun interceptOnInsertIncomingMessage(
                type: Conversation.ConversationType,
                targetId: String,
                senderId: String,
                receivedStatus: ReceivedStatus,
                content: MessageContent,
                sentTime: Long
            ): Boolean {
                return false
            }

            override fun interceptOnSendMessage(message: Message): Boolean {
                Log.d("ChatFrag", "interceptOnSendMessage: ")
                if (isDetached || activity == null || activity?.isFinishing == true) {
                    return false
                }
                if (limitChatNow) {
                    // 检查Fragment是否已经被销毁
                    showFullShopping()
                    return true
                }

                sendMessageBack?.sendMessage()

                return false
            }

            override fun interceptOnSentMessage(message: Message): Boolean {
                Log.d("ChatFrag", "interceptOnSentMessage: ") //发送完成
                if (isDetached || activity == null || activity?.isFinishing == true) {
                    return false
                }
                if (isLimit && !limitChatNow) {
                    isLimit = false //只上报一次
                    createImSessionLimit()
                }
                return false
            }
        })
        EventBus.getDefault().register(this)
        checkChatLimit()
        if (mRongExtension == null || mRongExtension.inputPanel == null || mRongExtension.inputPanel.rootView == null) {
            return
        }
        val sendText =
            mRongExtension.inputPanel.rootView.findViewById<TextView>(com.juicy.app.R.id.input_panel_send_btn)
        sendText.text = ""
        val limitText =
            mRongExtension.inputPanel.rootView.findViewById<TextView>(com.juicy.app.R.id.pauseOuter)
        limitText.text = LanguageManager.instance?.getLocalTranslate("Chat_Now")

        val pressToSpeech =
            mRongExtension.inputPanel.rootView.findViewById<TextView>(com.juicy.app.R.id.press_to_speech_btn)
        pressToSpeech.text = LanguageManager.instance?.getLocalTranslate("Press_to_speech")

        //        pressToSpeech.setOnClickListener();
//        if (mRongExtension != null && mRongExtension.getInputPanel() != null) {
//            View.OnTouchListener originalListener = null;
//            if (pressToSpeech != null) {
//                originalListener = chat();
//            }
//
//            // 设置新的触摸事件监听器
//            final View.OnTouchListener finalOriginalListener = originalListener;
//            pressToSpeech.setOnTouchListener((v, event) -> {
//                switch (event.getAction()) {
//                    case MotionEvent.ACTION_DOWN:
//                        // 按下时检查权限
//                        String[] permissions = {Manifest.permission.RECORD_AUDIO};
//                        return false; // 继续传递触摸事件
//
//                }
//                return false; // 继续传递触摸事件
//            });
//
//        }
    }

    fun hideExten() {
        mRongExtension.collapseExtension()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        if (permissions.isNotEmpty() && grantResults.isNotEmpty() && permissions[0] == Manifest.permission.RECORD_AUDIO && grantResults[0] != 0) {
            showMicrophoneRequestPermissionFailedAlter()
        } else {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        }
    }

    private fun sendGift(giftName: String) {
        if (giftResMap?.get(giftName) != null) {
            GiftAnimationFragmentShow(giftName)

        }
        calculateNumGift(giftName)
    }

    private fun calculateNumGift(giftName: String) {
        if (giftTimer != null && giftTimer?.isDisposed == false) {
            giftTimer?.dispose()
        }
        sendGift?.visibility = View.VISIBLE
        if (giftName == currentGift) {
            giftCount++
        } else {
            giftCount = 1
        }
        currentGift = giftName
        val builder = StringBuilder()
        builder.append("x").append(giftCount).append(" ")
        sendGiftNum?.text = builder
        if (giftResMap?.get(giftName) != null) {
            giftImg?.setImageResource(giftResMap?.get(giftName)?:0)
        }
        giftTimer = Observable.timer(5, TimeUnit.SECONDS)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                giftCount = 0
                sendGift?.visibility = View.INVISIBLE
            }
    }


    override fun onDestroy() {
        if (giftTimer != null && giftTimer?.isDisposed == false) {
            giftTimer?.dispose()
        }
        super.onDestroy()
    }

    companion object {
        fun excludeOfficial(id: String): Boolean {
            if (Cache.instance.userStratResult != null) {
                val userIds = Cache.instance.userStratResult?.topOfficialUserIds
                if (userIds != null) {
                    for (i in userIds.indices) {
                        if (userIds[i] == id) {
                            return true
                        }
                    }
                }
            }

            val nativeUserIds: List<String>? = nativeOfficeUserList
            if (null != nativeUserIds) {
                for (i in nativeUserIds.indices) {
                    if (nativeUserIds[i] == id) {
                        return true
                    }
                }
            }
            return null != Cache.instance.userInfo && Cache.instance.userInfo?.userId == id
        }
    }
}
