package com.juicy.message.View

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.android.arouter.facade.annotation.Route
import com.juicy.message.adapter.PagerAdapter
import com.juicy.message.adapter.MessageTabPageAdapter
import com.juicy.app.databinding.ItemButtonBinding
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.LanguageManager

@Route(path = Constant.MESSAGE_FRAGMENT_ROUTE)
class MessageFragment : BaseFragment() {
    private var messageFragmentBinding: ItemButtonBinding? = null
    private val fragmentBases: MutableList<Fragment> = ArrayList()
    private val tabs: MutableList<String> = ArrayList()
    private var messageTabPageAdapter: MessageTabPageAdapter? = null
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        messageFragmentBinding = ItemButtonBinding.inflate(inflater)
        return messageFragmentBinding!!.root
    }

    override fun initData() {
        messageTabPageAdapter = MessageTabPageAdapter(
            tabs,
            callBack = object :MessageTabPageAdapter.CallBack{
                override fun onCallBack(position: Int) {
                    messageFragmentBinding!!.autoHeader.currentItem = position
                }

            }
        )
        fragmentBases.add(MyConversationListFragment())
        fragmentBases.add(CalledFragment())
        fragmentBases.add(FollowedFragment())
        tabs.add(LanguageManager.instance!!.getLocalTranslate("Message"))
        tabs.add(LanguageManager.instance!!.getLocalTranslate("Calls"))
        tabs.add(LanguageManager.instance!!.getLocalTranslate("Followed"))
    }

    override fun initView() {
        messageFragmentBinding!!.root.post(Runnable {
            if (isDestroy(activity)) return@Runnable
            if (!isAdded || view == null) return@Runnable
            reduceSensitivity()

            val linearLayoutManager = LinearLayoutManager(
                context
            )
            linearLayoutManager.orientation =
                RecyclerView.HORIZONTAL
            messageFragmentBinding!!.narrowLoading.layoutManager = linearLayoutManager
            messageFragmentBinding!!.narrowLoading.adapter = messageTabPageAdapter
            messageFragmentBinding!!.narrowLoading.isNestedScrollingEnabled = false

            val pagerAdapter = PagerAdapter(
                childFragmentManager, lifecycle
            )
            pagerAdapter.setFragmentBases(fragmentBases)
            messageFragmentBinding!!.autoHeader.adapter = pagerAdapter
            messageFragmentBinding!!.autoHeader.registerOnPageChangeCallback(object :
                ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    messageTabPageAdapter!!.selectPosition = position
                    messageTabPageAdapter!!.notifyDataSetChanged()
                }
            })
            setRedPoint(Cache.instance.unreadCount > 0)
        })
    }

    private fun reduceSensitivity() {
        try {
            val recyclerViewField = ViewPager2::class.java.getDeclaredField("mRecyclerView")
            recyclerViewField.isAccessible = true

            val recyclerView =
                recyclerViewField[messageFragmentBinding!!.autoHeader] as RecyclerView

            val touchSlopField = RecyclerView::class.java.getDeclaredField("mTouchSlop")
            touchSlopField.isAccessible = true

            val touchSlop = touchSlopField[recyclerView] as Int
            touchSlopField[recyclerView] = touchSlop * 4 // 增加滑动阈值
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun setRedPoint(isShow: Boolean) {
        if (messageTabPageAdapter != null) {
            messageTabPageAdapter!!.setRedPoint = isShow
            messageTabPageAdapter!!.notifyItemChanged(0)
        }
    }
}
