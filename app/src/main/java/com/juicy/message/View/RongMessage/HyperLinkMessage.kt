package com.juicy.message.View.RongMessage

import android.content.Context
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.text.style.UnderlineSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.text.TextUtilsCompat
import com.blankj.utilcode.util.ToastUtils
import com.google.gson.Gson
import com.juicy.message.CustomRongDateUtils.getCallRecordTime
import com.juicy.app.R
import com.juicy.app.modules.base.dialog.AnchorShoppingDialog
import com.juicy.common.networks.delegate.GetCheckRechargeInterface.CheckRecharge
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.rcMessage.HLinkMsg
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.LanguageManager
import io.reactivex.rxjava3.disposables.Disposable
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.utils.RongDateUtils
import io.rong.imkit.utils.RouteUtils
import io.rong.imkit.utils.TextViewUtils
import io.rong.imkit.widget.LinkTextViewMovementMethod
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import io.rong.message.HistoryDividerMessage
import java.util.Locale

class HyperLinkMessage : BaseMessageItemProvider<HLinkMsg?>() {
    override fun bindViewHolder(
        holder: ViewHolder,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        super.bindViewHolder(holder, uiMessage, position, list, listener)

        holder.getView<View>(io.rong.imkit.R.id.rc_content)
            .setBackgroundResource(if (LanguageManager.instance!!.isLanguageForce) R.drawable.bg_text_message_user_rlt_bg_teal else R.drawable.bg_text_message_user_bg_silver)
        initTime(list, uiMessage.message,holder, position)
    }

    private fun initTime(
        data: List<UiMessage>,
        message: Message,
        holder: ViewHolder,
        position: Int
    ) {
        val time = getCallRecordTime(message.sentTime)
        holder.setText(io.rong.imkit.R.id.rc_time, time)
        if (position == 0) {
            holder.setVisible(io.rong.imkit.R.id.rc_time, message.content !is HistoryDividerMessage)
        } else {
            val pre = data[position - 1]
            if (pre.message != null && RongDateUtils.isShowChatTime(
                    holder.context,
                    message.sentTime,
                    pre.message.sentTime,
                    180
                )
            ) {
                holder.setVisible(io.rong.imkit.R.id.rc_time, true)
            } else {
                holder.setVisible(io.rong.imkit.R.id.rc_time, false)
            }
        }
    }



    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        hLinkMsg: HLinkMsg?,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        val linkText = holder.getView<AppCompatTextView>(R.id.xsDynamic)
        if (TextUtilsCompat.getLayoutDirectionFromLocale(Locale.getDefault()) == 1) {
            linkText.textAlignment = View.TEXT_ALIGNMENT_CENTER
        }
        linkText.tag = uiMessage.messageId
        if (uiMessage.contentSpannable == null) {
            val spannable = TextViewUtils.getSpannable(
                hLinkMsg!!.content
            ) { spannable1: SpannableStringBuilder? ->
                uiMessage.contentSpannable =
                    spannable1
                if (linkText.tag == uiMessage.messageId) {
                    linkText.post { //                            linkText.setText(uiMessage.getContentSpannable());
                        val text = uiMessage.contentSpannable.toString()
                        val spannableString =
                            SpannableString(text)

                        // 添加下划线
                        spannableString.setSpan(
                            UnderlineSpan(),
                            0,
                            text.length,
                            0
                        )
                        // 设置下划线颜色
                        spannableString.setSpan(
                            ForegroundColorSpan(
                                holder.context.getColor(io.rong.imkit.R.color.rc_blue)
                            ), 0, text.length, 0
                        )
                        linkText.text = spannableString
                    }
                }
            }
            uiMessage.contentSpannable = spannable
        }

        val text = uiMessage.contentSpannable.toString()
        val spannableString = SpannableString(text)

        // 添加下划线
        spannableString.setSpan(UnderlineSpan(), 0, text.length, 0)
        // 设置下划线颜色
        spannableString.setSpan(
            ForegroundColorSpan(holder.context.getColor(io.rong.imkit.R.color.rc_blue)),
            0,
            text.length,
            0
        )

        linkText.text = spannableString

        //        linkText.setText(uiMessage.getContentSpannable());
        if (hLinkMsg != null) {
            //确定消息类型
            if (hLinkMsg.contentType == "recharge_link") {
                linkText.setTextColor(holder.context.getColor(io.rong.imkit.R.color.rc_blue))
            }
        }
        linkText.movementMethod = LinkTextViewMovementMethod { link ->
            var result = false
            if (RongConfigCenter.conversationConfig().conversationClickListener != null) {
                result =
                    RongConfigCenter.conversationConfig().conversationClickListener.onMessageLinkClick(
                        holder.context,
                        link,
                        uiMessage.message
                    )
            }
            if (result) {
                true
            } else {
                val str = link.lowercase(Locale.getDefault())
                if (str.startsWith("http") || str.startsWith("https")) {
                    RouteUtils.routeToWebActivity(linkText.context, link)
                    result = true
                }

                result
            }
        }
        linkText.setOnClickListener { view1: View? ->
            if (isFastClick) {
                return@setOnClickListener
            }
            //充值逻辑
            if (hLinkMsg != null) {
                //确定消息类型
                if (hLinkMsg.contentType == "recharge_link") {
                    clickLink(holder, uiMessage, hLinkMsg)
                }
            }
        }
        linkText.setOnLongClickListener { view ->
            val parent = view.parent
            parent is View && (parent as View).performLongClick()
        }
    }
    override fun onCreateMessageContentViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view =
            LayoutInflater.from(parent.context).inflate(R.layout.rc_hyper_message, parent, false)
        return ViewHolder(parent.context, view)
    }

    private fun clickLink(holder: ViewHolder, uiMessage: UiMessage, hLinkMsg: HLinkMsg) {
        val gson = Gson()
        val linkBean = gson.fromJson(hLinkMsg.extra, com.juicy.common.model.bean.InvitationInfoBean::class.java)
        //验证链接有效性
        CheckRecharge(linkBean.invitationId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<Boolean>>() {
            override fun onSubscribe(d: Disposable) {
                super.onSubscribe(d)
            }

            override fun onNext(t: com.juicy.common.model.bean.NetResponseBean<Boolean>) {
                super.onNext(t)
                if (t != null && t.isOk) {
                    if (t.data != null) {
                        val isGood = t.data
                        if (isGood!!) {
                            val userInfo = uiMessage.userInfo
                            val anchorShoppingDialog = AnchorShoppingDialog(
                                linkBean.invitationId!!,
                                userInfo.name,
                                userInfo.portraitUri.toString()
                            )
                            val currentActivity = currentActivity as AppCompatActivity?
                            anchorShoppingDialog.entry = "recharge_link"
                            anchorShoppingDialog.show(currentActivity!!.supportFragmentManager, "")
                        } else {
                            ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Recharge_connection_has_expired"))
                        }
                    }
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
            }

            override fun onComplete() {
                super.onComplete()
            }
        })
    }

    override fun onItemClick(
        holder: ViewHolder,
        hLinkMsg: HLinkMsg?,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ): Boolean {
        return false
    }

    override fun isMessageViewType(messageContent: MessageContent): Boolean {
        return messageContent is HLinkMsg && !messageContent.isDestruct()
    }

    override fun getSummarySpannable(context: Context, hLinkMsg: HLinkMsg?): Spannable {
        if (hLinkMsg != null && !TextUtils.isEmpty(hLinkMsg.content) && hLinkMsg.contentType == "recharge_link") {
            val content = "[" + LanguageManager.instance!!.getLocalTranslate("link") + "] "

            return SpannableString(content)
        } else {
            return SpannableString("")
        }
    }
}
