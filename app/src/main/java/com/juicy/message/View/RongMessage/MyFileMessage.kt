package com.juicy.message.View.RongMessage

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableString
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.juicy.message.CustomRongDateUtils.getCallRecordTime
import com.juicy.common.utils.LanguageManager
import io.rong.imkit.R
import io.rong.imkit.conversation.messgelist.provider.FileMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.utils.RongDateUtils
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.message.FileMessage
import io.rong.message.HistoryDividerMessage

class MyFileMessage : FileMessageItemProvider() {
    override fun getSummarySpannable(context: Context, fileMessage: FileMessage): Spannable {
        val content = "[" + LanguageManager.instance!!.getLocalTranslate("file") + "]"
        return SpannableString(content)
    }

    override fun bindViewHolder(
        holder: ViewHolder,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        holder.getView<View>(R.id.rc_content).background =
            null as Drawable?
        super.bindViewHolder(holder, uiMessage, position, list, listener)
        holder.getView<View>(R.id.rc_content).background =
            null as Drawable?
        initTime(holder, position, list, uiMessage.message)
    }

    private fun initTime(
        holder: ViewHolder,
        position: Int,
        data: List<UiMessage>,
        message: Message
    ) {
        val time = getCallRecordTime(message.sentTime)
        holder.setText(R.id.rc_time, time)
        if (position == 0) {
            holder.setVisible(R.id.rc_time, message.content !is HistoryDividerMessage)
        } else {
            val pre = data[position - 1]
            if (pre.message != null && RongDateUtils.isShowChatTime(
                    holder.context,
                    message.sentTime,
                    pre.message.sentTime,
                    180
                )
            ) {
                holder.setVisible(R.id.rc_time, true)
            } else {
                holder.setVisible(R.id.rc_time, false)
            }
        }
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        fileMessage: FileMessage,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        val isSender =
            (uiMessage
                .message
                .messageDirection
                    == Message.MessageDirection.SEND)
        val textContent = holder.getView<LinearLayout>(com.juicy.app.R.id.rc_voice_bg)
        textContent.setBackgroundResource(
            if (isSender)
                if (LanguageManager.instance!!.isLanguageForce) com.juicy.app.R.drawable.bg_text_message_self_rlt_bg_violet else com.juicy.app.R.drawable.bg_text_message_self_bg_ash
            else
                if (LanguageManager.instance!!.isLanguageForce) com.juicy.app.R.drawable.bg_text_message_user_rlt_bg_teal else com.juicy.app.R.drawable.bg_text_message_user_bg_silver
        )

        val textView = holder.getView<TextView>(com.juicy.app.R.id.syncTitle)
        textView.text =
            LanguageManager.instance!!.getLocalTranslate("File_messages_are_not_currently_supported")
    }

    override fun onItemClick(
        holder: ViewHolder,
        fileMessage: FileMessage,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ): Boolean {
        return true
    }
}