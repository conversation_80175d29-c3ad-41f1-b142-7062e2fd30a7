package com.juicy.message.View

import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.ToastUtils
import com.bumptech.glide.Glide
import com.juicy.app.R
import com.juicy.message.VewModel.MessageInfoViewModel
import com.juicy.app.databinding.ActivityPanelBinding
import com.juicy.app.modules.base.dialog.BlockDialogFragment
import com.juicy.app.modules.base.dialog.BlockDialogFragment.BlockCallBack
import com.juicy.app.modules.base.activity.BaseActivity
import com.juicy.common.model.event.BlockParamsEvent
import com.juicy.common.model.event.FollowParamsEvent
import com.juicy.common.model.event.OnlineStatusChangeEvent
import com.juicy.common.status.UserOnlineStatusService
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.Constant.nativeOfficeUserList
import com.juicy.common.utils.ButtonClickUtils.isFastClick
import com.juicy.common.utils.CallUtil.callHandle
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.PermissionUtil.Companion.checkVideoPermission
import com.juicy.common.utils.PermissionUtil.PermissionCallback
import com.juicy.common.utils.QuickDrawable.Companion.create
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum.CoreErrorCode
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

@Route(path = Constant.CHAT_ROUTE)
class MessageActivity : BaseActivity() {
    private var id = ""
    private var userTargetId: String? = ""
    private var isFollow = false
    private var mTitle: String? = null
    private var messageInfoViewModel: MessageInfoViewModel? = null
    private var binding: ActivityPanelBinding? = null
    private var mUserId: String? = null
    private  var mFollow: Boolean = false
    private var user: com.juicy.common.model.bean.JuicyUserInfoBean? = null

    private var status = ""
    public override fun onCreate(savedInstanceState: Bundle?) {
        mTitle = "Message"
        mFollow = false
        mUserId = id
        binding = ActivityPanelBinding.bind(
            View.inflate(
                this, R.layout.activity_panel, null
            )
        )

        if (LanguageManager.instance!!.isLanguageForce) {
            binding!!.emptyBadge.scaleX = -1f
        }
        setContentView(binding!!.root)
        //强制竖屏
        this.requestedOrientation =
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT //强制竖屏
        super.onCreate(savedInstanceState)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onUserStatus(event: OnlineStatusChangeEvent?) {
        //
        val status = UserOnlineStatusService.instance!!.getStatus(id)
        if (status != null) {
            updateUserStatus(status)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFollowEvent(followEvent: FollowParamsEvent) {
        Log.d("xxx", "xxxxx")
        if (followEvent.userId == id) {
            user?.isFriend = followEvent.isFollow
            Cache.instance.networkCacheManager.putObject("userInfo_$id", user)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onBlockEvent(event: BlockParamsEvent) {
        //
        if (event.userId == id) {
            user?.block = event.isBlock
            Cache.instance.networkCacheManager.putObject("userInfo_$id", user)
        }
        //监听状态
    }

    override fun initData() {
        try {
            messageInfoViewModel = ViewModelProvider(this)[MessageInfoViewModel::class.java]
            val stringExtra = intent.getStringExtra("targetId")?:""
            if (!stringExtra.isNullOrEmpty()) id = stringExtra?:""
            initViewModel()
            initEvent()
            initClick()
            if (excludeOfficial(id) || Cache.instance.reviewPkg) {
                binding!!.hugeSelected.visibility = View.GONE
            }

            val targetId = stringExtra
            userTargetId = targetId
            val conversationType = Conversation.ConversationType.PRIVATE
            var isMoreBar = true
            val userIds = Cache.instance.userStratResult?.topOfficialUserIds?: listOf()
            for (j in userIds.indices) {
                if (userIds[j] == targetId) {
                    isMoreBar = false
                    break
                }
            }
            if (Cache.instance.userStratResult != null && Cache.instance.userStratResult?.userServiceAccountId != null &&
                targetId == Cache.instance.userStratResult?.userServiceAccountId
            ) {
                isMoreBar = false
            }

            if (!isMoreBar) {
                binding!!.pressedOuter.visibility = View.GONE
                binding!!.removeExtended.visibility = View.GONE
                binding!!.expandedUnfocused.visibility = View.GONE
            }
        } catch (e: Exception) {
            e.printStackTrace()
            ARouter.getInstance()
                .build(Constant.SPLASH_ACTIVITY_ROUTE)
                .addFlags(
                    Intent.FLAG_ACTIVITY_NEW_TASK
                            or Intent.FLAG_ACTIVITY_CLEAR_TASK
                )
                .navigation()
        }


        RongCoreClient.getInstance().getConversation(
            Conversation.ConversationType.PRIVATE,
            userTargetId,
            object : IRongCoreCallback.ResultCallback<Conversation?>() {
                override fun onSuccess(conversation: Conversation?) {
//                if (conversation == null) {
//                    binding.expandedUnfocused.setVisibility(View.GONE);
//                    return;
//                }
                    // 成功并返回会话信息
                    if (conversation != null && conversation.isTop) {
                        binding!!.expandedUnfocused.setImageResource(R.drawable.ic_chat_top_icon_charcoal)
                    } else {
                        binding!!.expandedUnfocused.setImageResource(R.drawable.ic_chat_bottom_icon_gold)
                    }
                }

                override fun onError(coreErrorCode: CoreErrorCode) {
                }
            })
    }

    var chatFragment: ChatFragment? = null
    @SuppressLint("CommitTransaction")
    private fun initFragment() {
        //防止缓存重复刷新页面
        if (chatFragment != null) return
        chatFragment = ChatFragment(id, user?.nickname?:"")
        chatFragment!!.sendMessageBack = object : sendMessageBack {
            override fun sendMessage() {
                binding!!.expandedUnfocused.visibility = View.VISIBLE
            }
        }

        chatFragment!!.onLimitBack = object :onLimitBack {
            override fun canShow(ishow: Boolean) {
                binding!!.lockedInput.visibility =
                    if (ishow) View.VISIBLE else View.GONE
            }
        }

        supportFragmentManager.beginTransaction().replace(
            R.id.minimalUndo,
            chatFragment!!
        ).commit()
        this.requestedOrientation =
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
    }

    private fun call() {
        var unitPrice = -1
        if (user != null) {
            if (Cache.instance.userInfo != null && Cache.instance.userInfo?.availableCoins != null) {
                if (Cache.instance.userInfo!!.vip != null &&
                    Cache.instance.userInfo!!.vip && user!!.vipUnitPrice != null
                ) {
                    unitPrice = user!!.vipUnitPrice
                } else if (user!!.unitPrice != null) {
                    unitPrice = user!!.unitPrice
                }
            }
        }

        callHandle(unitPrice, Constant.CONVERSATION, user!!.userId, status, "conversation")
    }

    private fun initClick() {
        binding!!.removeExtended.setOnClickListener(View.OnClickListener {
            if (isFastClick) return@OnClickListener
            chatFragment?.hideExten()
            if (user == null) return@OnClickListener
            checkVideoPermission(this@MessageActivity, object : PermissionCallback {
                override fun complete() {
                    call()
                }
            })
        })

        binding!!.expandedUnfocused.setOnClickListener { view: View? ->
            RongCoreClient.getInstance().getConversation(
                Conversation.ConversationType.PRIVATE,
                userTargetId,
                object : IRongCoreCallback.ResultCallback<Conversation?>() {
                    override fun onSuccess(conversation: Conversation?) {
                        val isTop = conversation != null && conversation.isTop

                        RongIMClient.getInstance().setConversationToTop(
                            Conversation.ConversationType.PRIVATE,
                            userTargetId,
                            !isTop,
                            true,
                            object : RongIMClient.ResultCallback<Boolean?>() {
                                override fun onSuccess(aBoolean: Boolean?) {
                                    if (isTop) {
                                        binding!!.expandedUnfocused.setImageResource(R.drawable.ic_chat_bottom_icon_gold)
                                    } else {
                                        binding!!.expandedUnfocused.setImageResource(R.drawable.ic_chat_top_icon_charcoal)
                                    }
                                    ToastUtils.showShort(
                                        LanguageManager.instance!!.getLocalTranslate(
                                            if (!isTop) "Sticky on Top Successful" else "Sticky on Top Cancelled"
                                        )
                                    )
                                }

                                override fun onError(e: RongIMClient.ErrorCode) {
                                }
                            })
                    }

                    override fun onError(e: CoreErrorCode) {
                    }
                })
        }



        binding!!.emptyBadge.setOnClickListener { v: View? -> finish() }
        binding!!.pressedOuter.setOnClickListener { v: View? ->
            if (isFastClick) return@setOnClickListener
            if (chatFragment != null) {
                chatFragment!!.hideExten()
            }
            if (user != null && user!!.userId != null && !user!!.userId.isEmpty()) {
                val blockDialogFragment = BlockDialogFragment(object : BlockCallBack {
                    override fun deleteBlock() {
                    }

                    override fun addBlock() {
                    }

                    override fun addFollow() {
                        isFollow = true
                    }

                    override fun deleteFollow() {
                        isFollow = false
                    }
                }, isFollow, id, user!!.nickname, user!!.avatarUrl)
                blockDialogFragment.show(supportFragmentManager, "")
            } else {
                ToastUtils.showShort(
                    LanguageManager.instance!!.getLocalTranslate(
                        "Error......"
                    )
                )
            }
        }
    }


    private fun initViewModel() {
        messageInfoViewModel?.statusData?.observe(this) { s: String -> this.updateStatus(s) }
        messageInfoViewModel?.userData?.observe(this) { user: com.juicy.common.model.bean.JuicyUserInfoBean? -> this.updateInfo(user) }
        messageInfoViewModel?.isFollowData?.observe(this) { aBoolean -> isFollow = aBoolean }
    }

    private fun updateInfo(user: com.juicy.common.model.bean.JuicyUserInfoBean?) {
        if (user == null) {
            ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Error"))
        } else {
            this.user = user
            binding!!.fluidForeground.borderWidth = dip2px(2f)
            binding!!.fluidForeground.borderColor = getColor(R.color.white)
            Glide.with(this).load(user.avatarUrl).into(binding!!.fluidForeground)
            binding!!.stretchRight.text = user.nickname
            initFragment()
        }
    }

    private fun updateUserStatus(status: String) {
        this.status = status
        val isEnableCall = UserOnlineStatusService.instance!!.getStatusEnableCall(status)
        val color = UserOnlineStatusService.instance!!.getStatusColor(status)
        binding!!.copyLeft.background = create()
            .corner(dip2px(200f).toFloat())
            .bgColor(color)
            .build()

        binding!!.connectedInside.isEnabled = isEnableCall
    }

    private fun updateStatus(s: String) {
        updateUserStatus(s)
    }


    private fun initEvent() {
        //        chatViewModel.loadFollowData(id);
//        chatViewModel.setStatusDot(id);

        messageInfoViewModel?.updateUser(id?:"")
    }

    override fun initView() {
        val mainPageLayoutParams = binding!!.selectVisible.layoutParams as LinearLayout.LayoutParams
        mainPageLayoutParams.setMargins(0, BarUtils.getStatusBarHeight(), 0, 0)
        binding!!.selectVisible.layoutParams = mainPageLayoutParams

        binding!!.pauseOuter.text = LanguageManager.instance!!.getLocalTranslate("Chat_Now")
        binding!!.lockedInput.setOnClickListener { view: View? ->
            if (chatFragment != null) {
                chatFragment!!.showFullShopping()
            }
        }
    }

    override fun transLocalText() {
    }

    override fun onResume() {
        super.onResume()
        val status = UserOnlineStatusService.instance!!.getStatus(id)
        if (status != null) {
            updateUserStatus(status)
        }
        UserOnlineStatusService.instance!!.addUserIdAndForceRefresh(id)
    }

    companion object {
        fun excludeOfficial(id: String): Boolean {
            val userIds = Cache.instance.userStratResult!!.topOfficialUserIds
            if (userIds != null) {
                for (i in userIds.indices) {
                    if (userIds[i] == id) {
                        return true
                    }
                }
            }
            val nativeUserIds: List<String>? = nativeOfficeUserList
            if (null != nativeUserIds) {
                for (i in nativeUserIds.indices) {
                    if (nativeUserIds[i] == id) {
                        return true
                    }
                }
            }
            return null != Cache.instance.userInfo && Cache.instance.userInfo!!.userId == id
        }
    }
}