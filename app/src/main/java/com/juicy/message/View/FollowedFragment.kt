package com.juicy.message.View

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ToastUtils
import com.juicy.app.R
import com.juicy.message.VewModel.MessageFragmentViewModel
import com.juicy.message.adapter.FollowsAnchorAdapter
import com.juicy.app.databinding.LayoutIndicatorBinding
import com.juicy.app.modules.base.activity.BaseFragment
import com.juicy.common.model.event.TabSelectStatusChangeEvent
import com.juicy.common.model.event.FollowParamsEvent
import com.juicy.common.model.event.OnlineStatusChangeEvent
import com.juicy.common.status.UserOnlineStatusService
import com.juicy.common.config.Cache
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.AppActivityUtil.isDestroy
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.throttle.Throttle.throttle
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class FollowedFragment : BaseFragment() {
    private var followedLayoutBinding: LayoutIndicatorBinding? = null
    private var followsAnchorAdapter: FollowsAnchorAdapter? = null
    private var fragmentViewModel: MessageFragmentViewModel? = null

    private var myFollowList: MutableList<com.juicy.common.model.bean.BaseUserInfoBean>? = ArrayList()

    //记录是否已经加载过数据
    private var hasLoaded = false
    private var manager: LinearLayoutManager? = null

    private var isResume = false
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        followedLayoutBinding = LayoutIndicatorBinding.inflate(inflater)
        return followedLayoutBinding!!.root
    }

    override fun initData() {
        //判断是否审核模式
        if (Cache.instance.reviewPkg) {
            followedLayoutBinding!!.borderMacro.visibility = View.GONE
        } else {
            followedLayoutBinding!!.borderMacro.visibility = View.VISIBLE
        }
        followedLayoutBinding!!.gridDistant.text =
            LanguageManager.instance!!.getLocalTranslate("No_Data")

        followsAnchorAdapter = FollowsAnchorAdapter(R.layout.item_body)
        activity?.let {
            fragmentViewModel = ViewModelProvider(it).get(
                MessageFragmentViewModel::class.java
            )
        }

        fragmentViewModel?.loadFollowData?.observe(this, object : Observer<Int> {
            override fun onChanged(status: Int) {
                if (!isDestroy(activity)) {
                    activity!!.runOnUiThread(Runnable {
                        if (status == -1) {
                            followedLayoutBinding!!.smallOutside.finishRefresh(false)
                            followedLayoutBinding!!.smallOutside.finishLoadMore(false)
                        }
                        if (status == -2) {
                            followedLayoutBinding!!.smallOutside.finishRefresh(false)
                            followedLayoutBinding!!.smallOutside.finishLoadMore(false)
                            if (isDestroy(activity)) return@Runnable
                            ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate("Refresh_failed"))
                        } else if (status == 1) {
                            if (fragmentViewModel!!.page == 1) {
                                followedLayoutBinding!!.smallOutside.finishRefresh()
                            } else {
                                followedLayoutBinding!!.smallOutside.finishLoadMoreWithNoMoreData()
                            }
                        } else {
                            followedLayoutBinding!!.smallOutside.finishRefresh()
                            followedLayoutBinding!!.smallOutside.finishLoadMore()
                        }
                        //                followedLayoutBinding.smallOutside.finishRefresh();
                        //                followedLayoutBinding.smallOutside.finishLoadMore(false);
                        myFollowList = fragmentViewModel!!.followList
                        if (myFollowList != null && !myFollowList!!.isEmpty()) {
                            if (myFollowList!!.isEmpty()) {
                                followedLayoutBinding!!.topExpanded.visibility = View.VISIBLE
                            } else {
                                hasLoaded = true
                                followedLayoutBinding!!.topExpanded.visibility = View.GONE
                            }
                            followsAnchorAdapter!!.setList(fragmentViewModel!!.followList)
                        } else {
                            if (myFollowList == null || myFollowList!!.isEmpty()) {
                                followedLayoutBinding!!.topExpanded.visibility = View.VISIBLE
                            } else {
                                followedLayoutBinding!!.topExpanded.visibility = View.GONE
                            }
                            followsAnchorAdapter!!.setList(fragmentViewModel!!.followList)
                        }
                    })
                }
            }
        })
    }

    override fun initView() {
        manager = LinearLayoutManager(context)
        manager!!.orientation = RecyclerView.VERTICAL
        followedLayoutBinding!!.titleUnderlay.layoutManager = manager
        followedLayoutBinding!!.titleUnderlay.adapter = followsAnchorAdapter
        followedLayoutBinding!!.smallOutside.setOnRefreshListener { refreshData() }

        followedLayoutBinding!!.titleUnderlay.addOnScrollListener(object :
            RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    val manager = recyclerView.layoutManager as LinearLayoutManager?
                    val firstVisiblePosition = manager!!.findFirstVisibleItemPosition()
                    val lastVisiblePosition = manager.findLastVisibleItemPosition()
                    if (firstVisiblePosition > -1 && lastVisiblePosition > -1) {
                        throttle("wall_refresh", 200,
                            {},
                            {
                                if (lastVisiblePosition < myFollowList!!.size) {
                                    val anchorIds: MutableList<String> = ArrayList()
                                    for (i in firstVisiblePosition..lastVisiblePosition) {
                                        anchorIds.add(myFollowList!![i].userId?:"")
                                    }
                                    if (activity === currentActivity) {
                                        UserOnlineStatusService.instance!!.addUserIdsAndRefresh(
                                            anchorIds.toList()?: listOf<String>()
                                        )
                                    }
                                }
                            })
                    }
                }
            }
        })
        followedLayoutBinding!!.smallOutside.setEnableLoadMore(true)
        followedLayoutBinding!!.smallOutside.setOnRefreshListener {
            if (fragmentViewModel != null) {
                followedLayoutBinding!!.smallOutside.resetNoMoreData()
                fragmentViewModel!!.page = 1
                fragmentViewModel!!.loadFollowData()
            }
        }
        followedLayoutBinding!!.smallOutside.setOnLoadMoreListener {
            if (fragmentViewModel != null) {
                fragmentViewModel!!.page++
                fragmentViewModel!!.loadFollowData()
            }
        }
        val onlyChild = followedLayoutBinding!!.smallOutside.getChildAt(0)
        followedLayoutBinding!!.smallOutside.setOnScrollChangeListener { v, scrollX, scrollY, oldScrollX, oldScrollY -> // 如果满足就是到底部了,加载更多
            if (onlyChild.height <= scrollY + followedLayoutBinding!!.smallOutside.height) {
                followedLayoutBinding!!.smallOutside.autoLoadMore()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        isResume = true
        //更改为不刷新，监听关注状态
        if (!hasLoaded) {
            followedLayoutBinding!!.smallOutside.autoRefresh()
        }
        //这里有刷新
        resetStatueUserId()
    }

    override fun onPause() {
        super.onPause()
        isResume = false
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onUserStatus(event: OnlineStatusChangeEvent?) {
        //
        for (userBean in myFollowList!!) {
            val status = UserOnlineStatusService.instance!!.getStatus(userBean.userId!!)
            if (status != null) {
                userBean.onlineStatus = status
            }
        }
        followsAnchorAdapter!!.notifyDataSetChanged()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onTagChange(event: TabSelectStatusChangeEvent) {
        //
        if (event.page == 2) {
            resetStatueUserId()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFollowEvent(followEvent: FollowParamsEvent) {
        if (isDestroy(activity) || !isAdded) return
        followedLayoutBinding!!.smallOutside.resetNoMoreData()
        if (followEvent.isFollow) {
            followedLayoutBinding!!.smallOutside.autoRefresh()
        } else {
            val follows = myFollowList
            for (userBean in follows!!) {
                if (userBean.userId == followEvent.userId) {
                    follows.remove(userBean)
                    fragmentViewModel!!.followList = follows
                    fragmentViewModel!!.loadFollowData?.value = 0
                    break
                }
            }
        }
    }

    fun resetStatueUserId() {
        if (!isAdded) return
        if (!isResume) return
        val manager = followedLayoutBinding!!.titleUnderlay.layoutManager as LinearLayoutManager?
        val visibleCount = manager!!.childCount
        for (i in 0 until visibleCount) {
            val child = followedLayoutBinding!!.titleUnderlay.getChildAt(i)
            if (child != null) {
                val viewHolder = followedLayoutBinding!!.titleUnderlay.getChildViewHolder(child)
                if (viewHolder != null && viewHolder.javaClass.simpleName == "FollowVH") {
                    try {
                        val anchorInfo = viewHolder.javaClass.getField("anchorInfo")[viewHolder]
                        if (anchorInfo != null) {
                            val userId = anchorInfo.javaClass.getMethod("getUserId")
                                .invoke(anchorInfo) as String
                            if (userId != null) {
                                UserOnlineStatusService.instance!!.addUserId(userId)
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }

        UserOnlineStatusService.instance!!.forceRefresh()
    }

    private fun refreshData() {
        fragmentViewModel!!.loadFollowData()
    }
}
