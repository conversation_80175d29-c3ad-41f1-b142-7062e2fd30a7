package com.juicy.message.View.RongMessage

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.net.Uri
import android.text.Spannable
import android.text.SpannableString
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.constraintlayout.widget.ConstraintLayout
import com.google.gson.Gson
import com.juicy.message.CustomRongDateUtils.getCallRecordTime
import com.juicy.app.R
import com.juicy.app.modules.base.activity.WebViewActivity
import com.juicy.common.model.bean.TppInfoBean
import com.juicy.common.rcMessage.SJMsg
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.config.Constant.giftResMap
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.ImageLoadingUtils.loadImage
import com.juicy.common.utils.LanguageManager
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversation.extension.component.emoticon.AndroidEmoji
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.utils.RongDateUtils
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import io.rong.message.HistoryDividerMessage
import java.util.UUID

class SingleJsonMessage : BaseMessageItemProvider<SJMsg>() {
    override fun onCreateMessageContentViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view =
            LayoutInflater.from(parent.context).inflate(R.layout.rc_single_message, parent, false)
        return ViewHolder(parent.context, view)
    }

    override fun bindViewHolder(
        holder: ViewHolder,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        val isSender =
            (uiMessage
                .message
                .messageDirection
                    == Message.MessageDirection.SEND)



        super.bindViewHolder(holder, uiMessage, position, list, listener)

        if (mConfig.showPortrait) {
            holder.setVisible(io.rong.imkit.R.id.rc_left_portrait, !isSender)
            holder.setVisible(io.rong.imkit.R.id.rc_right_portrait, isSender)
        } else {
            holder.setVisible(io.rong.imkit.R.id.rc_left_portrait, false)
            holder.setVisible(io.rong.imkit.R.id.rc_right_portrait, false)
        }

        val layout = holder.getView<LinearLayout>(io.rong.imkit.R.id.rc_layout)
        if (mConfig.centerInHorizontal) {
            layout.gravity = Gravity.CENTER_HORIZONTAL
        } else {
            layout.gravity = if (isSender) Gravity.END else Gravity.START
        }
        if (mConfig.showContentBubble) {
            holder.getView<View>(io.rong.imkit.R.id.rc_content)
                .setBackgroundResource(if (LanguageManager.instance!!.isLanguageForce) R.drawable.bg_text_message_user_rlt_bg_teal else R.drawable.bg_text_message_user_bg_silver)
        } else {
            holder.getView<View>(io.rong.imkit.R.id.rc_content).background =
                null as Drawable?
        }
        val view =
            holder.getView<View>(if (isSender) io.rong.imkit.R.id.rc_right_portrait else io.rong.imkit.R.id.rc_left_portrait) as ImageView
        val userInfo = uiMessage.userInfo
        if (userInfo != null && userInfo.portraitUri != null) {
            RongConfigCenter.featureConfig().kitImageEngine.loadConversationPortrait(
                holder.context,
                userInfo.portraitUri.toString(),
                view,
                uiMessage.message
            )
        }
        initTime(holder, position, list, uiMessage.message)
    }

    private fun initTime(
        holder: ViewHolder,
        position: Int,
        data: List<UiMessage>,
        message: Message
    ) {
        val time = getCallRecordTime(message.sentTime)
        holder.setText(io.rong.imkit.R.id.rc_time, time)
        if (position == 0) {
            holder.setVisible(io.rong.imkit.R.id.rc_time, message.content !is HistoryDividerMessage)
        } else {
            val pre = data[position - 1]
            if (pre.message != null && RongDateUtils.isShowChatTime(
                    holder.context,
                    message.sentTime,
                    pre.message.sentTime,
                    180
                )
            ) {
                holder.setVisible(io.rong.imkit.R.id.rc_time, true)
            } else {
                holder.setVisible(io.rong.imkit.R.id.rc_time, false)
            }
        }
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        SJMsg: SJMsg,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ) {
        val imCard = holder.getView<ConstraintLayout>(R.id.longBorder)
        val title = holder.getView<AppCompatTextView>(R.id.maximalCounter)
        val activityImage = holder.getView<AppCompatImageView>(R.id.chipAbove)
        val more = holder.getView<RelativeLayout>(R.id.selectedDimmed)

        val tv_text = holder.getView<AppCompatTextView>(R.id.showXl)
        tv_text.text = LanguageManager.instance!!.getLocalTranslate("Click_to_check_details")

        val parentLy = holder.getView<ConstraintLayout>(R.id.updateNested)

        val giftMessage = holder.getView<LinearLayoutCompat>(R.id.thickHeader)
        val name = holder.getView<AppCompatTextView>(R.id.stretchRight)
        val giftIcon = holder.getView<AppCompatImageView>(R.id.dimmedToggle)
        val giftText = holder.getView<AppCompatTextView>(R.id.normalMain)
        val gifta = holder.getView<AppCompatTextView>(R.id.checkedSwipe)
        val accessImg = holder.getView<AppCompatImageView>(R.id.badgeHolder)
        if (LanguageManager.instance!!.isLanguageForce) {
            accessImg.scaleX = -1f
        }


        parentLy.setOnClickListener { v: View ->
            val parent = v.parent
            if (parent is View) {
                (parent as View).performClick()
            }
        }
        parentLy.setOnLongClickListener { v: View ->
            val parent = v.parent
            parent is View && (parent as View).performLongClick()
        }
        val isSender =
            (uiMessage
                .message
                .messageDirection
                    == Message.MessageDirection.SEND)
        if (SJMsg.contentType == "tpp") {
            if (!mConfig.showPortrait) {
                mConfig.showPortrait = true
                mConfig.centerInHorizontal = false
                mConfig.showContentBubble = true
                uiMessage.isChange = true
            }

            holder.setVisible(io.rong.imkit.R.id.rc_left_portrait, !isSender)
            holder.setVisible(io.rong.imkit.R.id.rc_right_portrait, isSender)
            ThatIsImCard(holder, imCard, title, activityImage, more, SJMsg, giftMessage)
        } else {
            if (mConfig.showPortrait) {
                mConfig.showPortrait = false
                mConfig.centerInHorizontal = true
                mConfig.showContentBubble = false
                uiMessage.isChange = true
            }

            holder.setVisible(io.rong.imkit.R.id.rc_left_portrait, false)
            holder.setVisible(io.rong.imkit.R.id.rc_right_portrait, false)
            giftText.text = LanguageManager.instance!!.getLocalTranslate("you_have_send")
            gifta.text = LanguageManager.instance!!.getLocalTranslate("a")
            ThatIsGiftMessage(holder, SJMsg, giftMessage, name, giftIcon, imCard)
        }
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun ThatIsGiftMessage(
        holder: ViewHolder,
        SJMsg: SJMsg,
        giftMessage: LinearLayoutCompat,
        name: AppCompatTextView,
        giftIcon: AppCompatImageView,
        imCard: ConstraintLayout
    ) {
        imCard.visibility = View.GONE
        giftMessage.visibility = View.VISIBLE


        val gift = Gson().fromJson(SJMsg.content, com.juicy.common.model.bean.GiftEventBean::class.java)
        if (gift != null) {
            if (giftResMap!![gift.giftCode] != null) {
                giftIcon.setImageResource(giftResMap!![gift.giftCode]!!)
                //                giftIcon.setBackground(AppCompatResources.getDrawable(holder.getContext(), Constant.getGiftResMap().get(gift.getGiftCode())));
            }

            name.text = gift.getToUserName()
        }
    }

    private fun ThatIsImCard(
        holder: ViewHolder,
        imCard: ConstraintLayout,
        title: AppCompatTextView,
        activityImage: AppCompatImageView,
        more: RelativeLayout,
        SJMsg: SJMsg,
        giftMessage: LinearLayoutCompat
    ) {
        imCard.visibility = View.VISIBLE
        giftMessage.visibility = View.GONE

        val card = Gson().fromJson(SJMsg.content, TppInfoBean::class.java)
        if (card != null) {
            title.text = card.tppTitle
            loadImage(holder.context, card.tppImageUrl, activityImage)
            //            Glide.with(holder.getContext()).load(card.getTppImageUrl()).placeholder(com.juicy.utils.R.drawable.img_empty_big_full_pearl).into(activityImage);
            imCard.setOnClickListener(View.OnClickListener {
                val Url: String
                if (Cache.instance.tppOpenType != null) {
                    if (Cache.instance.tppOpenType == "1") {
                        Url =
                            card.tppUrl + "&source=" + Constant.ImTppSource.IM + "&uuid=" + UUID.randomUUID()
                                .toString()
                        val uri = Uri.parse(Url)
                        val i1 = Intent(Intent.ACTION_VIEW, uri)
                        holder.context.startActivity(i1)
                        return@OnClickListener
                    }
                }

                //默认跳转到内部浏览器
                Url =
                    card.tppUrl + "&source=" + Constant.ImTppSource.IM + "&uuid=" + UUID.randomUUID()
                        .toString()
                val intent = Intent(
                    currentActivity,
                    WebViewActivity::class.java
                )
                intent.putExtra(Constant.URL_PATH, Url)
                intent.putExtra(Constant.ORDER_NO, "")
                currentActivity!!.startActivity(intent)
            })
        }
    }

    override fun onItemClick(
        holder: ViewHolder,
        SJMsg: SJMsg,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>,
        listener: IViewProviderListener<UiMessage>
    ): Boolean {
        return false
    }

    override fun isMessageViewType(messageContent: MessageContent): Boolean {
        return messageContent is SJMsg && !messageContent.isDestruct()
    }

    override fun getSummarySpannable(context: Context, SJMsg: SJMsg?): Spannable {
        if (SJMsg?.contentType != null && SJMsg.contentType == "tpp") {
            val content = "[" + LanguageManager.instance!!.getLocalTranslate("recharge_card") + "]"
            return SpannableString(AndroidEmoji.ensure(content))
        } else {
            if (null != SJMsg && !TextUtils.isEmpty(SJMsg.content)) {
                val content = "[" + LanguageManager.instance!!.getLocalTranslate("gift") + "]"
                return SpannableString(content)
            } else {
                return SpannableString("")
            }
        }
    }
}
