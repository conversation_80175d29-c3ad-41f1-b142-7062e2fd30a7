package com.juicy.message.View

import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ToastUtils
import com.juicy.message.dialog.ConversationMenuDialog
import com.juicy.message.dialog.ConversationMenuDialog.ConversationListLongDialogCallBack
import com.juicy.common.networks.delegate.GetUserInfoInterface.getUserInfo
import com.juicy.common.networks.delegate.ObservableCallBack
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant.nativeOfficeUserList
import com.juicy.common.utils.AppActivityUtil.currentActivity
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.RongCloud.RongUtil.UnReadCount
import com.juicy.common.utils.throttle.Throttle.throttle
import io.reactivex.rxjava3.disposables.Disposable
import io.rong.common.RLog
import io.rong.imkit.IMCenter
import io.rong.imkit.conversationlist.ConversationListFragment
import io.rong.imkit.conversationlist.model.BaseUiConversation
import io.rong.imkit.conversationlist.viewmodel.ConversationListViewModel
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imkit.widget.refresh.constant.RefreshState
import io.rong.imlib.IRongCoreCallback
import io.rong.imlib.IRongCoreEnum.CoreErrorCode
import io.rong.imlib.RongCoreClient
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.UserInfo
import org.greenrobot.eventbus.EventBus

class MyConversationListFragment : ConversationListFragment() {
    private val TAG: String = MyConversationListFragment::class.java.simpleName

    // 上次刷新时间
    private var lastRefreshTime: Long = 0

    override fun onItemLongClick(view: View, holder: ViewHolder, position: Int): Boolean {
        //弹出弹框自己处理
//        ConversationLongDialog dialog = ConversationLongDialog();
//        mList

        val data = mAdapter.data
        val targetId = data[position].mCore.targetId
        val conversationType = Conversation.ConversationType.PRIVATE
        var isShowDialog = true
        var userIds: List<String> = ArrayList()
        if (Cache.instance.userStratResult != null) {
            userIds = Cache.instance.userStratResult?.topOfficialUserIds?: listOf()
        }
        for (j in userIds.indices) {
            if (userIds[j] == targetId &&
                !(Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.userServiceAccountId != null &&
                        targetId == Cache.instance.userStratResult!!.userServiceAccountId)
            ) {
                isShowDialog = false
                break
            }
        }

        //测试有问题，在修复
//        List<String> followUserIds = Cache.getInstance().strategyResult.getBroadcasterFollowOfficialUserIds();
//        for (int j = 0; j < followUserIds.size(); j++) {
//            if (followUserIds.get(j).equals(targetId)) {
//                isShowDialog = false;
//                break;
//            }
//        }

//        if (targetId.equals(Cache.getInstance().strategyResult.getUserServiceAccountId())) {
//            isShowDialog = false;
//        }
        if (!isShowDialog) return false

        RongCoreClient.getInstance().getConversation(
            conversationType,
            targetId,
            object : IRongCoreCallback.ResultCallback<Conversation>() {
                override fun onSuccess(conversation: Conversation) {
                    // 成功并返回会话信息

                    val longDialog = ConversationMenuDialog(
                        conversation.isTop,
                        object : ConversationListLongDialogCallBack {
                            override fun didSelectTop() {
                                var isTop = true

                                isTop = !conversation.isTop

                                IMCenter.getInstance()
                                    .setConversationToTop(
                                        conversationType,
                                        targetId,
                                        isTop,
                                        false, null
                                    )
                                //Toast
                                ToastUtils.showShort(LanguageManager.instance!!.getLocalTranslate(if (isTop) "Sticky on Top Successful" else "Sticky on Top Cancelled"))
                            }

                            override fun didSelectDelete() {
                                IMCenter.getInstance()
                                    .removeConversation(
                                        conversationType,
                                        targetId,
                                        null
                                    )
                            }
                        })
                    val currentActivity = currentActivity as AppCompatActivity?
                    longDialog.show(currentActivity!!.supportFragmentManager, "")
                }

                override fun onError(coreErrorCode: CoreErrorCode) {
                    Log.e("MyCOnv","onError $coreErrorCode")
                }
            })

        return false
    }

    override fun onResume() {
        super.onResume()
        if (this.mConversationListViewModel != null) {
            mConversationListViewModel.getConversationList(false, true, 0L)
        } else {
        }
    }

    fun setTop(targetId: String?) {
        val conversationType = Conversation.ConversationType.PRIVATE

        IMCenter.getInstance()
            .setConversationToTop(
                conversationType,
                targetId,
                true,
                false, null
            )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (!isAdded) {
            return
        }
        //        TextView customTitle = (TextView)view.findViewById(R.id.rc_conversation_title);
//        customTitle.setText(LanguageManager.getInstance().getLocalTranslate("Customer_Service"));
//        ConstraintLayout robotLayout = (ConstraintLayout)view.findViewById(R.id.gridExpand);
//        if (Cache.getInstance().reviewPkg){
//            robotLayout.setVisibility(View.GONE);
//            LayoutInflater layoutInflater = LayoutInflater.from(getContext());
//            View emptyView = layoutInflater.inflate(io.rong.imkit.R.layout.rc_conversationlist_empty_view, null);
//            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
//            layoutParams.gravity = Gravity.CENTER;
//            emptyView.setLayoutParams(layoutParams);
//            emptyView.findViewById(io.rong.imkit.R.id.rc_empty_iv).setVisibility(View.GONE);
//            setEmptyView(emptyView);
//        }
//        robotLayout.setOnClickListener(v -> {
//            getActivity().startActivity(new Intent(requireContext(), RobotServiceActivity.class));
//        });
    }

    public override fun subscribeUi() {
        if (Cache.instance.reviewPkg) {
            var nativeUserIds2: List<String> = ArrayList()
            if (Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.reviewOfficialBlacklistUserIds != null) {
                nativeUserIds2 = Cache.instance.userStratResult!!.reviewOfficialBlacklistUserIds?: listOf()
            }
            //融云拉黑  nativeUserIds2
            for (i in nativeUserIds2.indices) {
                RongIMClient.getInstance().addToBlacklist(nativeUserIds2[i], null)
                //并且清理未读数
                RongIMClient.getInstance().clearMessagesUnreadStatus(
                    Conversation.ConversationType.PRIVATE,
                    nativeUserIds2[i], null
                )
            }
        } else {
            if (Cache.instance.userStratResult != null && Cache.instance.userStratResult?.reviewOfficialBlacklistUserIds != null) {
                val nativeUserIds2 = Cache.instance.userStratResult?.reviewOfficialBlacklistUserIds
                //解除拉黑
                if (nativeUserIds2 != null) {
                    for (i in nativeUserIds2.indices) {
                        RongIMClient.getInstance().removeFromBlacklist(nativeUserIds2[i], null)
                    }
                }
            }
        }


        this.mConversationListViewModel = (ViewModelProvider(this)).get(
            ConversationListViewModel::class.java
        )
        mConversationListViewModel.getConversationList(false, false, 0)
        mConversationListViewModel.conversationListLiveData.observe(
            this.viewLifecycleOwner
        ) { uiConversations ->
            val myUiConversations: MutableList<BaseUiConversation> = ArrayList()
            val tempList: MutableList<BaseUiConversation> = ArrayList()
            tempList.addAll(uiConversations)
            if (!Cache.instance.reviewPkg) {
                filter(myUiConversations, tempList) //过滤官方账号，将其置顶；
            } else {
                deleteNativeOfficeUser(myUiConversations, tempList) //过滤官方账号
            }

            EventBus.getDefault().post(UnReadCount())

            // 检查并刷新空头像
            checkAndRefreshEmptyPortraits(myUiConversations)
        }
        mConversationListViewModel.noticeContentLiveData.observe(
            this.viewLifecycleOwner
        ) { noticeContent ->
            if (mNoticeContainerView.visibility == View.GONE) {
                mHandler.postDelayed({
                    <EMAIL>(
                        mConversationListViewModel.noticeContentLiveData.value
                    )
                }, 4000L)
            } else {
                <EMAIL>(noticeContent)
            }
        }
        mConversationListViewModel.refreshEventLiveData.observe(
            this.viewLifecycleOwner
        ) { refreshEvent ->
            if (refreshEvent.state == RefreshState.LoadFinish) {
                mRefreshLayout.finishLoadMore()
            } else if (refreshEvent.state == RefreshState.RefreshFinish) {
                mRefreshLayout.finishRefresh()
            }
        }
        lastRefreshTime = System.currentTimeMillis() - (8 * 1000)


        if (!Cache.instance.reviewPkg) {  // 如果不是审核包才添加机器人服务
            // 添加机器人服务 header
            val customerServiceHeaderView = CustomerServiceHeaderView(requireContext())
            // 添加到列表头部
            addHeaderView(customerServiceHeaderView)
        }

        mList.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)

                throttle("conversation_refresh", 500,
                    {},
                    {
                        checkAndRefreshEmptyPortraits(mAdapter.data)
                    })
            }
        })

        super.subscribeUi()
    }

    private fun filter(
        myUiConversations: MutableList<BaseUiConversation>,
        tempList: List<BaseUiConversation>
    ) {
        var userIds: List<String>? = ArrayList()
        var nativeUserIds: List<String>? = ArrayList()
        if (Cache.instance.userStratResult != null) {
            userIds = Cache.instance.userStratResult!!.topOfficialUserIds
            nativeUserIds = Cache.instance.userStratResult!!.officialBlacklistUserIds
        }
        val filterPositionList: MutableList<Int> = ArrayList()
        for (i in tempList.indices) {
            if (userIds != null) {
                for (j in userIds.indices) {
                    if (userIds[j] == tempList[i].mCore.targetId) {
                        myUiConversations.add(0, tempList[i])
                        if (!tempList[i].mCore.isTop &&
                            !(Cache.instance.userStratResult != null && Cache.instance.userStratResult!!.userServiceAccountId != null && tempList[i].mCore.targetId == Cache.instance.userStratResult!!.userServiceAccountId)
                        ) {
                            setTop(tempList[i].mCore.targetId)
                        }
                        //
                        filterPositionList.add(i)
                    }
                }
            }
            //            if (tempList.get(i).mCore.getTargetId().equals(Cache.getInstance().strategyResult.getUserServiceAccountId()) &&
//                    !tempList.get(i).mCore.isTop()
//            ) {
//
//                setTop(tempList.get(i).mCore.getTargetId());
//            }
            if (null != nativeUserIds) {
                for (k in nativeUserIds.indices) {
                    if (nativeUserIds[k] == tempList[i].mCore.targetId) {
                        if (userIds!!.contains(nativeUserIds[k])) continue
                        myUiConversations.add(0, tempList[i])
                        filterPositionList.add(i)
                    }
                }
            }
        }
        for (i in tempList.indices) {
            if (filterPositionList.contains(i)) {
                continue
            }
            myUiConversations.add(tempList[i])
        }
    }

    private fun deleteNativeOfficeUser(
        myUiConversations: MutableList<BaseUiConversation>,
        tempList: List<BaseUiConversation>
    ) {
        var userIds: List<String>? = ArrayList()
        var vipUserId: String? = ""
        val nativeUserIds: List<String>? = nativeOfficeUserList
        var nativeUserIds2: List<String>? = ArrayList()
        if (Cache.instance.userStratResult != null) {
            userIds = Cache.instance.userStratResult!!.topOfficialUserIds
            vipUserId = Cache.instance.userStratResult!!.userServiceAccountId
            nativeUserIds2 = Cache.instance.userStratResult!!.reviewOfficialBlacklistUserIds
        }
        val filterPositionList: MutableList<Int> = ArrayList()
        for (i in tempList.indices) {
            if (userIds != null) {
                for (j in userIds.indices) {
                    if (userIds[j] == tempList[i].mCore.targetId) {
                        filterPositionList.add(i)
                    }
                }
            }
            if (null != nativeUserIds) {
                for (k in nativeUserIds.indices) {
                    if (nativeUserIds[k] == tempList[i].mCore.targetId) {
                        if (userIds!!.contains(nativeUserIds[k])) continue

                        filterPositionList.add(i)
                    }
                }
            }
            if (null != nativeUserIds2) {
                for (k in nativeUserIds2.indices) {
                    if (nativeUserIds2[k] == tempList[i].mCore.targetId) {
                        if (userIds!!.contains(nativeUserIds2[k])) continue

                        filterPositionList.add(i)
                    }
                }
            }
            if (vipUserId != null && vipUserId == tempList[i].mCore.targetId) {
                filterPositionList.add(i)
            }
        }
        for (i in tempList.indices) {
            if (filterPositionList.contains(i)) {
                continue
            }
            myUiConversations.add(tempList[i])
        }
    }

    /**
     * 检查并刷新空头像
     */
    private fun checkAndRefreshEmptyPortraits(conversations: List<BaseUiConversation>?) {
        if (conversations == null || conversations.isEmpty()) {
            return
        }

        // 检查是否达到刷新间隔
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastRefreshTime < REFRESH_INTERVAL) {
            RLog.d(TAG, "Skip refresh: too frequent")
            return
        }

        val emptyPortraitUserIds: MutableList<String> = ArrayList()


        // 收集所有空头像的用户ID
        for (conversation in conversations) {
            val portraitUri = conversation.mCore.portraitUrl
            if (TextUtils.isEmpty(portraitUri)) {
                val targetId = conversation.mCore.targetId
                if (!TextUtils.isEmpty(targetId)) {
                    emptyPortraitUserIds.add(targetId)
                }
            }
        }

        // 如果有空头像的用户，刷新他们的信息
        if (!emptyPortraitUserIds.isEmpty()) {
            lastRefreshTime = currentTime // 更新最后刷新时间
            refreshUserPortraits(emptyPortraitUserIds)
        }
    }

    /**
     * 刷新用户头像信息
     */
    private fun refreshUserPortraits(userIds: List<String>) {
        // 使用融云SDK刷新用户信息
        for (userId in userIds) {
//            Log.d(TAG, "refreshUserPortraits: ");
            getUserInfo(userId, object : ObservableCallBack<com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>>() {
                override fun onSubscribe(d: Disposable) {
                    super.onSubscribe(d)
                }

                override fun onNext(azBaseBean: com.juicy.common.model.bean.NetResponseBean<com.juicy.common.model.bean.JuicyUserInfoBean>) {
                    super.onNext(azBaseBean)
                    if (azBaseBean != null && azBaseBean.isOk) {
                        if (azBaseBean.data != null) {
                            val az = azBaseBean.data
                            val userInfo = if (az!!.avatarThumbUrl != null) {
                                UserInfo(
                                    az.userId, az.nickname, Uri.parse(
                                        az.avatarThumbUrl
                                    )
                                )
                            } else {
                                UserInfo(
                                    az.userId,
                                    az.nickname,
                                    Uri.parse("")
                                )
                            }
                            RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
                        }
                    }
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                }

                override fun onComplete() {
                    super.onComplete()
                }
            })
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清除刷新时间
        lastRefreshTime = 0
    }

    companion object {
        // 刷新间隔时间 10秒
        private const val REFRESH_INTERVAL = (10 * 1000).toLong()
    }
}
