package com.juicy.message.adapter

import android.view.View
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.app.R

class CallLogViewHolder(view: View) : BaseViewHolder(view) {

    var called_icon: ImageView =
        view.findViewById(R.id.responsiveInside)
    var callType: AppCompatTextView =
        view.findViewById(R.id.rigidAdaptive)
    var callDuration: AppCompatTextView =
        view.findViewById(R.id.clockLast)
    var anchorPhoto: AppCompatImageView =
        view.findViewById(R.id.fluidForeground)
    var anchorName: AppCompatTextView =
        view.findViewById(R.id.avatarLarge)
    var overTime: AppCompatTextView =
        view.findViewById(R.id.containerTimer)
    var item: ConstraintLayout =
        view.findViewById(R.id.nearCenter)
}
