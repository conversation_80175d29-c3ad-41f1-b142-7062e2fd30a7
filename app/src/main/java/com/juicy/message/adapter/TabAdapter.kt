package com.juicy.message.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.Keep
import androidx.recyclerview.widget.RecyclerView
import com.juicy.app.R

class TabAdapter(private val textList: List<String>, private val callBack: CallBack) :
    RecyclerView.Adapter<MessageLogVewHost>() {
    var selectPosition: Int = 0


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MessageLogVewHost {
        return MessageLogVewHost(
            LayoutInflater.from(parent.context).inflate(R.layout.item_container, parent, false)
        )
    }

    override fun onBindViewHolder(holder: MessageLogVewHost, @SuppressLint("RecyclerView") position: Int) {
        holder.title.text = textList[position]
        if (position == selectPosition) {
            holder.title.setTextColor(Color.parseColor("#2C2E33"))
            holder.line.visibility = View.VISIBLE
            holder.title.textSize = 16f

        } else {
            holder.title.textSize = 14f
            holder.title.setTextColor(Color.parseColor("#939099"))
            holder.line.visibility = View.INVISIBLE
        }
        holder.itemView.setOnClickListener {
            callBack.onCallBack(
                position
            )
        }
    }

    override fun getItemCount(): Int {
        return textList.size
    }

    @Keep
    interface CallBack {
        fun onCallBack(position: Int)
    }
}
