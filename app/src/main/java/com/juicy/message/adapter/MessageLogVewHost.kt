package com.juicy.message.adapter

import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.RecyclerView
import com.juicy.app.R


class MessageLogVewHost(view: View) : RecyclerView.ViewHolder(view) {
    var title: AppCompatTextView =
        view.findViewById(R.id.maximalCounter)
    var line: View = view.findViewById(R.id.xxlSecondary)
    var redPoint: View =
        view.findViewById(R.id.nestedTab)
    val star : AppCompatImageView = view.findViewById(R.id.copyPrev)
    val tabBg: AppCompatImageView = view.findViewById(R.id.mediumConfirm)
}
