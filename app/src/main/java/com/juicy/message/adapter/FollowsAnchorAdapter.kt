package com.juicy.message.adapter

import android.graphics.Color
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.ActivityUtils
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.juicy.message.adapter.FollowsAnchorAdapter.FollowVH
import com.juicy.app.R
import com.juicy.common.config.Cache
import com.juicy.common.config.Constant
import com.juicy.common.utils.AppDeviceUtils.dip2px
import com.juicy.common.utils.LanguageManager
import com.juicy.common.utils.QuickDrawable.Companion.create
import com.juicy.common.utils.view.CircleImageView

class FollowsAnchorAdapter(layoutResId: Int) : BaseQuickAdapter<com.juicy.common.model.bean.BaseUserInfoBean?, FollowVH>(layoutResId) {
    override fun convert(holder: FollowVH, item: com.juicy.common.model.bean.BaseUserInfoBean?) {
        item?.let {


            holder.anchorInfo = it
            if (it.avatarUrl != null) Glide.with(context).load(it.avatarUrl)
                .into(holder.anchorPhoto)
            if (Cache.instance.reviewPkg) {
                holder.statusPoint.visibility = View.GONE
            }
            if (it.gender != null) {
                if (it.gender == Constant.SEX_MAN) {
                    holder.gender.setImageDrawable(
                        AppCompatResources.getDrawable(
                            context,
                            R.drawable.ic_boyl_orange
                        )
                    )
                    holder.age.setTextColor(Color.parseColor("#FF00DDFF"))

                } else if (it.gender == Constant.SEX_WOMAN) {
                    holder.gender.setImageDrawable(
                        AppCompatResources.getDrawable(
                            context,
                            R.drawable.ic_girl_ash
                        )
                    )
                    holder.age.setTextColor(ActivityUtils.getTopActivity().resources.getColor(R.color.material_33aa))

                }
                holder.info.background = create()
                    .corner(dip2px(2f).toFloat())
                    .bgColor(
                        if (it.gender == 1) Color.parseColor("#1A00DDFF") else Color.parseColor(
                            "#1AFF33CC"
                        )
                    )
                    .build()
            }
            if (it.age != null) holder.age.text = it.age.toString()
            if (it.country != null) holder.country.text = it.country
            if (it.nickname != null) holder.anchorName.text = it.nickname

            var color = 0
            if (it.onlineStatus == Constant.IN_CALL) {
                color = (ActivityUtils.getTopActivity().resources.getColor(R.color.material_3333))
            } else if (it.onlineStatus == Constant.ONLINE || it.onlineStatus == Constant.AVAILABLE) {
                color = (ActivityUtils.getTopActivity().resources.getColor(R.color.material_ff00))
            } else if (it.onlineStatus == Constant.BUSY) {
                color = (ActivityUtils.getTopActivity().resources.getColor(R.color.material_de40))
            } else if (it.onlineStatus == Constant.OFFLINE) {
                color = (ActivityUtils.getTopActivity().resources.getColor(R.color.basic_bbbf))
            }
            holder.statusPoint.background = create()
                .corner(dip2px(4f).toFloat())
                .bgColor(color)
                .build()
            holder.followItem.setOnClickListener { v: View? ->
                ARouter
                    .getInstance()
                    .build(Constant.ANCHOR_INFO_ACTIVITY_ROUTE)
                    .withString(Constant.USER_ID, it.userId)
                    .navigation()
            }
        }

    }


    class FollowVH(view: View) : BaseViewHolder(view) {
        var statusPoint: View =
            view.findViewById(com.juicy.app.R.id.copyLeft)
        var anchorPhoto: CircleImageView =
            view.findViewById(com.juicy.app.R.id.fluidForeground)
        var followItem: ConstraintLayout =
            view.findViewById(com.juicy.app.R.id.containerResponsive)
        var info: LinearLayout =
            view.findViewById(com.juicy.app.R.id.xsMedium)
        var anchorName: AppCompatTextView =
            view.findViewById(com.juicy.app.R.id.avatarLarge)
        var age: TextView =
            view.findViewById(com.juicy.app.R.id.autoTimer)
        var country: TextView =
            view.findViewById(com.juicy.app.R.id.playGrid)
        var anchorInfo: com.juicy.common.model.bean.BaseUserInfoBean? = null
        var gender: ImageView =
            view.findViewById(com.juicy.app.R.id.brightThin)


        init {
            if (LanguageManager.instance!!.isLanguageForce) {
                anchorName.gravity = Gravity.END
            }
        }
    }
}

