package com.juicy.message.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter

class PagerAdapter(fragmentManager: FragmentManager, lifecycle: Lifecycle) :
    FragmentStateAdapter(fragmentManager, lifecycle) {
    private var fragmentBases: List<Fragment> = ArrayList()
    override fun getItemCount(): Int {
        return fragmentBases.size
    }

    fun setFragmentBases(fragmentBases: List<Fragment>) {
        this.fragmentBases = fragmentBases
    }
    override fun createFragment(position: Int): Fragment {
        return fragmentBases[position]
    }


}
