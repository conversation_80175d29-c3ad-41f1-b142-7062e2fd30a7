package com.juicy.message.adapter

import android.annotation.SuppressLint
import android.graphics.Color
import android.view.Gravity
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.bumptech.glide.Glide
import com.chad.library.adapter.base.BaseQuickAdapter
import com.juicy.app.R
import com.juicy.common.db.User
import com.juicy.common.config.Constant
import com.juicy.common.utils.DateMetaUtil.formatDate
import com.juicy.common.utils.LanguageManager

class CallsLogAdapter(layoutResId: Int) : BaseQuickAdapter<User?, CallLogViewHolder>(layoutResId) {
    override fun convert(holder: CallLogViewHolder, item: User?) {
        if (item == null) return

        holder.anchorName.text = item.name
        holder.item.setOnClickListener {
            ARouter.getInstance().build(Constant.ANCHOR_INFO_ACTIVITY_ROUTE)
                .withString(Constant.USER_ID, item.id)
                .navigation(context)
        }
        val builder = StringBuilder()
        builder.append("[").append(LanguageManager.instance!!.getLocalTranslate(item.callType))
            .append("]")
        holder.callType.text = builder
        Glide.with(context).load(item.photo)
            .into(holder.anchorPhoto)
        if (item.callType == Constant.REJECTED_CALL
            || item.callType == Constant.MISSED_CALL
            || item.callType == Constant.UNANSWERED_CALL
            || item.callType == Constant.CANCELLED_CALL
        ) {
            holder.callType.setTextColor(context.getColor(R.color.material_3333))
        } else {
            holder.callType.setTextColor(Color.parseColor("#B8BABA"))
            holder.called_icon.setImageResource(R.drawable.ic_call_success_mint)
        }
        holder.called_icon.setImageResource(R.drawable.ic_call_error_ivory)
        if (item.callType == Constant.OUTGOING_CALL
            || item.callType == Constant.INCOMING_CALL
        ) {
            holder.callDuration.visibility = View.VISIBLE
        } else {
            holder.callDuration.visibility = View.GONE
        }


        try {
            holder.overTime.text =
                formatDate(item.callOverTime.toLong(), System.currentTimeMillis())
            holder.callDuration.text = formatSeconds(item.duration.toInt())

        } catch (e: Exception) {
            e.printStackTrace()
        }
        if (LanguageManager.instance!!.isLanguageForce) {
            holder.anchorName.gravity = Gravity.END
        }
    }



    @SuppressLint("DefaultLocale")
    fun formatSeconds(duration22: Int?): String {
        var duration = duration22
        if (duration == null) {
            duration = 0 // 如果 duration 为 null，默认设置为 0
        }
        if (duration == 0) return ""

        val hour = duration / 3600
        val min = (duration / 60) % 60
        val second = duration % 60
        val durationString = StringBuilder()

        if (hour > 0) {
            durationString.append(String.format("%02d:%02d:%02d", hour, min, second))
        } else if (min > 0) {
            durationString.append(String.format("%02d:%02d", min, second))
        } else {
            durationString.append(String.format("00:%02d", second))
        }

        return durationString.toString()
    }
    private fun formatDate(endTime: Long, currentTime: Long): String {
        val nd = (1000 * 24 * 60 * 60).toLong()
        val nh = (1000 * 60 * 60).toLong()
        val nm = (1000 * 60).toLong()
        val time: String
        val diff = currentTime - endTime
        // 计算差多少天
        val day = diff / nd
        // 计算差多少小时
        val hour = diff % nd / nh
        // 计算差多少分钟
        val min = diff % nd % nh / nm
        //判断相差多少小时
        time = if (hour < 1) {
            //显示多少分钟之前
            "$min m ago"
        } else if (day < 1) {
            "$hour h ago"
        } else if (day < 7) {
            "$day d ago"
        } else {
            formatDate(endTime)
        }
        return time
    }
}

