package com.juicy.message

import android.content.Context
import android.provider.Settings
import android.text.TextUtils
import com.juicy.common.utils.LanguageManager
import io.rong.common.RLog
import io.rong.imkit.utils.language.LangUtils
import io.rong.imkit.utils.language.LangUtils.RCLocale
import io.rong.imkit.utils.language.RongConfigurationManager
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.concurrent.TimeUnit

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


object CustomRongDateUtils {
    private val TAG: String = CustomRongDateUtils::class.java.canonicalName
    private const val SPACE_CHAR = " "
    private const val OTHER = 2014
    private const val TODAY = 6
    private const val YESTERDAY = 15

    fun judgeDate(context: Context, date: Date): Int {
        val locale = LangUtils.getAppLanguageLocal(context).toLocale()
        val calendarToday = Calendar.getInstance(locale)
        calendarToday[11] = 0
        calendarToday[12] = 0
        calendarToday[13] = 0
        calendarToday[14] = 0
        val calendarYesterday = Calendar.getInstance()
        calendarYesterday.add(5, -1)
        calendarYesterday[11] = 0
        calendarYesterday[12] = 0
        calendarYesterday[13] = 0
        calendarYesterday[14] = 0
        val calendarTomorrow = Calendar.getInstance()
        calendarTomorrow.add(5, 1)
        calendarTomorrow[11] = 0
        calendarTomorrow[12] = 0
        calendarTomorrow[13] = 0
        calendarTomorrow[14] = 0
        val calendarTarget = Calendar.getInstance(locale)
        calendarTarget.time = date
        return if (calendarTarget.before(calendarYesterday)) {
            2014
        } else if (calendarTarget.before(calendarToday)) {
            15
        } else {
            if (calendarTarget.before(calendarTomorrow)) 6 else 2014
        }
    }

    private fun getWeekDay(context: Context, dayInWeek: Int): String {
        var weekDay = ""
        when (dayInWeek) {
            1 -> weekDay = LanguageManager.instance!!.getLocalTranslate("Sunday")
            2 -> weekDay = LanguageManager.instance!!.getLocalTranslate("Monday")
            3 -> weekDay = LanguageManager.instance!!.getLocalTranslate("Tuesday")
            4 -> weekDay = LanguageManager.instance!!.getLocalTranslate("Wednesday")
            5 -> weekDay = LanguageManager.instance!!.getLocalTranslate("Thursday")
            6 -> weekDay = LanguageManager.instance!!.getLocalTranslate("Friday")
            7 -> weekDay = LanguageManager.instance!!.getLocalTranslate("Saturday")
        }

        return weekDay
    }

    fun isTime24Hour(context: Context): Boolean {
        val timeFormat = Settings.System.getString(context.contentResolver, "time_12_24")
        return timeFormat != null && timeFormat == "24"
    }

    private fun getTimeString(dateMillis: Long, context: Context): String {
        if (dateMillis <= 0L) {
            return ""
        } else {
            val date = Date(dateMillis)
            var formatTime: String
            if (isTime24Hour(context)) {
                formatTime = formatDate(context, date, "HH:mm")
            } else {
                formatTime = formatDate(context, date, "h:mm")
                formatTime = if (RongConfigurationManager.getInstance()
                        .getLanguageLocal(context) === RCLocale.LOCALE_CHINA
                ) {
                    getTime12HourDes(dateMillis, context) + " " + formatTime
                } else {
                    formatTime + " " + getTime12HourDes(dateMillis, context)
                }
            }

            return formatTime
        }
    }

    private fun getTime12HourDes(dateMillis: Long, context: Context): String {
        val calendarTime = Calendar.getInstance(LangUtils.getAppLanguageLocal(context).toLocale())
        calendarTime.timeInMillis = dateMillis
        val hour = calendarTime[10]
        var des = ""
        des = if (calendarTime[9] == 0) {
            if (hour < 6) {
                LanguageManager.instance!!.getLocalTranslate("AM")
            } else {
                LanguageManager.instance!!.getLocalTranslate("AM")
            }
        } else if (hour == 0) {
            LanguageManager.instance!!.getLocalTranslate("PM")
        } else if (hour <= 5) {
            LanguageManager.instance!!.getLocalTranslate("PM")
        } else {
            LanguageManager.instance!!.getLocalTranslate("PM")
        }

        return des
    }

    private fun getDateTimeString(dateMillis: Long, showTime: Boolean, context: Context): String? {
        if (dateMillis <= 0L) {
            return ""
        } else {
            var formatDate: String? = null
            val date = Date(dateMillis)
            val type = judgeDate(context, date)
            val time = System.currentTimeMillis()
            val locale = LangUtils.getAppLanguageLocal(context).toLocale()
            val calendarCur = Calendar.getInstance(locale)
            val calendardate = Calendar.getInstance(locale)
            calendardate.timeInMillis = dateMillis
            calendarCur.timeInMillis = time
            val month = calendardate[2]
            val year = calendardate[1]
            val weekInMonth = calendardate[4]
            val monthCur = calendarCur[2]
            val yearCur = calendarCur[1]
            val weekInMonthCur = calendarCur[4]
            when (type) {
                6 -> formatDate = getTimeString(dateMillis, context)
                15 -> {
                    val formatString = LanguageManager.instance!!.getLocalTranslate("Yesterday")
                    formatDate = if (showTime) {
                        formatString + " " + getTimeString(dateMillis, context)
                    } else {
                        formatString
                    }
                }

                2014 -> {
                    formatDate = if (year == yearCur) {
                        if (month == monthCur && weekInMonth == weekInMonthCur) {
                            getWeekDay(context, calendardate[7])
                        } else {
                            formatDate(context, date, "M/d")
                        }
                    } else {
                        formatDate(context, date, "yyyy/M/d")
                    }

                    if (showTime) {
                        formatDate = formatDate + " " + getTimeString(dateMillis, context)
                    }
                }
            }

            return formatDate
        }
    }

    @JvmStatic
    fun getCallRecordTime(timestamp: Long): String {
        // 将时间戳转换为秒（如果大于10位数，则认为是毫秒）
        var timestamp = timestamp
        if (timestamp > 9999999999L) {
            timestamp = timestamp / 1000
        }

        val messageTime = Date(timestamp * 1000) // 转换为毫秒
        val currentTime = Date()

        val timeDifference = currentTime.time - messageTime.time // 时间差（毫秒）

        // 计算时间差的单位
        val days = TimeUnit.MILLISECONDS.toDays(timeDifference)
        val hours = TimeUnit.MILLISECONDS.toHours(timeDifference) % 24

        val formatter: SimpleDateFormat
        if (days < 1) { // 小于1天
            formatter = SimpleDateFormat("HH:mm")
            return formatter.format(messageTime)
        } else { // 大于等于1天
            formatter = SimpleDateFormat("M-d HH:mm")
            return formatter.format(messageTime)
        }
    }

    fun getConversationListFormatDate(dateMillis: Long, context: Context): String? {
        return getDateTimeString(dateMillis, false, context)
    }

    fun getConversationFormatDate(dateMillis: Long, context: Context): String? {
        return getDateTimeString(dateMillis, true, context)
    }

    fun isShowChatTime(context: Context, currentTime: Long, preTime: Long, interval: Int): Boolean {
        val typeCurrent = judgeDate(context, Date(currentTime))
        val typePre = judgeDate(context, Date(preTime))
        return if (typeCurrent == typePre) {
            currentTime - preTime > (interval * 1000).toLong()
        } else {
            true
        }
    }

    fun formatDate(context: Context, date: Date, fromat: String?): String {
        if (TextUtils.isEmpty(fromat)) {
            return ""
        } else {
            try {
                val sdf =
                    SimpleDateFormat(fromat, LangUtils.getAppLanguageLocal(context).toLocale())
                return sdf.format(date)
            } catch (var4: IllegalArgumentException) {
                RLog.e(TAG, "the given pattern is invalid.")
                return ""
            }
        }
    }
}
