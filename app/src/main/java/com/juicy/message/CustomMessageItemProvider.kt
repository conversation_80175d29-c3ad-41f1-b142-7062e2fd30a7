package com.juicy.message

import android.content.Context
import android.text.Spannable
import android.view.ViewGroup
import androidx.core.text.toSpannable
import com.juicy.app.R
import com.juicy.common.networks.delegate.GetUserInfoInterface.getCacheUserInfo
import com.juicy.common.config.Cache
import com.juicy.common.utils.ImageLoadingUtils.loadCircleImage
import io.rong.imkit.conversation.messgelist.provider.BaseMessageItemProvider
import io.rong.imkit.model.UiMessage
import io.rong.imkit.widget.adapter.IViewProviderListener
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent

class CustomMessageItemProvider : BaseMessageItemProvider<MessageContent?>() {
    override fun bindViewHolder(
        holder: ViewHolder,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>?,
        listener: IViewProviderListener<UiMessage>?
    ) {
        super.bindViewHolder(holder, uiMessage, position, list, listener)

        val time =
            CustomRongDateUtils.getCallRecordTime(uiMessage.sentTime)
        holder.setText(R.id.rc_time, time)

        val isSender =
            (uiMessage
                .message
                .messageDirection
                    == Message.MessageDirection.SEND)

        if (mConfig.showPortrait) {
            holder.setVisible(io.rong.imkit.R.id.rc_left_portrait, !isSender)
            holder.setVisible(io.rong.imkit.R.id.rc_right_portrait, isSender)
        } else {
            holder.setVisible(io.rong.imkit.R.id.rc_left_portrait, false)
            holder.setVisible(io.rong.imkit.R.id.rc_right_portrait, false)
        }
        if (isSender) {
            //自己
            val user = Cache.instance.userInfo
            if (user != null) {
                loadCircleImage(
                    holder.context,
                    user.avatarThumbUrl,
                    holder.getView(io.rong.imkit.R.id.rc_right_portrait)
                )
            }
        } else {
            val user = getCacheUserInfo(uiMessage.targetId)
            if (user != null) {
                loadCircleImage(
                    holder.context,
                    user.avatarThumbUrl,
                    holder.getView(io.rong.imkit.R.id.rc_left_portrait)
                )
            }
        }
    }


    override fun onCreateMessageContentViewHolder(parent: ViewGroup, viewType: Int): ViewHolder? {
        return null
    }

    override fun isMessageViewType(messageContent: MessageContent): Boolean {
        return false
    }

    override fun onItemClick(
        holder: ViewHolder,
        messageContent: MessageContent?,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>?,
        listener: IViewProviderListener<UiMessage>?
    ): Boolean {
        return false
    }

    override fun bindMessageContentViewHolder(
        holder: ViewHolder,
        parentHolder: ViewHolder,
        messageContent: MessageContent?,
        uiMessage: UiMessage,
        position: Int,
        list: List<UiMessage>?,
        listener: IViewProviderListener<UiMessage>?
    ) {
    }

    override fun getSummarySpannable(context: Context?, t: MessageContent?): Spannable {
        return "".toSpannable()
    }

    override fun isItemViewType(item: UiMessage?): Boolean {
        return false
    }

//    override fun bindViewHolder(
//        holder: ViewHolder?,
//        o: UiMessage?,
//        position: Int,
//        list: List<UiMessage>?,
//        listener: IViewProviderListener<UiMessage>?
//    ) {
//    }
}