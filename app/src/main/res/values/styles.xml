<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
    </style>

    <!--    圆形图片style-->
    <style name="widget_circularimageview">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    <declare-styleable name="CircleImageView">
        <attr name="civ_border_width" format="dimension" />
        <attr name="civ_border_color" format="color" />
        <attr name="civ_border_overlay" format="boolean" />
        <attr name="civ_fill_color" format="color" />
        <attr name="circle_width" format="dimension" />
        <attr name="circle_color" format="color" />
        <attr name="circle_immerse" format="boolean" />
    </declare-styleable>
    <style name="text_tabtextstyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="dialog_bottomdialogstyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>
    <declare-styleable name="RoundImageView">
        <!-- 描边宽度-->
        <attr name="rouond_border_width" format="dimension"/>
        <!-- 描边颜色-->
        <attr name="rouond_border_color" format="color"/>
        <!--圆角大小-->
        <attr name="rouond_corner_radius" format="dimension"/>
        <!--左上圆角大小-->
        <attr name="leftTop_corner_radius" format="dimension"/>
        <!--右上圆角大小-->
        <attr name="rightTop_corner_radius" format="dimension"/>
        <!--左下圆角大小-->
        <attr name="leftBottom_corner_radius" format="dimension"/>
        <!--右下圆角大小-->
        <attr name="rightBottom_corner_radius" format="dimension"/>
        <!--图片类型：圆角或者圆形-->
        <attr name="type" format="enum">
            <enum name="oval" value="2"/>
            <enum name="round" value="1"/>
            <enum name="circle" value="0"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="autoWrapLineLayout">
        <attr name="verticalGap" format="dimension" />
        <attr name="horizontalGap" format="dimension" />
        <attr name="fillMode" format="integer" />
    </declare-styleable>

    <!--实现BottomSheetDialog圆角效果-->
    <style name="dialog_bottomsheetdialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/app_bottomsheetstylewrapper</item>
    </style>
    <style name="app_bottomsheetstylewrapper" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>

    <style name="animation_message_notication_anim_style">
        <item name="android:windowEnterAnimation">@anim/anim_popshow_anim_jade</item>
        <!-- 指定显示的动画xml -->

        <item name="android:windowExitAnimation">@anim/anim_pophide_anim_emerald</item>
        <!-- 指定消失的动画xml -->
    </style>
    <style name="custom_roundedcorner8dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>

    <style name="custom_roundedcorner10dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>

    <style name="app_dialogtheme" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>
    <style name="dialog_fullscreendialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

</resources>