<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="app_no_action" parent="Theme.Design.Light.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/material_00ee</item>
        <item name="colorPrimaryVariant">@color/material_00b3</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/material_dac5</item>
        <item name="colorSecondaryVariant">@color/material_8786</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <!-- 禁用暗夜模式 -->
        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
    </style>
    
    <style name="Theme.AppCompat.Empty" parent="app_no_action"/>
    <style name="Theme.AppCompat.Light.NoActionBar" parent="app_no_action">

    </style>
    
    <!--    圆形图片style-->
    <style name="widget_roundimageview">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    
    <style name="Theme.AppCompat.SplashActivityBg" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowSplashScreenBackground" tools:ignore="NewApi">@color/white</item>
        <item name="android:windowBackground">@mipmap/img_bg_platinum</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:navigationBarColor">@color/Transparent</item>
        <item name="backgroundColor">@color/white</item>
    </style>

        <style name="Theme.AppJuicy" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/material_00ee</item>
        <item name="colorPrimaryVariant">@color/material_00b3</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/material_dac5</item>
        <item name="colorSecondaryVariant">@color/material_8786</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>
</resources>
