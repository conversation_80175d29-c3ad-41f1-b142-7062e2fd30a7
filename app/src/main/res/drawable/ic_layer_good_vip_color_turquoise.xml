<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 渐变边框层 -->
    <item>
        <shape android:shape="rectangle">
            <gradient 
                android:type="linear"
                android:startColor="#E6E7CB81"
                android:centerColor="#E6F1E2BA"
                android:endColor="#E6CE9757"
                android:angle="90" />
            <corners android:radius="12dp" />
            <size android:width="0.5dp" />
        </shape>
    </item>

    <!-- 内容层，向内缩进0.5dp以显示边框 -->
    <item
        android:left="0.5dp"
        android:top="0.5dp"
        android:right="0.5dp"
        android:bottom="0.5dp">
        <shape android:shape="rectangle">
            <gradient 
                android:type="linear" 
                android:useLevel="true" 
                android:startColor="#F8F6F7"
                android:endColor="#E6FFE7A9"
                android:angle="90" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</layer-list>