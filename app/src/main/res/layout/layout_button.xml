<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".anchorrank.RankFragment">
    <RelativeLayout
        android:id="@+id/lgUpload"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
           >
            <RelativeLayout
                android:id="@+id/thinShort"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_marginTop="30dp"
                android:layout_height="wrap_content"
                >
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/updateUnfocused"
                    android:layout_width="match_parent"
                    android:layout_height="166dp"
                    android:layout_marginTop="-40dp"
                    android:layout_below="@id/openSmall"
                    android:src="@drawable/ic_rank_top2_bg_maroon"
                    />
                <TextView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:layout_above="@id/openSmall"
                    android:textSize="10sp"
                    android:background="@drawable/bg_pink_cricle_violet"
                    android:textColor="@color/white"
                    android:text="2"
                    />


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/openSmall"
                    android:layout_width="74dp"
                    android:layout_height="74dp"
                    android:layout_centerHorizontal="true"
                    >
                    <com.juicy.common.utils.view.CircleImageView
                        android:id="@+id/previewInside"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_centerInParent="true"
                        android:scaleType="centerCrop"
                        android:layout_margin="6dp"
                        app:layout_constraintTop_toTopOf="@id/headerFilter"
                        app:layout_constraintBottom_toBottomOf="@id/headerFilter"
                        app:layout_constraintStart_toStartOf="@id/headerFilter"
                        app:layout_constraintEnd_toEndOf="@id/headerFilter"
                        />
                    <View
                        android:id="@+id/headerFilter"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/img_rank_2_sage"
                        />

                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/overlayClear"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/openSmall"
                    android:singleLine="true"
                    android:gravity="center"
                    android:textSize="18sp"
                    android:fontFamily="@font/protest_strike_regular"
                    android:layout_marginHorizontal="24dp"
                    android:layout_marginTop="6dp"
                    tools:text="dsadasadsdasdsa"
                    android:textColor="@color/black"
                    />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Top 2"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="26sp"
                    android:layout_marginTop="10dp"
                    android:shadowColor="#FF000000"
                    android:shadowRadius="1.5"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:strokeWidth="2.0"
                    android:strokeColor="#FF000000"
                    android:fontFamily="@font/protest_strike_regular"
                    android:layout_below="@id/overlayClear"
                    android:layout_centerHorizontal="true"/>


            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/primaryChip"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                >
                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="match_parent"
                    android:layout_height="166dp"
                    android:src="@drawable/ic_rank_top1_bg_indigo"
                    android:layout_below="@id/uncheckedCenter"
                    android:layout_marginTop="-50dp"
                    />
                <TextView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:layout_above="@id/uncheckedCenter"
                    android:textSize="10sp"
                    android:background="@drawable/bg_yellow_cricle_cyan"
                    android:textColor="@color/white"
                    android:text="1"
                    />
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/uncheckedCenter"
                    android:layout_width="84dp"
                    android:layout_height="84dp"
                    android:layout_centerHorizontal="true"
                    >
                    <com.juicy.common.utils.view.CircleImageView
                        android:id="@+id/removeAbove"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:layout_margin="10dp"
                        android:layout_centerInParent="true"
                        app:layout_constraintTop_toTopOf="@id/distantSmall"
                        app:layout_constraintBottom_toBottomOf="@id/distantSmall"
                        app:layout_constraintStart_toStartOf="@id/distantSmall"
                        app:layout_constraintEnd_toEndOf="@id/distantSmall"
                        />
                    <View
                        android:id="@+id/distantSmall"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/img_rank_1_violet"
                        />

                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/resetChoose"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/uncheckedCenter"
                    android:layout_marginTop="6dp"
                    android:gravity="center"
                    android:singleLine="true"
                    android:fontFamily="@font/protest_strike_regular"
                    android:textSize="18sp"
                    android:layout_marginHorizontal="24dp"
                    android:layout_centerHorizontal="true"
                    tools:text="dsadasadsdasdsadsa"
                    android:textColor="@color/black"
                    />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Top 1"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="26sp"
                    android:layout_marginTop="10dp"
                    android:shadowColor="#FF000000"
                    android:shadowRadius="1.5"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:strokeWidth="2.0"
                    android:strokeColor="#FF000000"
                    android:fontFamily="@font/protest_strike_regular"
                    android:layout_below="@id/resetChoose"
                    android:layout_centerHorizontal="true"/>

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/selectActive"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_marginTop="30dp"
                android:layout_height="wrap_content"
                >
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/briefHeader"
                    android:layout_width="match_parent"
                    android:layout_height="166dp"
                    android:layout_marginTop="-40dp"
                    android:layout_below="@id/disconnectedUpload"
                    android:src="@drawable/ic_rank_top3_bg_beige"
                    />
                <TextView
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:layout_centerHorizontal="true"
                    android:gravity="center"
                    android:layout_above="@id/disconnectedUpload"
                    android:textSize="10sp"
                    android:background="@drawable/bg_blue_cricle_snow"
                    android:textColor="@color/white"
                    android:text="3"
                    />
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/disconnectedUpload"
                    android:layout_width="74dp"
                    android:layout_height="74dp"
                    android:layout_centerHorizontal="true"
                    >
                    <com.juicy.common.utils.view.CircleImageView
                        android:id="@+id/aroundLeft"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:layout_margin="6dp"
                        android:layout_centerInParent="true"
                        app:layout_constraintTop_toTopOf="@id/successEmpty"
                        app:layout_constraintBottom_toBottomOf="@id/successEmpty"
                        app:layout_constraintStart_toStartOf="@id/successEmpty"
                        app:layout_constraintEnd_toEndOf="@id/successEmpty"
                        />
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/successEmpty"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/img_rank_3_cherry"
                        />

                </androidx.constraintlayout.widget.ConstraintLayout>
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/connectedForward"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/disconnectedUpload"
                    android:singleLine="true"
                    android:gravity="center"
                    android:textSize="18sp"
                    android:layout_marginTop="6dp"
                    android:fontFamily="@font/protest_strike_regular"
                    android:layout_marginHorizontal="24dp"
                    tools:text="dsadasadsdasdsa"
                    android:textColor="@color/black"
                    />
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Top 3"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="26sp"
                    android:layout_marginTop="10dp"
                    android:shadowColor="#FF000000"
                    android:shadowRadius="1.5"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:strokeWidth="2.0"
                    android:strokeColor="#FF000000"
                    android:fontFamily="@font/protest_strike_regular"
                    android:layout_below="@id/connectedForward"
                    android:layout_centerHorizontal="true"/>
            </RelativeLayout>
        </LinearLayout>

    </RelativeLayout>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/toggleUnfocused"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
         android:layout_below="@id/lgUpload"
        android:layout_above="@id/focusedSuccess"
        android:layout_marginHorizontal="16dp"
        android:background="@drawable/bg_rank_rc_purple"
        />
      <RelativeLayout
          android:id="@+id/focusedSuccess"
          android:layout_width="match_parent"
          android:layout_height="72dp"
          android:background="@drawable/ic_rank_user_bottom_amber"
          android:layout_alignParentBottom="true"
          android:elevation="50dp"
          >
         <TextView
             android:id="@+id/swipeToggle"
             android:layout_width="wrap_content"
             android:layout_height="wrap_content"
             android:textSize="30sp"
             android:textStyle="bold"
             android:layoutDirection="ltr"
             android:fontFamily="@font/protest_strike_regular"
             android:layout_marginStart="33dp"
             android:layout_centerVertical="true"
             android:textColor="@color/black"
             />
          <com.juicy.common.utils.view.CircleImageView
             android:id="@+id/btnStretch"
              android:layout_width="48dp"
              android:layout_height="48dp"
              android:layout_centerVertical="true"
              android:scaleType="centerCrop"
              android:layout_toEndOf="@id/swipeToggle"
              android:layout_marginStart="12dp"
              />
          <TextView

              android:layout_width="200dp"
              android:layout_height="wrap_content"
              android:id="@+id/xxlXl"
              android:textColor="@color/black"
              android:textStyle="bold"
              android:textSize="18sp"
              android:gravity="start"
              android:fontFamily="@font/protest_strike_regular"
              android:singleLine="true"
              android:layout_centerVertical="true"
              android:layout_toEndOf="@id/btnStretch"
              android:layout_marginStart="12dp"
         />

      </RelativeLayout>


</RelativeLayout>