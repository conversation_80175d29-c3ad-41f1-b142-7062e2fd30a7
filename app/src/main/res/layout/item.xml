<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
<com.google.android.exoplayer2.ui.StyledPlayerView
    android:id="@+id/switchOpen"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:use_controller="true"
    android:clickable="true"
    app:show_buffering="always"
    />

    <RelativeLayout
        android:id="@+id/collapseFlexible"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >
        <ImageView
            android:id="@+id/unselectedFlexible"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            />
        <ProgressBar
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            />
    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>