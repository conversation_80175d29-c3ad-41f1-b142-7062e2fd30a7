<?xml version="1.0" encoding="utf-8"?>
<io.rong.imkit.widget.RoundCornerLinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    app:circularflow_defaultRadius="16dp"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/timerWithin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <com.juicy.common.utils.RoundImageView
                android:id="@+id/aroundCut"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/img_empty_big_full_pearl"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:rouond_corner_radius="8dp"
                app:type="round" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/switchClose"
                android:layout_width="70dp"
                android:layout_height="50dp"
                android:visibility="gone"

                android:background="@color/transparent"
                app:layout_constraintRight_toRightOf="@+id/aroundCut"
                app:layout_constraintTop_toTopOf="@id/aroundCut"
                tools:visibility="visible" />
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@drawable/bg_wall_bg_frost"
                app:layout_constraintTop_toTopOf="@id/disconnectedMain"
                app:layout_constraintBottom_toBottomOf="parent"
                />
            <LinearLayout
                android:id="@+id/disconnectedMain"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="@id/aroundCut"
                app:layout_constraintEnd_toEndOf="@id/aroundCut"
                app:layout_constraintBottom_toBottomOf="parent"
                >

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_weight="1"
                    android:layout_marginBottom="13dp"
                    >
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="10dp"
                        >
                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/coordinatorFlexible"
                            android:layout_width="8dp"
                            android:layout_height="8dp"
                            android:layout_marginStart="8dp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="@id/stretchRight"
                            android:layout_marginTop="3dp"
                            app:layout_constraintBottom_toBottomOf="@id/stretchRight"
                            android:src="@drawable/bg_online_frost"
                            />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/stretchRight"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:textColor="@color/white"
                            android:textSize="15sp"
                            tools:text="BBBBB"
                            android:textStyle="normal"
                            android:layout_gravity="center_vertical"
                            app:layout_constraintStart_toEndOf="@id/coordinatorFlexible"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:fontFamily="@font/protest_strike_regular"
                            android:maxLines="1"
                            android:ellipsize="end"
                            android:singleLine="true"
                            />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="4dp"
                        >
                        <LinearLayout
                            android:id="@+id/xsMedium"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingVertical="2dp"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="4dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="8dp"
                            >
                            <TextView
                                android:id="@+id/autoTimer"
                                android:layout_width="26dp"
                                android:layout_height="16dp"
                                android:textColor="@color/white"
                                android:textSize="12sp"
                                tools:text="22"
                                android:textStyle="bold"
                                android:layout_gravity="center"
                                android:gravity="center"
                                />
                        </LinearLayout>
                        <LinearLayout
                            android:id="@+id/downFull"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingVertical="2dp"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="4dp"
                            android:background="@drawable/bg_wall_location_cherry"
                            android:layout_marginStart="4dp"
                            android:layout_gravity="center_vertical"
                            >
                            <!--<ImageView
                                android:layout_width="12dp"
                                android:layout_height="12dp"
                                android:src="@drawable/ic_location_snow"
                                />-->
                            <TextView
                                android:id="@+id/playGrid"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="#D9D9D9"
                                android:maxLines="1"
                                android:textStyle="bold"
                                android:ellipsize="end"
                                android:textSize="12sp"
                                />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/fullAround"
                    android:layout_width="42dp"
                    android:layout_height="42dp"
                    android:scaleType="fitCenter"
                    android:layout_marginEnd="8dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:layout_gravity="center_vertical"
                    />

            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

</io.rong.imkit.widget.RoundCornerLinearLayout>