<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_main_header_cyan"
    tools:context="com.juicy.my.AppInfoActivity">
    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="190dp"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/primarySync"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="48dp">

        <LinearLayout
            android:id="@+id/underlayShort"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/startEnabled"
                android:background="@drawable/ic_rank_back_mint"
                android:layout_marginStart="16dp"
                android:layout_width="24dp"
                android:layout_height="24dp"/>
        </LinearLayout>

        <TextView
            android:id="@+id/switchSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:includeFontPadding="false"
            android:text="My Level"
            android:fontFamily="@font/protest_strike_regular"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <com.juicy.common.utils.view.CircleImageView
        android:id="@+id/clearLoaded"
        android:layout_width="80dp"
        android:layout_height="80dp"
        tools:src="@drawable/ic_coi_icon_crimson"
        app:layout_constraintTop_toBottomOf="@id/primarySync"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/enabledFill"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="Lv2"
        android:textColor="@color/white"
        android:paddingHorizontal="10dp"
        android:textStyle="bold"
        android:paddingVertical="2dp"
        android:layout_marginBottom="-6dp"
        android:textSize="12sp"
        android:background="@drawable/bg_level_bg_mint"
        app:layout_constraintBottom_toBottomOf="@id/clearLoaded"
        app:layout_constraintStart_toStartOf="@id/clearLoaded"
        app:layout_constraintEnd_toEndOf="@id/clearLoaded"/>

    <LinearLayout
        android:id="@+id/beyondMicro"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:orientation="horizontal"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="22dp"
        android:layout_marginRight="22dp"
        app:layout_constraintTop_toBottomOf="@id/clearLoaded"
        >

        <LinearLayout
            android:id="@+id/overlayWrap"
            android:layout_width="0dp"
            android:layout_height="10dp"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:padding="1dp"
            android:layout_weight="1"
            android:layout_gravity="center_vertical"
            android:orientation="horizontal"
            android:background="@drawable/bg_level_progress_all_black"
            >
            <View
                android:id="@+id/counterCollapse"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@drawable/bg_level_progress_done_turquoise"
                android:layout_weight="0.99"
                android:layout_gravity="center"
                />
            <View
                android:id="@+id/dynamicInactive"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="0.01"
                android:layout_gravity="center"
                />

        </LinearLayout>


    </LinearLayout>

    <TextView
        android:id="@+id/switchReset"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Lv0"
        android:layout_gravity="center_vertical"
        android:textColor="#D9000000"
        android:textSize="13sp"
        android:textStyle="normal"
        android:layout_marginStart="4dp"
        app:layout_constraintTop_toBottomOf="@id/beyondMicro"
        app:layout_constraintStart_toStartOf="@id/beyondMicro"
        />

    <TextView
        android:id="@+id/contentHeader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Lv1"
        android:layout_gravity="center"
        android:textColor="#D9000000"
        android:textSize="13sp"
        android:textStyle="normal"
        app:layout_constraintTop_toBottomOf="@id/beyondMicro"
        app:layout_constraintEnd_toEndOf="@id/beyondMicro"
        />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/switchReset"
        android:orientation="vertical"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="148dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginTop="12dp"
            android:orientation="vertical"
            android:visibility="gone"
            android:background="@drawable/ic_level_top_bg_beige"
            >

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_level_top_icon_indigo"
                android:layout_gravity="center"
                android:layout_marginTop="14dp"
                />


            <TextView
                android:id="@+id/gridToggle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Top UP 1700 coins to level up."
                android:layout_marginTop="8dp"
                android:layout_gravity="center"
                android:textColor="#FFFFFF"
                android:textSize="10sp"
                android:textFontWeight="@integer/material_motion_duration_long_2"
                />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="12dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_weight="1"
            android:orientation="vertical"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="38dp"
                android:background="@drawable/bg_level_item_top_platinum"
                >
                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    >

                    <TextView
                        android:id="@+id/footerReleased"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="Level"
                        android:textColor="#D9000000"
                        android:textStyle="bold"
                        android:textSize="15sp" />


                </RelativeLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/black"/>

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    >

                    <TextView
                        android:id="@+id/redoMaximal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="Top-up Amout"
                        android:gravity="center"
                        android:textColor="#D9000000"
                        android:textStyle="bold"
                        android:textSize="15sp" />

                </RelativeLayout>
            </LinearLayout>

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    >
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/hiddenRight"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"

                        />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="38dp"
                        android:visibility="gone"
                        android:background="@drawable/bg_level_item_bottom_navy"
                        />
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>


        </LinearLayout>


        <RelativeLayout
            android:id="@+id/xxlDynamic"
            android:layout_width="match_parent"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/bg_submit_navy"
            android:orientation="horizontal"

            android:layout_height="44dp">

            <TextView
                android:id="@+id/offlineAvatar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Top Up Now"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:textSize="17sp" />
        </RelativeLayout>

    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>