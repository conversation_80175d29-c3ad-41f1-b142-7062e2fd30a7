<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:fitsSystemWindows="true"
    tools:context=".anchorvideo.AnchorVideoActivity">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/wrapTab"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
    
    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@drawable/bg_header_blur_pink"
        app:layout_constraintTop_toTopOf="parent"/>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        >

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="20dp"
            >
            <LinearLayout
                android:id="@+id/xsMedium"
                android:layout_width="wrap_content"
                android:layout_height="55dp"
                android:layout_marginStart="5dp"
                android:background="@drawable/bg_user_bg_n_smoke"
                android:orientation="horizontal"
                >

                <RelativeLayout
                    android:id="@+id/hugeSelected"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="8dp"
                    >
                    <com.juicy.common.utils.view.CircleImageView
                        android:id="@+id/fluidForeground"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        />
                    <RelativeLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignBottom="@+id/fluidForeground"
                        android:layout_alignEnd="@id/fluidForeground">
                        <View
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:layout_centerInParent="true"
                            android:background="@drawable/bg_point_white_slate"
                            />

                        <View
                            android:id="@+id/copyLeft"
                            android:layout_width="9dp"
                            android:layout_height="9dp"
                            android:layout_centerInParent="true" />

                    </RelativeLayout>


                </RelativeLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_weight="1"
                    android:layout_marginStart="5dp"
                    android:layout_gravity="center_vertical"
                    >

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/avatarLarge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:maxLength="12"
                        android:singleLine="true"
                        android:textSize="18sp"
                        tools:text="dasdasddsadasdsdaasdadasdasdasdasdasdasdasda"
                        android:includeFontPadding="false"
                        android:textFontWeight="500"
                        android:fontFamily="@font/protest_strike_regular"
                        />
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="4dp"
                        >
                        <TextView
                            android:id="@+id/autoTimer"
                            android:layout_width="32dp"
                            android:layout_height="20dp"
                            android:textColor="@color/white"
                            android:gravity="center"
                            android:textSize="12sp"
                            android:background="@drawable/bg_famel_olive"
                            tools:text="18"
                            android:textStyle="bold"
                            />
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/maximalBtn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:textColor="@color/white"
                            android:layout_marginStart="6dp"
                            android:textSize="12sp"
                            android:paddingHorizontal="4dp"
                            android:paddingVertical="3dp"
                            android:textStyle="normal"
                            android:background="@drawable/bg_wall_location_cherry"
                            android:maxLines="1"
                            tools:text="USA" />
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/lockedRemove"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:textColor="@color/white"
                            android:includeFontPadding="false"
                            tools:text="dasdasddasdasdasda"
                            android:visibility="gone"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:textFontWeight="500"
                            />


                    </LinearLayout>

                </LinearLayout>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/frameSort"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="10dp"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/ic_like_gray_mint" />

            </LinearLayout>
        </LinearLayout>

        <RelativeLayout
            android:id="@+id/sidebarBadge"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:padding="4dp"
            android:background="@drawable/bg_gray_black_mint"

            android:layout_gravity="center_vertical"
            >
            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_report_cherry"
                />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/extendedUpper"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="16dp"
            android:padding="4dp"
            android:layout_marginStart="16dp"
            android:layout_gravity="center_vertical"
            >
            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_back_close_ebony"
                />

        </RelativeLayout>



    </LinearLayout>



    
    
    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="20dp"
        app:layout_constraintBottom_toTopOf="@id/emptyRight"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        >
        <LinearLayout
            android:id="@+id/chipStart"
            android:layout_width="0dp"
            android:layout_weight="0.4"
            android:background="@mipmap/ic_anchor_info_chat_bg_lead"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/removeExtended"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="16dp"
                android:src="@mipmap/ic_anchor_info_chat_icon_charcoal"
                android:layout_gravity="center_vertical"
                />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/surfaceAcross"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Chat"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_gravity="center_vertical"
                android:fontFamily="@font/protest_strike_regular"
                app:layout_constraintTop_toTopOf="@id/playShort"
                app:layout_constraintBottom_toBottomOf="@id/playShort"
                app:layout_constraintStart_toEndOf="@id/playShort"
                android:layout_marginStart="10dp"/>

        </LinearLayout>


        <LinearLayout
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="0dp"
            android:layout_weight="0.6"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center_vertical"
            >
            <LinearLayout
                android:id="@+id/errorMaximal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:height="38dp"
                android:gravity="center_vertical"
                android:background="@drawable/ic_anchor_vip_bg_steel"
                android:textDirection="ltr"
                android:layoutDirection="ltr"
                android:visibility="gone"
                >
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="25dp"
                    android:text="VIP: "
                    android:textColor="@color/custom_4200"
                    android:textFontWeight="@integer/m3_sys_motion_duration_long2"
                    android:textDirection="ltr"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_coin_mist" />
                <TextView
                    android:id="@+id/autoBody"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    android:layout_gravity="center"
                    android:text="100/min"
                    android:textDirection="ltr"
                    android:textColor="@color/custom_4200"
                    android:textFontWeight="@integer/m3_sys_motion_duration_long2"
                    android:textSize="12sp" />

            </LinearLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/maximalHeader"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@mipmap/ic_anchor_info_call_online_ash"
                >

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/textToggle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:layoutDirection="ltr"
                    android:textDirection="ltr"
                    >

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/badgeEnd"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@mipmap/ic_anchor_info_online_icon_lemon" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/dragDrop"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:visibility="gone"
                        android:src="@drawable/ic_coin_mist" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/refreshClear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:shadowColor="#FF000000"
                        android:shadowRadius="1.5"
                        android:shadowDx="0"
                        android:textStyle="bold"
                        android:shadowDy="0"
                        android:fontFamily="@font/protest_strike_regular"
                        android:strokeWidth="3.0"
                        android:strokeColor="#FF000000"
                        android:layout_marginStart="10dp"
                        android:textDirection="ltr"
                        android:text="Video Call" />
                </androidx.appcompat.widget.LinearLayoutCompat>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </LinearLayout>
    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/emptyRight"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:gravity="center"
        android:background="@drawable/bg_point_bg_mint"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        />



</androidx.constraintlayout.widget.ConstraintLayout>