<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    tools:ignore="MissingDefaultResource">
<RelativeLayout
    android:id="@+id/thumbnailSwitch"
    android:layout_width="match_parent"
    android:layout_height="58dp"
    android:layout_below="@id/stretchLong"
   >

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/barProgress"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_setting_button2_frost"
         />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/counterReload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="12dp"
        android:background="@color/white"
        android:layout_centerVertical="true"
        tools:text="Name"
        android:visibility="visible"
        android:textSize="18sp"
        tools:visibility="visible"
        android:textColor="@color/black"
        android:includeFontPadding="false"
        android:layout_marginStart="40dp"
        android:fontFamily="@font/protest_strike_regular"
        />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/sortIcon"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:layout_marginStart="10dp"
        android:layout_marginTop="1dp"
        android:layout_marginEnd="1dp"
        android:layout_marginBottom="1dp"
        android:layout_toEndOf="@id/counterReload"
        android:background="@drawable/bg_setting_button_3_mint"
        android:gravity="end|center_vertical"
        android:maxLength="18"
        tools:text="1991 01 01"
        android:drawablePadding="10dp"
        android:paddingHorizontal="16dp"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="14sp" />
    
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/maskLarge"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
          android:src="@drawable/ic_arrow_turn_azure"/>
    <View
        android:id="@+id/uploadThin"
        android:layout_width="20dp"
        android:background="@color/white"
        android:layout_height="20dp"
        android:layout_centerVertical="true"
        android:layout_marginEnd="10dp"
        android:visibility="gone"
        android:layout_alignEnd="@id/sortIcon"
        />
</RelativeLayout>

    <View
        android:id="@+id/stretchLong"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        />



</RelativeLayout>