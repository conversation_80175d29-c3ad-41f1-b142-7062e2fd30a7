<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/Transparent"
    android:orientation="vertical"
    >

    <LinearLayout
        android:id="@+id/selectedLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_load_bg_violet"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingStart="25dp"
        android:paddingTop="10dp"
        android:paddingEnd="25dp"
        android:paddingBottom="10dp">

        <ImageView
            android:id="@+id/fieldTitle"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/ic_loading_copper" />

        <TextView
            android:id="@+id/counterOutside"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="5dp"
            android:text="@string/msg_timeout"
            android:textColor="@android:color/white"
            android:textSize="12sp" />
    </LinearLayout>

</LinearLayout>