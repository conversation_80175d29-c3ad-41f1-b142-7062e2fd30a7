<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    android:fillViewport="true"
    android:background="@drawable/bg_main_header_cyan"
    android:orientation="vertical"
    tools:context="com.juicy.my.EditUserInfoActivity">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:id="@+id/lowerMd"
        >
        <RelativeLayout
            android:id="@+id/downloadSort"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/briefFluid"
                android:layout_width="match_parent"
                android:layout_height="140dp"
                />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/xlScroll"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/toggleEdit"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="16dp"
                    android:src="@drawable/ic_rank_back_mint" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/closedHeader"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/btn_close"
                    android:layout_marginEnd="24dp"
                    android:fontFamily="@font/protest_strike_regular"
                    android:textColor="@color/black"
                    android:textSize="22sp"
                    android:textStyle="bold" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <RelativeLayout
                android:id="@+id/hugeSelected"
                android:layout_width="93dp"
                android:layout_height="93dp"
                android:layout_below="@id/briefFluid"
                android:background="@drawable/bg_avatar_bg_lavender"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="-50dp">

                <com.juicy.common.utils.view.CircleImageView
                    android:id="@+id/previewSelect"
                    android:layout_width="91dp"
                    android:layout_height="91dp"
                    android:layout_centerInParent="true"
                    />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_below="@+id/previewSelect"
                    android:layout_marginStart="-20dp"
                    android:layout_marginTop="-30dp"
                    android:layout_toEndOf="@id/previewSelect"
                    android:src="@drawable/img_edit_photo_frost" />
            </RelativeLayout>
            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/upperBack"
                android:layout_below="@id/hugeSelected"
                android:layout_marginTop="24dp"
                android:layout_marginStart="16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                
                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/offlineNear"
                        android:layout_width="100dp"
                        android:layout_height="70dp"
                        android:scaleType="fitCenter"
                        android:src="@drawable/bg_add_photo_blue" />
                    
                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/img_photo_add_charcoal"
                        android:layout_centerInParent="true"/>
                    
                </RelativeLayout>

          
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/staticCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    />
            </androidx.appcompat.widget.LinearLayoutCompat>
            <com.juicy.common.utils.view.EditItem
                android:id="@+id/shadowVisible"
                android:layout_below="@+id/upperBack"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                />
            <com.juicy.common.utils.view.EditItem
                android:id="@+id/previewPlay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/shadowVisible"
                />
            <com.juicy.common.utils.view.EditItem
                android:id="@+id/pagerMicro"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/previewPlay"
                />
            <RelativeLayout
                android:id="@+id/forwardAdd"
                android:layout_below="@id/pagerMicro"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                >
                <RelativeLayout
                    android:id="@+id/thumbnailSwitch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/stretchLong"
                    >

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/longExpanded"
                        android:layout_width="match_parent"
                        android:layout_height="123dp"
                        android:background="@drawable/bg_info_button_brown"
                        />



                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:layout_marginStart="1dp"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="1dp"
                        android:layout_marginBottom="1dp"
                        android:background="@drawable/bg_setting_button_3_mint">

                        <EditText
                            android:id="@+id/cardFlexible"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_margin="16dp"
                            android:background="@color/white"
                            android:hint="Type here…"
                            tools:text="fdsd"
                            android:gravity="start|top"
                            android:minHeight="50dp"
                            android:textSize="14sp" />
                    </RelativeLayout>
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/belowReload"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#AAAAAA"
                        android:textSize="14sp"
                        android:layout_marginBottom="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentBottom="true"
                        tools:text="dasdasda"
                        />
                </RelativeLayout>
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/extendedInner"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="12dp"
                    android:background="@color/white"
                    android:textSize="12sp"
                    android:text="@string/btn_open"
                    android:textColor="#AAAAAA"
                    android:visibility="gone"
                    android:includeFontPadding="false"
                    android:layout_marginStart="40dp"
                    android:layout_above="@id/thumbnailSwitch"
                    android:layout_marginBottom="-10dp"
                    />
                <View
                    android:id="@+id/stretchLong"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    />


            </RelativeLayout>
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/rightBrief"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_below="@id/forwardAdd"
                android:gravity="center"
                android:textSize="18sp"
                android:text="@string/common_ok_25"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:layout_marginTop="25dp"
                android:layout_marginHorizontal="20dp"
                android:background="@drawable/bg_submit_navy"
                />

        </RelativeLayout>
    </androidx.core.widget.NestedScrollView>
    <LinearLayout
        android:id="@+id/cutFlexible"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="horizontal"
        />

</LinearLayout>