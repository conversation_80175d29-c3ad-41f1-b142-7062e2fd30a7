<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="20dp"
        android:background="@drawable/ic_full_coin_dialog_bg_salmon"
        >

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/sliderBar"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_full_close_dialog_lime"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="16dp"/>

        <androidx.cardview.widget.CardView
            android:layout_width="96dp"
            android:layout_height="6dp"
            app:cardCornerRadius="3dp"
            android:layout_centerHorizontal="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"
            android:layout_marginTop="8dp"
            android:background="#ffffff"/>

       <LinearLayout
           android:id="@+id/fullDisabled"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:orientation="horizontal"
           android:layout_marginTop="60dp"
           app:layout_constraintStart_toStartOf="parent"

           app:layout_constraintTop_toTopOf="parent"
           >
           <com.juicy.common.utils.view.CircleImageView
               android:id="@+id/wrapClosed"
               android:layout_width="83dp"
               android:layout_height="83dp"
               android:src="@drawable/bg_avatar_bg_lavender"
                android:layout_marginEnd="10dp"
               android:layout_marginStart="10dp"
               />


           <LinearLayout

               android:layout_width="250dp"
               android:layout_height="79dp"

               android:background="@drawable/ic_shop_girl_pop_white"
               android:orientation="horizontal"

               >


               <LinearLayout
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:orientation="vertical"
                   android:layout_gravity="center_vertical"
                   android:layout_marginStart="34dp"
                   >

                   <androidx.appcompat.widget.AppCompatTextView
                       android:id="@+id/avatarLarge"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:textColor="@color/black"
                       android:textStyle="bold"
                       android:textSize="16sp"
                       android:singleLine="true"
                       app:autoSizeTextType="uniform"
                       android:autoSizeMinTextSize="10sp"
                       tools:text="Bạn có thích không? Name Name"
                       />
                   <androidx.appcompat.widget.AppCompatTextView
                       android:id="@+id/updateConfirm"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:textColor="@color/black"
                       android:textSize="14sp"
                       android:textStyle="bold"
                       android:layout_marginTop="8dp"
                       android:text="@string/nav_back"
                       app:autoSizeTextType="uniform"
                       android:autoSizeMinTextSize="10sp"
                       />

                   <androidx.appcompat.widget.AppCompatTextView
                       android:id="@+id/sortLoading"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:textColor="@color/white"
                       android:textStyle="bold"
                       android:textSize="14sp"
                       android:layout_above="@id/disabledExpand"
                       android:layout_marginTop="8dp"
                       android:text="@string/common_ok_29"
                       android:visibility="gone"
                       />



               </LinearLayout>


           </LinearLayout>

       </LinearLayout>


        <LinearLayout
            android:id="@+id/disabledExpand"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:gravity="center"
            android:paddingTop="16dp"
            android:layoutDirection="ltr"
            android:layout_marginTop="170dp"
            android:layout_below="@id/fullDisabled"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginHorizontal="16dp"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent"
            >

            <com.juicy.common.view.MdBannerView
                android:id="@+id/headerAbove"
                android:layout_width="match_parent"
                android:layout_height="81dp"
                android:layout_marginHorizontal="10dp"
                android:layout_marginBottom="10dp"
                android:visibility="gone"
                />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/xxlUpper"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="8dp"
                android:layout_weight="1"
                />

            <LinearLayout
                android:id="@+id/gridExpand"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:orientation="horizontal"
                >
                <ImageView

                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_profile_custom_bronze"
                    android:layout_gravity="center_vertical"
                    />
                <TextView
                    android:id="@+id/lockedFill"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:text="Customer Service"
                    android:layout_gravity="center_vertical"
                    />

            </LinearLayout>

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>






</androidx.constraintlayout.widget.ConstraintLayout>