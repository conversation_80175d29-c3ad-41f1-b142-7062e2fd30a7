<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".modules.base.activity.BasicWebActivity">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/primarySync"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="48dp">

        <LinearLayout
            android:id="@+id/underlayShort"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/startEnabled"
                android:background="@drawable/ic_rank_back_mint"
                android:layout_marginStart="16dp"
                android:layout_width="24dp"

                android:layout_height="24dp"/>
        </LinearLayout>

        <TextView
            android:id="@+id/switchSelect"
            android:text="@string/common_new"
            android:textSize="17sp"
            android:textColor="@color/black"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/underlayShort"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="16dp"

            android:includeFontPadding="false"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/bg_mine_bg_white_iron"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/primarySync"
        >
        <WebView
            android:id="@+id/nestedDisabled"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.constraintlayout.widget.ConstraintLayout>