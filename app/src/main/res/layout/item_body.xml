<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/containerResponsive"
    android:layout_height="86dp">

    <RelativeLayout
        android:id="@+id/hugeSelected"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:background="@drawable/bg_anchor_avatar_bg_gold"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <com.juicy.common.utils.view.CircleImageView
            android:id="@+id/fluidForeground"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_centerInParent="true"
            />
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@+id/fluidForeground"
            android:layout_alignEnd="@id/fluidForeground">
            <View
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_centerInParent="true"
                android:background="@drawable/bg_point_white_slate"
                />

            <View
                android:id="@+id/copyLeft"
                android:layout_width="9dp"
                android:layout_height="9dp"
                android:layout_centerInParent="true" />

        </RelativeLayout>


    </RelativeLayout>
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/avatarLarge"
        android:layout_width="230dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:singleLine="true"
        android:textColor="#FF222222"
        android:fontFamily="@font/protest_strike_regular"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/hugeSelected"
        app:layout_constraintTop_toTopOf="@id/hugeSelected"

        tools:text="dsadasdasdasdasas" />
    <LinearLayout
        android:id="@+id/footerBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginStart="16dp"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toEndOf="@id/hugeSelected"
        app:layout_constraintTop_toBottomOf="@+id/avatarLarge"

        >
        <LinearLayout
            android:id="@+id/xsMedium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingVertical="2dp"
            android:gravity="center_vertical"
            android:paddingHorizontal="4dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            >
            <ImageView
                android:id="@+id/brightThin"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:visibility="gone"
                />
            <TextView
                android:id="@+id/autoTimer"
                android:layout_width="26dp"
                android:layout_height="20dp"
                android:textColor="@color/white"
                android:layout_gravity="center"
                android:gravity="center"
                android:background="@drawable/bg_famel_olive"
                android:textSize="12sp"
                tools:text="18"
                />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/downFull"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingVertical="2dp"
            android:gravity="center_vertical"
            android:paddingHorizontal="4dp"
            android:background="@drawable/bg_wall_location_cherry"
            android:layout_marginStart="4dp"
            android:layout_gravity="center_vertical"
            >
            <!--<ImageView
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:src="@drawable/ic_location_snow"
                />-->
            <TextView
                android:id="@+id/playGrid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#D9D9D9"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="12sp"
                android:text="USA"
                />
        </LinearLayout>
    </LinearLayout>


  <androidx.appcompat.widget.LinearLayoutCompat
      android:id="@+id/disabledExpand"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      android:gravity="center_vertical"
      app:layout_constraintBottom_toBottomOf="parent"
      android:layout_marginEnd="16dp"
      >




      
      
      
  </androidx.appcompat.widget.LinearLayoutCompat>
    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/footerBack"
        app:layout_constraintEnd_toEndOf="@id/disabledExpand"
        android:background="#E8EBEB"
        />

</androidx.constraintlayout.widget.ConstraintLayout>