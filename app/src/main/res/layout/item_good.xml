<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/innerGrid"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="168dp"
    xmlns:tools="http://schemas.android.com/tools"
    tools:background="@drawable/bg_good_bg_yellow">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/cancelChip"
        android:layout_width="80dp"
        android:maxWidth="120dp"
        android:layout_height="28dp"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:paddingHorizontal="8dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:shadowColor="#FF000000"
        android:layout_marginTop="8dp"
        android:shadowRadius="1.5"
        android:shadowDx="0"
        android:shadowDy="0"
        android:strokeWidth="2.0"
        android:strokeColor="#FF000000"
        android:fontFamily="@font/protest_strike_regular"
        android:gravity="center_horizontal"
        android:layout_marginStart="6dp"
        tools:text="11:23:12"
        tools:background="@drawable/ic_my_coins_icon_snow"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/contentAuto"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="26dp"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        >

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/bodyCard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:textColor="@color/black"
                android:textSize="22sp"
                android:textStyle="bold"
                tools:text="15000"
                />
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/offlineRigid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2.5dp"
                android:includeFontPadding="false"
                android:textColor="@color/black"
                android:layout_marginTop="2dp"
                android:textFontWeight="500"
                android:textSize="8sp"
                tools:text="111"
                />

            <ImageView
                android:id="@+id/clearError"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_coi_icon_crimson"
                />


        </androidx.appcompat.widget.LinearLayoutCompat>
        <TextView
            android:id="@+id/upSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="13sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_gravity="center_horizontal"
            android:text="+"
            />

    </androidx.appcompat.widget.LinearLayoutCompat>


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/mediumLast"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="10dp"
        android:src="@drawable/ic_coi_icon_crimson"
        app:layout_constraintTop_toBottomOf="@id/contentAuto"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/progressDimmed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layoutDirection="ltr"
        app:layout_constraintTop_toBottomOf="@id/contentAuto"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/nearForeground"
            android:layout_width="59dp"
            android:layout_height="26dp"
            android:text="VIP"
            android:textColor="@color/white"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_marginTop="3dp"
            android:textSize="18sp"
            android:background="@drawable/bg_vip_pop_solid_bg_violet"
            app:layout_constraintTop_toTopOf="@id/addScroll"
            app:layout_constraintStart_toStartOf="@id/addScroll"
            app:layout_constraintBottom_toBottomOf="@id/addScroll"
            android:layout_marginStart="18dp"/>

        <ImageView
            android:id="@+id/addScroll"
            android:layout_width="36dp"
            android:layout_height="36dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="10dp"
            android:src="@drawable/ic_vip_pop_king_icon_platinum"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>




    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/selectedExpanded"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FFFF8441"
        android:textSize="12sp"
        android:includeFontPadding="false"
        app:layout_constraintTop_toBottomOf="@id/mediumLast"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="2dp"
        tools:text="dsadasds"
        tools:visibility="visible"
        />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/maskMicro"
        android:layout_width="90dp"
        android:layout_height="25dp"
        android:layout_marginTop="2dp"
        android:layoutDirection="ltr"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:background="@drawable/bg_buy_bg_rose"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:paddingVertical="2dp"
        android:paddingHorizontal="12dp"
        android:textSize="12sp"
        tools:text="$49.99"
        android:textStyle="bold"
        android:textColor="@color/black"
        />
</androidx.constraintlayout.widget.ConstraintLayout>