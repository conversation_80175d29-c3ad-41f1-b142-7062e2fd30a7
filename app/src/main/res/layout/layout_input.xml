<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="112dp"
    android:layout_height="169dp"
    android:background="@drawable/bg_seft_bg_olive"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
   <FrameLayout
       android:id="@+id/errorUnlocked"
       android:layout_width="match_parent"
       android:layout_height="match_parent"
      />
   <androidx.appcompat.widget.AppCompatImageView
       android:id="@+id/distantLast"
       android:layout_width="24dp"
       android:layout_height="24dp"
       android:src="@drawable/ic_camera_lavender"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
       app:layout_constraintStart_toStartOf="parent"
      />
   <View
       android:id="@+id/nestedTab"
       android:layout_width="5dp"
      android:background="@drawable/bg_red_point_indigo"
       android:layout_height="5dp"
      app:layout_constraintTop_toTopOf="@id/distantLast"
      app:layout_constraintEnd_toEndOf="@id/distantLast"
      android:layout_marginEnd="2dp"
      android:layout_marginTop="3dp"
      />
    

</androidx.constraintlayout.widget.ConstraintLayout>