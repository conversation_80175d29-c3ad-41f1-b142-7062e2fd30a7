<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="99dp"
    android:layout_height="131dp"
    android:layout_marginEnd="12dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource">
  <com.juicy.common.utils.RoundImageView
      android:id="@+id/backgroundHidden"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      app:type="round"
      app:rouond_corner_radius="12dp"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      />
    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/raw_video_default_snow"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>