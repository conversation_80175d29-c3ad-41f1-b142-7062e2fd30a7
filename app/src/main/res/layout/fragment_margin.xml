<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/closedLeft"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_main_header_cyan"
    tools:context="com.juicy.my.MineFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <RelativeLayout
                android:id="@+id/compactUpload"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                android:layout_marginTop="36dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    android:visibility="gone"
                    android:layout_alignBottom="@+id/adaptivePanel"
                    android:src="@drawable/bg_mine_bg_white_iron" />
                <RelativeLayout
                    android:id="@+id/dragCounter"
                    android:layout_width="82dp"
                    android:layout_height="82dp"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/bg_avatar_bg_lavender">
                    <com.juicy.common.utils.view.CircleImageView
                        android:id="@+id/farBottom"
                        android:layout_width="74dp"
                        android:layout_height="74dp"
                        android:scaleType="centerCrop"
                        android:layout_centerInParent="true"
                      />
                </RelativeLayout>



                <ImageView
                    android:id="@+id/inputUnlocked"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="40dp"
                    android:layout_marginTop="0dp"
                    android:rotation="20"
                    android:src="@drawable/ic_person_vip_icon_turquoise"
                    tools:visibility="visible"
                    android:visibility="gone" />


                <LinearLayout
                    android:id="@+id/adaptivePanel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toEndOf="@id/dragCounter"
                    android:layout_centerVertical="true"
                    android:gravity="center_horizontal"
                    android:layout_marginStart="10dp"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/copyDown"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:layout_gravity="start"
                        android:fontFamily="@font/protest_strike_regular"
                        android:textStyle="bold"
                        tools:text="dsadasd" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:id="@+id/miniSelect"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingHorizontal="4dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/brightThin"
                                android:layout_width="16dp"
                                android:visibility="gone"
                                android:layout_height="16dp"
                                tools:src="@drawable/ic_gender_boy_pink" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/autoTimer"
                                android:layout_width="26dp"
                                android:layout_height="20dp"
                                android:background="@drawable/bg_famel_olive"
                                android:textColor="@color/white"
                                android:textSize="12sp"
                                android:gravity="center"
                                tools:text="22" />


                        </LinearLayout>


                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:visibility="gone"
                            android:src="@drawable/ic_local_white" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/maximalBtn"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/white"
                            android:background="@drawable/bg_wall_location_cherry"
                            android:paddingVertical="2dp"
                            android:paddingHorizontal="4dp"
                            android:textSize="12sp"
                            tools:text="USA" />


                    </LinearLayout>



                </LinearLayout>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/edit_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="20dp"
                    android:background="@drawable/ic_profile_edit_icon_lavender"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="4dp">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_edit_frost" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/thickFlexible"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:includeFontPadding="false"
                        tools:text="edit_data"
                        android:fontFamily="@font/protest_strike_regular"
                        android:textColor="@color/black"
                        android:textSize="16sp" />

                </androidx.appcompat.widget.LinearLayoutCompat>

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/cardPressed"
                android:layout_width="match_parent"
                android:layout_height="77dp"
                android:layout_marginTop="23dp"
                android:layout_marginHorizontal="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:background="@drawable/bg_coin_item_purple"
                app:layout_constraintTop_toBottomOf="@+id/compactUpload"
                >

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentBottom="true"
                    android:layout_marginBottom="15dp"
                    />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingLeft="24dp"
                    android:paddingRight="24dp">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="46dp"
                        android:layout_height="46dp"
                        android:src="@drawable/ic_coi_icon_crimson"
                        android:layout_gravity="center_vertical"/>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/downloadAround"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="#FF656D7B"
                            android:layout_marginStart="12dp"
                            android:text="My coins"
                            android:textSize="12sp"/>



                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/unselectedNext"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="12dp"
                            android:textColor="@color/black"
                            android:layout_gravity="center_vertical"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            tools:text="1000" />

                    </LinearLayout>

                    <View
                        android:layout_width="wrap_content"
                        android:layout_height="1dp"
                        android:layout_weight="1" />


                </LinearLayout>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/surfaceOnline"
                    android:layout_width="7dp"
                    android:layout_height="14dp"
                    android:src="@drawable/ic_vip_me_right_back_arrow_khaki"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"/>

            </RelativeLayout>



            <RelativeLayout
                android:id="@+id/deleteDrag"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginHorizontal="14dp"
                app:layout_constraintTop_toBottomOf="@id/cardPressed"
                >


                <ImageView
                    android:id="@+id/errorNested"
                    android:layout_width="match_parent"
                    android:layout_height="77dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/bg_vip_item_bg_olive" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="14dp"
                        android:src="@drawable/ic_vip_me_king_icon_platinum"/>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_gravity="center_vertical"
                        android:orientation="vertical">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/autoAuto"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Unavailable VIP"
                            android:textColor="#FF656D7B"
                            android:textSize="12sp"/>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/narrowBeyond"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="VIP + 1200"

                                android:layout_gravity="center_vertical"
                                android:textColor="@color/black"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/pickerEdit"
                                android:layout_width="14dp"
                                android:layout_height="14dp"
                                android:src="@drawable/ic_coi_icon_crimson"
                                android:layout_gravity="center_vertical"
                                android:layout_marginStart="2dp"/>

                        </LinearLayout>

                    </LinearLayout>



                    <View
                        android:layout_width="wrap_content"
                        android:layout_height="1dp"
                        android:layout_weight="1" />

                </LinearLayout>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/longAcross"
                    android:layout_width="7dp"
                    android:layout_height="14dp"
                    android:src="@drawable/ic_vip_me_right_back_arrow_khaki"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="16dp"/>
            </RelativeLayout>

            <com.juicy.common.view.MdBannerView
                android:id="@+id/headerAbove"
                android:layout_width="match_parent"
                android:layout_marginHorizontal="14dp"
                android:layout_height="81dp"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_marginTop="8dp"
                app:layout_constraintTop_toBottomOf="@id/deleteDrag"
                />


            <LinearLayout
                android:id="@+id/playTop"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/bg_mine_item_mint"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/headerAbove">


                <com.juicy.common.utils.view.MineItem
                    android:id="@+id/hiddenSlider"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <com.juicy.common.utils.view.MineItem
                    android:id="@+id/hugeLeft"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <com.juicy.common.utils.view.MineItem
                    android:id="@+id/collapsedUp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <com.juicy.common.utils.view.MineItem
                    android:id="@+id/listMiddle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
                <com.juicy.common.utils.view.MineItem
                    android:id="@+id/outsideOffline"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/closeExpand"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/common_last"
                android:fontFamily="@font/protest_strike_regular"
                app:layout_constraintTop_toTopOf="@id/topBright"
                app:layout_constraintStart_toStartOf="@id/topBright"
                app:layout_constraintEnd_toEndOf="@id/topBright"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:visibility="gone"
                android:textStyle="bold" />


            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/topBright"
                android:layout_width="94dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_my_coins_icon_snow"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="-10dp"
                android:scaleType="centerCrop"
                app:layout_constraintEnd_toEndOf="@id/deleteDrag"
                app:layout_constraintTop_toTopOf="@id/deleteDrag"/>

            <TextView
                android:id="@+id/barAcross"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="$9.99/month"
                android:layout_marginTop="2dp"
                android:fontFamily="@font/protest_strike_regular"
                app:layout_constraintTop_toTopOf="@id/topBright"
                app:layout_constraintStart_toStartOf="@id/topBright"
                app:layout_constraintEnd_toEndOf="@id/topBright"
                android:textDirection="ltr"
                android:textColor="@color/black"
                android:textSize="12sp" />



        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>
</LinearLayout>

