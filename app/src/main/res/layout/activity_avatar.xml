<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_main_header_cyan"
    tools:context="com.juicy.my.LocaleActivity">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:scaleType="centerCrop"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/connectedUnlocked"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/cancelMedium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/bodyLg"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="16dp"

                android:src="@drawable/ic_rank_back_mint" />
        </LinearLayout>

        <TextView
            android:id="@+id/wideThick"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:includeFontPadding="false"
            android:text="Language"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:fontFamily="@font/protest_strike_regular"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/upperInner"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        android:layout_marginTop="50dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/connectedUnlocked">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/clearTiny"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>