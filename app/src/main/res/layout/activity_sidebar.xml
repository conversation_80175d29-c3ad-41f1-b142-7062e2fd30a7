<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/ic_vip_full_bg_charcoal"
    tools:context=".modules.base.activity.PremiumActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/primarySync"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="48dp">

        <LinearLayout
            android:id="@+id/underlayShort"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/startEnabled"
                android:background="@drawable/ic_rank_back_mint"
                android:layout_marginStart="16dp"
                android:layout_width="34dp"
                android:layout_height="34dp"/>
        </LinearLayout>

        <TextView
            android:id="@+id/switchSelect"
            android:text="VIP"
            android:textSize="22sp"
            android:textColor="@color/black"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="16dp"
            android:fontFamily="@font/protest_strike_regular"
            android:includeFontPadding="false"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:layout_marginTop="-120dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clearMain"
        >

        <LinearLayout
            android:id="@+id/editDimmed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:orientation="horizontal"
            />
        <TextView
            android:id="@+id/closePicker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:visibility="gone"
            android:gravity="center"
            android:minHeight="60dp"
            android:paddingHorizontal="10dp"
            android:text="Gift of 1200 coins"
            android:textColor="#1A1A1A"
            android:textStyle="bold"
            android:textSize="20sp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/mdAdaptive"
            android:layout_width="match_parent"
            android:layout_height="230dp"
            android:layout_gravity="center"
            android:layout_marginHorizontal="10dp"
            android:layout_marginTop="18dp"
            android:visibility="visible"
            android:background="@drawable/bg_vip_banner_bg_salmon"
            >

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/backgroundNext"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_coi_icon_crimson"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginTop="30dp"
                android:layout_marginStart="26dp"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/bodyCard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/black"
                tools:text="1200 coins per month."
                android:textStyle="bold"
                android:layout_marginStart="14dp"
                app:layout_constraintStart_toEndOf="@id/backgroundNext"
                app:layout_constraintTop_toTopOf="@id/backgroundNext"
                app:layout_constraintBottom_toBottomOf="@id/backgroundNext"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/hugeLg"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_vip_pop_message_icon_frost"
                app:layout_constraintStart_toStartOf="@id/backgroundNext"
                app:layout_constraintTop_toBottomOf="@id/backgroundNext"
                android:layout_marginTop="16dp"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/hiddenOpen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/black"
                tools:text="Chat anytime with anyone"
                android:textStyle="bold"
                android:layout_marginStart="14dp"
                app:layout_constraintStart_toEndOf="@id/hugeLg"
                app:layout_constraintTop_toTopOf="@id/hugeLg"
                app:layout_constraintBottom_toBottomOf="@id/hugeLg"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/longTab"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_vip_pop_recharge_icon_sage"
                app:layout_constraintStart_toStartOf="@id/backgroundNext"
                app:layout_constraintTop_toBottomOf="@id/hugeLg"
                android:layout_marginTop="16dp"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/outsideLeft"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/black"
                tools:text="10% off on calls"
                android:textStyle="bold"
                android:layout_marginStart="14dp"
                app:layout_constraintStart_toEndOf="@id/longTab"
                app:layout_constraintTop_toTopOf="@id/longTab"
                app:layout_constraintBottom_toBottomOf="@id/longTab"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/loadedConnected"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:src="@drawable/ic_vip_pop_king_icon_platinum"
                app:layout_constraintStart_toStartOf="@id/backgroundNext"
                app:layout_constraintTop_toBottomOf="@id/longTab"
                android:layout_marginTop="16dp"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/secondaryDelete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/black"
                tools:text="Premium VIP badge"
                android:textStyle="bold"
                android:layout_marginStart="14dp"
                app:layout_constraintStart_toEndOf="@id/loadedConnected"
                app:layout_constraintTop_toTopOf="@id/loadedConnected"
                app:layout_constraintBottom_toBottomOf="@id/loadedConnected"/>


        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/responsiveDistant"
            android:layout_width="match_parent"
            android:layout_height="71dp"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:layout_marginHorizontal="15dp"
            android:background="@drawable/ic_vip_glodbg_gold"
            android:orientation="horizontal"
            android:gravity="center"
            >

            <TextView
                android:id="@+id/elasticBorder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp"
                android:text="1 Month"
                android:visibility="gone"
                android:textColor="@color/black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:textStyle="bold"
                android:textSize="14sp" />
            
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/nearForeground"
                android:layout_width="59dp"
                android:layout_height="26dp"
                android:text="VIP"
                android:textColor="@color/white"
                android:gravity="center"
                android:textStyle="bold"
                android:layout_marginTop="3dp"
                android:textSize="18sp"
                android:background="@drawable/bg_vip_pop_solid_bg_violet"
                app:layout_constraintTop_toTopOf="@id/addScroll"
                app:layout_constraintStart_toStartOf="@id/addScroll"
                app:layout_constraintBottom_toBottomOf="@id/addScroll"
                android:layout_marginStart="18dp"/>

            <ImageView
                android:id="@+id/addScroll"
                android:layout_width="36dp"
                android:layout_height="36dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginStart="10dp"
                android:src="@drawable/ic_vip_pop_king_icon_platinum"
                />
            
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="26dp"
                android:orientation="horizontal"
                android:paddingHorizontal="6dp"
                android:gravity="center"
                app:layout_constraintStart_toEndOf="@id/nearForeground"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                >
                <TextView
                    android:id="@+id/xxlCoordinator"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+1000"
                    android:textColor="#FFFF7F37"
                    android:textStyle="bold"
                    android:textSize="16sp" />
                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:src="@drawable/ic_coi_icon_crimson" />
            </LinearLayout>


            <TextView
                android:id="@+id/scrollDisconnected"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="$9.99"
                app:layout_constraintEnd_toStartOf="@id/rigidMinimal"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginEnd="10dp"
                android:textColor="#000000"
                android:textStyle="bold"
                android:textDirection="ltr"
                android:textSize="15sp" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/rigidMinimal"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_vip_pop_selec_rose"
                app:layout_constraintEnd_toEndOf="parent"
                android:layout_marginEnd="10dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <RelativeLayout
            android:id="@+id/dimmedClose"
            android:layout_width="match_parent"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginTop="15dp"
            android:background="@drawable/bg_extremely_btn_frost"
            android:orientation="horizontal"

            android:layout_height="48dp">

            <TextView
                android:id="@+id/pauseOuter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Subscribe"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/removePlay"
            android:layout_width="match_parent"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:layout_marginTop="80dp"
            android:background="@drawable/bg_vip_valid_btn_brown"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_height="48dp">

            <TextView
                android:id="@+id/briefAvatar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Valid until 2021-12-20"
                android:textColor="#FFF2B300"
                android:textFontWeight="@integer/m3_sys_motion_duration_long1"

                android:textSize="17sp" />
        </RelativeLayout>

        <LinearLayout
            android:id="@+id/previewAdaptive"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="center"
            >
            <TextView
                android:id="@+id/elasticChoose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Restore purchase"
                android:layout_marginTop="10dp"
                android:textColor="#FF007BFF"
                android:textSize="15sp"
                android:textStyle="bold"
                />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                >
                <TextView
                    android:id="@+id/connectedWithin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="16dp"
                    android:layout_marginTop="10dp"
                    tools:visibility="visible"
                    tools:text="1111"
                    android:textColor="#FFB3B3B3"
                    android:layout_marginBottom="30dp"
                    />
            </androidx.core.widget.NestedScrollView>

        </RelativeLayout>



    </LinearLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/clearMain"
        android:layout_width="220dp"
        android:layout_height="220dp"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_vip_pop_icon_pink"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="20dp"
        app:layout_constraintEnd_toEndOf="parent"
        />



</androidx.constraintlayout.widget.ConstraintLayout>