<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:tag="FlashRunFragmentTag"
    android:background="@drawable/bg_match_bg_crimson"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".FlashRunFragment">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        />
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        >
        <ImageView
            android:id="@+id/closeWide"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:src="@drawable/ic_rank_back_mint"
            android:layout_marginTop="12dp"
            android:layout_marginStart="16dp"
            />
    </LinearLayout>


    <LinearLayout
        android:id="@+id/xsDistant"
        android:layout_width="match_parent"
        android:layout_height="414dp"
        android:orientation="vertical"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="120dp"
        android:layout_marginHorizontal="20dp"
        android:background="@drawable/bg_match_result_bg_crimson"
        android:visibility="gone"
        tools:visibility="visible"
        >
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="334dp"
            app:cardCornerRadius="10dp"
            android:layout_marginHorizontal="6dp"
            android:layout_marginTop="6dp"
            android:layout_gravity="center_horizontal"
            >
            <ImageView
                android:id="@+id/longCalendar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </androidx.cardview.widget.CardView>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginStart="16dp"
            >
            <TextView
                android:id="@+id/stopAround"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:fontFamily="@font/protest_strike_regular"
                android:gravity="center"
                android:layout_marginTop="8dp"
                android:textFontWeight="500"
                android:text="Lidale"
                />
            <LinearLayout
                android:id="@+id/xsMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingVertical="2dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="4dp"
                android:layout_gravity="center_vertical"
                app:layout_constraintTop_toBottomOf="@id/stopAround"
                app:layout_constraintStart_toStartOf="parent"

                >
                <ImageView
                    android:id="@+id/brightThin"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:visibility="gone"
                    />
                <TextView
                    android:id="@+id/autoTimer"
                    android:layout_width="26dp"
                    android:layout_height="20dp"
                    android:background="@drawable/bg_famel_olive"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:gravity="center"
                    tools:text="22"
                    />
            </LinearLayout>
            <LinearLayout
                android:id="@+id/downFull"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingVertical="2dp"
                android:gravity="center_vertical"
                android:paddingHorizontal="4dp"
                android:layout_marginStart="4dp"
                app:layout_constraintStart_toEndOf="@id/xsMedium"
                app:layout_constraintTop_toTopOf="@id/xsMedium"
                app:layout_constraintBottom_toBottomOf="@id/xsMedium"
                android:layout_gravity="center_vertical"
                >

                <TextView
                    android:id="@+id/playGrid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:background="@drawable/bg_wall_location_cherry"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textSize="12sp"
                    android:paddingVertical="2dp"
                    android:paddingHorizontal="4dp"
                    tools:text="USA"
                    />
            </LinearLayout>
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                android:paddingHorizontal="20dp"
                android:paddingVertical="9dp"
                android:layout_marginEnd="12dp"
                android:layout_marginTop="10dp"
                app:layout_constraintTop_toTopOf="@id/stopAround"
                android:background="@drawable/bg_submit_navy">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Connecting…"
                    android:textStyle="normal"
                    android:textSize="14sp"
                    android:textColor="@color/black"/>


            </RelativeLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>



    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_centerHorizontal="true"
        >

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/fullBack"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:paddingBottom="60dp"
        >
        <com.airbnb.lottie.LottieAnimationView

            android:id="@+id/startNarrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_speed="2"
            android:visibility="visible"
            tools:visibility="gone"
            android:layout_gravity="center"
            android:layout_centerHorizontal="true"
            app:lottie_fileName="fast_flash.json"
            />

    </RelativeLayout>

    <TextView
        android:id="@+id/expandedHolder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        />

    <TextView
        android:id="@+id/tabLast"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="30dp"
        android:paddingRight="30dp"
        android:text="Connecting…"
        android:textColor="@color/black"
        android:textSize="22sp"
        android:textStyle="bold"
        android:layout_below="@+id/expandedHolder"
        android:layout_marginTop="170dp"
        />

    <TextView
        android:id="@+id/openInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="30dp"
        android:paddingRight="30dp"
        android:text=""
        android:textColor="@color/black"
        android:textSize="16sp"
        android:layout_marginTop="20dp"
        android:layout_below="@+id/tabLast"
        />

</RelativeLayout>