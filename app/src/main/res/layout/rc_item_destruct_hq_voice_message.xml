<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rc_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <androidx.constraintlayout.widget.ConstraintLayout

        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/rc_voice_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4.5dp"
            android:layout_marginTop="4.5dp"
            android:layout_marginEnd="4.5dp"
            android:adjustViewBounds="true"
            android:gravity="center_vertical"
            android:maxWidth="230dp"
            android:minWidth="52dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="0dp"
            app:layout_goneMarginStart="0dp">

            <ImageView
                android:id="@+id/rc_voice"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/rc_voice_receive_play3"
                android:layout_marginStart="5dp"
                android:visibility="gone"/>

            <TextView
                android:id="@+id/rc_duration"
                style="@style/TextStyle.Alignment"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical|start"
                android:textColor="@color/rc_voice_color"
                android:textSize="14sp"
                android:textStyle="bold" />
            <ImageView
                android:id="@+id/rc_voice_send"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/rc_voice_send_play3"
                android:layout_marginEnd="5dp"
                android:visibility="gone"/>
        </LinearLayout>

        <FrameLayout
            android:id="@+id/closeUnselected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/surfaceNested"
                android:layout_width="@dimen/rc_margin_size_15"
                android:layout_height="@dimen/rc_margin_size_15"
                android:layout_gravity="center"
                android:src="@drawable/rc_fire" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/selectedSwipe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|top"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/fullBrief"
                android:layout_width="@dimen/rc_margin_size_15"
                android:layout_height="@dimen/rc_margin_size_15"
                android:layout_gravity="center"
                android:src="@drawable/rc_fire" />

            <TextView
                android:id="@+id/backPicker"
                style="@style/TextStyle.Alignment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@drawable/rc_image_msg_count_down"
                android:gravity="center"
                android:maxLines="1"
                android:textColor="@color/rc_white_color"
                android:textSize="11dp"
                android:visibility="gone" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/rc_voice_unread"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="2dp"
        android:src="@drawable/rc_voice_unread" />

    <ImageView
        android:id="@+id/rc_voice_download_error"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="2dp"
        android:src="@drawable/rc_voice_hq_message_download_error"
        android:visibility="gone" />

    <ProgressBar
        android:id="@+id/rc_download_progress"
        style="?android:attr/progressBarStyle"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="2dp"
        android:indeterminateDrawable="@drawable/rc_hq_voice_message_downloading_style"
        android:visibility="gone" />
</LinearLayout>