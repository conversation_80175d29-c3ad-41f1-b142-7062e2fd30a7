<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/nearCenter"
    android:layout_height="86dp">

    <RelativeLayout
        android:id="@+id/sidebarForeground"
        android:layout_width="60dp"
        android:layout_height="60dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="16dp"
        android:background="@drawable/bg_anchor_avatar_bg_gold">

        <com.juicy.common.utils.view.CircleImageView
            android:id="@+id/fluidForeground"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_centerInParent="true"
            />
    </RelativeLayout>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/avatarLarge"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:autoSizeMinTextSize="10sp"
        android:gravity="start"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="#FF222222"
        android:textSize="16sp"
        android:textStyle="bold"
        android:fontFamily="@font/protest_strike_regular"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toStartOf="@+id/layoutXs"
        app:layout_constraintStart_toEndOf="@id/sidebarForeground"
        app:layout_constraintTop_toTopOf="@id/sidebarForeground"
        android:layout_marginEnd="4dp"
        tools:text="dsadasdasdasdasas" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/rigidAdaptive"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="6dp"
        android:textColor="#B8BABA"
        android:textSize="14sp"
        app:layout_constraintStart_toEndOf="@id/sidebarForeground"
        app:layout_constraintTop_toBottomOf="@+id/avatarLarge"
        tools:text="dsadasdasdasdasas" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/clockLast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:textColor="#B8BABA"
        android:textSize="14sp"
        app:layout_constraintStart_toEndOf="@id/rigidAdaptive"
        app:layout_constraintTop_toTopOf="@id/rigidAdaptive"

        tools:text="dsadasdasdasdasas" />

    <RelativeLayout
        android:id="@+id/layoutXs"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/containerTimer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:textColor="#FF222222"
                android:textSize="14sp"
                android:textStyle="normal"
                tools:text="dasdasa" />

            <ImageView
                android:id="@+id/responsiveInside"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="5dp"
                android:src="@drawable/ic_call_error_ivory" />

        </LinearLayout>


    </RelativeLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/rigidAdaptive"
        app:layout_constraintEnd_toEndOf="@id/layoutXs"
        android:background="#E8EBEB"
        />

</androidx.constraintlayout.widget.ConstraintLayout>