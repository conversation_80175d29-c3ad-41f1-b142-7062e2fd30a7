<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_editext_frost"
    android:layout_marginHorizontal="8dp"
    android:layout_width="match_parent"
    android:layout_gravity="center_vertical"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/input_panel_voice_toggle"
        android:layout_width="22dp"
        android:layout_height="28dp"
        android:layout_marginStart="8dp"
        android:src="@drawable/rc_ext_toggle_voice"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <io.rong.imkit.widget.RongEditText
        android:id="@+id/edit_btn"
        style="@style/EditTextStyle.Alignment"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="6.5dp"
        android:gravity="center_vertical"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="6.5dp"
        android:background="@color/transparent"
        android:maxLines="4"
        android:textColor="@color/black"
        android:minHeight="39dp"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/input_panel_emoji_btn"
        app:layout_constraintStart_toEndOf="@+id/input_panel_voice_toggle"
        app:layout_constraintTop_toTopOf="parent" />
    <TextView
        android:id="@+id/chat_limit_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="6.5dp"
        android:gravity="center_vertical"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="6.5dp"
        android:background="@color/transparent"
        android:textColor="#939099"
        android:minHeight="39dp"
        android:visibility="gone"
        android:text=" You have 4 free chat."
        android:textSize="14sp"
        android:textFontWeight="@integer/m3_sys_motion_duration_medium2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/input_panel_emoji_btn"
        app:layout_constraintStart_toEndOf="@+id/input_panel_voice_toggle"
        app:layout_constraintTop_toTopOf="parent"
        />
    <TextView
        android:id="@+id/press_to_speech_btn"
        style="@style/TextStyle.Alignment"
        android:layout_width="0dp"
        android:layout_height="@dimen/rc_ext_input_panel_editbox_height"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="5dp"
        android:layout_marginTop="6.5dp"
        android:layout_marginBottom="6.5dp"
        android:background="@drawable/rc_ext_voice_idle_button"
        android:gravity="center"
        android:text="@string/rc_voice_press_to_input"
        android:textColor="@color/rc_text_main_color"
        android:textSize="@dimen/rc_font_nav_or_date_size"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/input_panel_emoji_btn"
        app:layout_constraintStart_toEndOf="@+id/input_panel_voice_toggle"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/input_panel_emoji_btn"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:layout_marginTop="6.5dp"
        android:layout_marginBottom="6.5dp"
        android:layout_marginEnd="10dp"
        android:src="@drawable/rc_ext_input_panel_emoji"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/input_panel_add_or_send" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/input_panel_add_or_send"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6.5dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="6.5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/reloadList"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/input_panel_add_btn"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginEnd="4dp"
            android:src="@drawable/rc_ext_input_panel_add"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/input_panel_send_btn"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:textAllCaps="false"
            android:background="@drawable/rc_msg_send"
            android:scaleType="fitXY"
            android:textSize="@dimen/rc_font_text_third_size"
            android:maxLines="1"
            android:visibility="visible"

            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/reloadList"
        android:layout_width="28dp"
        android:layout_height="30dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_gift_button_icon_frost"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />

    <RelativeLayout
        android:id="@+id/thickBrief"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="0dp"
        android:layout_marginEnd="0dp"
        android:layout_marginBottom="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/input_panel_emoji_btn"
        app:layout_constraintStart_toEndOf="@+id/input_panel_voice_toggle"
        app:layout_constraintTop_toTopOf="parent"
        android:minHeight="39dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:background="@drawable/bg_extremely_btn_frost"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/pauseOuter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="Chat Now"
            android:textColor="@color/black"
            android:textFontWeight="@integer/m3_sys_motion_duration_long1"
            android:textSize="17sp" />
    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
