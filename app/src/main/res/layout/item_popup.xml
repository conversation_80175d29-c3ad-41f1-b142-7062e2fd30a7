<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">
  <RelativeLayout
      android:layout_width="100dp"
      android:layout_height="70dp"
      android:layout_marginEnd="10dp"
      android:layout_marginStart="10dp"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent">

      <LinearLayout
          android:id="@+id/contentSave"
          android:layout_width="100dp"
          android:layout_height="70dp">
          <androidx.cardview.widget.CardView
              android:layout_width="100dp"
              android:layout_height="70dp"
              app:cardCornerRadius="12dp"
              >

              <ImageView
                  android:id="@+id/stretchSecondary"
                  android:layout_width="100dp"
                  android:layout_height="70dp"
                  android:scaleType="centerCrop"
                  />

          </androidx.cardview.widget.CardView>
      </LinearLayout>
<!--      <com.juicy.utils.utils.RoundImageView-->
<!--          android:id="@+id/stretchSecondary"-->
<!--          android:layout_width="100dp"-->
<!--          android:layout_height="55dp"-->
<!--          app:type="round"-->
<!--          app:rouond_corner_radius="12dp"-->
<!--          android:scaleType="centerCrop"-->
<!--          />-->
      <androidx.appcompat.widget.AppCompatImageView
          android:id="@+id/coordinatorLeft"
          android:layout_width="16dp"
          android:layout_height="16dp"
          android:src="@drawable/img_edit_photo_delete_azure"
          android:layout_toEndOf="@id/contentSave"
          android:layout_marginStart="-10dp"
          />

  </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>