<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/adaptiveList"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_main_header_cyan"
        />

    <View
        android:id="@+id/onlineAuto"
        android:layout_width="match_parent"
        android:layout_height="150dp"
        android:visibility="gone"
        android:background="@drawable/bg_rank_center_teal"
        android:layout_below="@id/adaptiveList" />
    <RelativeLayout
        android:id="@+id/maximalCounter"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        >
     <androidx.appcompat.widget.AppCompatImageView
         android:id="@+id/emptyBadge"
         android:layout_width="34dp"
         android:layout_height="34dp"
         android:layout_marginStart="16dp"
         android:src="@drawable/ic_rank_back_mint"
         android:layout_centerVertical="true"
         />
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/withinSlider"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/emptyBadge"
            android:layout_marginStart="12dp"
            android:layout_centerVertical="true"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/activeSm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:visibility="gone"
            android:layout_centerVertical="true"
            />

    </RelativeLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/belowSecondary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_rank_month_steel"
        tools:text="Rank of August"
        android:layout_below="@id/maximalCounter"
        android:layout_marginTop="10dp"
        android:textSize="12sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:layout_centerHorizontal="true"
        android:paddingVertical="6dp"
        android:paddingHorizontal="16dp"/>


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/aboveTab"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
       android:layout_below="@id/belowSecondary" />

</RelativeLayout>