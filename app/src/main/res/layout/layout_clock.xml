<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/loadedProgress"
    >
    <View
        android:id="@+id/fillGrid"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />
    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_margin="1dp"
        />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/aroundCancel"
        android:layout_width="60dp"
        android:layout_height="45dp"
        android:layout_marginStart="13dp"
        android:layout_marginVertical="9dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/microShow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="13dp"

        app:layout_constraintEnd_toStartOf="@+id/swipeDynamic"
        app:layout_constraintStart_toEndOf="@+id/aroundCancel"
        app:layout_constraintTop_toTopOf="@+id/aroundCancel">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/headerCompact"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/common_ok_34"
            android:textColor="#FF232839"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/dimmedMedium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_marginStart="13dp"
        app:layout_constraintStart_toEndOf="@+id/aroundCancel"
        app:layout_constraintTop_toBottomOf="@+id/microShow">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_big_coin_ivory"
            android:layout_gravity="center_vertical"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/openBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:gravity="start"
            android:textColor="#FF33CC"
            android:textSize="9sp"
            android:textStyle="bold"
            tool:text="+20% More Coins" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/swipeDynamic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="11dp"
        android:text="@string/common_ok_36"
        android:textColor="#FF1A07"
        android:textSize="11sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
       app:layout_constraintEnd_toStartOf="@+id/mediumXxl"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/mediumXxl"
        android:layout_width="21dp"
        android:layout_height="21dp"
        android:layout_marginEnd="13dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/bg_pay_channel_check_btn_ebony"
        >

    </Button>

</androidx.constraintlayout.widget.ConstraintLayout>