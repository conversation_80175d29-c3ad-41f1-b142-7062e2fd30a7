<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="71dp"
    android:paddingLeft="16dp"
    android:paddingRight="16dp"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_centerVertical="true"
        >
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_centerVertical="true"
            >

            <TextView
                android:id="@+id/rigidEmpty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#212121"
                android:textSize="18sp"
                android:text="English"
                android:textFontWeight="500"
                />

            <TextView
                android:layout_marginTop="5dp"
                android:id="@+id/acrossCollapsed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#212121"
                android:text="英语"
                android:textFontWeight="400"
                android:visibility="gone"
                />


        </LinearLayout>
        <View
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:layout_weight="1"
            />

        <ImageView
            android:id="@+id/overlayMask"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_language_nor_ruby"
            />
    </LinearLayout>
    
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#10000000"
        android:layout_alignParentBottom="true"
        />

</RelativeLayout>