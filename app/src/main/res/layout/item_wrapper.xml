<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/upCard"
    android:layoutDirection="ltr"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/mediumConfirm"
            android:layout_width="44dp"
            android:layout_height="18dp"
            android:src="@drawable/bg_main_first_tab_beige"
            app:layout_constraintTop_toTopOf="@id/outerMicro"
            android:layout_marginTop="16dp"
            app:layout_constraintStart_toStartOf="@id/outerMicro"
            />
    <TextView
        android:id="@+id/outerMicro"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FF141414"
        tools:text="New"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center"
        tools:textStyle="bold"
        android:fontFamily="@font/protest_strike_regular"
        android:textSize="18sp"
        app:layout_constraintStart_toStartOf="parent"
        />
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/copyPrev"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:src="@drawable/ic_main_star_violet"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />
</androidx.constraintlayout.widget.ConstraintLayout>

