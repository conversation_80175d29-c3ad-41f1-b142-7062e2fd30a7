<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

   <androidx.constraintlayout.widget.ConstraintLayout
       android:layout_width="match_parent"
       android:layout_height="match_parent">

       <androidx.appcompat.widget.AppCompatImageView
           android:id="@+id/connectedInput"
           android:layout_width="match_parent"
           android:layout_height="match_parent"
           android:src="@drawable/ic_five_star_p_pink"/>

       <RelativeLayout
           android:id="@+id/scrollFlexible"
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           app:layout_constraintTop_toTopOf="@id/connectedInput"
           app:layout_constraintBottom_toBottomOf="@id/connectedInput"
           app:layout_constraintStart_toStartOf="@id/connectedInput"
           app:layout_constraintEnd_toEndOf="@id/connectedInput"
           >

           <RelativeLayout
               android:layout_width="match_parent"
               android:layout_height="wrap_content"
               android:layout_marginTop="140dp">

               <TextView
                   android:id="@+id/maximalCounter"
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content"
                   android:text="Five star"
                   android:textStyle="bold"
                   android:textSize="22sp"
                   android:fontFamily="@font/protest_strike_regular"
                   android:gravity="center"
                   android:textColor="@color/black"
                   android:layout_marginHorizontal="20dp"
                   android:layout_marginTop="82dp"
                   />

               <androidx.appcompat.widget.AppCompatTextView
                   android:id="@+id/backFirst"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:textColor="@color/black"
                   android:textSize="17sp"
                   android:text="Loved the call? Rate 5 ⭐️ and keep the connections shining!"
                   android:gravity="center"
                   android:layout_marginHorizontal="20dp"
                   android:layout_below="@+id/maximalCounter"
                   android:layout_marginTop="20dp"
                   />
               <androidx.appcompat.widget.LinearLayoutCompat
                   android:layout_width="match_parent"
                   android:layout_height="wrap_content"
                   android:layout_below="@+id/backFirst"
                   android:layout_marginTop="32dp"
                   android:orientation="vertical">
                   <View
                       android:layout_width="match_parent"
                       android:layout_height="0.5dp"
                       android:background="#E8EBEB"
                       android:visibility="gone"
                       />
                   <androidx.appcompat.widget.AppCompatTextView
                       android:id="@+id/fitHolder"
                       android:layout_width="257dp"
                       android:layout_height="52dp"
                       android:layout_gravity="center_horizontal"
                       android:background="@drawable/bg_submit_navy"
                       android:text="⭐️ ⭐️ ⭐️ ⭐️ ⭐️"
                       android:textSize="18sp"
                       android:textStyle="bold"
                       android:textColor="@color/black"
                       android:gravity="center"
                       />
                   <View
                       android:layout_width="match_parent"
                       android:layout_height="0.5dp"
                       android:background="#E8EBEB"
                       android:visibility="gone"
                       />
                   <androidx.appcompat.widget.AppCompatTextView
                       android:id="@+id/acrossExpand"
                       android:layout_width="257dp"
                       android:layout_height="52dp"
                       android:textSize="17sp"
                       android:layout_marginTop="10dp"
                       android:background="@drawable/bg_cancel_gray_platinum"
                       android:layout_gravity="center_horizontal"
                       android:textColor="#FF9E9E9E"
                       android:gravity="center"
                       android:text="Not great"
                       />
                   <View
                       android:layout_width="match_parent"
                       android:layout_height="0.5dp"
                       android:background="#E8EBEB"
                       android:visibility="gone"
                       />
                   <androidx.appcompat.widget.AppCompatTextView
                       android:id="@+id/pressedBrief"
                       android:layout_width="257dp"
                       android:layout_height="52dp"
                       android:text="@string/btn_resume"
                       android:layout_marginTop="10dp"
                       android:textSize="17sp"
                       android:visibility="gone"
                       android:textColor="#FF9E9E9E"
                       android:background="@drawable/bg_cancel_gray_platinum"
                       android:layout_gravity="center_horizontal"
                       android:gravity="center"
                       android:layout_marginBottom="12dp"
                       />
               </androidx.appcompat.widget.LinearLayoutCompat>


           </RelativeLayout>

       </RelativeLayout>

       <androidx.appcompat.widget.AppCompatImageView
           android:id="@+id/counterMacro"
           android:layout_width="144dp"
           android:layout_height="144dp"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintStart_toStartOf="parent"
           app:layout_constraintEnd_toEndOf="parent"
           android:layout_centerHorizontal="true"
           android:layout_marginBottom="-72dp"
           android:layout_above="@+id/scrollFlexible"
           android:visibility="gone"
           android:src="@drawable/img_dame_big_logo_cream" />

   </androidx.constraintlayout.widget.ConstraintLayout>




</RelativeLayout>