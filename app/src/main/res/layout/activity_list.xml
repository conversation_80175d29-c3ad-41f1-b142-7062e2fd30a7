<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_main_header_cyan"
    tools:context="com.juicy.my.SetAppActivity">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:scaleType="centerCrop"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/connectedUnlocked"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/cancelMedium"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/bodyLg"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="16dp"
                android:src="@drawable/ic_rank_back_mint" />
        </LinearLayout>

        <TextView
            android:id="@+id/wideThick"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:includeFontPadding="false"
            android:text="@string/common_current"
            android:textColor="@color/black"
            android:fontFamily="@font/protest_strike_regular"
            android:textSize="22sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/upperInner"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:layout_marginHorizontal="12dp"
        app:layout_constraintBottom_toTopOf="@+id/firstChecked"
        app:layout_constraintTop_toBottomOf="@id/connectedUnlocked">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:background="@drawable/bg_setting_bg_coral"
            android:paddingHorizontal="16dp">

            <TextView
                android:id="@+id/overlayElastic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/common_top"
                android:textColor="@color/basic_1a1a"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/throughBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:textColor="#939099"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/fullCancel"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/fullCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_copy_bronze"
                android:paddingHorizontal="10dp"
                android:paddingVertical="4dp"
                android:textSize="16sp"
                android:background="@drawable/bg_submit_navy"
                android:text="Copy"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/throughDrop"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginHorizontal="40dp"
                android:background="@color/basic_ebeb"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/bg_setting_bg_coral"
            android:paddingHorizontal="16dp">

            <TextView
                android:id="@+id/beyondPick"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/common_bottom"
                android:textColor="@color/basic_1a1a"
                android:textSize="15sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/badgeCut"
                android:layout_width="47dp"
                android:layout_height="26dp"
                android:src="@drawable/ic_close_switch_lime"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/farUnfocused"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginHorizontal="40dp"
                android:background="@color/basic_ebeb"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/bg_setting_bg_coral"
            android:paddingHorizontal="16dp">

            <TextView
                android:id="@+id/leftFrame"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/common_left"
                android:textColor="@color/basic_1a1a"
                android:textSize="15sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/connectedVisible"
                android:layout_width="47dp"
                android:layout_height="26dp"
                android:src="@drawable/ic_close_switch_lime"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/uploadContainer"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginHorizontal="40dp"
                android:background="@color/basic_ebeb"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/bg_setting_bg_coral"
            android:paddingHorizontal="16dp">

            <TextView
                android:id="@+id/filterInput"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/msg_info"
                android:textColor="@color/basic_1a1a"
                android:textSize="15sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/fitPick"
                android:layout_width="47dp"
                android:layout_height="26dp"
                android:src="@drawable/ic_close_switch_lime"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/pasteTop"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginHorizontal="40dp"
                android:background="@color/basic_ebeb"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/innerSwitch"
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/bg_setting_bg_coral"
            android:paddingHorizontal="16dp">

            <TextView
                android:id="@+id/footerSwipe"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="Language"
                android:textColor="@color/basic_1a1a"
                android:textSize="15sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/pressedInput"
                android:layout_width="12dp"
                android:layout_height="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:src="@drawable/ic_arrow_turn_azure"
                />

            <TextView
                android:id="@+id/briefMenu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="english"
                android:textColor="#8C8C8C"
                android:textSize="15sp"
                android:textStyle="bold"
                android:layout_marginEnd="5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/pressedInput"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/uploadCancel"
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_marginHorizontal="40dp"
                android:background="@color/basic_ebeb"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/overlayDelete"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:gravity="center"
        android:text="@string/btn_add"
        android:textColor="#FF007BFF"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/firstChecked"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginHorizontal="40dp"
        android:background="@drawable/bg_submit_navy"
        app:layout_constraintBottom_toTopOf="@+id/overlayDelete">

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginHorizontal="40dp"
            android:background="@color/basic_ebeb"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/thumbnailAround"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/common_right"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>