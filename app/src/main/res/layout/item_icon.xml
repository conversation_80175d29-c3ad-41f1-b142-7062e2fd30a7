<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    >

    <ImageView
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:src="@drawable/ic_robot_icon_ash"
        android:layout_marginStart="8dp"
        android:layout_marginTop="5dp"
        android:layout_gravity="top"
        />
    <LinearLayout
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_marginStart="6dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        >
        <LinearLayout
            android:layout_marginTop="15dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:paddingTop="7dp"
            android:background="@drawable/bg_robot_q_item_bg_platinum"
            >
            <TextView
                android:id="@+id/recyclerSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Hi! Welcome to here."
                android:textColor="@color/white"
                android:textSize="13sp"
                />

            <LinearLayout
                android:id="@+id/minimalCounter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                >
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#20313131"
                    />
                <TextView
                    android:id="@+id/saveSurface"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="13sp"
                    android:text="Hi! Welcome to here."
                    />

            </LinearLayout>
            
            <TextView
                android:id="@+id/counterLoaded"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="VIEW EXAMPLE"
                android:textSize="14sp"
                android:layout_marginTop="5dp"
                android:textColor="#FF53A3FF"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="#20313131"
                android:layout_marginTop="10dp"
                >
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal"
                >

                <RelativeLayout
                    android:id="@+id/dimmedTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    
                    >
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:orientation="horizontal"
                        >
                        <ImageView
                            android:id="@+id/narrowUnchecked"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_helpful_nor_slate"
                            />
                        <TextView
                            android:id="@+id/highlightedIcon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/white"
                            android:layout_marginStart="5dp"
                            android:textSize="14sp"
                            android:textFontWeight="500"
                            android:layout_gravity="center_vertical"
                            android:text="Helpful"
                            />
                    </LinearLayout>

                </RelativeLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="#20313131"
                    />

                <RelativeLayout
                    android:id="@+id/stretchConfirm"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    >

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:orientation="horizontal"
                        >
                        <ImageView
                            android:id="@+id/redoEmpty"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_unhelpful_nor_mint"
                            />
                        <TextView
                            android:id="@+id/longRefresh"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textFontWeight="500"
                            android:layout_marginStart="5dp"
                            android:layout_gravity="center_vertical"
                            android:text="Unhelpful"
                            />
                    </LinearLayout>


                </RelativeLayout>


            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/backgroundThin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:orientation="horizontal"
            >
            <ImageView
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/ic_translate_icon_blue"
                android:layout_gravity="center_vertical"
                />
            <TextView
                android:id="@+id/toggleNested"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Click to translate"
                android:textColor="#FF53A3FF"
                />
        </LinearLayout>


    </LinearLayout>
    <View

        android:layout_width="65dp"
        android:layout_height="1dp"/>

</LinearLayout>