<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginHorizontal="20dp"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:background="@drawable/bg_mine_bg_white_iron"
       >
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/inputStop"
            android:layout_width="94dp"
            android:layout_height="94dp"
            android:src="@drawable/bg_handup_dialog_ic"
            android:scaleType="centerCrop"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="10dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/stopDistant"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="20sp"
            android:textColor="@color/black"
            tools:text="Ending the call now? I will miss you~"
            android:textStyle="bold"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="120dp"
            android:paddingHorizontal="30dp"
            />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="14dp"
            android:gravity="center_horizontal"
            app:layout_constraintTop_toBottomOf="@id/stopDistant"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal"
            android:visibility="visible"
            >
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/editLower"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:paddingVertical="10dp"
                android:singleLine="true"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/bg_cancel_gray_platinum"
                android:text="@string/btn_resume"
                android:textColor="#FF9E9E9E"
                android:gravity="center"
                />
            <View
                android:layout_width="30dp"
                android:layout_height="match_parent"
                />
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/holderMain"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:paddingVertical="10dp"
                android:singleLine="true"
                android:textStyle="bold"
                android:textSize="16sp"
                android:textColor="@color/black"
                android:background="@drawable/bg_submit_navy"
                android:text="@string/btn_clear"
                android:gravity="center"
                />
        </androidx.appcompat.widget.LinearLayoutCompat>


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

