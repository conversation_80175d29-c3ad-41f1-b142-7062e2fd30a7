<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginEnd="8dp"
    android:layout_marginBottom="8dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@drawable/bg_item_gift_emerald">
 <androidx.appcompat.widget.AppCompatImageView
     android:id="@+id/dimmedToggle"
     android:layout_width="64dp"
     android:layout_height="64dp"
     android:layout_marginTop="24dp"
     app:layout_constraintTop_toTopOf="parent"
     app:layout_constraintEnd_toEndOf="parent"
     app:layout_constraintStart_toStartOf="parent"
     />
 <androidx.appcompat.widget.LinearLayoutCompat
     android:layout_width="wrap_content"
     android:layout_height="wrap_content"
     android:orientation="horizontal"
     app:layout_constraintStart_toStartOf="parent"
     app:layout_constraintEnd_toEndOf="parent"
     android:layout_marginTop="12dp"
     android:gravity="center_vertical"
     app:layout_constraintTop_toBottomOf="@id/dimmedToggle"
     >
     <androidx.appcompat.widget.AppCompatImageView
         android:layout_width="16dp"
         android:layout_height="16dp"
         android:src="@drawable/ic_coi_icon_crimson"
         />
     <androidx.appcompat.widget.AppCompatTextView
         android:id="@+id/rightCoordinator"
         android:layout_width="wrap_content"
         android:layout_height="wrap_content"
         android:textSize="14sp"
         tools:text="dsadasdas"
         android:textColor="@color/white"
         />
 </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.constraintlayout.widget.ConstraintLayout>