<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>
    <RelativeLayout
        android:id="@+id/contentSelect"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            >

            <RelativeLayout
                android:id="@+id/disabledExpand"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:minHeight="48dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="5dp"
                android:orientation="horizontal">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/withinSlider"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/clockEnabled"
                    android:layout_width="25dp"
                    android:layout_height="29dp"
                    android:src="@drawable/ic_main_country_olive"
                    android:background="@null"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="10dp"
                    android:scaleType="centerCrop"
                    android:layout_toStartOf="@id/wideFlexible" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/wideFlexible"
                    android:layout_width="34dp"
                    android:layout_height="29dp"
                    android:layout_marginStart="5dp"
                    android:scaleType="fitCenter"
                    android:layout_marginEnd="5dp"
                    android:src="@drawable/ic_main_rk_platinum"
                    android:background="@null"
                    android:layout_centerVertical="true"
                    android:layout_alignParentEnd="true" />

            </RelativeLayout>

            <FrameLayout
                android:id="@+id/nextDown"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </LinearLayout>




    </RelativeLayout>

</layout>