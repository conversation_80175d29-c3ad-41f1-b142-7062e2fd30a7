<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginHorizontal="20dp"
        android:paddingBottom="30dp"
        android:background="@drawable/bg_setting_dg_cherry">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/throughPlay"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_close_gray_indigo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_margin="12dp"
            android:visibility="gone"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/backFirst"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="20sp"
            android:textColor="@color/basic_1a1a"
            android:text="Confirm account deletion?"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginTop="10dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/throughPlay"
            android:paddingHorizontal="30dp"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/distantSwipe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:layout_marginTop="16dp"
            android:textColor="#FFFF0107"
            android:text="This will erase all your data, including any products you’ve purchased. Please make sure this is what you want."
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backFirst"
            android:paddingHorizontal="30dp"
            />
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="28dp"
            app:layout_constraintTop_toBottomOf="@id/distantSwipe"
            android:layout_marginHorizontal="16dp"
            android:orientation="horizontal"
            >
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/pagerBar"
                android:layout_width="150dp"
                android:layout_height="50dp"
                android:background="@drawable/bg_submit_navy"
                android:textColor="@color/black"
                android:gravity="center"
                android:textSize="16sp"
                android:text="Yes"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/pressedBrief"
                android:layout_width="150dp"
                android:layout_height="50dp"
                android:textColor="#FF9E9E9E"
                android:gravity="center"
                android:background="@drawable/bg_cancel_gray_platinum"
                android:textSize="16sp"
                android:text="No"

                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />



        </androidx.constraintlayout.widget.ConstraintLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

