<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rc_conversation_item"
    android:layout_width="match_parent"
    android:layout_height="86dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/farInside"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <View
            android:id="@+id/hugeReload"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:layout_marginBottom="0dp"
            />
        <RelativeLayout
            android:id="@+id/rc_conversation_portrait_rl"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginStart="@dimen/rc_margin_size_12"
            android:layout_marginVertical="@dimen/rc_margin_size_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <RelativeLayout
                android:layout_width="@dimen/rc_reference_ext_view_height"
                android:layout_height="@dimen/rc_reference_ext_view_height"
                android:background="@drawable/bg_anchor_avatar_bg_gold"
                >
                <ImageView
                    android:id="@+id/rc_conversation_portrait"
                    android:layout_width="@dimen/rc_reference_image_size"
                    android:layout_height="@dimen/rc_reference_image_size"
                    android:scaleType="centerCrop"
                    android:layout_centerInParent="true"
                    app:shapeAppearanceOverlay="@style/widget_roundimageview" />
            </RelativeLayout>


        </RelativeLayout>

        <TextView
            android:id="@+id/rc_conversation_title"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="200dp"
            android:layout_marginStart="@dimen/rc_margin_size_12"
            android:layout_marginEnd="@dimen/rc_margin_size_12"
            android:ellipsize="end"
            android:textStyle="bold"
            android:singleLine="true"
            tools:text="张三"
            android:fontFamily="@font/protest_strike_regular"
            android:textColor="#FF222222"
            android:textSize="@dimen/rc_font_secondary_size"

            app:layout_constraintStart_toEndOf="@+id/rc_conversation_portrait_rl"
            app:layout_constraintTop_toTopOf="@+id/rc_conversation_portrait_rl" />

        <ImageView
            android:id="@+id/tinyLarge"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@mipmap/ic_system_icon_jade"
            android:layout_marginStart="5dp"
            app:layout_constraintTop_toTopOf="@+id/rc_conversation_title"
            app:layout_constraintBottom_toBottomOf="@+id/rc_conversation_title"
            app:layout_constraintStart_toEndOf="@+id/rc_conversation_title"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/rc_conversation_content"
            style="@style/TextStyle.Alignment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="60dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="你好，朋友！"
            android:textColor="#FF656D7B"
            android:textSize="@dimen/rc_font_text_third_size"
            app:layout_constraintBottom_toBottomOf="@+id/rc_conversation_portrait_rl"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/rc_conversation_portrait_rl"
            />

        <TextView
            android:id="@+id/rc_conversation_date"
            style="@style/TextStyle.Alignment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/rc_margin_size_12"
            android:text="3 月 22 日"
            android:textColor="#FF222222"
            android:textSize="@dimen/rc_font_nav_or_date_size"
            android:gravity="end"
            android:fontFamily="@font/protest_strike_regular"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tinyLarge"
            app:layout_constraintTop_toTopOf="@+id/rc_conversation_title"
            app:layout_constraintBottom_toBottomOf="@+id/rc_conversation_title"
            />


        <RelativeLayout
            android:id="@+id/rc_conversation_unread"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginTop="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="12sp"
            app:layout_constraintTop_toBottomOf="@+id/rc_conversation_date"
            >

            <ImageView
                android:id="@+id/rc_conversation_unread_bg"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                app:srcCompat="@drawable/rc_unread_count_bg_normal" />

            <TextView
                android:id="@+id/rc_conversation_unread_count"
                style="@style/TextStyle.Alignment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text= "99+"
                android:textColor="@color/rc_white_color"
                android:textSize="@dimen/rc_font_auxiliary_size" />
        </RelativeLayout>
        <ImageView
            android:id="@+id/rc_conversation_no_disturb"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/rc_margin_size_12"
            android:layout_marginBottom="@dimen/rc_margin_size_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:srcCompat="@drawable/rc_no_disturb" />

        <ImageView
            android:id="@+id/rc_conversation_read_receipt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/rc_margin_size_2"
            android:layout_marginBottom="@dimen/rc_margin_size_12"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/rc_conversation_no_disturb"
            app:layout_goneMarginRight="@dimen/rc_margin_size_12"
            app:srcCompat="@drawable/rc_read_receipt" />

        <View
            android:id="@+id/xxlSecondary"
            android:layout_width="wrap_content"
            android:layout_height="0.5dp"
            android:layout_marginTop="71.5dp"
            android:background="@color/rc_divider_color"
            app:layout_constraintStart_toStartOf="@id/rc_conversation_title"
            app:layout_constraintTop_toTopOf="parent" />
        <View
            android:id="@+id/divider"
            android:layout_width="wrap_content"
            android:layout_height="0.5dp"
            android:layout_marginTop="71.5dp"
            android:visibility="gone"
            android:background="@color/rc_divider_color"
            app:layout_constraintStart_toStartOf="@id/rc_conversation_title"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>