<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    >
    <LinearLayout
        android:id="@+id/fillGrid"
        android:layout_width="match_parent"
        android:layout_height="77.5dp"
        android:orientation="horizontal"
        android:background="@drawable/bg_dialog_message_bg_violet"
        android:gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="20dp"
        android:clickable="true"
        >

    <com.juicy.common.utils.RoundImageView
        android:id="@+id/acrossLocked"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:scaleType="centerCrop"
        app:type="circle"
        android:cropToPadding="true"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_vertical"
        android:layout_weight="1"
        >
        <!-- 昵称 -->
        <TextView
            android:id="@+id/bottomTop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="nickName"
            android:textSize="18sp"
            android:fontFamily="@font/protest_strike_regular"
            android:textColor="@color/black"
            android:maxLines="1"
            android:ellipsize="end"
            />
        <!-- 内容 -->
        <TextView
            android:id="@+id/compactNarrow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="xxx"
            android:textSize="16sp"
            android:textFontWeight="500"
            android:textColor="@color/black"
            android:maxLines="1"
            android:ellipsize="end"
            />

    </LinearLayout>
    <ImageView
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:src="@drawable/ic_messgae_right_violet"
        android:layout_marginEnd="8dp"
        />

    </LinearLayout>
</LinearLayout>