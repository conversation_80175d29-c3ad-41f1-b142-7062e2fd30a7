<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_w_c_2_mist"
    android:layout_gravity="bottom">


    <com.juicy.common.utils.view.CircleImageView
        android:id="@+id/fluidForeground"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_marginTop="32dp"
        android:layout_centerHorizontal="true" />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/emptyBelow"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginTop="20dp"
        android:layout_alignParentEnd="true"
        android:src="@drawable/ic_close_black_gray" />


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_below="@id/fluidForeground"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp">


        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/disabledExpand"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="4dp"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/avatarLarge"
                android:layout_width="180dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:layout_gravity="center_horizontal"
                android:textStyle="bold"
                android:fontFamily="@font/protest_strike_regular"
                android:gravity="center"
                tools:text="dsadsadsadasdas" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/clockLast"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FF818181"
                android:layout_gravity="center_horizontal"
                android:textSize="12sp"
                tools:text="dsadasdsadas" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <View
            android:id="@+id/stretchLong"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@id/disabledExpand"
            android:layout_marginTop="32dp"
            android:background="#E8EBEB" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/maximalCounter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/stretchLong"
            android:layout_marginTop="12dp"
            android:text="@string/btn_remove"
            android:fontFamily="@font/protest_strike_regular"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <com.juicy.common.utils.view.AutoWrapLineLayout
            android:id="@+id/mainUp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/maximalCounter"
            
            android:layout_marginTop="12dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/emptyFocused"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_below="@id/mainUp"
            android:layout_marginTop="12dp"
            android:background="@drawable/bg_submit_navy"
            android:gravity="center"
            android:textSize="18sp"
            android:text="@string/common_ok_48"
            android:textColor="@color/black"
            android:layout_marginBottom="10dp"
            android:textStyle="bold" />


    </RelativeLayout>


</RelativeLayout>