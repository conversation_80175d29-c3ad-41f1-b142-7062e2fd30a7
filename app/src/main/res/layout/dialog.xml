<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_ra_16_white_blue">

    <ImageView
        android:id="@+id/farToggle"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_marginTop="24dp"
        android:src="@drawable/img_logo_app_ebony"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/elasticSurface"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:includeFontPadding="false"
        android:text="@string/common_least"
        android:textColor="@color/basic_1a1a"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/farToggle" />

    <TextView
        android:id="@+id/chooseBrief"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="12dp"
        android:text="@string/common_first"
        android:textColor="@color/basic_6166"
        android:textSize="13sp"
        android:textAlignment="center"
        app:layout_constraintTop_toBottomOf="@id/elasticSurface" />

    ]
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/chooseBrief"
        android:layout_marginTop="12dp"
        android:layout_marginHorizontal="24dp"
        android:paddingBottom="24dp"
        >


        <TextView
            android:id="@+id/dynamicAround"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingVertical="12dp"
            android:text="@string/common_most"
            android:textSize="17sp"
            android:textColor="#979999"
            android:background="@drawable/bg_setting_button_4_sapphire"
            />
        <View
            android:layout_width="30dp"
            android:layout_height="match_parent"
            />
        <TextView
            android:id="@+id/underlayFixed"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_block_btn_bg_frost"
            android:gravity="center"
            android:paddingVertical="12dp"
            android:text="Agree"
            android:textColor="@color/white"
            android:textSize="17sp"
            android:layout_weight="1"
            />
    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>