<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="bottom">


    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/disabledExpand"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/fluidForeground"
        android:background="@drawable/bg_block_dialog_cyan"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingHorizontal="16dp"
        android:paddingBottom="20dp">
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/avatarLarge"
            android:layout_width="230dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="dsaasdasdsasdasadasdasdsadsa" />
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/fillSurface"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:itemCount="5"
        tools:listitem="@layout/item_avatar"
        />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#E8EBEB"
            android:layout_marginBottom="16dp"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/pressedBrief"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:textSize="15sp"
            android:textColor="#1A1A1A"
            android:gravity="center"
            android:text="@string/rc_cancel"
            android:background="@drawable/bg_cancel_green"
            />


    </androidx.appcompat.widget.LinearLayoutCompat>
    <com.juicy.common.utils.view.CircleImageView
        android:id="@+id/fluidForeground"
        android:layout_width="96dp"
        android:layout_height="96dp"
        android:layout_marginBottom="-50dp"
        android:layout_centerHorizontal="true"
        />
</RelativeLayout>