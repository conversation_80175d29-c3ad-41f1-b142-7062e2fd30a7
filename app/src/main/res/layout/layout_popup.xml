<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/aroundLarge">

        <com.scwang.smartrefresh.header.MaterialHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/miniCopy"
                android:visibility="gone"
                >
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="60dp"
                    android:layout_centerInParent="true"
                    android:orientation="vertical"
                    >
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/borderMacro"
                        android:layout_width="200dp"
                        android:layout_height="150dp"
                        android:src="@drawable/ic_nodata_icon_tan"
                        android:scaleType="fitCenter"
                        android:layout_gravity="center_horizontal"
                        />

                    <TextView
                        android:id="@+id/shadowCoordinator"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="No Data"
                        android:textColor="#B4ADE4"
                        />
                </LinearLayout>
            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/tabDynamic"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </RelativeLayout>
        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>
</layout>
