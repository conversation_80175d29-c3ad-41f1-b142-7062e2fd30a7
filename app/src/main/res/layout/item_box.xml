<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="16dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
   <com.juicy.common.utils.view.CircleImageView
       android:id="@+id/fluidForeground"
       android:layout_width="52dp"
       android:layout_height="52dp"
       app:layout_constraintStart_toStartOf="parent"
       app:layout_constraintTop_toTopOf="parent"
       />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/avatarLarge"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="@id/fluidForeground"
        app:layout_constraintEnd_toEndOf="@id/fluidForeground"
        app:layout_constraintTop_toBottomOf="@id/fluidForeground"
        android:textSize="12sp"
        android:gravity="center"
        android:singleLine="true"
        tools:text="dasdasddasddasdsas"
        android:textColor="#979999"
        />


</androidx.constraintlayout.widget.ConstraintLayout>