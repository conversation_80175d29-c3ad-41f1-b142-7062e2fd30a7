<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="35dp"
    android:orientation="horizontal"
    >

  <LinearLayout
      android:id="@+id/syncReleased"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:orientation="horizontal">

      <RelativeLayout
          android:id="@+id/endSelect"
          android:layout_width="0dp"
          android:layout_height="match_parent"
          android:layout_weight="1"
          >

          <ImageView
              android:id="@+id/distantUp"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_centerInParent="true"
              android:visibility="gone"
              />
          <androidx.appcompat.widget.AppCompatTextView
              android:id="@+id/successLeft"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_centerInParent="true"
              tools:text="Lv1"
              tools:background="@drawable/bg_level0_mist"
              android:textSize="12sp"
              android:textColor="@color/white"
              android:paddingHorizontal="10dp"
              android:paddingVertical="2dp"/>
          <View
              android:layout_width="1dp"
              android:layout_height="match_parent"
              android:background="@color/black"/>

          <View
              android:layout_width="match_parent"
              android:layout_height="1dp"
              android:layout_alignParentBottom="true"
              android:background="@color/black"/>
      </RelativeLayout>

      <View
          android:layout_width="1dp"
          android:layout_height="match_parent"
          android:background="@color/black"/>

      <RelativeLayout
          android:id="@+id/pauseNested"
          android:layout_width="0dp"
          android:layout_height="match_parent"
          android:layout_weight="1"
          android:orientation="vertical"
          >

          <TextView
              android:id="@+id/syncContent"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_centerInParent="true"
              android:text="0"
              android:textSize="15sp"
              android:textFontWeight="400"
              android:textColor="#000000"
              />

          <View
              android:layout_width="match_parent"
              android:layout_height="1dp"
              android:layout_alignParentBottom="true"
              android:background="@color/black"/>

      </RelativeLayout>
      <View
          android:layout_width="1dp"
          android:layout_height="match_parent"
          android:background="@color/black"/>

  </LinearLayout>


    <LinearLayout
        android:id="@+id/shadowInactive"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:visibility="gone"
        android:background="@drawable/bg_level_item_bottom_navy"
        >
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            >

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/defaultRecycler"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                tools:text="Lv1"
                tools:background="@drawable/bg_level0_mist"
                android:textSize="12sp"
                android:textColor="@color/white"
                android:paddingHorizontal="10dp"
                android:paddingVertical="2dp"/>


        </RelativeLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/black"/>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            >

            <TextView
                android:id="@+id/removeSm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="0"
                android:gravity="center"
                android:textColor="#D9000000"
                android:textStyle="bold"
                android:textSize="15sp" />

        </RelativeLayout>
    </LinearLayout>


</LinearLayout>