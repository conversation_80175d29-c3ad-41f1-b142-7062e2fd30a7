<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:orientation="horizontal"
    android:layout_height="wrap_content">
    <!--    添加两个背景-->

<!--    <View-->
<!--        android:id="@+id/beyondSubmit"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:background="@drawable/bg_country_item_sel_orange"-->
<!--        />-->
<!--    <View-->
<!--        android:id="@+id/aroundLoading"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:background="@drawable/bg_country_item_nor_maroon"-->
<!--        />-->


    <LinearLayout
        android:id="@+id/calendarLower"
        android:layout_width="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:layout_marginBottom="4dp"
        android:layout_height="28dp"
        android:orientation="horizontal"
        android:background="@drawable/bg_country_item_nor_maroon"
        >
        <ImageView
            android:id="@+id/timerEdit"
            android:layout_gravity="center"
            android:layout_marginStart="7dp"
            android:layout_width="17dp"
            android:layout_height="17dp"/>

        <TextView
            android:id="@+id/largeSurface"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="7dp"
            android:layout_marginEnd="7dp"
            android:textSize="12sp"
            android:textColor="@color/black"
            android:text="测试item"
            />
    </LinearLayout>




</LinearLayout>