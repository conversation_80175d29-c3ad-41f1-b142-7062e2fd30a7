<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:paddingStart="16dp"
    android:orientation="horizontal"
    >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_centerVertical="true"

        >
        <RelativeLayout
            android:id="@+id/dragCounter"
            android:layout_width="62dp"
            android:layout_height="62dp"
            android:background="@drawable/bg_avatar_bg_lavender">
            <com.juicy.common.utils.view.CircleImageView
                android:id="@+id/fluidForeground"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_centerInParent="true"
                />
        </RelativeLayout>


        <LinearLayout
            android:id="@+id/compactUpload"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:paddingEnd="30dp"
            android:orientation="vertical"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/fluidForeground"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/avatarLarge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FF222222"
                android:fontFamily="@font/protest_strike_regular"
                android:textSize="16sp"
                android:singleLine="true"
                tools:text="Name" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="6dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="26dp"
                    android:layout_height="20dp"
                    android:layout_below="@id/avatarLarge"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    tools:text="22"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:background="@drawable/bg_famel_olive"
                    android:id="@+id/miniInner"
                    />
                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:visibility="gone"
                    android:src="@drawable/ic_block_local_pink"
                    android:layout_below="@id/avatarLarge"
                    android:layout_toEndOf="@id/miniInner"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="3dp"
                    android:id="@+id/redoBelow"
                    />
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:layout_marginStart="6dp"
                    android:background="@drawable/bg_wall_location_cherry"
                    tools:text="USA"
                    android:paddingVertical="2dp"
                    android:paddingHorizontal="4dp"
                    android:id="@+id/outerWide"
                    />
            </LinearLayout>

        </LinearLayout>
        
        <TextView
            android:id="@+id/coordinatorHide"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:gravity="center"
            android:minWidth="65dp"
            android:layout_marginEnd="16dp"
            android:layout_marginStart="10dp"
            android:layout_gravity="center_vertical"
            android:textSize="14sp"
            android:text="Unblock"
            android:textColor="@color/black"
            android:background="@drawable/bg_block_btn_bg_frost"
            />
    </LinearLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_alignParentBottom="true"
        />


</RelativeLayout>