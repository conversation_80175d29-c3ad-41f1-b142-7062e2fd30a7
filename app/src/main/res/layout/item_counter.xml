<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/gridExpand"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:background="@drawable/bg_top_bg_platinum"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    
    <!-- 复制原有的机器人服务布局内容 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/farInside"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <View
            android:id="@+id/hugeReload"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/basic_ffff_4"
            android:layout_marginBottom="0dp"
            android:visibility="visible" />

        <RelativeLayout
            android:id="@+id/rc_conversation_portrait_rl"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="@dimen/rc_margin_size_12"
            android:layout_marginVertical="@dimen/rc_margin_size_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            
            <androidx.cardview.widget.CardView
                android:layout_width="@dimen/rc_conversation_portrait_size"
                android:layout_height="@dimen/rc_conversation_portrait_size"
                app:cardCornerRadius="24dp">
                <ImageView
                    android:id="@+id/rc_conversation_portrait"
                    android:layout_width="@dimen/rc_conversation_portrait_size"
                    android:layout_height="@dimen/rc_conversation_portrait_size"
                    android:scaleType="centerCrop"
                    android:src="@drawable/ic_chat_list_robot_service_mint" />
            </androidx.cardview.widget.CardView>
        </RelativeLayout>

        <TextView
            android:id="@+id/rc_conversation_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/rc_margin_size_12"
            android:layout_marginEnd="@dimen/rc_margin_size_12"
            android:ellipsize="end"
            android:singleLine="true"
            android:gravity="start"
            android:text="Customer Service"
            android:textColor="@color/rc_text_main_color"
            android:textSize="@dimen/rc_font_secondary_size"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/rc_conversation_portrait_rl"
            app:layout_constraintStart_toEndOf="@+id/rc_conversation_portrait_rl"
            app:layout_constraintTop_toTopOf="@+id/rc_conversation_portrait_rl" />
        <ImageView
            android:id="@+id/tinyLarge"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@mipmap/ic_system_icon_jade"
            android:layout_marginStart="5dp"
            app:layout_constraintTop_toTopOf="@+id/rc_conversation_title"
            app:layout_constraintBottom_toBottomOf="@+id/rc_conversation_title"
            app:layout_constraintStart_toEndOf="@+id/rc_conversation_title"
            />
        <View
            android:id="@+id/resetWithin"
            android:layout_width="wrap_content"
            android:layout_height="0.5dp"
            android:layout_marginTop="71.5dp"
            android:background="@color/rc_divider_color"
            app:layout_constraintStart_toStartOf="@id/rc_conversation_title"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout> 