<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/innerToggle"
    tools:context=".dialog.CountrySreenDialogFragment">

    <!-- TODO: Update blank fragment layout -->
    <RelativeLayout
        android:id="@+id/submitConnected"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="50dp">
        <ImageView
            android:layout_width="15dp"
            android:layout_height="10dp"
            android:src="@drawable/ic_md_country_arrow_frost"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="60dp"
            />
        <RelativeLayout
            android:id="@+id/surfaceDynamic"
            android:layout_marginTop="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:background="@drawable/bg_country_black_tan"
            >

            <com.juicy.common.utils.view.AutoWrapLineLayout
                android:id="@+id/maskUp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                />

        </RelativeLayout>


    </RelativeLayout>

</FrameLayout>