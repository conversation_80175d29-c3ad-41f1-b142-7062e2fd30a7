<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/resetDefault"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ProgressBar
            android:id="@+id/contentEmpty"
            android:indeterminateTint="#FFC4FF01"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/syncToggle"
            android:layout_marginTop="8dp"
            android:textSize="12sp"
            android:textColor="#FFC4FF01"
            android:text="@string/msg_failed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>