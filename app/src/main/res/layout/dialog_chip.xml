<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="center"
    android:layout_height="wrap_content">
    
<RelativeLayout
    android:id="@+id/compactNarrow"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="20dp"
    android:background="@drawable/bg_whit_c_frost"
    android:paddingVertical="12dp"
    android:paddingHorizontal="12dp"
    >
    <com.juicy.common.utils.view.CircleImageView
        android:id="@+id/fluidForeground"
        tools:src="@drawable/ic_coi_icon_crimson"
        android:layout_width="88dp"
        android:layout_height="88dp"
        android:layout_marginTop="30dp"
        android:layout_centerHorizontal="true"
        />
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/emptyBelow"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/ic_close_black_gray"
        android:layout_alignParentEnd="true"
        />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/avatarLarge"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="20sp"
        android:layout_marginHorizontal="20dp"
        android:layout_centerHorizontal="true"
        android:layout_below="@id/fluidForeground"
        android:textColor="#FF212121"
        android:fontFamily="@font/protest_strike_regular"
        android:maxLines="1"
        android:gravity="center"
        android:layout_marginTop="20dp"
        android:textStyle="bold"
        android:ellipsize="end"
        tools:text="dsadasdassadsadassadasdasdasdsadadsfsdfsfasdfsdfdsaf"
        />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/clockLast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/avatarLarge"
        android:layout_marginTop="4dp"
        android:textColor="#FF818181"
        android:textSize="12sp"
        tools:text="dasdasdadsadadas"
        android:layout_centerHorizontal="true"
        />
    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/shadowLower"
        android:layout_width="150dp"
        android:layout_height="90dp"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:layout_below="@id/clockLast"
        android:background="@drawable/bg_evaluate_btn_purple"
        android:layout_alignParentStart="true"
        android:layout_marginStart="9dp"
        >
        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_helpful_turquoise"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/hugeNear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/common_ok_45"
            android:textSize="12sp"
            android:includeFontPadding="false"
            />



    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/highlightedCard"
        android:layout_width="150dp"
        android:layout_height="90dp"
        android:orientation="vertical"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:background="@drawable/bg_evaluate_btn_purple"
        android:layout_below="@id/clockLast"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="9dp"
        >
        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_unhelpful_teal"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/gridFirst"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/common_ok_46"
            android:textSize="12sp"
            android:includeFontPadding="false"
            />

    </androidx.appcompat.widget.LinearLayoutCompat>



    
    
</RelativeLayout>

</RelativeLayout>