<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/basic_bbbf"
    android:layout_height="match_parent"
    tools:context=".anchorinfo.AnchorInfoActivity"
    >
    <androidx.core.widget.NestedScrollView
        android:id="@+id/switchBody"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        >
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >
            <!-- 占位图 -->
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/lastNarrow"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:src="@drawable/img_empty_big_full_pearl"
                android:scaleType="center"
                android:background="#E7E7E7"
                />
            <com.youth.banner.Banner
                android:id="@+id/headerAbove"
                android:layoutDirection="ltr"
                android:layout_width="match_parent"
                android:layout_height="300dp" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/frameSort"
                android:layout_width="41dp"
                android:layout_height="41dp"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="410dp"
                android:layout_marginEnd="30dp"
                android:src="@drawable/ic_like_color_jade" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_below="@id/headerAbove"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="-80dp"
                android:layout_marginEnd="0dp"
                >

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:src="@drawable/bg_info_bg_red"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_marginTop="68dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    />


                <RelativeLayout
                    android:id="@+id/compactUpload"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    >

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:id="@+id/labelCompact"
                        android:layout_marginTop="80dp"
                        android:orientation="horizontal">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/flexibleVisible"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            android:layout_width="72dp"
                            android:layout_height="72dp"
                            android:layout_gravity="center_vertical"
                            android:padding="2dp"
                            android:background="@drawable/bg_anchor_avatar_bg_gold"
                            >
                            <com.juicy.common.utils.RoundImageView
                                android:id="@+id/acrossLocked"
                                android:layout_width="68dp"
                                android:layout_height="68dp"
                                app:type="circle"
                                android:src="@drawable/img_empty_big_full_pearl"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintBottom_toBottomOf="parent"
                                />
                            <LinearLayout
                                android:id="@+id/abovePreview"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@drawable/bg_status_charcoal"
                                android:gravity="center"
                                android:visibility="gone"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintBottom_toBottomOf="parent"
                                android:paddingHorizontal="4dp"
                                >

                                <View
                                    android:id="@+id/copyLeft"
                                    android:layout_width="9dp"
                                    android:layout_height="9dp"
                                    android:layout_marginEnd="6dp" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/xlFilter"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textSize="11sp" />
                            </LinearLayout>
                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <LinearLayout
                            android:id="@+id/contentClock"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            android:layout_marginStart="12dp"
                            app:layout_constraintStart_toEndOf="@id/flexibleVisible"
                            >

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/visibleMenu"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:lines="1"
                                android:ellipsize="end"
                                android:textAlignment="center"
                                android:gravity="start"
                                android:textColor="@color/black"
                                android:textSize="20sp"
                                android:fontFamily="@font/protest_strike_regular"
                                android:textStyle="bold"
                                tools:text="Name" />

                            <!--用户在线状态-->
                            <!--用户信息-->
                            <androidx.appcompat.widget.LinearLayoutCompat
                                android:id="@+id/adaptivePanel"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/contentClock"
                                android:layout_marginTop="8dp"
                                android:gravity="center"
                                android:layout_centerInParent="true"
                                android:orientation="horizontal">
                                <LinearLayout
                                    android:id="@+id/xsMedium"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal"
                                    android:paddingVertical="2dp"
                                    android:gravity="center_vertical"
                                    android:paddingHorizontal="4dp"
                                    android:layout_gravity="center_vertical"
                                    android:background="@drawable/bg_famel_olive"
                                    >
                                    <ImageView
                                        android:id="@+id/brightThin"
                                        android:layout_width="12dp"
                                        android:layout_height="12dp"
                                        android:visibility="gone"
                                        />
                                    <TextView
                                        android:id="@+id/autoTimer"
                                        android:layout_width="26dp"
                                        android:layout_height="16dp"
                                        android:textColor="@color/white"
                                        android:gravity="center"
                                        android:textSize="12sp"
                                        tools:text="18"
                                        android:textStyle="bold"
                                        />
                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:layout_marginStart="8dp"
                                    android:background="@drawable/bg_wall_location_cherry"
                                    android:layout_weight="1"
                                    >
                                    <LinearLayout
                                        android:id="@+id/aboveSave"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        android:paddingVertical="2dp"
                                        android:paddingHorizontal="4dp"
                                        android:gravity="center_vertical"
                                        android:background="@drawable/bg_wall_location_cherry"

                                        >

                                        <androidx.appcompat.widget.AppCompatImageView
                                            android:layout_width="16dp"
                                            android:layout_height="16dp"
                                            android:layout_marginEnd="2dp"
                                            android:visibility="gone"
                                            android:src="@drawable/ic_local_white" />

                                        <androidx.appcompat.widget.AppCompatTextView
                                            android:id="@+id/maximalBtn"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:ellipsize="end"
                                            android:textColor="@color/white"
                                            android:textSize="12sp"
                                            android:textStyle="normal"
                                            android:maxLines="1"
                                            tools:text="USA" />


                                    </LinearLayout>
                                </LinearLayout>
                            </androidx.appcompat.widget.LinearLayoutCompat>

                        </LinearLayout>



                    </androidx.constraintlayout.widget.ConstraintLayout>


                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/pickerWarning"
                        android:layout_width="41dp"
                        android:layout_height="41dp"
                        android:src="@drawable/ic_chat_frost"
                        android:layout_alignParentTop="true"
                        android:layout_marginTop="50dp"
                        android:visibility="gone"
                        android:layout_marginStart="30dp"/>



                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/selectedInfo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/labelCompact"
                        android:layout_marginHorizontal="0dp"
                        android:layout_marginTop="16dp"
                        android:textColor="@color/black"
                        android:gravity="start"
                        android:textSize="12sp"
                        tools:text="dsadasdsasdasdsasadasdasadadsaa" />

                    <LinearLayout
                        android:id="@+id/withinDefault"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/selectedInfo"
                        android:layout_marginStart="0dp"
                        android:layout_marginTop="20dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        >
                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@mipmap/ic_info_video_icon_turquoise"
                            />
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/dragMicro"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/common_ok_5"
                            android:textColor="@color/black"
                            android:textSize="18sp"
                            android:layout_marginStart="6dp"
                            android:fontFamily="@font/protest_strike_regular"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/activeBright"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/withinDefault"
                        android:layout_marginHorizontal="0dp"
                        android:layout_marginTop="12dp" />

                    <LinearLayout
                        android:id="@+id/extendedDownload"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/activeBright"
                        android:layout_marginStart="0dp"
                        android:layout_marginTop="20dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        >
                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:scaleType="fitCenter"
                            android:src="@mipmap/ic_info_imp_icon_tan"
                            />
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/mainDisabled"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/protest_strike_regular"
                            android:layout_marginStart="6dp"
                            android:text="@string/btn_remove"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>


                    <com.juicy.common.utils.view.AutoWrapLineLayout
                        android:id="@+id/switchBright"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/extendedDownload"
                        android:layout_marginHorizontal="0dp"
                        android:layout_marginTop="12dp" />

                    <LinearLayout
                        android:id="@+id/counterChip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/switchBright"
                        android:layout_marginStart="0dp"
                        android:layout_marginTop="20dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        >
                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@mipmap/ic_info_gift_icon_charcoal"
                            />
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/outerInfo"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/common_ok_6"
                            android:fontFamily="@font/protest_strike_regular"
                            android:layout_marginStart="6dp"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/normalLeft"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/counterChip"
                        android:layout_marginHorizontal="0dp"
                        android:layout_marginBottom="106dp"
                        android:layout_marginTop="12dp" />


                </RelativeLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/errorMaximal"
                    android:layout_width="wrap_content"
                    android:layout_height="28dp"
                    android:gravity="center_vertical"
                    android:layout_marginTop="100dp"
                    android:textDirection="ltr"
                    android:layoutDirection="ltr"
                    app:layout_constraintTop_toTopOf="@id/compactUpload"
                    app:layout_constraintEnd_toEndOf="parent"
                    >

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/sliderShow"
                        android:layout_width="98dp"
                        android:layout_height="26dp"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:layout_marginStart="18dp"
                        android:background="@drawable/ic_anchor_info_vip_price_bg1_turquoise"/>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/firstDownload"
                        android:layout_width="36dp"
                        android:layout_height="28dp"
                        android:src="@drawable/ic_vip_pop_king_icon_platinum"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"/>

                    <TextView
                        android:id="@+id/rigidHuge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="/time"
                        android:visibility="visible"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/lastPlay"
                        android:textDirection="ltr"
                        android:textSize="14sp" />
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/lastPlay"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/autoBody"
                        android:layout_marginStart="1dp"
                        android:src="@drawable/ic_coi_icon_crimson" />
                    <TextView
                        android:id="@+id/autoBody"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:layout_gravity="center"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/firstDownload"
                        android:text="100"
                        android:layout_marginStart="1dp"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:textSize="12sp" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginEnd="10dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/errorMaximal">


                    <TextView
                        android:id="@+id/sliderOnline"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:layout_gravity="center"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/firstDownload"
                        android:text="100"
                        android:layout_marginStart="1dp"
                        android:textColor="#FF33373E"
                        android:textSize="12sp" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/firstResponsive"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="1dp"
                        android:src="@drawable/ic_coi_icon_crimson" />

                    <TextView
                        android:id="@+id/customIcon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="/time"
                        android:visibility="visible"
                        android:textColor="#FF33373E"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/lastPlay"
                        android:textDirection="ltr"
                        android:textSize="14sp" />

                </androidx.appcompat.widget.LinearLayoutCompat>

            </androidx.constraintlayout.widget.ConstraintLayout>


        </RelativeLayout>

    </androidx.core.widget.NestedScrollView>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:fitsSystemWindows="true"
        android:layout_marginTop="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"

        >

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/emptyBadge"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:src="@drawable/ic_anchor_back_maroon" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/secondaryFirst"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_alignParentEnd="true"
                android:src="@drawable/ic_right_icon_frost" />


    </RelativeLayout>
    <LinearLayout
        android:layout_alignParentEnd="true"
        android:layout_marginBottom="33dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginHorizontal="16dp"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="58dp"
            android:visibility="visible"
            tools:visibility="visible"
           >

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/onlineReload"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="0.4"
                    app:layout_constraintStart_toStartOf="parent"
                    android:background="@mipmap/ic_anchor_info_chat_bg_lead"
                    >

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/playShort"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@mipmap/ic_anchor_info_chat_icon_charcoal"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:layout_marginStart="30dp"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/surfaceAcross"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Chat"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/protest_strike_regular"
                        app:layout_constraintTop_toTopOf="@id/playShort"
                        app:layout_constraintBottom_toBottomOf="@id/playShort"
                        app:layout_constraintStart_toEndOf="@id/playShort"
                        android:layout_marginStart="10dp"/>


                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/textToggle"
                    android:layout_width="0dp"
                    android:layout_weight="0.6"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:layoutDirection="ltr"
                    android:textDirection="ltr"
                    android:background="@mipmap/ic_anchor_info_call_online_ash"
                    >

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/badgeEnd"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@mipmap/ic_anchor_info_online_icon_lemon" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/refreshClear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:layout_marginStart="10dp"
                        android:shadowColor="#FF000000"
                        android:shadowRadius="1.5"
                        android:shadowDx="0"
                        android:textStyle="bold"
                        android:shadowDy="0"
                        android:strokeWidth="3.0"
                        android:strokeColor="#FF000000"
                        android:fontFamily="@font/protest_strike_regular"
                        android:textSize="16sp"
                        tools:text="Video Call" />

                </androidx.appcompat.widget.LinearLayoutCompat>

            </androidx.appcompat.widget.LinearLayoutCompat>




        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

    <com.youth.banner.Banner
        android:id="@+id/openUpload"
        android:layoutDirection="ltr"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        android:visibility="gone"
        />
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/avatarActive"
        android:layout_width="34dp"
        android:layout_height="34dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="20dp"
        android:visibility="gone"
        android:layout_marginStart="12dp"
        android:src="@drawable/ic_anchor_back_maroon" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/onlineSwipe"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="20dp"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:visibility="gone"
        android:layout_marginEnd="16dp"
        android:layout_alignParentEnd="true"
        android:src="@drawable/ic_right_icon_frost" />

    <LinearLayout
        android:id="@+id/overlayProgress"
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:orientation="horizontal"
        android:gravity="bottom"
        android:visibility="gone"
        tools:visibility="gone"
        >
        <LinearLayout
            android:id="@+id/staticSlider"
            android:layout_width="0dp"
            android:layout_weight="0.4"
            android:background="@mipmap/ic_anchor_info_chat_bg_lead"
            android:layout_height="match_parent">
            <ImageView
                android:id="@+id/pauseMask"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="16dp"
                android:src="@mipmap/ic_anchor_info_chat_icon_charcoal"
                android:layout_gravity="center_vertical"
                />
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/errorCalendar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Chat"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                android:fontFamily="@font/protest_strike_regular"
               android:layout_gravity="center_vertical"
                android:layout_marginStart="10dp"/>

        </LinearLayout>


        <LinearLayout
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="0dp"
            android:layout_weight="0.6"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            >
            <LinearLayout
                android:id="@+id/confirmActive"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:height="38dp"
                android:gravity="center_vertical"
                android:visibility="gone"
                android:background="@drawable/ic_anchor_vip_bg_steel"
                android:textDirection="ltr"
                android:layoutDirection="ltr"
                >
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="25dp"
                    android:text="VIP: "
                    android:textColor="@color/custom_4200"
                    android:textFontWeight="@integer/m3_sys_motion_duration_long2"
                    android:textDirection="ltr"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:src="@drawable/ic_coin_mist" />
                <TextView
                    android:id="@+id/distantPicker"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    android:layout_gravity="center"
                    android:text="100/min"
                    android:textDirection="ltr"
                    android:textColor="@color/custom_4200"
                    android:textFontWeight="@integer/m3_sys_motion_duration_long2"
                    android:textSize="12sp" />

            </LinearLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/briefWithin"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@mipmap/ic_anchor_info_call_online_ash"
                >

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/calendarFrame"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:layoutDirection="ltr"
                    android:textDirection="ltr"
                    android:visibility="visible"
                    >

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/disabledOverlay"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@mipmap/ic_anchor_info_online_icon_lemon" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/dragDrop"
                        android:layout_width="16dp"
                        android:visibility="gone"
                        android:layout_height="16dp"
                        android:src="@drawable/ic_coin_mist" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/surfaceDefault"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:layout_marginStart="10dp"
                        android:shadowColor="#FF000000"
                        android:shadowRadius="1.5"
                        android:shadowDx="0"
                        android:textStyle="bold"
                        android:shadowDy="0"
                        android:fontFamily="@font/protest_strike_regular"
                        android:strokeWidth="3.0"
                        android:strokeColor="#FF000000"
                        android:visibility="visible"
                        android:textDirection="ltr"
                        android:text="Video Call" />
                </androidx.appcompat.widget.LinearLayoutCompat>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>

