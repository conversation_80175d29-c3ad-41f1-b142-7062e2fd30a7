<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/black"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

 <androidx.constraintlayout.widget.ConstraintLayout
     android:layout_width="183dp"
     android:layout_height="58dp"
     android:layout_marginStart="20dp"
     app:layout_constraintTop_toTopOf="parent"
     android:background="@drawable/bg_top_info_bg_sage"
     app:layout_constraintStart_toStartOf="parent">
     
     <com.juicy.common.utils.view.CircleImageView
         android:id="@+id/clearLoaded"
         android:layout_width="40dp"
         android:layout_height="40dp"
         android:layout_marginStart="8dp"
         app:layout_constraintTop_toTopOf="parent"
         app:layout_constraintBottom_toBottomOf="parent"
         app:layout_constraintStart_toStartOf="parent"/>

     <androidx.appcompat.widget.AppCompatTextView
         android:id="@+id/copyDown"
         android:layout_width="wrap_content"
         android:layout_height="wrap_content"
         tools:text="Beatrice"
         android:layout_marginStart="6dp"
         app:layout_constraintStart_toEndOf="@id/clearLoaded"
         android:textSize="18sp"
         app:layout_constraintTop_toTopOf="@id/clearLoaded"
         android:textColor="@color/white"
         android:fontFamily="@font/protest_strike_regular"/>

     <androidx.appcompat.widget.AppCompatTextView
         android:id="@+id/autoTimer"
         android:layout_width="26dp"
         android:layout_height="wrap_content"
         android:textColor="@color/white"
         android:gravity="center"
         android:textSize="12sp"
         tools:text="18"
         android:textStyle="bold"
         android:paddingVertical="2dp"
         android:paddingHorizontal="4dp"
         android:background="@drawable/bg_famel_olive"
         app:layout_constraintTop_toBottomOf="@id/copyDown"
         app:layout_constraintStart_toStartOf="@id/copyDown"
         />

     <androidx.appcompat.widget.AppCompatTextView
         android:id="@+id/maximalBtn"
         android:layout_width="wrap_content"
         android:layout_height="wrap_content"
         android:ellipsize="end"
         android:textColor="@color/white"
         android:textSize="12sp"
         android:textStyle="normal"
         android:layout_marginStart="6dp"
         app:layout_constraintTop_toTopOf="@id/autoTimer"
         app:layout_constraintStart_toEndOf="@id/autoTimer"
         android:maxLines="1"
         android:background="@drawable/bg_wall_location_cherry"
         android:paddingVertical="2dp"
         android:paddingHorizontal="4dp"
         tools:text="USA" />


     <androidx.appcompat.widget.AppCompatImageView
         android:layout_width="28dp"
         android:layout_height="30dp"
         android:src="@drawable/ic_like_gray_mint"
         android:layout_marginEnd="8dp"
         app:layout_constraintEnd_toEndOf="parent"
         app:layout_constraintTop_toTopOf="parent"
         app:layout_constraintBottom_toBottomOf="parent"/>
     
 </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/backgroundBody"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_report_cherry"
            android:layout_marginEnd="16dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/sliderBar"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/sliderBar"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_back_close_ebony"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>