<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/black"
    android:splitMotionEvents="false"
    android:fitsSystemWindows="true"
    tools:context=".CallingFragment">
    <FrameLayout
        android:id="@+id/lgShow"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="0dp"
        android:layout_marginTop="0dp"
        android:layout_marginEnd="0dp"
        />
    <!-- 等待主播加入 -->
    <TextView
        android:id="@+id/dropWithin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="The anchor is joining..."
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintTop_toTopOf="parent"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/maskDynamic"
        android:layout_width="wrap_content"
        android:layout_height="300dp"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="20dp"
        tools:listitem="@layout/item_divider"
        app:layout_constraintBottom_toTopOf="@id/selectedMini"
        app:layout_constraintStart_toStartOf="parent"
        />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adaptivePanel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/bg_top_info_bg_sage"
        android:paddingHorizontal="8dp"
        android:paddingVertical="6dp"
       app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:id="@+id/hugeSelected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">
            <com.juicy.common.utils.view.CircleImageView
                android:id="@+id/fluidForeground"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                tools:src="@drawable/ic_coi_icon_crimson"
                />
        </RelativeLayout>
       <androidx.constraintlayout.widget.ConstraintLayout
           android:id="@+id/footerBack"
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintBottom_toBottomOf="parent"
           app:layout_constraintStart_toEndOf="@id/hugeSelected"
           >
           <androidx.appcompat.widget.AppCompatTextView
               android:id="@+id/avatarLarge"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:textColor="@color/white"
               android:singleLine="true"
               app:layout_constraintStart_toStartOf="parent"
               android:layout_marginStart="12dp"
               android:textSize="18sp"
               android:fontFamily="@font/protest_strike_regular"
               android:gravity="start|center_vertical"
               android:includeFontPadding="false"
               android:text="Name"
               android:textStyle="bold"
               app:layout_constraintTop_toTopOf="parent"
               />
           <androidx.appcompat.widget.AppCompatTextView
               android:id="@+id/autoTimer"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:textSize="12sp"
               android:textColor="@color/white"
               android:layout_marginStart="12dp"
               android:includeFontPadding="false"
               tools:text="22"
               android:paddingVertical="2dp"
               android:paddingHorizontal="4dp"
               android:background="@drawable/bg_famel_olive"
               app:layout_constraintTop_toBottomOf="@id/avatarLarge"
               app:layout_constraintStart_toStartOf="parent"
               />
           <androidx.appcompat.widget.AppCompatTextView
               android:id="@+id/playGrid"
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:textSize="12sp"
               android:textColor="@color/white"
               tools:text="USA"
               android:layout_marginStart="4dp"
               android:background="@drawable/bg_wall_location_cherry"
               android:paddingVertical="2dp"
               android:paddingHorizontal="4dp"
               android:includeFontPadding="false"
               app:layout_constraintTop_toBottomOf="@id/avatarLarge"
               app:layout_constraintStart_toEndOf="@id/autoTimer"
               />

       </androidx.constraintlayout.widget.ConstraintLayout>
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/frameSort"
            android:layout_width="28dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_like_gray_mint"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/footerBack"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>


    <RelativeLayout
        android:id="@+id/pressedOuter"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:padding="4dp"
        android:background="@drawable/bg_gray_black_mint"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintTop_toTopOf="@id/adaptivePanel"
        app:layout_constraintBottom_toBottomOf="@id/adaptivePanel"
        app:layout_constraintEnd_toStartOf="@id/extendedUpper"
        android:layout_marginTop="8dp"
        >
        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/ic_report_cherry"
            />

    </RelativeLayout>
    <RelativeLayout
        android:id="@+id/extendedUpper"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:padding="4dp"
        android:background="@drawable/bg_gray_black_mint"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintTop_toTopOf="@id/adaptivePanel"
        app:layout_constraintBottom_toBottomOf="@id/adaptivePanel"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="8dp"
        >
        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/ic_back_close_ebony"
            />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/largeSecondary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:minHeight="24dp"
        android:paddingHorizontal="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/adaptivePanel"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_free_bg_turquoise"
        android:visibility="gone"
        >

        <TextView

            android:id="@+id/viewAvatar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="After  10s，you’ll be charged 100 per minute."
            android:textColor="@color/white"
            android:maxLines="2"
            android:textSize="12sp"
            android:gravity="center"
            android:layout_centerVertical="true"
            android:textFontWeight="400"
            />
<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_gravity="center"-->
<!--            android:layout_centerInParent="true"-->
<!--            >-->

<!--            <ImageView-->
<!--                android:layout_width="21dp"-->
<!--                android:layout_height="17dp"-->
<!--                android:src="@drawable/ic_match_free_icon_gold"-->
<!--                android:layout_gravity="center_vertical"-->
<!--                android:layout_marginStart="5dp"-->
<!--                />-->

<!--           -->

<!--        </LinearLayout>-->

    </RelativeLayout>

    <com.juicy.common.utils.view.DraggableView
        android:id="@+id/customTitle"
        android:layout_width="112dp"
        android:layout_height="169dp"
        app:layout_constraintTop_toBottomOf="@+id/pressedOuter"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="28dp"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <LinearLayout
        android:id="@+id/extendedFit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="20dp"
        android:gravity="bottom"
        android:orientation="horizontal"
        android:visibility="invisible"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/updateSelected"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            >
            <LinearLayout
                android:id="@+id/thinPanel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_video_im_bg_1_olive"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:layout_weight="1"
                android:padding="2dp">

                <com.juicy.common.utils.view.CircleImageView
                    android:id="@+id/activeAuto"
                    android:layout_width="24dp"
                    android:layout_gravity="center_vertical"
                    android:layout_height="24dp"
                    />

                <TextView
                    android:id="@+id/defaultField"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:layout_weight="1"
                    app:autoSizeTextType="uniform"
                    tools:text="dsadsasasdssssssssssssdfasddddddddddddddadssssss" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/stretchLong"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@drawable/bg_video_im_bg_2_charcoal"
                android:orientation="horizontal"

                >

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/xsPaste"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:layout_gravity="center_vertical"
                    />

                <TextView
                    android:id="@+id/connectedAvatar"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="4dp"
                    android:background="@drawable/bg_send_bg_azure"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="center"
                    android:text="send"
                    android:maxLines="1"
                    android:textSize="14sp"
                    app:autoSizeTextType="uniform"
                    android:autoSizeMinTextSize="9sp"
                    android:textColor="@color/black"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>


    </LinearLayout>
    <RelativeLayout
        android:id="@+id/updateSelected"
        android:layout_width="wrap_content"
        android:layout_height="64dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="16dp"
        android:layout_marginTop="211dp"
        android:visibility="invisible"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/briefFluid"
            android:layout_width="146dp"
            android:layout_height="54dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_gift_bg_beige" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/responsiveLower"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="0dp"
            />

        <TextView
            android:id="@+id/shortInput"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/responsiveLower"
            android:layout_centerVertical="true"
            android:textColor="@color/white"
            android:textStyle="italic"
            android:layout_marginStart="10dp"
            android:textSize="30sp"
            android:shadowDx="3"
            tools:text="dasdas"
            android:shadowDy="3"
            android:shadowRadius="3.0" />
    </RelativeLayout>
    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/selectedMini"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/swipeDelete"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="24dp"
        android:orientation="horizontal">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/reloadList"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:layout_marginStart="16dp"
            android:src="@drawable/raw_video_gift_icon_lavender"
            />
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/focusedSmall"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:layout_marginStart="12dp"
            android:src="@drawable/raw_video_coin_icon_turquoise"
            />
        <RelativeLayout
            android:id="@+id/syncCopy"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@drawable/bg_call_bottom_sapphire"
            android:layout_marginHorizontal="8dp"
            android:paddingHorizontal="14dp"
            android:layout_marginBottom="8dp"
            >
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/inactiveBottom"
                android:layout_width="20dp"
                android:layout_centerVertical="true"
                android:layout_height="20dp"
                android:src="@drawable/ic_emoji_indigo"
                />
            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/pasteEdit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:layout_centerVertical="true"
                android:textColor="@color/white"
                android:hint="@string/common_ok_37"
                android:background="@null"
                android:textColorHint="#939099"
                android:layout_marginHorizontal="12dp"
                android:layout_toEndOf="@id/inactiveBottom"
                android:layout_toStartOf="@id/mdBackground"
                />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/mdBackground"
                android:layout_width="38dp"
                android:layout_height="38dp"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:src="@drawable/bg_send_button_lemon"
                />

        </RelativeLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>

<!--    <androidx.appcompat.widget.AppCompatImageView-->
<!--        android:id="@+id/microUpdate"-->
<!--        android:layout_width="300dp"-->
<!--        android:layout_height="300dp"-->
<!--        android:layout_centerInParent="true"-->
<!--        android:src="@drawable/anim_gift_animal_bg_green"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        android:visibility="gone"-->
<!--        tools:visibility="visible" />-->
<!--    <androidx.appcompat.widget.AppCompatImageView-->
<!--        android:id="@+id/largeWithin"-->
<!--        android:layout_width="150dp"-->
<!--        android:layout_height="150dp"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintEnd_toEndOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        />-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/swipeDelete"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        >
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/pasteContent"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/mdDimmed"

            />
        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/mdDimmed"
            android:layout_width="match_parent"
            android:layout_height="22dp"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />
           <androidx.appcompat.widget.AppCompatImageView
               android:id="@+id/coordinatorLeft"
               android:layout_width="20dp"
               android:layout_height="20dp"
               android:src="@drawable/ic_my_delete_olive"
              app:layout_constraintEnd_toEndOf="parent"
              android:layout_marginEnd="30dp"
               app:layout_constraintBottom_toBottomOf="parent"
               android:layout_marginBottom="10dp"
               />



    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/updateSelect"
        android:layout_width="0dp"
        android:layout_height="92dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="10dp"
        android:fitsSystemWindows="true"
        android:background="@drawable/bg_not_coin_bg_magenta"
        android:orientation="horizontal"
        android:visibility="gone"
        tools:visibility="gone"
        >

        <ImageView
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:src="@drawable/ic_coi_icon_crimson"
            android:layout_marginStart="12dp"
            android:layout_marginTop="20dp"
            />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:orientation="vertical"
            >
            <TextView
                android:id="@+id/largeClosed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="20sp"
                android:textColor="#1A1A1A"
                android:maxLines="1"
                app:autoSizeTextType="uniform"
                android:autoSizeMinTextSize="10sp"
                android:text="Coins not enough"
                android:textFontWeight="500"

                />
            <TextView
                android:id="@+id/xsUnselected"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:maxLines="1"
                app:autoSizeTextType="uniform"
                android:autoSizeMinTextSize="10sp"
                android:text="Video duration remaining:55s"
                android:textSize="14sp"
                android:textColor="#5F5F5F"
                />

        </LinearLayout>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            >

            <ImageView
                android:id="@+id/mdDynamic"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="-5dp"
                android:layout_marginTop="5dp"
                android:layout_alignEnd="@+id/nextDisconnected"
                android:src="@drawable/ic_vip_dialog_close_yellow"
                />

            <TextView
                android:id="@+id/nextDisconnected"
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:layout_centerVertical="true"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:text="Recharge"
                app:autoSizeTextType="uniform"
                android:autoSizeMinTextSize="8sp"
                android:maxLines="1"
                android:layout_marginEnd="10dp"
                android:background="@drawable/bg_recharge_bg_yellow"
                />

        </RelativeLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>