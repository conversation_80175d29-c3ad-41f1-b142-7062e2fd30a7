<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/timerFront"
    android:background="@drawable/ic_full_coin_dialog_bg_salmon"
    android:fitsSystemWindows="false"
    android:layout_height="match_parent">



    <RelativeLayout
        android:layout_width="match_parent"
        android:fitsSystemWindows="true"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/wrapperCustom"
            android:layout_width="match_parent"
            android:layout_height="181dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />



        <!--            <LinearLayout-->
        <!--                android:id="@+id/xlScroll"-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="wrap_content">-->
        <!--                -->
        <!--            </LinearLayout>-->

        <androidx.cardview.widget.CardView
            android:id="@+id/gridPreview"
            android:layout_width="96dp"
            android:layout_height="6dp"
            app:cardCornerRadius="3dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="8dp"
            android:background="#ffffff"/>

        <LinearLayout
            android:id="@+id/xlScroll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <RelativeLayout
                android:id="@+id/menuOpen"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_marginTop="12dp"
                android:visibility="visible"
                >

                <ImageView
                    android:id="@+id/emptyBadge"
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:src="@drawable/ic_full_close_dialog_lime"
                    android:layout_marginEnd="16dp"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    />

            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/maximalCounter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="15dp"
                android:visibility="gone"
                android:text="@string/common_ok_23"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/confirmPaste"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/xlScroll"
            android:gravity="center"
            android:orientation="horizontal">
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/mediumLast"
                android:layout_width="38dp"
                android:layout_height="38dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/ic_coi_icon_crimson" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/wideClose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:textColor="@color/black"
                android:textSize="30sp"
                android:layout_marginStart="14dp"
                android:textStyle="bold"
                tools:text="dasdasd" />

        </LinearLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/wrapperCustom" />



        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:paddingTop="16dp"
            android:layoutDirection="ltr"
            android:layout_below="@id/confirmPaste"
            android:layout_marginTop="10dp"
            android:layout_marginHorizontal="16dp"
            android:orientation="vertical"
            >

            <com.juicy.common.view.MdBannerView
                android:id="@+id/headerAbove"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_below="@id/wrapperCustom"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="10dp"
                android:visibility="gone"
                />

            <TextView
                android:id="@+id/elasticPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="Recharge or become VIP to chat unlimited times"
                android:gravity="center_horizontal"
                android:textColor="@color/material_3333"
                android:layout_marginBottom="10dp"
                android:visibility="gone"
                />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/xxlUpper"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="8dp"
                android:layout_weight="1"
                />
            
            <LinearLayout
                android:id="@+id/gridExpand"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:orientation="horizontal"
                >
                <ImageView

                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_profile_custom_bronze"
                    android:layout_gravity="center_vertical"
                    />
                <TextView
                    android:id="@+id/lockedFill"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:textSize="12sp"
                    android:textColor="#FF656D7B"
                    android:text="Customer Service"
                    android:layout_gravity="center_vertical"
                    />

            </LinearLayout>


        </LinearLayout>
    </RelativeLayout>

<!--    <androidx.core.widget.NestedScrollView-->
<!--        android:layout_width="match_parent"-->
<!--        android:id="@+id/unfocusedPressed"-->
<!--        android:background="@drawable/bg_shop_top_bg_mint"-->
<!--        android:layout_height="match_parent">-->
<!--        -->
<!--    </androidx.core.widget.NestedScrollView>-->
</RelativeLayout>