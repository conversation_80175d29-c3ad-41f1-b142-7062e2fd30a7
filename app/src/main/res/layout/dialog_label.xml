<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_conversation_longtop_bg_snow"
    tools:context=".dialog.ConversationLongDialog">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        >
        <RelativeLayout
            android:id="@+id/wideMain"
            android:layout_width="match_parent"
            android:layout_height="58dp">

            <TextView
                android:id="@+id/fluidBeside"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="#1A1A1A"
                android:textSize="16sp"
                android:textStyle="bold"
                android:text="Stick on Top" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E8EBEB"
            />

        <RelativeLayout
            android:id="@+id/fitUnfocused"
            android:layout_width="match_parent"
            android:layout_height="58dp">

            <TextView
                android:id="@+id/toggleContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Remove from chat list"
                android:textColor="#1A1A1A"
                android:textStyle="bold"
                android:textSize="16sp" />
        </RelativeLayout>


    </LinearLayout>


</FrameLayout>