<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingVertical="20dp"
    android:paddingHorizontal="16dp"
    android:background="@drawable/bg_gift_dialog_lime">
   <androidx.appcompat.widget.AppCompatImageView
       android:id="@+id/mediumLast"
       android:layout_width="32dp"
       android:layout_height="32dp"
       android:src="@drawable/ic_coi_icon_crimson"
       app:layout_constraintStart_toStartOf="parent"
       app:layout_constraintTop_toTopOf="parent"
       />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/unselectedNext"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="24sp"
        android:textStyle="bold"
        tools:text="1000"
        app:layout_constraintStart_toEndOf="@id/mediumLast"
        app:layout_constraintTop_toTopOf="@id/mediumLast"
        app:layout_constraintBottom_toBottomOf="@id/mediumLast"
        android:layout_marginStart="9dp"
        />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/normalWide"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:background="@drawable/bg_submit_navy"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:gravity="center"
        android:text="@string/common_ok_39"
        android:paddingHorizontal="16dp"
        android:paddingVertical="4dp"
        android:includeFontPadding="false"
        android:textColor="#FF222222"
        />
   <androidx.recyclerview.widget.RecyclerView
       android:id="@+id/miniText"
       android:layout_width="match_parent"
       android:layout_height="280dp"
       app:layout_constraintTop_toBottomOf="@id/normalWide"
       android:layout_marginTop="20dp"
       android:fadeScrollbars="false"
       android:scrollbarSize="5dp"
       android:scrollbarStyle="outsideInset"


       />

    <LinearLayout
        android:id="@+id/rigidCancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="10dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        >

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>