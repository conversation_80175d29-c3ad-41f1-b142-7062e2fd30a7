<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:layout_marginBottom="12dp">
    <com.juicy.common.utils.view.CircleImageView
        android:id="@+id/fluidForeground"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginEnd="8dp"
        />

    <RelativeLayout
        android:id="@+id/forwardAdd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_chat_bg_cyan"
        android:padding="12dp"
        android:layout_toEndOf="@+id/fluidForeground"
        >


        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            >

            <LinearLayout
                android:id="@+id/footerFooter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                >
                <TextView
                    android:id="@+id/downloadHuge"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="start"
                    android:maxWidth="184dp"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:layout_weight="1"
                    tools:text="dsadasdadsasadasdaasdasdasdsa" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/highlightedMedium"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_gravity="center_vertical"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>



            <View
                android:id="@+id/stretchLong"
                android:layout_width="wrap_content"
                android:layout_height="0.5dp"
                android:layout_below="@+id/footerFooter"
                android:layout_marginTop="4dp"
                android:layout_toStartOf="@+id/footerFooter"
                android:layout_toEndOf="@+id/footerFooter"
                android:background="@color/white"
                android:visibility="gone"
                tools:visibility="visible"/>
            <ProgressBar
                android:id="@+id/enabledLg"
                style="?android:attr/progressBarStyle"
                android:layout_marginTop="4dp"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_below="@+id/stretchLong"
                android:indeterminate="true"
                android:indeterminateTint="@color/material_8aff"
                android:layout_centerInParent="true"

                />

            <TextView
                android:id="@+id/connectedLg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/stretchLong"
                android:layout_marginTop="4dp"
                android:gravity="start"
                android:maxWidth="184dp"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="dsadasda" />

        </RelativeLayout>


    </RelativeLayout>


</RelativeLayout>
