<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cancelSurface"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_main_header_cyan"
    android:orientation="vertical">

          <androidx.recyclerview.widget.RecyclerView
              android:id="@+id/narrowLoading"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:layout_marginTop="30dp"
              app:layout_constraintTop_toTopOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              android:layout_marginStart="16dp"
              />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/autoHeader"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/white"
            android:layout_marginTop="20dp"
            app:layout_constraintTop_toBottomOf="@id/narrowLoading"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"

            />
</androidx.constraintlayout.widget.ConstraintLayout>