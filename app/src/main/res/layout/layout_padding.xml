<?xml version="1.0" encoding="utf-8"?>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tool="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_three_pay_bg_mist">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        >

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/emptyBelow"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_close_gray_indigo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/maximalCounter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:text="@string/common_ok_33"
            android:textColor="#FF232839"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginStart="13dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tool:text="Payment method" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/narrowOutside"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:layout_marginStart="13dp"
            android:layout_marginEnd="13dp"
            android:background="@drawable/bg_pay_channel_coin_bg_crimson"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/maximalCounter">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                >

                <TextView
                    android:id="@+id/pickForward"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="Item"
                    android:textColor="#313131"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginStart="13dp"
                    />

                <View
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    />

                <TextView
                    android:id="@+id/closedMenu"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="Price"
                    android:textColor="#313131"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginEnd="13dp"
                    />

            </LinearLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="#D2D2D2"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:orientation="horizontal"
                >

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_gravity="center"
                    android:layout_marginStart="13dp"

                    >
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/downSelected"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_coi_icon_crimson" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/primaryUp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        tool:text="600"
                        android:textColor="#FF232839"
                        android:textSize="18sp"
                        android:textStyle="bold" />
                    <LinearLayout
                        android:id="@+id/firstShadow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:paddingHorizontal="4dp"
                        android:layout_marginStart="4dp"
                        android:visibility="visible"
                        android:background="@drawable/ic_coin_channel_ex_bg_bronze"
                        >
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/innerEmpty"
                            android:layout_width="wrap_content"
                            android:textSize="9sp"
                            tool:text="+1000"
                            android:layout_gravity="center"
                            android:textColor="@color/white"
                            android:layout_height="wrap_content"
                            />

                    </LinearLayout>


                </androidx.appcompat.widget.LinearLayoutCompat>

                <View
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/frameAround"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tool:text="dsaa"
                    android:textColor="#FF232839"
                    android:layout_gravity="center"
                    android:layout_marginEnd="13dp"
                    android:textSize="16sp"
                    android:textFontWeight="@integer/m3_sys_motion_duration_long1"
                    />


            </LinearLayout>



        </androidx.appcompat.widget.LinearLayoutCompat>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@+id/narrowOutside"
            app:layout_constraintBottom_toBottomOf="parent"
            >

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/contentTiny"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="13dp"
                android:layout_marginRight="13dp"
                android:layout_marginTop="12dp"
                android:layout_weight="1"
                tool:itemCount="2"
                tool:listitem="@layout/layout_clock" />
            <!--tool:listitem="@layout/layout_clock"-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cancelBtn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="13dp"
                android:layout_marginRight="13dp"
                android:paddingVertical="15dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/containerThumbnail"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/wrapperUpper"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/nav_search"
                    android:textColor="#B8BABA"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/pagerDefault"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="13dp"
                android:layout_marginRight="13dp"
                android:layout_marginTop="12dp"
                android:layout_weight="1"

                tool:itemCount="2"
                tool:listitem="@layout/layout_avatar" />

            <RelativeLayout
                android:id="@+id/forwardShadow"
                android:layout_width="match_parent"
                android:layout_marginLeft="13dp"
                android:layout_marginRight="13dp"
                android:background="@drawable/bg_three_button_turquoise"
                android:layout_height="44dp"
                android:layout_margin="24dp"
                android:layout_marginBottom="20dp"
                >
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    >

                    <ImageView
                        android:id="@+id/insideFluid"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="10dp"
                        android:layout_gravity="center_vertical"
                        android:background="@drawable/ic_loading_copper"
                        android:visibility="gone"
                        />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/outsideUndo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:text="@string/common_ok_35"
                        android:textColor="@color/black"
                        android:textSize="17sp"
                        android:textStyle="bold" />



                </LinearLayout>


            </RelativeLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

<!--        <androidx.core.widget.NestedScrollView-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            >-->
<!--            <androidx.constraintlayout.widget.ConstraintLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                app:layout_constraintTop_toTopOf="parent"-->
<!--                app:layout_constraintBottom_toBottomOf="parent"-->
<!--                >-->

<!--                <androidx.appcompat.widget.AppCompatImageView-->
<!--                    android:id="@+id/emptyBelow"-->
<!--                    android:layout_width="20dp"-->
<!--                    android:layout_height="20dp"-->
<!--                    android:layout_marginTop="12dp"-->
<!--                    android:layout_marginEnd="12dp"-->
<!--                    android:src="@drawable/ic_close_gray_indigo"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintTop_toTopOf="parent" />-->

<!--                <androidx.appcompat.widget.AppCompatTextView-->
<!--                    android:id="@+id/maximalCounter"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginTop="15dp"-->
<!--                    android:text="@string/common_ok_33"-->
<!--                    android:textColor="#ff1a1a1a"-->
<!--                    android:textSize="17sp"-->
<!--                    android:textFontWeight="@integer/m3_sys_motion_duration_long1"-->
<!--                    android:layout_marginStart="13dp"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toTopOf="parent"-->
<!--                    tool:text="Payment method" />-->

<!--                <androidx.appcompat.widget.LinearLayoutCompat-->
<!--                    android:id="@+id/narrowOutside"-->
<!--                    android:layout_width="0dp"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginTop="20dp"-->
<!--                    android:gravity="center_horizontal"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginStart="13dp"-->
<!--                    android:layout_marginEnd="13dp"-->
<!--                    android:background="@drawable/bg_pay_channel_coin_bg_crimson"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toBottomOf="@+id/maximalCounter">-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="56dp"-->
<!--                        android:orientation="horizontal"-->
<!--                        >-->

<!--                        <TextView-->
<!--                            android:id="@+id/pickForward"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:text="Item"-->
<!--                            android:textColor="#313131"-->
<!--                            android:textSize="16sp"-->
<!--                            android:textFontWeight="@integer/m3_sys_motion_duration_long1"-->
<!--                            android:layout_marginStart="13dp"-->
<!--                            />-->

<!--                        <View-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            />-->

<!--                        <TextView-->
<!--                            android:id="@+id/closedMenu"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:text="Price"-->
<!--                            android:textColor="#313131"-->
<!--                            android:textSize="16sp"-->
<!--                            android:textFontWeight="@integer/m3_sys_motion_duration_long1"-->
<!--                            android:layout_marginEnd="13dp"-->
<!--                            />-->

<!--                    </LinearLayout>-->
<!--                    <View-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="0.5dp"-->
<!--                        android:background="#D2D2D2"-->
<!--                        />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="56dp"-->
<!--                        android:orientation="horizontal"-->
<!--                        >-->

<!--                        <androidx.appcompat.widget.LinearLayoutCompat-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:orientation="horizontal"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:layout_marginStart="13dp"-->

<!--                            >-->
<!--                            <androidx.appcompat.widget.AppCompatImageView-->
<!--                                android:id="@+id/downSelected"-->
<!--                                android:layout_width="24dp"-->
<!--                                android:layout_height="24dp"-->
<!--                                android:src="@drawable/ic_big_coin_ivory" />-->

<!--                            <androidx.appcompat.widget.AppCompatTextView-->
<!--                                android:id="@+id/primaryUp"-->
<!--                                android:layout_width="wrap_content"-->
<!--                                android:layout_height="wrap_content"-->
<!--                                android:layout_marginStart="4dp"-->
<!--                                tool:text="600"-->
<!--                                android:textColor="#313131"-->
<!--                                android:textSize="16sp"-->
<!--                                android:textStyle="bold" />-->
<!--                            <LinearLayout-->
<!--                                android:id="@+id/firstShadow"-->
<!--                                android:layout_width="wrap_content"-->
<!--                                android:layout_height="wrap_content"-->
<!--                                android:layout_gravity="center"-->
<!--                                android:paddingHorizontal="4dp"-->
<!--                                android:layout_marginStart="4dp"-->
<!--                                android:visibility="visible"-->
<!--                                android:background="@drawable/ic_coin_channel_ex_bg_bronze"-->
<!--                                >-->
<!--                                <androidx.appcompat.widget.AppCompatTextView-->
<!--                                    android:id="@+id/innerEmpty"-->
<!--                                    android:layout_width="wrap_content"-->
<!--                                    android:textSize="9sp"-->
<!--                                    tool:text="+1000"-->
<!--                                    android:layout_gravity="center"-->
<!--                                    android:textColor="@color/white"-->
<!--                                    android:layout_height="wrap_content"-->
<!--                                   />-->

<!--                            </LinearLayout>-->


<!--                        </androidx.appcompat.widget.LinearLayoutCompat>-->

<!--                        <View-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            />-->

<!--                        <androidx.appcompat.widget.AppCompatTextView-->
<!--                            android:id="@+id/frameAround"-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            tool:text="dsaa"-->
<!--                            android:textColor="#F23030"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:layout_marginEnd="13dp"-->
<!--                            android:textSize="15sp"-->
<!--                            android:textFontWeight="@integer/m3_sys_motion_duration_long1"-->
<!--                            />-->


<!--                    </LinearLayout>-->



<!--                </androidx.appcompat.widget.LinearLayoutCompat>-->

<!--                <androidx.recyclerview.widget.RecyclerView-->
<!--                    android:id="@+id/contentTiny"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginLeft="13dp"-->
<!--                    android:layout_marginRight="13dp"-->
<!--                    android:layout_marginTop="12dp"-->
<!--                    app:layout_constraintBottom_toTopOf="@id/cancelBtn"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toBottomOf="@+id/narrowOutside"-->
<!--                    tool:itemCount="2"-->
<!--                    tool:listitem="@layout/layout_clock" />-->
<!--                &lt;!&ndash;tool:listitem="@layout/layout_clock"&ndash;&gt;-->
<!--                <androidx.constraintlayout.widget.ConstraintLayout-->
<!--                    android:id="@+id/cancelBtn"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_marginLeft="13dp"-->
<!--                    android:layout_marginRight="13dp"-->
<!--                    android:paddingVertical="15dp"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toBottomOf="@+id/contentTiny">-->

<!--                    <androidx.appcompat.widget.AppCompatImageView-->
<!--                        android:id="@+id/containerThumbnail"-->
<!--                        android:layout_width="12dp"-->
<!--                        android:layout_height="12dp"-->
<!--                        app:layout_constraintBottom_toBottomOf="parent"-->
<!--                        app:layout_constraintEnd_toEndOf="parent"-->
<!--                        app:layout_constraintTop_toTopOf="parent" />-->

<!--                    <TextView-->
<!--                        android:id="@+id/wrapperUpper"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/nav_search"-->
<!--                        android:textColor="#B8BABA"-->
<!--                        android:textSize="16sp"-->
<!--                        android:textStyle="bold"-->
<!--                        app:layout_constraintBottom_toBottomOf="parent"-->
<!--                        app:layout_constraintStart_toStartOf="parent"-->
<!--                        app:layout_constraintTop_toTopOf="parent" />-->

<!--                </androidx.constraintlayout.widget.ConstraintLayout>-->

<!--                <androidx.recyclerview.widget.RecyclerView-->
<!--                    android:id="@+id/pagerDefault"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->

<!--                    android:layout_marginLeft="13dp"-->
<!--                    android:layout_marginRight="13dp"-->
<!--                    android:layout_marginTop="12dp"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toBottomOf="@+id/cancelBtn"-->
<!--                    tool:itemCount="2"-->
<!--                    tool:listitem="@layout/layout_avatar" />-->

<!--                <RelativeLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_marginLeft="13dp"-->
<!--                    android:layout_marginRight="13dp"-->
<!--                    android:background="@drawable/bg_three_button_turquoise"-->
<!--                    android:layout_height="44dp"-->
<!--                    app:layout_constraintBottom_toBottomOf="parent"-->
<!--                    app:layout_constraintEnd_toEndOf="parent"-->
<!--                    app:layout_constraintStart_toStartOf="parent"-->
<!--                    app:layout_constraintTop_toBottomOf="@+id/pagerDefault"-->
<!--                    android:layout_margin="24dp"-->
<!--                    >-->

<!--                    <androidx.appcompat.widget.AppCompatTextView-->
<!--                        android:id="@+id/outsideUndo"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_centerInParent="true"-->
<!--                        android:text="@string/common_ok_35"-->
<!--                        android:textColor="#ffffffff"-->
<!--                        android:textSize="17sp"-->
<!--                        android:textStyle="bold" />-->

<!--                </RelativeLayout>-->

<!--            </androidx.constraintlayout.widget.ConstraintLayout>-->
<!--        </androidx.core.widget.NestedScrollView>-->




    </androidx.constraintlayout.widget.ConstraintLayout>


