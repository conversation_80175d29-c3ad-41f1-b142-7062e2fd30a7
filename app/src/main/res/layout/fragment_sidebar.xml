<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_main_header_cyan"
    android:orientation="vertical"
    tools:context=".VideoCallActivity">

    <ImageView
        android:id="@+id/xlAdd"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        />



    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        >
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_centerVertical="true"
                android:gravity="center"
                >
                <RelativeLayout
                    android:layout_width="180dp"
                    android:layout_height="180dp">



                    <com.juicy.common.utils.view.CircleImageView
                        android:id="@+id/wrapClosed"
                        android:layout_width="108dp"
                        android:layout_height="108dp"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center_horizontal" />
                </RelativeLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/hiddenForeground"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:singleLine="true"
                    android:textColor="@color/black"
                    android:fontFamily="@font/protest_strike_regular"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:layout_gravity="center_horizontal"
                    tools:text="Name" />

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/adaptivePanel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:layout_gravity="center_horizontal">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/rightThin"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:visibility="gone"
                        android:src="@drawable/ic_call_sex_0_lead" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/reloadDisabled"
                        android:layout_width="30dp"
                        android:layout_height="20dp"
                        android:layout_marginStart="2dp"
                        android:includeFontPadding="false"
                        android:gravity="center"
                        android:background="@drawable/bg_famel_olive"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        tools:text="22" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:visibility="gone"
                        android:src="@drawable/raw_video_local_jade" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/fieldUpdate"
                        android:layout_width="wrap_content"
                        android:layout_marginStart="6dp"
                        android:layout_height="wrap_content"
                        android:maxLines="1"
                        android:ellipsize="end"
                        android:paddingVertical="4dp"
                        android:paddingHorizontal="6dp"
                        android:background="@drawable/bg_call_country_bg_rose"
                        android:includeFontPadding="false"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        tools:text="USA" />

                </androidx.appcompat.widget.LinearLayoutCompat>

                <LinearLayout
                    android:id="@+id/gridLarge"
                    android:layout_width="wrap_content"
                    android:layout_height="22dp"
                    android:layout_marginTop="20dp"
                    android:gravity="center"
                    android:paddingHorizontal="7dp"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    tools:visibility="visible"
                    >
                    <ImageView
                        android:layout_width="15dp"
                        android:layout_height="16dp"
                        android:visibility="gone"
                        android:src="@drawable/ic_big_coin_ivory"
                        />
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/copyPanel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        tools:text="You’ll be charged 120  coins per minute"
                        android:textColor="@color/black"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:textStyle="normal"
                        android:textSize="12sp"
                        />
                </LinearLayout>

            </LinearLayout>
        </RelativeLayout>


        <RelativeLayout
            android:id="@+id/holderAround"
            android:layout_width="match_parent"
            android:layout_height="115dp"
            android:layout_marginBottom="50dp"
            android:layout_marginStart="50dp"
            android:layout_marginEnd="40dp"

            >
            <com.juicy.common.utils.view.TransableImageView
                android:id="@+id/compactBorder"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@drawable/ic_hang_tan"
                android:layout_marginBottom="12dp"
                android:layout_alignParentBottom="true"
                android:layout_alignParentStart="true"
                />

            <com.juicy.common.utils.view.TransableImageView
                android:id="@+id/successFocused"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@drawable/ic_pick_up_black"
                android:layout_marginBottom="12dp"
                android:layout_alignParentBottom="true"
                android:layout_alignParentEnd="true"
                />

            
            <RelativeLayout
                android:id="@+id/lastFlexible"
                android:layout_width="wrap_content"
                android:layout_height="27dp"
                android:paddingHorizontal="5dp"
                android:layout_marginTop="5dp"
                android:layout_alignParentEnd="true"
                android:visibility="gone"
                >
                <ImageView
                    android:id="@+id/mdForward"
                    android:layout_width="wrap_content"
                    android:layout_height="27dp"
                    android:src="@drawable/ic_call_free_icon_turquoise"
                    />
                <TextView
                    android:id="@+id/stopNear"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="Free"
                    android:textStyle="bold"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:maxLines="1"
                    />
            </RelativeLayout>



        </RelativeLayout>
    </LinearLayout>

</FrameLayout>