<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"

    >

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="false"
        tools:context="com.juicy.Login.AuthActivity">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@mipmap/img_bg_platinum"
            android:scaleType="centerCrop"
            />

        <TextView
            android:id="@+id/stretchSelect"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            android:layout_marginBottom="68dp"
            android:background="@drawable/bg_ra_16_white_blue"
            android:gravity="center"
            android:paddingVertical="12dp"
            android:text="@string/common_less"
            android:textColor="@color/black"
            android:textSize="19sp"
            app:layout_constraintBottom_toTopOf="@id/defaultCounter"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:id="@+id/defaultCounter"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="32dp"
            android:layout_marginBottom="24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:orientation="horizontal"
            >

            <CheckBox
                android:id="@+id/topWrapper"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:button="@drawable/ic_custom_checkbox_selector_peach"
                android:checked="false" />

            <TextView
                android:paddingTop="2dp"
                android:id="@+id/unfocusedMask"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="我的寄快递设计费加快速度放大数据库风扇电机房间卡受打击开发机手打风扇电机封建时代"
                android:includeFontPadding="false"
                android:textColor="#000000"
                android:textSize="13sp" />
        </LinearLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>