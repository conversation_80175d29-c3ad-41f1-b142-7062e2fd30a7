<?xml version="1.0" encoding="utf-8"?>
<io.rong.imkit.widget.RoundCornerLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:layout_constraintCircleRadius="10dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">
<LinearLayout
    android:id="@+id/pressedBeyond"
    android:layout_width="200dp"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="12dp"
    android:orientation="vertical">
<TextView
    android:id="@+id/nextDisconnected"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:textColor="@color/black"
    android:textSize="16sp"
    android:gravity="start"/>
<com.juicy.common.utils.view.CircleImageView
    android:id="@+id/topSm"
    android:layout_width="176dp"
    android:layout_height="99dp"
    android:layout_marginTop="7.5dp"
    app:rouond_corner_radius="6dp"
    />
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#EBEBEB"
        android:layout_marginTop="8dp"
        />
    <RelativeLayout
        android:id="@+id/expandLong"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:gravity="start"
            android:textColor="#BCBBBF"
            android:textSize="12sp" />

        <ImageView
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_centerVertical="true"
             />

    </RelativeLayout>

</LinearLayout>
<LinearLayout
    android:id="@+id/thickHeader"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:visibility="gone"
    android:paddingEnd="6dp"
    android:paddingStart="4dp"
    android:orientation="horizontal">
<TextView
    android:id="@+id/normalMain"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:textSize="14sp"
    android:textColor="@color/white"
    android:layout_gravity="center_vertical"
    android:layout_marginStart="5dp"
    />
 <ImageView
     android:id="@+id/responsiveLower"
     android:layout_width="48dp"
     android:layout_height="48dp"
     android:layout_gravity="center_vertical"
     />
</LinearLayout>



</io.rong.imkit.widget.RoundCornerLinearLayout>

