<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:id="@+id/updateNested"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/longBorder"
        android:layout_width="224dp"
        android:layout_height="wrap_content"
        android:padding="8dp"

        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/maximalCounter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tool:text="11111" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/chipAbove"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="8dp"
            android:background="#E7E7E7"
            android:src="@drawable/img_empty_big_full_pearl"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/maximalCounter" />

        <View
            android:id="@+id/xxlSecondary"
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:layout_marginTop="12dp"
            android:background="#E8EAED"
            app:layout_constraintEnd_toEndOf="@+id/chipAbove"
            app:layout_constraintStart_toStartOf="@+id/chipAbove"
            app:layout_constraintTop_toBottomOf="@id/chipAbove" />
        <RelativeLayout
            android:id="@+id/selectedDimmed"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/xxlSecondary"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp">
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/showXl"
                style="@style/TextStyle.Alignment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/btn_play"
                android:textColor="#FFC6C6C6"
                android:textColorLink="@color/rc_blue"
                android:textSize="12sp"
                tool:ignore="RelativeOverlap" />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/badgeHolder"
                android:layout_width="16dp"
                android:layout_height="16dp"
                 android:layout_alignParentEnd="true"
                android:src="@drawable/ic_arrow_turn_azure"
                />


        </RelativeLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
<androidx.appcompat.widget.LinearLayoutCompat
    android:id="@+id/thickHeader"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="8dp"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    >

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_gravity="center_horizontal"
        >
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/normalMain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:text="@string/common_few"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="3dp"
            android:textStyle="bold"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/stretchRight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:maxWidth="50dp"
            android:layout_marginEnd="3dp"
            android:layout_gravity="center_vertical"
            tool:text="dasdasdasda"
            android:singleLine="true"
            android:textStyle="bold"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/checkedSwipe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:text="@string/common_many"
            android:layout_gravity="center_vertical"
            android:singleLine="true"
            android:textStyle="bold"
            />
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/dimmedToggle"
            android:layout_width="50dp"
            android:layout_height="40dp"
            android:scaleType="fitCenter"
            tool:src="@drawable/ic_gift_erhuan_green"
            />
    </LinearLayout>


</androidx.appcompat.widget.LinearLayoutCompat>


</androidx.constraintlayout.widget.ConstraintLayout>