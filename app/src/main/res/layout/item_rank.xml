<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/thickToggle"
    android:layout_height="72dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/extendedChecked"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="18dp"
        android:minWidth="25dp"
        android:textColor="@color/black"
        android:gravity="center"
        tools:text="1"
        android:fontFamily="@font/protest_strike_regular"
        android:textSize="30sp"
        android:textStyle="bold" />

    <com.juicy.common.utils.view.CircleImageView
        android:id="@+id/fluidForeground"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="18dp"
        android:layout_toEndOf="@+id/extendedChecked" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="18dp"
        android:layout_marginEnd="10dp"
        android:layout_toEndOf="@+id/fluidForeground"
        android:layout_toStartOf="@+id/backOverlay"
        >
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/avatarLarge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:singleLine="true"
            tools:text="Name"
            android:fontFamily="@font/protest_strike_regular"
            android:textSize="18sp" />
    </LinearLayout>
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/backOverlay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="16dp"
        android:textSize="20dp"
        android:text="..."
        android:visibility="gone"
        />


</RelativeLayout>