<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"

    xmlns:tools="http://schemas.android.com/tools">
    <data>
    </data>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="false"
        android:background="@drawable/bg_main_header_cyan"
        tools:context=".CoreActivity">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/selectedMini"
            android:orientation="vertical">
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:visibility="gone"
                android:background="#8966FF"
                />
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"

                />
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/white"
                android:layout_weight="2"
                />




        </LinearLayout>

        <FrameLayout
            android:id="@+id/contentSelect"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/selectedMini"
            />


        <LinearLayout
            android:id="@+id/selectedMini"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@color/white"
            android:layout_alignParentBottom="true">
<!--主播墙标签-->

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@id/home"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center">

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/focusedStop"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:src="@drawable/ic_home_select_emerald" />

                    <TextView
                        android:id="@+id/footerAdaptive"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/nav_home"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        android:visibility="gone" />

                </androidx.appcompat.widget.LinearLayoutCompat>


            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/narrowAvatar"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:layout_weight="1"
                >

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/pasteNormal"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_horizontal"
                        android:src="@drawable/ic_calls_select_tan" />

                    <TextView
                        android:id="@+id/wrapTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Calls"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        android:visibility="gone" />

                </androidx.appcompat.widget.LinearLayoutCompat>


            </androidx.appcompat.widget.LinearLayoutCompat>
<!--消息标签页-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@id/message"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:layout_weight="1"
                >

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/disabledExpand"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/xlPager"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:src="@drawable/ic_message_select_mint" />

                    <TextView
                        android:id="@+id/mediumPicker"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/msg_success"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        android:visibility="gone" />

                </androidx.appcompat.widget.LinearLayoutCompat>
                <TextView
                    android:textDirection="ltr"
                    android:id="@+id/timerFixed"
                    android:layout_width="wrap_content"
                    android:layout_height="15dp"
                    android:minWidth="15dp"
                    android:layout_gravity="center"
                    android:background="@drawable/bg_red_point_indigo"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:paddingHorizontal="4dp"
                    android:textColor="@color/white"
                    android:textSize="9sp"
                    android:visibility="gone"
                    tools:text="99+"
                    app:layout_constraintBottom_toTopOf="@+id/disabledExpand"
                    app:layout_constraintStart_toEndOf="@+id/disabledExpand"
                    android:layout_marginBottom="-10dp"
                    android:layout_marginStart="-5dp"
                    tools:visibility="visible" />


            </androidx.constraintlayout.widget.ConstraintLayout>




<!--个人中心标签页-->
            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/miniLocked"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:layout_weight="1"
                >

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/connectedMinimal"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:src="@drawable/ic_mine_select_magenta" />

                    <TextView
                        android:id="@+id/panelStop"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/common_ok"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        android:visibility="gone" />

                </androidx.appcompat.widget.LinearLayoutCompat>


            </androidx.appcompat.widget.LinearLayoutCompat>


        </LinearLayout>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="20dp"
            android:gravity="center_horizontal"
            android:layout_above="@id/selectedMini"
            android:layout_marginBottom="30dp"
            android:orientation="vertical">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/checkedSmall"
                android:layout_width="72dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_marginBottom="10dp">
               <!-- android:background="@drawable/new_icon"-->

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/dynamicRecycler"
                    android:layout_width="54dp"
                    android:layout_height="54dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_user_n_tan"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    />

                <TextView
                    android:id="@+id/nearThick"
                    android:layout_width="match_parent"
                    android:layout_height="22dp"
                    android:layout_marginTop="-10dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/bg_main_user_n_lead"
                    android:gravity="center"
                    android:paddingHorizontal="10dp"
                    android:text="11:11:11"
                    android:textColor="@color/white"
                    android:textFontWeight="700"
                    android:textSize="10sp"
                    app:layout_constraintTop_toBottomOf="@id/dynamicRecycler"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/filterCollapse"
                android:layout_width="72dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_marginBottom="10dp">
               <androidx.appcompat.widget.AppCompatImageView
                   android:id="@+id/enabledSmall"
                   android:layout_width="54dp"
                   android:layout_height="54dp"
                   android:scaleType="fitCenter"
                   android:src="@drawable/ic_active_emerald"
                   app:layout_constraintTop_toTopOf="parent"
                   app:layout_constraintStart_toStartOf="parent"
                   app:layout_constraintEnd_toEndOf="parent"
                   />

                <TextView
                    android:id="@+id/avatarTitle"
                    android:layout_height="22dp"
                    android:paddingHorizontal="6dp"
                    android:textSize="10sp"
                    android:gravity="center"
                    android:layout_marginTop="-16dp"
                    android:textFontWeight="700"
                    app:layout_constraintTop_toBottomOf="@id/enabledSmall"
                    tools:text="dsadasdas"
                    android:textColor="@color/white"
                    android:background="@drawable/bg_active_platinum"
                    android:layout_width="match_parent"
                    tools:ignore="SmallSp" />

            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/xsPick"
                android:layout_width="72dp"
                android:layout_height="wrap_content"
                >
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/middleSync"
                    android:layout_centerHorizontal="true"
                    android:layout_width="54dp"
                    android:layout_height="54dp"
                    android:src="@drawable/ic_coi_icon_crimson"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    />

                <TextView
                    android:id="@+id/warningUp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="-10dp"
                    android:layout_height="22dp"
                    android:textSize="10sp"
                    android:textFontWeight="700"
                    android:text="Store"
                    android:paddingHorizontal="6dp"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:background="@drawable/bg_coio_mint"
                    android:layout_width="match_parent"
                    app:layout_constraintTop_toBottomOf="@+id/middleSync"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    />
            </androidx.constraintlayout.widget.ConstraintLayout>



        </androidx.appcompat.widget.LinearLayoutCompat>

        <com.juicy.app.modules.base.dialog.NewUserDialogView
            android:id="@+id/loadedFront"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <com.juicy.app.modules.base.dialog.NewGoodDialogView
            android:id="@+id/clockXl"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            />

    </RelativeLayout>
</layout>