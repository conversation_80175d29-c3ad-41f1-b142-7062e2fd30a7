<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_match_bg_crimson"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".FlashFragment">
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        />
    <LinearLayout
        android:id="@+id/contentSelect"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        >

        <RelativeLayout

            android:layout_width="match_parent"
            android:layout_height="48dp">
            
            <LinearLayout
                android:id="@+id/adaptiveOnline"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:paddingHorizontal="10dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="12dp"
                android:background="@drawable/bg_falsh_vip_bg_maroon"
                android:gravity="center"
                android:visibility="gone"
                >
                <ImageView
                    android:layout_width="27dp"
                    android:layout_height="21dp"
                    android:layout_centerInParent="true"
                    android:src="@drawable/ic_falsh_vip_icon_crimson"
                    />
                <TextView
                    android:id="@+id/collapsedContent"
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:gravity="center"
                    android:layout_marginStart="5dp"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:textSize="14sp"
                    android:visibility="gone"
                    />

            </LinearLayout>

          <androidx.constraintlayout.widget.ConstraintLayout
              android:id="@+id/iconUp"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_centerVertical="true"
              android:layout_marginStart="10dp">

              <androidx.appcompat.widget.AppCompatImageView
                  android:id="@+id/mediumConfirm"
                  android:layout_width="44dp"
                  android:layout_height="18dp"
                  android:src="@drawable/bg_main_first_tab_beige"
                  app:layout_constraintTop_toTopOf="@id/maximalCounter"
                  android:layout_marginTop="16dp"
                  android:visibility="gone"
                  app:layout_constraintStart_toStartOf="@id/maximalCounter"
                  />

              <androidx.appcompat.widget.AppCompatTextView
                  android:id="@+id/nearForeground"
                  android:layout_width="59dp"
                  android:layout_height="26dp"
                  android:text="VIP"
                  android:textColor="@color/white"
                  android:gravity="center"
                  android:textStyle="bold"
                  android:layout_marginTop="3dp"
                  android:textSize="18sp"
                  android:background="@drawable/bg_vip_pop_solid_bg_violet"
                  app:layout_constraintTop_toTopOf="@id/addScroll"
                  app:layout_constraintStart_toStartOf="@id/addScroll"
                  app:layout_constraintBottom_toBottomOf="@id/addScroll"
                  android:layout_marginStart="18dp"/>

              <ImageView
                  android:id="@+id/addScroll"
                  android:layout_width="36dp"
                  android:layout_height="36dp"
                  app:layout_constraintTop_toTopOf="parent"
                  app:layout_constraintBottom_toBottomOf="parent"
                  app:layout_constraintStart_toStartOf="parent"
                  android:layout_marginStart="10dp"
                  android:src="@drawable/ic_vip_pop_king_icon_platinum"
                  />

              <androidx.appcompat.widget.AppCompatTextView
                  android:id="@+id/maximalCounter"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"
                  android:text="Match"
                  android:visibility="gone"
                  app:layout_constraintTop_toTopOf="parent"
                  app:layout_constraintBottom_toBottomOf="parent"
                  app:layout_constraintStart_toStartOf="parent"
                  android:textSize="22sp"
                  android:textColor="@color/black"
                  android:fontFamily="@font/protest_strike_regular"/>

              <androidx.appcompat.widget.AppCompatImageView
                  android:id="@+id/copyPrev"
                  android:layout_width="8dp"
                  android:layout_height="8dp"
                  android:src="@drawable/ic_main_star_violet"
                  android:visibility="gone"
                  app:layout_constraintEnd_toEndOf="parent"
                  app:layout_constraintTop_toTopOf="parent"
                  />

          </androidx.constraintlayout.widget.ConstraintLayout>


            <LinearLayout
                android:id="@+id/confirmMacro"
                android:layout_width="wrap_content"
                android:layout_height="26dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="26dp"
                android:background="@drawable/bg_match_rig_mint"
                >

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="10dp"
                    android:src="@drawable/ic_coi_icon_crimson"
                    android:layout_gravity="center_vertical"
                    />

                <TextView
                    android:id="@+id/rigidForward"
                    android:layout_width="wrap_content"
                    android:minWidth="50dp"
                    android:layout_height="wrap_content"
                    android:textSize="18sp"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="6dp"
                    android:layout_gravity="center_vertical"
                    android:textColor="@color/black"
                    android:textStyle="bold"
                    tools:text="100"
                    />


            </LinearLayout>

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/throughSearch"
            android:layout_width="76dp"
            android:layout_height="76dp"
            android:layout_marginStart="5dp"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            >

            <com.juicy.common.utils.view.CircleImageView
                android:id="@+id/loadedUndo"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:scaleType="centerCrop"
                android:layout_centerInParent="true"
                />


            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:visibility="gone"
                android:src="@drawable/ic_falsh_cub_ebony" />


        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/footerUnderlay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            >

            <com.airbnb.lottie.LottieAnimationView

                android:id="@+id/startNarrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:visibility="visible"
                app:lottie_autoPlay="true"
                app:lottie_fileName="fast_flash.json"
                app:lottie_loop="true"/>

        </RelativeLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clockToggle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="60dp"
            android:orientation="vertical"
            android:layout_gravity="center_horizontal"
            >

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/underlayTop"
                android:orientation="vertical">

            <TextView
                android:id="@+id/loadingVisible"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:paddingHorizontal="16dp"
                android:text="Random Match"
                android:minWidth="220dp"
                android:gravity="center"
                android:layout_centerHorizontal="true"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:textSize="17sp"
                android:background="@drawable/bg_falsh_btn_bg_cream"
                />

            <LinearLayout
                android:id="@+id/undoClosed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/underlayTop"
                android:layout_marginTop="10dp"
                android:layout_gravity="center_horizontal"
                >

                <TextView
                    android:id="@+id/closeBeside"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    tools:text="9"
                    android:textFontWeight="400"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    />
                <ImageView
                    android:layout_width="19dp"
                    android:layout_height="18dp"
                    android:src="@drawable/ic_coi_icon_crimson"
                    android:layout_marginStart="5dp"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    />

                <TextView
                    android:id="@+id/macroStatic"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:layout_marginStart="5dp"
                    tools:text="/ match"
                    android:textFontWeight="400"
                    android:layout_gravity="center_vertical"
                    android:gravity="center_vertical"
                    />

                </LinearLayout>

            </LinearLayout>

            <RelativeLayout
                android:id="@+id/underlayTop"
                android:layout_width="113dp"
                android:layout_height="48dp"
                android:layout_gravity="end"
                android:background="@drawable/ic_match_free_blue"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                >

                <ImageView
                    android:id="@+id/tabHeader"
                    android:layout_width="113dp"
                    android:layout_height="48dp"
                    />
                <TextView
                    android:id="@+id/stopNear"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Free"
                    android:gravity="center"
                    android:layout_centerHorizontal="true"
                    android:layout_marginStart="0dp"
                    android:textSize="16sp"
                    android:layout_marginEnd="0dp"
                    android:textFontWeight="800"
                    android:textColor="@color/white"
                    />

            </RelativeLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>




</RelativeLayout>