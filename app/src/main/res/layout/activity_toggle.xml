<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_main_header_cyan"
    tools:context="com.juicy.my.AppInfoActivity">
    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="190dp"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/primarySync"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="48dp">

        <LinearLayout
            android:id="@+id/underlayShort"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/startEnabled"
                android:background="@drawable/ic_rank_back_mint"
                android:layout_marginStart="16dp"
                android:layout_width="24dp"
                android:layout_height="24dp"/>
        </LinearLayout>

        <TextView
            android:id="@+id/switchSelect"
            android:text="@string/common_new"
            android:textSize="22sp"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:fontFamily="@font/protest_strike_regular"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            android:includeFontPadding="false"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
  <androidx.constraintlayout.widget.ConstraintLayout
      android:layout_width="match_parent"
      android:layout_height="0dp"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintTop_toBottomOf="@id/primarySync"
      >
      <androidx.appcompat.widget.AppCompatImageView
        app:layout_constraintTop_toTopOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          android:id="@+id/adaptiveInactive"
          android:layout_marginTop="40dp"
          android:src="@drawable/img_logo_app_ebony"
          android:layout_width="105dp"
          android:layout_height="105dp"/>
      <androidx.appcompat.widget.AppCompatTextView
          android:id="@+id/switchTiny"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:textColor="@color/basic_1a1a"
          android:text="@string/app_name"
          android:textStyle="bold"
          android:textSize="24sp"
          app:layout_constraintTop_toBottomOf="@id/adaptiveInactive"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          />

      <TextView
          android:id="@+id/expandedLong"
          app:layout_constraintTop_toBottomOf="@id/switchTiny"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintEnd_toEndOf="parent"
          android:layout_marginTop="6dp"
          android:textSize="12sp"
          android:textColor="#CDCBD1"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"/>

      <LinearLayout
          android:id="@+id/mainPick"
          android:orientation="vertical"
          app:layout_constraintTop_toBottomOf="@id/expandedLong"
          android:layout_marginTop="40dp"
          android:paddingHorizontal="16dp"
          android:layout_width="match_parent"
          android:layout_height="wrap_content">
          <RelativeLayout
              android:id="@+id/menuMinimal"
              android:layout_width="match_parent"
              android:background="@drawable/bg_setting_bg_coral"
              android:layout_height="64dp">
              <TextView
                  android:id="@+id/insideReleased"
                  android:text="@string/common_latest"
                  android:includeFontPadding="false"
                  android:textSize="15sp"
                  android:textStyle="bold"
                  android:layout_marginStart="14dp"
                  android:textColor="@color/black"
                  android:layout_centerVertical="true"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"/>
              <ImageView
                  android:id="@+id/primaryBorder"
                  android:layout_alignParentEnd="true"
                  android:layout_centerVertical="true"
                  android:src="@drawable/ic_arrow_turn_azure"
                  android:visibility="gone"
                  android:layout_width="16dp"
                  android:layout_height="16dp"/>

              <View
                  android:background="@color/basic_ebeb"
                  android:layout_alignParentBottom="true"
                  android:layout_width="match_parent"
                  android:layout_height="0.5dp"/>
          </RelativeLayout>

          <RelativeLayout
              android:id="@+id/innerRigid"
              android:layout_width="match_parent"
              android:layout_marginTop="12dp"
              android:background="@drawable/bg_setting_bg_coral"
              android:layout_height="64dp">
              <TextView
                  android:id="@+id/besideLong"
                  android:text="@string/common_oldest"
                  android:includeFontPadding="false"
                  android:layout_marginStart="14dp"
                  android:textSize="15sp"
                  android:textStyle="bold"
                  android:textColor="@color/black"
                  android:layout_centerVertical="true"
                  android:layout_width="wrap_content"
                  android:layout_height="wrap_content"/>
              <ImageView
                  android:id="@+id/previewCompact"
                  android:layout_alignParentEnd="true"
                  android:layout_centerVertical="true"
                  android:src="@drawable/ic_arrow_turn_azure"
                  android:layout_width="16dp"
                  android:visibility="gone"
                  android:layout_height="16dp"/>

              <View
                  android:background="@color/basic_ebeb"
                  android:layout_alignParentBottom="true"
                  android:layout_width="match_parent"
                  android:layout_height="0.5dp"/>
          </RelativeLayout>

          <RelativeLayout
              android:id="@+id/headerMinimal"
              android:layout_width="match_parent"
              android:layout_marginTop="12dp"
              android:background="@drawable/bg_setting_bg_coral"
              android:layout_height="64dp">
              <TextView
                  android:id="@+id/largeAuto"
                  android:text="@string/common_best"
                  android:includeFontPadding="false"
                  android:textSize="15sp"
                  android:textColor="@color/black"
                  android:layout_marginStart="14dp"
                  android:layout_centerVertical="true"
                  android:layout_width="wrap_content"
                  android:textStyle="bold"
                  android:layout_height="wrap_content"/>
              <ImageView
                  android:id="@+id/forwardFirst"
                  android:layout_alignParentEnd="true"
                  android:layout_centerVertical="true"
                 android:src="@drawable/ic_arrow_turn_azure"
                  android:visibility="gone"
                  android:layout_width="16dp"
                  android:layout_height="16dp"/>

                          <View
                              android:background="@color/basic_ebeb"
                              android:layout_alignParentBottom="true"
                              android:layout_width="match_parent"
                              android:layout_height="0.5dp"/>
          </RelativeLayout>
      </LinearLayout>
  </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.constraintlayout.widget.ConstraintLayout>