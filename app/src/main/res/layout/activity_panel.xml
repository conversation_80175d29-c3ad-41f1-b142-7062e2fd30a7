<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="190dp"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

<androidx.appcompat.widget.LinearLayoutCompat
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/selectVisible"
        android:layout_width="match_parent"
        android:layout_height="48dp"
     >

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/emptyBadge"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="16dp"
            android:src="@drawable/ic_rank_back_mint"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <RelativeLayout
            android:id="@+id/hugeSelected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:layout_marginStart="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/emptyBadge"
            app:layout_constraintTop_toTopOf="parent">

            <com.juicy.common.utils.view.CircleImageView
                android:id="@+id/fluidForeground"
                android:layout_width="36dp"
                android:layout_height="36dp" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignEnd="@id/fluidForeground"
                android:layout_alignBottom="@+id/fluidForeground">

                <View
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/bg_point_white_slate" />

                <View
                    android:id="@+id/copyLeft"
                    android:layout_width="9dp"
                    android:layout_height="9dp"
                    android:layout_centerInParent="true" />

            </RelativeLayout>


        </RelativeLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/stretchRight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:maxWidth="150dp"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="22sp"
            android:fontFamily="@font/protest_strike_regular"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/hugeSelected"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="dsadasdasddasdaasd" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/pressedOuter"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_chat_more_icon_mint"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/removeExtended"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/pressedOuter"
            app:layout_constraintTop_toTopOf="parent"
            >
            <com.juicy.common.utils.view.TransableImageView
                android:id="@+id/connectedInside"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:src="@drawable/ic_chat_video_icon_magenta"
                 />
        </LinearLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/expandedUnfocused"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:scaleType="centerCrop"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_chat_top_icon_charcoal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/removeExtended"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/minimalUndo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />

</androidx.appcompat.widget.LinearLayoutCompat>
    <RelativeLayout
        android:id="@+id/lockedInput"
        android:layout_width="match_parent"
        android:layout_height="282dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="134dp"
                android:background="@drawable/bg_limit_bg_sage"
                />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="148dp"
                android:background="@color/white"
                />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center_horizontal"

            >
            <ImageView
                android:layout_width="53dp"
                android:layout_height="53dp"
                android:src="@mipmap/ic_chat_lock_icon_khaki"
                android:layout_marginTop="140dp"
                />
            <RelativeLayout
                android:id="@+id/thickBrief"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginTop="9dp"
                android:layout_marginStart="53dp"
                android:layout_marginEnd="53dp"
                android:visibility="visible"
                android:background="@drawable/bg_extremely_btn_frost"

                android:orientation="horizontal">

                <TextView
                    android:id="@+id/pauseOuter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="Chat Now"
                    android:textColor="#FFFFFF"
                    android:textFontWeight="@integer/m3_sys_motion_duration_long1"
                    android:textSize="17sp" />
            </RelativeLayout>

        </LinearLayout>

    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>