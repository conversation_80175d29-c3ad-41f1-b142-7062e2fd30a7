<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/cutLocked"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:text="message"
            android:textColor="#111111"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

        <com.juicy.common.utils.RoundImageView
            android:id="@+id/fixedActive"
            android:layout_width="9dp"
            android:layout_height="9dp"
            android:layout_marginTop="4dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:type="circle" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/extendedPressed"
            android:layout_width="24dp"
            android:layout_height="8dp"
            android:layout_below="@+id/cutLocked"
            android:layout_centerHorizontal="true"
            android:src="@drawable/ic_bar_drawable_mint"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="2.5dp"
            app:layout_constraintTop_toBottomOf="@+id/cutLocked"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>