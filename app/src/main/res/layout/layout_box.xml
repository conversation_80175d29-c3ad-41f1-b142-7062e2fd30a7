<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:id="@+id/timerFront"
    android:background="@drawable/ic_full_coin_dialog_bg_salmon"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/wrapperCustom"
            android:layout_width="match_parent"
            android:layout_height="181dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.cardview.widget.CardView
            android:layout_width="96dp"
            android:layout_height="6dp"
            app:cardCornerRadius="3dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="8dp"
            android:visibility="gone"
            android:background="#ffffff"/>



        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/playSidebar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_toEndOf="@id/emptyBadge"
            android:visibility="gone"
            android:fitsSystemWindows="true"
            android:text="@string/common_ok_26"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/maximalCounter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="15dp"
            android:text="@string/common_ok_23"
            android:visibility="gone"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/confirmPaste"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_below="@id/maximalCounter"
            android:layout_marginTop="38dp"
            android:visibility="gone"
            android:orientation="horizontal">
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/mediumLast"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="20dp"
                android:src="@drawable/ic_coi_icon_crimson" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/wideClose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/mediumLast"
                android:layout_marginStart="10dp"
                android:layout_toEndOf="@id/mediumLast"
                android:layout_gravity="center_vertical"
                android:textColor="@color/black"
                android:textSize="40sp"
                android:textStyle="bold"
                tools:text="dasdasd" />

        </androidx.appcompat.widget.LinearLayoutCompat>






        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/confirmPaste"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:layoutDirection="ltr"
            android:orientation="vertical"
            android:paddingTop="16dp">

            <com.juicy.common.view.MdBannerView
                android:id="@+id/headerAbove"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_below="@id/wrapperCustom"
                android:layout_marginHorizontal="10dp"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="10dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/elasticPrimary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="10dp"
                android:gravity="center_horizontal"
                android:text="Recharge or become VIP to chat unlimited times"
                android:textColor="@color/material_3333" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/xxlUpper"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="8dp"
                android:layout_weight="1" />

            <LinearLayout
                android:id="@+id/gridExpand"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_profile_custom_bronze" />

                <TextView
                    android:id="@+id/lockedFill"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="5dp"
                    android:text="Customer Service"
                    android:textColor="#FF656D7B"
                    android:textSize="12sp" />

            </LinearLayout>


        </LinearLayout>
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/emptyBadge"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:layout_alignParentEnd="true"
            android:fitsSystemWindows="true"
            android:visibility="visible"
            android:src="@drawable/ic_vip_dialog_close_yellow" />
    </RelativeLayout>
<!--    <androidx.core.widget.NestedScrollView-->
<!--        android:layout_width="match_parent"-->
<!--        android:id="@+id/unfocusedPressed"-->
<!--        android:background="@drawable/bg_shop_top_bg_mint"-->
<!--        android:layout_height="match_parent">-->
<!--        -->
<!--    </androidx.core.widget.NestedScrollView>-->
</RelativeLayout>