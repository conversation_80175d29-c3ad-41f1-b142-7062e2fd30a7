<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/closedLeft"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    
    <LinearLayout
        android:id="@+id/foregroundRefresh"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:orientation="vertical"
        android:background="@drawable/bg_chat_left_bg_cyan"
        >
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/firstPicker"
            style="@style/TextStyle.Alignment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:breakStrategy="simple"
            android:hyphenationFrequency="none"
            android:maxWidth="230dp"
            tools:text="111dsadasdasdadasdasddadsadasdsadsada1"
            android:textColor="@color/white"
            android:textColorLink="@color/material_00ee"
            android:textSize="14sp" />

        <LinearLayout
            android:id="@+id/stretchLong"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#E8EAED"
            android:layout_marginTop="11dp"
            android:orientation="horizontal"
            />

        <LinearLayout
            android:id="@+id/throughEnd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="vertical"
            >

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/startPager"
                style="@style/TextStyle.Alignment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:breakStrategy="simple"
                android:hyphenationFrequency="none"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:text="111dsadasdasdadasdasddadsadadsadasdsada1"
                android:maxWidth="230dp"
                android:gravity="start"
                android:textColor="@color/white"
                android:textColorLink="@color/material_00ee"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/middleLg"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:background="@drawable/anim_translate_loading_indigo"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:visibility="gone"
                />

        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/copyDynamic"
        android:layout_width="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginTop="5dp"
        android:layout_below="@+id/foregroundRefresh"
        android:layout_height="18dp"
        android:orientation="horizontal"
        >
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/contentBeyond"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_translate_icon_blue"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/saveSurface"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/btn_pause"
            android:includeFontPadding="false"
            android:textColor="#FF007BFF"
            android:layout_marginStart="8dp"
            android:textSize="14sp"

            />
    </LinearLayout>


</RelativeLayout>

