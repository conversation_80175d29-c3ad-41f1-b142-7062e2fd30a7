<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal"
    >

    <View
        android:layout_width="65dp"
        android:layout_height="1dp"/>
    <RelativeLayout
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_marginEnd="6dp"
        android:layout_height="wrap_content"
        >
        <RelativeLayout
            android:id="@+id/calendarInner"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="5dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:minHeight="40dp"
            android:paddingTop="7dp"
            android:paddingBottom="7dp"
            android:background="@drawable/bg_robot_a_item_bg_violet"
            >
            <TextView
                android:layout_centerVertical="true"
                android:id="@+id/recyclerSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Hi! Welcome to here."
                android:textColor="@color/black"
                android:textSize="13sp"
                android:textStyle="bold"
                />

        </RelativeLayout>


    </RelativeLayout>
    <ImageView
        android:id="@+id/hiddenMicro"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:src="@drawable/img_empty_big_red"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="5dp"
        android:layout_gravity="top"
        />


</LinearLayout>