<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/beyondMenu"
    android:background="#99000000"
    xmlns:tools="http://schemas.android.com/tools">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/compactNarrow"
        android:layout_marginHorizontal="32dp"
        android:background="@drawable/ic_new_user_bg_ash"
        android:paddingHorizontal="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <RelativeLayout
            android:id="@+id/emptyBelow"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_marginTop="60dp"
            android:background="@color/transparent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            >

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                android:src="@drawable/ic_alter_close_bronze" />
        </RelativeLayout>


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/customMd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/emptyBelow"
            android:layout_marginTop="20dp"
            android:textSize="26sp"
            android:textColor="#000000"
            android:gravity="center"
            android:textStyle="bold"
            android:text="@string/common_ok_30"
            />



       <androidx.appcompat.widget.LinearLayoutCompat
           android:id="@+id/gridLarge"
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           android:orientation="vertical"
           android:layout_marginTop="40dp"
           android:gravity="center_horizontal"
           app:layout_constraintTop_toBottomOf="@id/customMd"
           app:layout_constraintStart_toStartOf="parent"
           app:layout_constraintEnd_toEndOf="parent">
           <androidx.appcompat.widget.LinearLayoutCompat
               android:layout_width="wrap_content"
               android:layout_height="wrap_content"
               android:orientation="horizontal"
               android:gravity="center_vertical"
               >
               <androidx.appcompat.widget.AppCompatImageView
                   android:layout_width="52dp"
                   android:layout_height="52dp"
                   android:src="@drawable/ic_coi_icon_crimson"
                   />
               <androidx.appcompat.widget.AppCompatTextView
                   android:id="@+id/viewFit"
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:textSize="40sp"
                   android:textStyle="bold"
                   android:layout_marginStart="5dp"
                   android:textColor="#000000"
                   tools:text="100"
                   />
           </androidx.appcompat.widget.LinearLayoutCompat>

           
       </androidx.appcompat.widget.LinearLayoutCompat>

        <RelativeLayout
            android:id="@+id/flexiblePanel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layoutDirection="ltr"
            app:layout_constraintTop_toBottomOf="@+id/gridLarge"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="20dp"
            >
            <ImageView
                android:id="@+id/imageCounter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_new_user_offp_tan"
                android:scaleType="centerCrop"
                />

            <TextView
                android:id="@+id/selectedExpanded"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:textSize="20sp"
                android:textStyle="bold"
                tools:text="60%"
                tools:textColor="@color/black" />
        </RelativeLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/flexibleBackground"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginHorizontal="30dp"
            android:layout_marginBottom="20dp"
            android:layout_marginTop="18dp"
            android:gravity="center"
            android:textColor="@color/black"
            tools:text="dsadas"
            android:textSize="20sp"
            android:textStyle="bold"
            android:background="@drawable/bg_new_usr_btn_gray"
            app:layout_constraintTop_toBottomOf="@+id/flexiblePanel"
            />

        <com.juicy.common.utils.view.TimeBoxLayout
            android:id="@+id/endFlexible"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_gravity="center"
            app:layout_constraintTop_toBottomOf="@id/flexibleBackground"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:gravity="center"
            android:layoutDirection="ltr"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="20dp"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_marginHorizontal="72dp"
        android:layout_height="189dp"
        android:src="@drawable/ic_new_user_icon_maroon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="-90dp"
        app:layout_constraintTop_toTopOf="@id/compactNarrow"/>


</androidx.constraintlayout.widget.ConstraintLayout>