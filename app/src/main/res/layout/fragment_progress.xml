<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="center"
    android:orientation="vertical"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:background="@drawable/bg_assess_iron"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/thinLeft"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="Sorry – Coins"
            android:textSize="22sp"
            android:fontFamily="@font/protest_strike_regular"
            android:textColor="@color/black"
            android:layout_gravity="center_horizontal"
            android:textFontWeight="@integer/m3_sys_motion_duration_long1"
            />

        <ImageView
            android:layout_width="91dp"
            android:layout_height="98dp"
            android:src="@drawable/ic_coi_icon_crimson"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="14dp"
            />

        <TextView
            android:id="@+id/chooseFocused"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Your call cut off due to a service problem. 12     added to your account to make up for it. Thanks for your support."
            android:textStyle="normal"
            android:textSize="14sp"
            android:textColor="@color/black"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:layout_marginTop="10dp"
            />

        <RelativeLayout
            android:id="@+id/customScroll"
            android:layout_width="match_parent"
            android:layout_marginLeft="40dp"
            android:layout_marginRight="40dp"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/bg_extremely_btn_frost"
            android:orientation="horizontal"
            android:layout_height="44dp">

            <TextView
                android:id="@+id/badgeMedium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="It's okay"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:textSize="18sp" />
        </RelativeLayout>

    </LinearLayout>




</LinearLayout>