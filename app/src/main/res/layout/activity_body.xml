<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".modules.base.activity.BotActivity">
    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="190dp"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/primarySync"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="48dp">

        <LinearLayout
            android:id="@+id/flexibleFixed"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/startEnabled"
                android:background="@drawable/ic_rank_back_mint"
                android:layout_marginStart="16dp"
                android:layout_width="24dp"
                android:layout_height="24dp"/>
        </LinearLayout>

        <TextView
            android:id="@+id/choosePlay"
            android:text="Customer Service"
            android:textSize="22sp"
            android:textColor="@color/black"
            android:fontFamily="@font/protest_strike_regular"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="16dp"
            android:includeFontPadding="false"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        
        
        <ImageView
            android:id="@+id/disconnectedContent"
            android:layout_width="30dp"
            android:layout_height="30dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:src="@drawable/ic_profile_custom_bronze"
            android:layout_marginEnd="16dp"
            />
        
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/chipWrapper"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingTop="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/bg_mine_bg_white_iron"
        app:layout_constraintTop_toBottomOf="@id/primarySync"/>

    <ImageView
        android:id="@+id/bodySurface"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:visibility="gone"
        />


</androidx.constraintlayout.widget.ConstraintLayout>