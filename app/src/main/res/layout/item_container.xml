<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="44dp"
    android:layout_marginEnd="16dp"
    tools:ignore="MissingDefaultResource">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/mediumConfirm"
        android:layout_width="44dp"
        android:layout_height="18dp"
        android:src="@drawable/bg_main_first_tab_beige"
        app:layout_constraintTop_toTopOf="@id/maximalCounter"
        android:layout_marginTop="16dp"
        app:layout_constraintStart_toStartOf="@id/maximalCounter"
        />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/maximalCounter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        android:textStyle="bold"
        android:textSize="22sp"
        android:textColor="#FF141414"
        android:fontFamily="@font/protest_strike_regular"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/xxlSecondary"
        tools:text="dsadsadsad" />

    <View
        android:id="@+id/xxlSecondary"
        android:layout_width="27dp"
        android:layout_height="4dp"
        android:background="@drawable/bg_login_cream"
        app:layout_constraintStart_toStartOf="@id/maximalCounter"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />
    <View
        android:id="@+id/nestedTab"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:visibility="gone"
        android:background="@drawable/bg_red_point_indigo"
        app:layout_constraintStart_toEndOf="@id/maximalCounter"
        app:layout_constraintTop_toTopOf="@id/maximalCounter"
        />
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/copyPrev"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:src="@drawable/ic_main_star_violet"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>