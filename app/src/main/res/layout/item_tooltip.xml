<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="16dp"
   >
    <View
        android:id="@id/stretchLong"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="#F3F3F5"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/mediumLast"
        app:layout_constraintStart_toStartOf="@id/stretchRight"
        app:layout_constraintEnd_toEndOf="@id/rigidChecked"
        app:layout_constraintBottom_toBottomOf="parent"
        />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/mediumLast"
        android:layout_width="29dp"
        android:layout_height="29dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/stretchRight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="13dp"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:text=""
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/mediumLast"
        />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/downloadCenter"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:autoSizeTextType="uniform"
        android:autoSizeMinTextSize="8sp"
        android:text=""
        android:textColor="#B8BABA"
        android:layout_marginStart="5dp"
        android:gravity="end"
        app:layout_constraintStart_toEndOf="@+id/stretchRight"
        app:layout_constraintEnd_toStartOf="@id/rigidChecked"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="4dp"
        />
       <androidx.appcompat.widget.AppCompatImageView
           android:id="@+id/rigidChecked"
           android:layout_width="12dp"
           android:layout_height="12dp"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintBottom_toBottomOf="parent"
           app:layout_constraintEnd_toEndOf="parent"
           android:layout_marginEnd="3dp"
           android:src="@drawable/ic_arrow_turn_azure"
           />
</androidx.constraintlayout.widget.ConstraintLayout>