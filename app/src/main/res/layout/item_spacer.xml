<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="49dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/topBottom"
    android:layout_marginHorizontal="12dp"
    xmlns:tools="http://schemas.android.com/tools">

        <View
            android:layout_width="44dp"
            android:layout_height="18dp"
            android:layout_marginTop="20dp"
            android:id="@+id/coordinatorToggle"
            android:layout_alignStart="@+id/outerMicro"
            android:layout_alignBottom="@+id/outerMicro"
            android:background="@drawable/bg_main_first_tab_beige"
            android:visibility="gone"
            tools:visibility="visible"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/outerMicro"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FF141414"
            android:textSize="22sp"
            android:fontFamily="@font/protest_strike_regular"
            android:layout_centerVertical="true"
            tools:text="dsadsadas"
            />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/copyPrev"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:src="@drawable/ic_main_star_violet"
            android:layout_marginTop="10dp"
            android:layout_alignEnd="@id/outerMicro"
            android:visibility="invisible"
            />


</RelativeLayout>