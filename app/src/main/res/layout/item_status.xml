<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="4dp"
    android:layout_marginBottom="8dp"
    android:id="@+id/collapseWide"
    android:layout_marginEnd="8dp"
    android:gravity="center_vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto">

<androidx.appcompat.widget.AppCompatTextView
    android:id="@+id/footerOpen"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:textSize="12sp"
    />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/frontExpand"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="6dp"
        android:textSize="12sp"
        android:textColor="@color/white"
        android:paddingHorizontal="8dp"
        />




</LinearLayout>