<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginHorizontal="20dp"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/fullContent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="12dp"
        android:background="@color/black"
        android:layout_width="20dp"
        android:layout_height="20dp"/>

    <LinearLayout
        android:id="@+id/lastExpanded"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/fullContent"
        android:layout_marginHorizontal="36dp"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/downloadEnd"
            android:textSize="20sp"
            android:textColor="@color/basic_1a1a"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </LinearLayout>

    <RelativeLayout
        android:orientation="horizontal"
        android:id="@+id/fixedFull"
        app:layout_constraintTop_toBottomOf="@id/lastExpanded"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="16dp"
        android:layout_marginTop="32dp"
        android:layout_marginHorizontal="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:id="@+id/redoLarge"
            android:layout_width="wrap_content"
            android:textSize="16sp"
            android:paddingHorizontal="41dp"
            android:background="@drawable/bg_ra_22_gray_green"
            android:paddingVertical="10dp"
            android:layout_alignParentStart="true"
            android:includeFontPadding="false"
            android:textColor="@color/basic_9999"
            android:text="@string/btn_reset"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/dragNormal"
            android:layout_width="wrap_content"
            android:text="@string/btn_clear"
            android:textSize="16sp"
            android:background="@drawable/bg_ra_22_blue_orange"
            android:layout_marginStart="8dp"
            android:layout_alignParentEnd="true"
            android:paddingHorizontal="41dp"
            android:paddingVertical="10dp"
            android:includeFontPadding="false"
            android:textColor="@color/white"
            android:layout_height="wrap_content"/>
    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>