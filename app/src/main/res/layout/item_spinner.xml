<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="74dp"
    android:layout_height="102dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_gift_num_frost"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/dimmedToggle"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/disconnectedStatic"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        tools:text="X1"
        android:textColor="@color/black"
        android:textSize="14sp"
        android:textStyle="bold|italic"
        android:textAlignment="textEnd"
        android:paddingBottom="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dimmedToggle" />

</androidx.constraintlayout.widget.ConstraintLayout>