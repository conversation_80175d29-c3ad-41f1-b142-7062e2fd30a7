<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="bottom"
    android:paddingHorizontal="24dp"
    android:paddingTop="40dp"
    android:paddingBottom="20dp"
    android:background="@drawable/bg_derivative_lemon">
    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/disabledExpand"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        >
        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            >
            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="72dp"
                android:layout_height="72dp"
                android:src="@drawable/img_logo_app_ebony"
                />
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:layout_marginTop="2dp"
                android:text="@string/common_ok_2"
                />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_convert_azure"
            android:layout_marginHorizontal="32dp"
            />
    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        >
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/disabledWithin"
            android:layout_width="72dp"
            android:layout_height="72dp"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/switchTiny"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:layout_marginTop="2dp"
            />
    </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.appcompat.widget.LinearLayoutCompat>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/xlOverlay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/black"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toBottomOf="@id/disabledExpand"
            app:layout_constraintStart_toStartOf="parent"
            />
    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/textAbove"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/xlOverlay"
        >
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/sliderLower"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:layout_marginBottom="8dp"
            tools:text="@string/text_1"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/centerBadge"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="14sp"
            tools:text="@string/text_2"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/backgroundPanel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="14sp"
            tools:text="@string/common_ok_64"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/confirmMedium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="14sp"
            tools:text="@string/msg_invalid"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/swipeEnabled"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="14sp"
            tools:text="@string/common_ok_65"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/addFull"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:layout_marginTop="8dp"
            android:textSize="14sp"
            tools:text="@string/common_ok_65"
            />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/chooseSecondary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:gravity="center_vertical"
        app:layout_constraintTop_toBottomOf="@id/textAbove"
        app:layout_constraintStart_toStartOf="parent"
        >
        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_transalate_beige"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/firstAuto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/btn_pause"
            android:textSize="12sp"
            android:textColor="#B7B5BD"
            />
    </androidx.appcompat.widget.LinearLayoutCompat>
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/startCollapsed"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxLines="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="12dp"
        android:paddingVertical="12dp"
        android:textColor="@color/black"
        android:text="@string/nav_down"
        android:textSize="17sp"
        app:autoSizeTextType="uniform"
        android:autoSizeMinTextSize="10sp"

        android:gravity="center"
        android:textStyle="bold"
        android:background="@drawable/bg_download_magenta"
        app:layout_constraintTop_toBottomOf="@id/chooseSecondary"
        />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/emptyBelow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/startCollapsed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="8dp"
        android:textSize="14sp"
        android:textColor="#FF007BFF"
        android:text="@string/time_tomorrow"
        />



</androidx.constraintlayout.widget.ConstraintLayout>