<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.scwang.smartrefresh.layout.SmartRefreshLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/smallOutside">

        <com.scwang.smartrefresh.header.MaterialHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/btnCenter"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:itemCount="10"
            tools:listitem="@layout/item_text"
            />
        <com.scwang.smartrefresh.layout.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.scwang.smartrefresh.layout.SmartRefreshLayout>



    <LinearLayout
        android:id="@+id/topExpanded"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"

        android:orientation="vertical">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/borderMacro"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@drawable/img_empty_white"
            android:visibility="gone"
            />
        <TextView
            android:id="@+id/gridDistant"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No Data"
            android:textColor="@color/app_color_grey"
            android:textSize="16sp"
            ></TextView>
    </LinearLayout>





</androidx.constraintlayout.widget.ConstraintLayout>