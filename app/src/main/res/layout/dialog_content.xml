<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginHorizontal="20dp"
        android:background="@drawable/bg_setting_dg_cherry">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/throughPlay"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_close_gray_indigo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_margin="12dp"
            android:visibility="invisible"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/stopDistant"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="20sp"
            android:textColor="@color/basic_1a1a"
            tools:text="Are your confirm to delete your account?"
            android:textStyle="bold"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/throughPlay"
            android:paddingHorizontal="30dp"
            />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/selectedCollapsed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="12dp"
            android:textColor="#A64DFF"
            android:textSize="16sp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/stopDistant"
            />
        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            app:layout_constraintTop_toBottomOf="@id/selectedCollapsed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="40dp"
            android:gravity="center_horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal"
            >
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/editLower"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:paddingVertical="10dp"
                android:singleLine="true"
                android:textStyle="bold"
                android:textSize="16sp"
                android:background="@drawable/bg_cancel_gray_platinum"
                android:text="No"
                android:textColor="#979999"
                android:gravity="center"
                />
            <View
                android:layout_width="30dp"
                android:layout_height="match_parent"
                />
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/holderMain"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:paddingVertical="10dp"
                android:singleLine="true"
                android:textStyle="bold"
                android:textSize="16sp"
                android:textColor="@color/black"
                android:background="@drawable/bg_submit_navy"
                android:text="Yes"
                android:gravity="center"
                />
        </androidx.appcompat.widget.LinearLayoutCompat>


    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

