<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/beyondMenu"
    android:background="#99000000"
    android:layout_height="match_parent">
   <RelativeLayout
       android:id="@+id/compactNarrow"
       android:layout_width="match_parent"
       android:layout_height="wrap_content"
       android:orientation="vertical"
       android:layout_marginHorizontal="32dp"
       android:background="@drawable/ic_active_bg_ebony"
       app:layout_constraintStart_toStartOf="parent"
       app:layout_constraintTop_toTopOf="parent"
       app:layout_constraintEnd_toEndOf="parent"
       app:layout_constraintBottom_toBottomOf="parent"
       >


       <RelativeLayout
           android:layout_width="30dp"
           android:layout_height="30dp"
           android:layout_marginEnd="40dp"
           android:layout_marginTop="50dp"
           android:background="@color/transparent"
           android:layout_alignParentEnd="true"
           >

           <androidx.appcompat.widget.AppCompatImageView
               android:id="@+id/emptyBelow"
               android:layout_width="20dp"
               android:layout_height="20dp"
               android:layout_centerInParent="true"
               android:src="@drawable/ic_alter_close_bronze" />
       </RelativeLayout>

   </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/disconnectedCustom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/briefFluid"
        android:paddingBottom="10dp"
        android:layout_marginHorizontal="30dp"
        >

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/panelFront"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingHorizontal="20dp"
            android:textColor="#000000"
            android:textSize="28sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Limite Time Offer" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnFar"
            android:layout_width="match_parent"
            android:layout_height="130dp"
            android:layout_marginHorizontal="5dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/panelFront"
            tools:src="@color/material_3333" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/contentAuto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:layoutDirection="ltr"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btnFar">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:src="@drawable/ic_coi_icon_crimson" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/viewFit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:textColor="#000000"
                android:textStyle="bold"
                android:textSize="40sp"
                tools:text="dsadasd" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/playBack"
                android:layout_width="62dp"
                android:layout_height="32dp"
                android:textColor="@color/black"
                android:layout_marginStart="4dp"
                android:textSize="20sp"
                android:layout_gravity="center"
                android:gravity="center"
                android:textStyle="bold"
                android:background="@drawable/ic_active_pop_salmon"
                tools:text="+100" />
        </androidx.appcompat.widget.LinearLayoutCompat>


        <com.juicy.common.utils.view.TimeBoxLayout
            android:id="@+id/customIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:layout_gravity="center"
            android:gravity="center"
            android:layoutDirection="ltr"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/flexibleBackground"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/flexibleBackground"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginHorizontal="26dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_active_btn_iron"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/contentAuto"
            tools:text="dsadasd" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/secondaryScroll"
            android:layout_width="42dp"
            android:layout_height="22dp"
            android:layout_marginStart="-35dp"
            android:background="@drawable/bg_num_bg_mint"
            android:layout_marginBottom="-8dp"
            android:gravity="center"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            app:layout_constraintStart_toEndOf="@id/flexibleBackground"
            app:layout_constraintBottom_toTopOf="@id/flexibleBackground"
            tools:text="x3" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/briefFluid"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="72dp"
        android:layout_height="136dp"
        android:layout_marginTop="-60dp"
        android:src="@drawable/ic_new_active_icon_steel"
        app:layout_constraintTop_toTopOf="@id/compactNarrow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>