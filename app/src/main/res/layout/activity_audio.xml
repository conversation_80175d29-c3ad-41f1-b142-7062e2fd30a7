<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_main_header_cyan"
    tools:context="com.juicy.my.BlockListActivity">
    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="190dp"

        android:scaleType="centerCrop"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/upXxl"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="48dp">
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/thickEdit"
                android:background="@drawable/ic_rank_back_mint"
                android:layout_width="24dp"
                android:layout_height="24dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginStart="16sp"/>

        <TextView
            android:id="@+id/autoClose"
            android:text="@string/common_old"
            android:textSize="22sp"
            android:fontFamily="@font/protest_strike_regular"
            android:textColor="#FF222222"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="16dp"
            android:textStyle="bold"
            android:includeFontPadding="false"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </androidx.constraintlayout.widget.ConstraintLayout>


      <androidx.recyclerview.widget.RecyclerView
          android:id="@+id/imageClear"
          android:layout_width="match_parent"
          app:layout_constraintTop_toBottomOf="@id/upXxl"
          app:layout_constraintBottom_toBottomOf="parent"
          android:layout_marginTop="10dp"
          android:background="@color/white"
          android:layout_height="0dp"/>
    <LinearLayout
        android:id="@+id/topExpanded"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"

        android:orientation="vertical">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/borderMacro"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:src="@drawable/img_empty_white"
            android:visibility="gone"
            />
        <TextView
            android:id="@+id/gridDistant"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No Data"
            android:textColor="@color/app_color_grey"
            android:textSize="16sp"
            ></TextView>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>