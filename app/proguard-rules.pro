# ========== 基础混淆配置 ==========
# 代码混淆压缩比，在0~7之间，默认为5，一般不做修改
-optimizationpasses 7

# 混合时不使用大小写混合，混合后的类名为小写
-dontusemixedcaseclassnames


-obfuscationdictionary cls.txt
-classobfuscationdictionary cls.txt
-packageobfuscationdictionary cls.txt

# 指定不去忽略非公共库的类
-dontskipnonpubliclibraryclasses

# 这句话能够使我们的项目混淆后产生映射文件
# 包含有类名->混淆后类名的映射关系
-verbose

# 指定不去忽略非公共库的类成员
-dontskipnonpubliclibraryclassmembers

# 不做预校验，preverify是proguard的四个步骤之一，Android不需要preverify，去掉这一步能够加快混淆速度
-dontpreverify

# 保留Annotation不混淆
-keepattributes *Annotation*,InnerClasses

# 避免混淆泛型
-keepattributes Signature

-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*
# 抛出异常时保留代码行号
#-keepattributes SourceFile,LineNumberTable

# 重命名源文件名为"SourceFile"
#-renamesourcefileattribute SourceFile

# ========== Android基础组件 ==========
# 保留四大组件，自定义的Application等这些类不被混淆
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View

# 保留support下的所有类及其内部类
-keep class android.support.**{*;}
-keep public class * extends android.support.v4.**
-keep public class * extends android.support.v7.**
-keep public class * extends android.support.annotation.**
-keep public class * extends android.app.Fragment


# 保留R下面的资源
-keep class **.R$* {*;}

# 保留本地native方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留在Activity中的方法参数是view的方法，
# 这样以来我们在layout中写的onClick就不会被影响
-keepclassmembers class * extends android.app.Activity{
    public void *(android.view.View);
}

# 保留枚举类不被混淆
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保留我们自定义控件（继承自View）不被混淆
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# 保留Parcelable序列化类不被混淆
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# 保留Serializable序列化的类不被混淆
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 对于带有回调函数的onXXEvent、**On*Listener的，不能被混淆
-keepclassmembers class * {
    void *(**On*Event);
    void *(**On*Listener);
}

# webView处理，项目中没有使用到webView忽略即可
-keepclassmembers class fqcn.of.javascript.interface.for.webview {
    public *;
}
-keepclassmembers class * extends android.webkit.webViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}
-keepclassmembers class * extends android.webkit.webViewClient {
    public void *(android.webkit.webView, jav.lang.String);
}

# ========== 网络请求相关 ==========
# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-dontwarn org.conscrypt.**
-keep class okhttp3.** { *; }
-keep class okio.** { *; }

# OkHttp3
-keepattributes Signature
-keepattributes *Annotation*
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**

# Gson
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# 保护 Gson TypeToken 泛型信息
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}
#
# 特别保护使用 TypeToken 的类
-keep class com.juicy.app.modules.wall.WallFragment$** { *; }
-keepclassmembers class com.juicy.app.modules.wall.WallFragment {
    ** getCacheWall();
}


# 保护所有匿名 TypeToken 类
-keep class **$*TypeToken* { *; }
-keep class **$*$* extends com.google.gson.reflect.TypeToken { *; }

# 保护所有数据模型类的泛型信息
-keep class com.juicy.common.bean.** { *; }
#-keep class com.juicy.app.bean.** { *; }
#-keep class com.juicy.other.bean.** { *; }
#-keep class **.bean.** { *; }
#-keep class **.*Bean { *; }
#-keep class **.*Bean$** { *; }

# 保护泛型参数化类型
-keepattributes EnclosingMethod
-keepattributes InnerClasses
-keep class * extends java.lang.reflect.ParameterizedType { *; }

# FastJson
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.fastjson.** { *; }
-keepattributes Signature
-keepattributes *Annotation*
# 保护 TypeReference 的泛型信息
-keep class com.alibaba.fastjson.TypeReference { *; }
-keepclassmembers class * extends com.alibaba.fastjson.TypeReference {
    <fields>;
    <methods>;
}
# 保护匿名内部类的泛型信息
-keepattributes EnclosingMethod
-keepattributes InnerClasses
# 特别保护 SafetyInterceptor 中的 TypeReference 使用
-keep class com.juicy.common.networks.request.SafetyInterceptor$* { *; }

# ========== 响应式编程 ==========
# RxJava RxAndroid
-dontwarn sun.misc.**
-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
    long producerIndex;
    long consumerIndex;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueProducerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode producerNode;
}
-keepclassmembers class rx.internal.util.unsafe.BaseLinkedQueueConsumerNodeRef {
    rx.internal.util.atomic.LinkedQueueNode consumerNode;
}
-dontnote rx.internal.util.PlatformDependent

# RxJava3
-keep class io.reactivex.rxjava3.** { *; }
-dontwarn io.reactivex.rxjava3.**

# RxBinding
-keep class com.jakewharton.rxbinding4.** { *; }
-dontwarn com.jakewharton.rxbinding4.**

# ========== 图片加载库 ==========
# ========== 图片加载库 ==========
# Glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
    **[] $VALUES;
    public *;
}
-dontwarn com.bumptech.glide.load.resource.bitmap.VideoDecoder

# Glide Transformations
-keep class jp.wasabeef.glide.transformations.** { *; }
-dontwarn jp.wasabeef.glide.transformations.**

# UCrop
-keep class com.yalantis.ucrop.** { *; }
-dontwarn com.yalantis.ucrop.**
-keep class com.yalantis.ucrop.util.RectUtils { *; }

# ========== 事件总线 ==========
# EventBus
-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

# ========== 数据库 ==========
# Room
-keep class androidx.room.** { *; }
-dontwarn androidx.room.**
-keep @androidx.room.Entity class *
-keep @androidx.room.Dao class *
-keep @androidx.room.Database class *

# ========== UI组件库 ==========
# RecyclerView Adapter
#-keep class com.chad.library.adapter.** { *; }
#-dontwarn com.chad.library.adapter.**
-keep class com.chad.library.adapter.** { *; }
-keep public class * extends com.chad.library.adapter.base.BaseQuickAdapter
-keep public class * extends com.chad.library.adapter.base.viewholder.BaseViewHolder
-keepclassmembers public class * extends com.chad.library.adapter.base.viewholder.BaseViewHolder {
    <init>(android.view.View);
}



# SmartRefreshLayout
-keep class com.scwang.smartrefresh.** { *; }
-dontwarn com.scwang.smartrefresh.**

# Banner
-keep class com.youth.banner.** { *; }
-dontwarn com.youth.banner.**

# PhotoView
-keep class com.github.chrisbanes.photoview.** { *; }
-dontwarn com.github.chrisbanes.photoview.**

# PictureSelector
-keep class com.luck.picture.lib.** { *; }
-dontwarn com.luck.picture.lib.**

# Country Picker
-keep class com.hbb20.** { *; }
-dontwarn com.hbb20.**

# ========== 动画库 ==========
# Lottie
-dontwarn com.airbnb.lottie.**
-keep class com.airbnb.lottie.** { *; }

# ========== 音视频处理 ==========
# Agora SDK
-keep class io.agora.**{*;}
-dontwarn io.agora.**

# ExoPlayer
-keep class com.google.android.exoplayer2.** { *; }
-dontwarn com.google.android.exoplayer2.**

# ========== 路由框架 ==========
# ARouter
-keep public class com.alibaba.android.arouter.routes.**{*;}
-keep public class com.alibaba.android.arouter.facade.**{*;}
-keep class * implements com.alibaba.android.arouter.facade.template.ISyringe{*;}
-keep interface * implements com.alibaba.android.arouter.facade.template.IProvider
-keep class * implements com.alibaba.android.arouter.facade.template.IProvider

# ========== 第三方SDK ==========
# Facebook SDK
-keep class com.facebook.** { *; }
-dontwarn com.facebook.**

# AppsFlyer SDK
-keep class com.appsflyer.** { *; }
-dontwarn com.appsflyer.**

# Google Play Services
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**

# Google Play Billing
-keep class com.android.billingclient.** { *; }
-dontwarn com.android.billingclient.**

# Install Referrer
-keep class com.android.installreferrer.** { *; }
-dontwarn com.android.installreferrer.**

# ========== 实时通信 ==========
# Socket.IO
-keep class io.socket.** { *; }
-dontwarn io.socket.**
-keep class org.json.** { *; }

# ========== 工具类库 ==========
# UtilCodeX
-keep class com.blankj.utilcode.** { *; }
-dontwarn com.blankj.utilcode.**

# ========== 日志库 ==========
# LoggingInterceptor
-keep class com.ihsanbal.logging.** { *; }
-dontwarn com.ihsanbal.logging.**

# ========== Google服务 ==========
# Google Cloud Translate
-keep class com.google.cloud.** { *; }
-dontwarn com.google.cloud.**

# ========== 自定义实体类保护 ==========
# 保留所有实体类（根据项目包名调整）
# -keep class com.yourpackage.model.** { *; }
# -keep class com.yourpackage.bean.** { *; }
# -keep class com.yourpackage.entity.** { *; }
-keep class com.juicy.common.model.** { *; }

# ========== 反射相关 ==========
# 保留所有使用@Keep注解的类和方法
-keep @androidx.annotation.Keep class * { *; }
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}

# ========== 其他配置 ==========
# 忽略警告
-ignorewarnings

# 优化时允许访问并修改有修饰符的类和类的成员
-allowaccessmodification


# 保留所有的JNI方法
-keepclasseswithmembernames class * {
    native <methods>;
}

-keepattributes Exceptions,InnerClasses

-keepattributes Signature

-keep class io.rong.** {*;}
-keep class cn.rongcloud.** {*;}
-keep class * implements io.rong.imlib.model.MessageContent {*;}
-dontwarn io.rong.push.**
-dontnote com.xiaomi.**
-dontnote com.google.android.gms.gcm.**
-dontnote io.rong.**

# 下方混淆使用了融云 IMKit 提供的 locationKit 位置插件时才需要配置，可参考高德官网的混淆方式:https://lbs.amap.com/api/android-sdk/guide/create-project/dev-attention
-keep class com.amap.api.maps.**{*;}
-keep class com.autonavi.**{*;}
-keep class com.amap.api.trace.**{*;}
-keep class com.amap.api.location.**{*;}
-keep class com.amap.api.fence.**{*;}
-keep class com.loc.**{*;}
-keep class com.autonavi.aps.amapapi.model.**{*;}
-keep class com.amap.api.services.**{*;}

-ignorewarnings


-keep class io.rong.push.notification.PushMessageReceiver {*;}

# 过滤Log
-assumenosideeffects class android.util.Log {
   public static int v(...);
   public static int d(...);
   public static int i(...);
   public static int w(...);
   public static int e(...);
}