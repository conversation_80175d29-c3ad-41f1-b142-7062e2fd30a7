<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 保存图片，拍照等 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <!-- 网络 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <!-- 消息通道保活 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 定位，如果您需要定位相关的功能，可以打开以下注释 -->
    <!-- <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> -->

    <uses-permission android:name="${applicationId}.permission.RONG_ACCESS_RECEIVER" />

    <!--融云自定义接收广播权限，用于广播接收-->
    <permission
        android:name="${applicationId}.permission.RONG_ACCESS_RECEIVER"
        android:protectionLevel="signature" />

    <application>
        <service
            android:name="io.rong.imlib.ipc.RongService"
            android:process=":ipc" />

        <receiver android:name="io.rong.imlib.ConnectChangeReceiver" />

        <receiver android:name="io.rong.imlib.HeartbeatReceiver" />


        <!--必选： SDK 核心功能-->
        <service
            android:name="io.rong.push.rongpush.PushService"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process="io.rong.push" />

        <!-- push 相关事件接收器 -->
        <receiver
            android:name="io.rong.push.rongpush.PushReceiver"
            android:exported="true"
            android:process="io.rong.push">   <!-- 此处进程可以改名，名称需要和PushService所在进程统一 -->
        </receiver>

        <activity
            android:name="io.rong.push.notification.RongBridgeActivity"
            android:enabled="true"
            android:exported="true"
            android:excludeFromRecents="true"
            android:launchMode="singleInstance"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="io.rong.push.notification.RongBridgeActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
    </application>

</manifest>
