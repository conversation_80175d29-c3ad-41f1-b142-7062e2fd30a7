apply plugin: 'com.android.library'
apply plugin: 'org.jetbrains.kotlin.android'

android {
    compileSdkVersion 34
    namespace 'cn.rongcloud.im.lib'
    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 34
        versionName "5.5.3"
    }

    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            res.srcDirs = ['res']
            jniLibs.srcDirs = ['libs']
        }
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    api 'com.google.code.gson:gson:2.8.6'
    implementation 'cn.rongcloud.sdk:crash:1.0.5'
    implementation 'androidx.core:core-ktx:1.13.1'
}
