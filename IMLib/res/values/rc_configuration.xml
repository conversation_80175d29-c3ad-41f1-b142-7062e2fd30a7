<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--原图压缩比例-->
    <integer name="rc_image_quality">85</integer>
    <!--原图压缩宽、高-->
    <integer name="rc_image_size">1080</integer>

    <!--配置发送图片时，如果图片大小不超过 200k 则发送原图-->
    <integer name="rc_max_original_image_size">200</integer>

    <!--缩略图压缩比例-->
    <integer name="rc_thumb_quality">30</integer>
    <!--缩略图压缩宽、高-->
    <integer name="rc_thumb_compress_size">240</integer>
    <!--缩略图压缩最小宽、高-->
    <integer name="rc_thumb_compress_min_size">100</integer>

    <!--位置消息缩略图压缩比例-->
    <integer name="rc_location_thumb_quality">70</integer>
    <!--位置消息缩略图压缩宽度-->
    <integer name="rc_location_thumb_width">408</integer>
    <!--位置消息缩略图压缩高度-->
    <integer name="rc_location_thumb_height">240</integer>

    <!-- 设置发送输入状态 -->
    <bool name="rc_typing_status">true</bool>
    <!-- 设置发送输入状态等待时间 -->
    <integer name="rc_disappear_interval">6000</integer>
    <!-- 下载文件默认存储路径 -->
    <string name="rc_media_message_default_save_path">/RongCloud/Media/</string>
    <!-- 是否开启 Extension 记忆功能 -->
    <bool name="rc_extension_history">true</bool>
    <!-- 设置心跳包的timer, 默认2分半（150000） , 最小值为10秒（10000）建议不要超过3分钟-->
    <string name="rc_heartbeat_timer">150000</string>
    <!-- 设置心跳包持有电源锁时间，默认 1 秒，建议不要超过 5 秒-->
    <string name="rc_heartbeat_acquire_time">1000</string>
    <!--&lt;!&ndash;实时位置共享最大人数&ndash;&gt;-->
    <!--<integer name="rc_max_realtime_location_participants">5</integer>-->
    <!--&lt;!&ndash;实时位置共享会话类型&ndash;&gt;-->
    <!--<string-array name="rc_realtime_support_conversation_types" translatable="false">-->
    <!--<item>private</item>-->
    <!--<item>group</item>-->
    <!--</string-array>-->


    <!--自定义配置重连间隔，可配置 n (0 < n <= 10) 个-->
    <!--轮询一遍后，第 n + 1 次 默认以第 n 个时间间隔进行重连。-->
    <!--<string-array name="rc_reconnect_interval">
    <item>0.05</item>
    <item>0.25</item>
    <item>0.5</item>
    <item>1</item>
    <item>2</item>
    <item>4</item>
    <item>8</item>
    <item>16</item>
    <item>32</item>
    <item>64</item>
    </string-array>-->

    <!-- 断点续传临界值，大于临界值开始采用分片下载，默认分4片（单位：Byte）默认 20M -->
    <integer name="rc_resume_file_transfer_size_each_slice">20971520</integer>
    <!--是否开启 Q 存储空间模式-->
    <bool name="rc_q_storage_mode_enable">true</bool>
    <!--是否 sp 加密模式-->
    <bool name="rc_secure_shared_preferences">false</bool>

    <!--小视频压缩宽度,建议使用16的倍数-->
    <integer name="rc_sight_compress_width">544</integer>
    <!--小视频压缩高度，建议使用16的倍数-->
    <integer name="rc_sight_compress_height">960</integer>

    <!--push 心跳,默认 4 min-->
    <string name="push_heartbeat_timer">240000</string>
</resources>