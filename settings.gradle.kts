pluginManagement {
    repositories {
        // Use Aliyun mirrors for better connectivity in China
        maven("https://maven.aliyun.com/repository/google")
        maven("https://maven.aliyun.com/repository/central")
        maven("https://maven.aliyun.com/repository/public") 
        gradlePluginPortal()
        // Fallback to original repositories
        google()
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        maven { 
            name = "AliYunGoogle"
            url = uri("https://maven.aliyun.com/repository/google") 
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        maven { 
            name = "AliYunCentral"
            url = uri("https://maven.aliyun.com/repository/central") 
        }
        mavenCentral()
        maven { 
            name = "AliYunPublic"
            url = uri("https://maven.aliyun.com/repository/public") 
        }
        maven { 
            name = "JitPack"
            url = uri("https://jitpack.io") 
        }
        // 融云 maven 仓库地址
        maven { 
            name = "RongCloud"
            url = uri("https://maven.rongcloud.cn/repository/maven-releases/") 
        }
    }
}

rootProject.name = "Juicy"
include(":app")
include(":IMLib")
include(":IMKit")
