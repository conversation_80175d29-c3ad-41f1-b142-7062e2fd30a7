# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
org.gradle.parallel=true
# Enable Gradle build cache
org.gradle.caching=true
# Enable configuration cache (temporarily disabled for testing)
org.gradle.configuration-cache=false
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
android.enableJetifier=true
# D8 memory optimizations
android.enableD8.desugaring=true
android.enableD8.fullMode=true
# Increase D8 memory allocation
android.d8.jvm.args=-Xmx4096m
# Enable R8 optimization
android.enableR8.fullMode=true
# Suppress unsupported compile SDK warning
android.suppressUnsupportedCompileSdk=35

# Network and repository configuration  
systemProp.org.gradle.internal.http.connectionTimeout=300000
systemProp.org.gradle.internal.http.socketTimeout=300000

android.aapt2FromMavenOverride=/Users/<USER>/Library/Android/sdk/build-tools/35.0.0/aapt2